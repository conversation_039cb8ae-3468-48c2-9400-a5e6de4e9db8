<?php
/**
 * 会员系统GraphQL用户元数据
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 注册用户元数据到GraphQL
 */
function fd_member_register_user_meta() {
    if (!function_exists('register_graphql_field')) {
        return;
    }
    
    register_graphql_field('User', 'avatarUrl', [
        'type' => 'String',
        'description' => '用户头像URL',
        'resolve' => function($user) {
            try {
                if (!isset($user->databaseId)) {
                    return null;
                }
                
                // 获取用户设置的头像
                $user_avatar_id = get_user_meta($user->databaseId, 'fd_user_avatar_id', true);
                
                if (!empty($user_avatar_id)) {
                    // 用户上传了自定义头像
                    $avatar_src = wp_get_attachment_image_src($user_avatar_id, 'full');
                    if ($avatar_src) {
                        return $avatar_src[0];
                    }
                }
            
                // 直接返回固定的默认头像URL，而不是查找数据库中的设置
                return 'https://img.futuredecade.com/s3/ai-generated-8388405_1280.jpg';
            } catch (\Exception $e) {
                error_log('GraphQL avatarUrl错误: ' . $e->getMessage());
                return null;
            }
        }
    ]);
    
    register_graphql_field('User', 'phone', [
        'type' => 'String',
        'description' => '用户绑定的手机号',
        'resolve' => function($user, $args, $context, $info) {
            try {
                if (!isset($user->databaseId)) {
                    return null;
                }
                
                // 获取当前请求用户
                $current_user = wp_get_current_user();
                
                // 检查权限：只有当前用户或管理员可以查看手机号
                if ($current_user->ID === $user->databaseId || current_user_can('manage_options')) {
                    return get_user_meta($user->databaseId, 'mobile_phone', true);
                }
                
                // 对其他用户返回null或隐藏后的手机号
                return null;
            } catch (\Exception $e) {
                error_log('GraphQL phone字段错误: ' . $e->getMessage());
                return null;
            }
        }
    ]);
    
    register_graphql_field('User', 'isPhoneVerified', [
        'type' => 'Boolean',
        'description' => '用户手机是否已验证',
        'resolve' => function($user) {
            return (bool) get_user_meta($user->databaseId, 'fd_user_phone_verified', true);
        }
    ]);
    
    // 添加checkAccountExists查询字段
    register_graphql_field('RootQuery', 'checkAccountExists', [
        'type' => 'Boolean',
        'description' => '检查账号信息是否已被使用',
        'args' => [
            'type' => [
                'type' => ['non_null' => 'String'],
                'description' => '检查类型（email/username/phone）',
            ],
            'value' => [
                'type' => ['non_null' => 'String'],
                'description' => '要检查的值',
            ],
        ],
        'resolve' => function($source, $args) {
            try {
                $type = $args['type'];
                $value = $args['value'];
                
                // 根据类型检查是否存在
                switch ($type) {
                    case 'email':
                        return email_exists(sanitize_email($value)) ? true : false;
                    
                    case 'username':
                        return username_exists(sanitize_user($value)) ? true : false;
                    
                    case 'phone':
                        if (function_exists('fd_member_phone_exists')) {
                            return fd_member_phone_exists(sanitize_text_field($value)) ? true : false;
                        }
                        return false;
                    
                    default:
                        return false;
                }
            } catch (\Exception $e) {
                error_log('GraphQL checkAccountExists错误: ' . $e->getMessage());
                return false;
            }
        },
    ]);
}
add_action('graphql_register_types', 'fd_member_register_user_meta'); 