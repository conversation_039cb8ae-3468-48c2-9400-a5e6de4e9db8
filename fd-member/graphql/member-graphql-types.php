<?php
/**
 * 会员系统GraphQL类型定义
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 注册会员相关的GraphQL类型
 */
function fd_member_register_graphql_types() {
    if (!function_exists('register_graphql_input_type')) {
        return;
    }
    
    // 手机验证码输入类型
    register_graphql_input_type('PhoneCodeInput', [
        'description' => '手机验证码输入类型',
        'fields' => [
            'phone' => [
                'type' => ['non_null' => 'String'],
                'description' => '手机号码',
            ],
            'code' => [
                'type' => ['non_null' => 'String'],
                'description' => '验证码',
            ],
            'nationCode' => [
                'type' => 'String',
                'description' => '国际区号（默认86）',
            ],
        ]
    ]);
    
    // 手机登录输入类型
    /*
    register_graphql_input_type('PhoneLoginInput', [
        'description' => '手机登录输入',
        'fields' => [
            'phone' => [
                'type' => ['non_null' => 'String'],
                'description' => '手机号码',
            ],
            'code' => [
                'type' => ['non_null' => 'String'],
                'description' => '短信验证码',
            ],
            'nationCode' => [
                'type' => 'String',
                'description' => '国际区号，默认86',
            ],
        ],
    ]);
    */
    
    // 手机号+密码登录输入类型
    register_graphql_input_type('PhonePasswordLoginInput', [
        'description' => '手机号+密码登录输入',
        'fields' => [
            'phone' => [
                'type' => ['non_null' => 'String'],
                'description' => '手机号码',
            ],
            'password' => [
                'type' => ['non_null' => 'String'],
                'description' => '密码',
            ],
        ],
    ]);
    
    // 手机注册输入类型
    /*
    register_graphql_input_type('PhoneRegisterInput', [
        'description' => '手机注册输入',
        'fields' => [
            'phone' => [
                'type' => ['non_null' => 'String'],
                'description' => '手机号码',
            ],
            'code' => [
                'type' => ['non_null' => 'String'],
                'description' => '短信验证码',
            ],
            'password' => [
                'type' => 'String',
                'description' => '密码',
            ],
            'nationCode' => [
                'type' => 'String',
                'description' => '国际区号，默认86',
            ],
            'email' => [
                'type' => 'String',
                'description' => '可选电子邮箱',
            ],
            'username' => [
                'type' => 'String',
                'description' => '用户名（可选）',
            ],
            'displayName' => [
                'type' => 'String',
                'description' => '显示名称（可选）',
            ],
        ],
    ]);
    */
    
    // 绑定手机输入类型
    /*
    register_graphql_input_type('BindPhoneInput', [
        'description' => '绑定手机输入',
        'fields' => [
            'phone' => [
                'type' => ['non_null' => 'String'],
                'description' => '手机号码',
            ],
            'token' => [
                'type' => ['non_null' => 'String'],
                'description' => '验证令牌',
            ],
            'nationCode' => [
                'type' => 'String',
                'description' => '国际区号，默认86',
            ],
        ],
    ]);
    */
}
add_action('graphql_register_types', 'fd_member_register_graphql_types'); 