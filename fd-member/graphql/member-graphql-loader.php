<?php
/**
 * 会员系统GraphQL功能加载器
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

// 加载GraphQL变更操作
require_once dirname(__FILE__) . '/member-graphql-mutations.php';

// 加载用户元数据相关
require_once dirname(__FILE__) . '/member-graphql-user-meta.php';

// 加载用户角色
require_once dirname(__FILE__) . '/user-role-graphql.php';

// 加载作者文章操作
require_once dirname(__FILE__) . '/author-post-mutations.php';

// 加载作者公开主页
require_once dirname(__FILE__) . '/author-profile-graphql.php';

// 加载GraphQL类型定义
require_once dirname(__FILE__) . '/member-graphql-types.php'; 