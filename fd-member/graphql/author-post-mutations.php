<?php
/**
 * GraphQL Mutations for post management by authors and higher roles.
 */

if (!defined('ABSPATH')) {
    exit;
}

add_action('graphql_register_types', function () {

    // 1. Mutation to Create a Post
    register_graphql_mutation(
        'createMyPost',
        [
            'inputFields' => [
                'title' => [
                    'type' => ['non_null' => 'String'],
                    'description' => __('The title of the post.'),
                ],
                'content' => [
                    'type' => 'String',
                    'description' => __('The content of the post.'),
                ],
                'status' => [
                    'type' => 'String',
                    'description' => __('The post status (e.g., "publish" or "draft"). Default "draft".'),
                ],
            ],
            'outputFields' => [
                'post' => [
                    'type' => 'Post',
                    'description' => __('The newly created post object.'),
                ],
            ],
            'mutateAndGetPayload' => function ($input, $context) {
                if (!current_user_can('publish_posts')) {
                    throw new \GraphQL\Error\UserError(__('You do not have permission to publish posts.', 'fd-member'));
                }

                $post_data = [
                    'post_title' => sanitize_text_field($input['title']),
                    'post_content' => wp_kses_post($input['content'] ?? ''),
                    'post_author' => get_current_user_id(),
                    'post_status' => in_array($input['status'] ?? 'draft', ['publish', 'draft'], true) ? $input['status'] : 'draft',
                ];

                $post_id = wp_insert_post($post_data, true);

                if (is_wp_error($post_id)) {
                    throw new \GraphQL\Error\UserError($post_id->get_error_message());
                }

                return ['post' => $context->get_loader('post')->load_deferred($post_id)];
            },
        ]
    );

    // 2. Mutation to Update a Post
    register_graphql_mutation(
        'updateMyPost',
        [
            'inputFields' => [
                'id' => [
                    'type' => ['non_null' => 'ID'],
                    'description' => __('The Global ID of the post to update.'),
                ],
                'title' => ['type' => 'String'],
                'content' => ['type' => 'String'],
                'status' => ['type' => 'String'],
            ],
            'outputFields' => [
                'post' => [
                    'type' => 'Post',
                    'description' => __('The updated post object.'),
                ],
            ],
            'mutateAndGetPayload' => function ($input, $context) {
                $post_id = \GraphQLRelay\Relay::fromGlobalId($input['id'])['id'] ?? 0;

                if (!current_user_can('edit_post', $post_id)) {
                    throw new \GraphQL\Error\UserError(__('You do not have permission to edit this post.', 'fd-member'));
                }

                $post_data = ['ID' => $post_id];
                if (isset($input['title'])) {
                    $post_data['post_title'] = sanitize_text_field($input['title']);
                }
                if (isset($input['content'])) {
                    $post_data['post_content'] = wp_kses_post($input['content']);
                }
                if (isset($input['status']) && in_array($input['status'], ['publish', 'draft'], true)) {
                    $post_data['post_status'] = $input['status'];
                }

                $result = wp_update_post($post_data, true);

                if (is_wp_error($result)) {
                    throw new \GraphQL\Error\UserError($result->get_error_message());
                }

                return ['post' => $context->get_loader('post')->load_deferred($post_id)];
            },
        ]
    );

    // 3. Mutation to Delete a Post
    register_graphql_mutation(
        'deleteMyPost',
        [
            'inputFields' => [
                'id' => [
                    'type' => ['non_null' => 'ID'],
                    'description' => __('The Global ID of the post to delete.'),
                ],
            ],
            'outputFields' => [
                'deletedId' => [
                    'type' => 'ID',
                    'description' => __('The Global ID of the deleted post.'),
                ],
            ],
            'mutateAndGetPayload' => function ($input) {
                $post_id = \GraphQLRelay\Relay::fromGlobalId($input['id'])['id'] ?? 0;

                if (!current_user_can('delete_post', $post_id)) {
                    throw new \GraphQL\Error\UserError(__('You do not have permission to delete this post.', 'fd-member'));
                }

                $result = wp_delete_post($post_id, true); // true to force delete

                if (false === $result) {
                    throw new \GraphQL\Error\UserError(__('Failed to delete the post.', 'fd-member'));
                }

                return ['deletedId' => $input['id']];
            },
        ]
    );

}); 