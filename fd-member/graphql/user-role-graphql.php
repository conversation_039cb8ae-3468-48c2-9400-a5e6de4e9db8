<?php
/**
 * Exposes user role in GraphQL schema.
 */

if (!defined('ABSPATH')) {
    exit;
}

add_action('graphql_register_types', function() {
    register_graphql_field('User', 'role', [
        'type' => 'String',
        'description' => __('The primary role of the user.', 'fd-member'),
        'resolve' => function($user) {
            if (empty($user->roles)) {
                return null;
            }
            // Return the first role in the array.
            return $user->roles[0];
        }
    ]);

    // Also add to the Viewer type (represents the current logged-in user)
    register_graphql_field('Viewer', 'role', [
        'type' => 'String',
        'description' => __('The primary role of the current logged-in user.', 'fd-member'),
        'resolve' => function($viewer) {
            $user = wp_get_current_user();
            if (!$user || 0 === $user->ID || empty($user->roles)) {
                return null;
            }
            return $user->roles[0];
        }
    ]);
}); 