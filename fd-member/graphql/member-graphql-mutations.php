<?php
/**
 * 会员系统GraphQL变更操作
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 注册会员相关的GraphQL变更
 */
function fd_member_register_graphql_mutations() {
    if (!function_exists('register_graphql_mutation')) {
        return;
    }

    // 用户头像更新变更
    register_graphql_mutation('updateAvatar', [
        'inputFields' => [
            'mediaId' => [
                'type' => 'ID',
                'description' => '媒体库中的图像ID',
            ],
            'base64Image' => [
                'type' => 'String',
                'description' => 'Base64编码的图像数据',
            ],
        ],
        'outputFields' => [
            'success' => [
                'type' => 'Boolean',
                'description' => '操作是否成功',
            ],
            'message' => [
                'type' => 'String',
                'description' => '操作结果消息',
            ],
            'avatarUrl' => [
                'type' => 'String',
                'description' => '用户头像URL',
                'resolve' => function($payload) {
                    return isset($payload['avatarUrl']) ? $payload['avatarUrl'] : null;
                }
            ],
            'user' => [
                'type' => 'User',
                'description' => '更新后的用户',
                'resolve' => function($payload) {
                    if (!isset($payload['user_id'])) {
                        return null;
                    }
                    // 获取GraphQL上下文
                    $context = \WPGraphQL::get_app_context();
                    // 正确调用resolve_user函数，传递两个参数
                    return \WPGraphQL\Data\DataSource::resolve_user($payload['user_id'], $context);
                }
            ],
        ],
        'mutateAndGetPayload' => function($input, $context, $info) {
            // 检查用户是否已登录
            if (!is_user_logged_in()) {
                return [
                    'success' => false,
                    'message' => '请先登录后再进行操作',
                    'avatarUrl' => null,
                ];
            }
            
            $user_id = get_current_user_id();
            $media_id = null;
            
            // 处理base64图像数据
            if (isset($input['base64Image']) && !empty($input['base64Image'])) {
                if (!function_exists('wp_handle_sideload')) {
                    require_once(ABSPATH . 'wp-admin/includes/file.php');
                }
                if (!function_exists('wp_generate_attachment_metadata')) {
                    require_once(ABSPATH . 'wp-admin/includes/image.php');
                }
                if (!function_exists('wp_crop_image')) {
                    require_once(ABSPATH . 'wp-admin/includes/image.php');
                }
                
                // 解码base64数据
                $base64_img = $input['base64Image'];
                
                // 检查是否是有效的base64图像数据
                if (strpos($base64_img, ';base64,') === false) {
                    return [
                        'success' => false,
                        'message' => '无效的图像数据格式'
                    ];
                }
                
                // 提取图像类型和数据
                list($type, $base64_data) = explode(';', $base64_img);
                list(, $base64_data) = explode(',', $base64_data);
                list(, $type) = explode(':', $type);
                
                // 获取文件扩展名
                $extension = 'jpg'; // 默认
                switch ($type) {
                    case 'image/jpeg':
                    case 'image/jpg':
                        $extension = 'jpg';
                        break;
                    case 'image/png':
                        $extension = 'png';
                        break;
                    case 'image/gif':
                        $extension = 'gif';
                        break;
                    default:
                        return [
                            'success' => false,
                            'message' => '不支持的图像格式'
                        ];
                }
                
                // 解码base64数据
                $img_data = base64_decode($base64_data);
                
                // 创建临时文件
                $upload_dir = wp_upload_dir();
                $temp_filename = wp_unique_filename($upload_dir['path'], 'avatar-' . time() . '.' . $extension);
                $temp_file = $upload_dir['path'] . '/' . $temp_filename;
                
                // 保存图像到临时文件
                file_put_contents($temp_file, $img_data);
                
                // 准备图像元数据
                $file = [
                    'name' => $temp_filename,
                    'type' => $type,
                    'tmp_name' => $temp_file,
                    'error' => 0,
                    'size' => filesize($temp_file)
                ];
                
                // 检查文件类型
                $mimes = [
                    'jpg|jpeg|jpe' => 'image/jpeg',
                    'png' => 'image/png',
                    'gif' => 'image/gif'
                ];
                
                // 移动上传的文件到正确的位置
                $overrides = [
                    'test_form' => false,
                    'test_size' => true,
                    'mimes' => $mimes
                ];
                
                // 处理文件上传
                $file_data = wp_handle_sideload($file, $overrides);
                
                if (isset($file_data['error'])) {
                    return [
                        'success' => false,
                        'message' => '头像上传失败: ' . $file_data['error']
                    ];
                }
                
                // 创建附件
                $attachment = [
                    'post_mime_type' => $file_data['type'],
                    'post_title' => sanitize_file_name(pathinfo($file_data['file'], PATHINFO_FILENAME)),
                    'post_content' => '',
                    'post_status' => 'inherit'
                ];
                
                $media_id = wp_insert_attachment($attachment, $file_data['file']);
                
                // 生成附件元数据
                $attachment_data = wp_generate_attachment_metadata($media_id, $file_data['file']);
                wp_update_attachment_metadata($media_id, $attachment_data);
            }
            // 使用已有媒体项
            else if (isset($input['mediaId']) && !empty($input['mediaId'])) {
                $media_id = absint(str_replace('post:', '', $input['mediaId']));
                
                // 检查媒体项是否存在且为图像
                $attachment = get_post($media_id);
                if (!$attachment || $attachment->post_type !== 'attachment' || 
                    strpos(get_post_mime_type($media_id), 'image/') !== 0) {
                    return [
                        'success' => false,
                        'message' => '所选媒体项不存在或不是有效的图像'
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'message' => '必须提供有效的媒体ID或图像数据'
                ];
            }
            
            if (!$media_id) {
                return [
                    'success' => false,
                    'message' => '头像处理失败'
                ];
            }
            
            // 更新用户头像ID（元数据）
            update_user_meta($user_id, 'fd_user_avatar_id', $media_id);
            
            // 获取头像URL
            $avatar_url = wp_get_attachment_image_url($media_id, 'thumbnail');
            
            return [
                'success' => true,
                'message' => '头像更新成功',
                'avatarUrl' => $avatar_url,
                'user_id' => $user_id
            ];
        }
    ]);
    
    // 用户信息更新变更
    register_graphql_mutation('updateProfile', [
        'inputFields' => [
            'displayName' => [
                'type' => 'String',
                'description' => '显示名称',
            ],
            'firstName' => [
                'type' => 'String',
                'description' => '名',
            ],
            'lastName' => [
                'type' => 'String',
                'description' => '姓',
            ],
            'description' => [
                'type' => 'String',
                'description' => '个人描述',
            ],
        ],
        'outputFields' => [
            'success' => [
                'type' => 'Boolean',
                'description' => '操作是否成功',
            ],
            'message' => [
                'type' => 'String',
                'description' => '操作结果消息',
            ],
            'user' => [
                'type' => 'User',
                'description' => '更新后的用户信息',
            ],
        ],
        'mutateAndGetPayload' => function($input, $context, $info) {
            // 检查用户是否已登录
            if (!is_user_logged_in()) {
                return [
                    'success' => false,
                    'message' => '请先登录后再进行操作',
                    'user' => null,
                ];
            }
            
            $user_id = get_current_user_id();
            $user_data = [];
            
            // 设置需要更新的用户数据
            if (isset($input['displayName'])) {
                $user_data['display_name'] = sanitize_text_field($input['displayName']);
            }
            if (isset($input['firstName'])) {
                $user_data['first_name'] = sanitize_text_field($input['firstName']);
            }
            if (isset($input['lastName'])) {
                $user_data['last_name'] = sanitize_text_field($input['lastName']);
            }
            if (isset($input['description'])) {
                $user_data['description'] = sanitize_textarea_field($input['description']);
            }
            
            if (empty($user_data)) {
                return [
                    'success' => false,
                    'message' => '没有提供任何更新信息',
                    'user' => \GraphQLRelay\Relay::toGlobalId('user', $user_id),
                ];
            }
            
            // 设置用户ID
            $user_data['ID'] = $user_id;
            
            // 更新用户
            $result = wp_update_user($user_data);
            
            if (is_wp_error($result)) {
                return [
                    'success' => false,
                    'message' => $result->get_error_message(),
                    'user' => null,
                ];
            }
            
            return [
                'success' => true,
                'message' => '个人信息更新成功',
                'user' => \GraphQLRelay\Relay::toGlobalId('user', $user_id),
            ];
        }
    ]);
    
    // 用户密码更新变更
    register_graphql_mutation('changePassword', [
        'inputFields' => [
            'currentPassword' => [
                'type' => ['non_null' => 'String'],
                'description' => '当前密码',
            ],
            'newPassword' => [
                'type' => ['non_null' => 'String'],
                'description' => '新密码',
            ],
        ],
        'outputFields' => [
            'success' => [
                'type' => 'Boolean',
                'description' => '操作是否成功',
                'resolve' => function($payload) {
                    return isset($payload['success']) ? $payload['success'] : false;
                }
            ],
            'message' => [
                'type' => 'String',
                'description' => '结果消息',
                'resolve' => function($payload) {
                    return isset($payload['message']) ? $payload['message'] : '';
                }
            ],
        ],
        'mutateAndGetPayload' => function($input, $context, $info) {
            try {
                // 必须登录
                if (!is_user_logged_in()) {
                    return [
                        'success' => false,
                        'message' => '用户未登录'
                    ];
                }
                
                // 验证输入
                if (!isset($input['currentPassword']) || empty($input['currentPassword'])) {
                    return [
                        'success' => false,
                        'message' => '当前密码不能为空'
                    ];
                }
                
                if (!isset($input['newPassword']) || empty($input['newPassword'])) {
                    return [
                        'success' => false,
                        'message' => '新密码不能为空'
                    ];
                }
                
                // 验证新密码强度
                if (strlen($input['newPassword']) < 8) {
                    return [
                        'success' => false,
                        'message' => '新密码必须至少包含8个字符'
                    ];
                }
                
                $current_password = $input['currentPassword'];
                $new_password = $input['newPassword'];
                
                $user = wp_get_current_user();
                if (!$user || !$user->ID) {
                    return [
                        'success' => false,
                        'message' => '无法获取当前用户'
                    ];
                }
                
                // 验证当前密码
                if(!wp_check_password($current_password, $user->user_pass, $user->ID)) {
                    return [
                        'success' => false,
                        'message' => '当前密码不正确'
                    ];
                }
                
                // 更新密码
                wp_set_password($new_password, $user->ID);
                
                // 更新成功后保持用户登录状态
                wp_set_auth_cookie($user->ID);
                
                return [
                    'success' => true,
                    'message' => '密码已成功更新'
                ];
            } catch (\Exception $e) {
                error_log('GraphQL changePassword错误: ' . $e->getMessage());
                return [
                    'success' => false,
                    'message' => '更改密码过程中发生错误'
                ];
            }
        }
    ]);
}
add_action('graphql_register_types', 'fd_member_register_graphql_mutations'); 