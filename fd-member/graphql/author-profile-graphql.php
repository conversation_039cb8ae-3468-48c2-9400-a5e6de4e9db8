<?php
/**
 * GraphQL schema for public author profiles.
 */

if (!defined('ABSPATH')) {
    exit;
}

add_action('graphql_register_types', function () {
    // 1. Define the custom PublicAuthorProfile type
    register_graphql_object_type(
        'PublicAuthorProfile',
        [
            'description' => __('A publicly accessible author profile with limited information.', 'fd-member'),
            'fields' => [
                'name' => [
                    'type' => 'String',
                    'description' => __('The display name of the author.'),
                    'resolve' => fn($user) => $user->display_name,
                ],
                'description' => [
                    'type' => 'String',
                    'description' => __('The biographical info of the author.'),
                    'resolve' => fn($user) => get_the_author_meta('description', $user->ID),
                ],
                'avatar' => [
                    'type' => 'Avatar',
                    'description' => __("The author's avatar.", 'fd-member'),
                    'resolve' => function ($user) {
                        $avatar_data = get_avatar_data($user->ID, ['size' => 192]);
                        return !empty($avatar_data['url']) ? $avatar_data : null;
                    },
                ],
                'slug' => [
                    'type' => 'String',
                    'description' => __('The URL-friendly slug of the author.'),
                    'resolve' => fn($user) => $user->user_nicename,
                ],
                'posts' => [
                    'type' => ['list_of' => 'Post'],
                    'description' => __('A list of published posts by the author.', 'fd-member'),
                    'args' => [
                        'first' => [
                            'type' => 'Int',
                            'defaultValue' => 10,
                        ],
                        'after' => [
                            'type' => 'String',
                            'defaultValue' => null,
                        ],
                    ],
                    'resolve' => function ($user, $args) {
                        $query_args = [
                            'author' => $user->ID,
                            'post_status' => 'publish',
                            'posts_per_page' => $args['first'],
                            'paged' => $args['after'] ? (int) $args['after'] : 1,
                            'fields' => 'ids', // 仅返回ID，提升效率
                        ];

                        $post_query = new WP_Query($query_args);
                        $post_ids   = $post_query->posts;

                        if (empty($post_ids)) {
                            return [];
                        }

                        $posts   = [];
                        $context = \WPGraphQL::get_app_context();

                        foreach ($post_ids as $post_id) {
                            $post = \WPGraphQL\Data\DataSource::resolve_post_object($post_id, $context);
                            if ($post) {
                                $posts[] = $post;
                            }
                        }

                        return $posts;
                    },
                ],
                'databaseId' => [
                    'type' => 'Int',
                    'description' => __('The numeric ID of the author.', 'fd-member'),
                    'resolve' => fn($user) => (int) $user->ID,
                ],
            ],
        ]
    );

    // 2. Register the new root query field
    register_graphql_field(
        'RootQuery',
        'getAuthorProfile',
        [
            'type' => 'PublicAuthorProfile',
            'description' => __('Retrieves a public profile for a user, only if they are an author or higher role.', 'fd-member'),
            'args' => [
                'slug' => [
                    'type' => ['non_null' => 'String'],
                    'description' => __('The user\'s slug (login name).'),
                ],
            ],
            'resolve' => function ($root, $args) {
                if (empty($args['slug'])) {
                    return null;
                }

                $user = get_user_by('slug', $args['slug']);

                if (!$user) {
                    return null; // User not found
                }

                // --- SECURITY CHECK ---
                // Only return a profile for users who are authors or higher.
                $allowed_roles = ['author', 'editor', 'administrator'];
                $user_roles = (array) $user->roles;
                $is_allowed = !empty(array_intersect($user_roles, $allowed_roles));

                if (!$is_allowed) {
                    return null; // Protect privacy of subscribers
                }

                return $user;
            },
        ]
    );
}); 