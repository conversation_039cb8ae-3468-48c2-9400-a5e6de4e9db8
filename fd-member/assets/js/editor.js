const { registerPlugin } = wp.plugins;
const { PluginDocumentSettingPanel } = wp.editPost;
const { SelectControl, TextControl } = wp.components;
const { withSelect, withDispatch } = wp.data;
const { compose } = wp.compose;
const { __ } = wp.i18n;

// fdMemberLevels is passed from PHP via wp_localize_script
const allLevels = window.fdMemberLevels || [];

const MemberAccessControl = ( { postType, postMeta, setPostMeta } ) => {
    const levelOptions = [
        { label: __('Public (Everyone)', 'fd-member'), value: 0 },
        { label: __('Logged-in Users', 'fd-member'), value: -1 },
        ...allLevels.map(level => ({
            label: level.name,
            value: level.id
        }))
    ];

    return (
        <PluginDocumentSettingPanel
            name="fd-member-access-control-panel"
            title={__('Content Access', 'fd-member')}
            className="fd-member-access-control-panel"
        >
            <SelectControl
                label={__('Required Member Level', 'fd-member')}
                value={postMeta._fd_required_member_level}
                options={levelOptions}
                onChange={(value) => setPostMeta({ _fd_required_member_level: parseInt(value, 10) })}
                help={__('Select the minimum member level required to view this post\'s full content.', 'fd-member')}
            />

            <TextControl
                type="number"
                label={__('单独解锁价格', 'fd-member')}
                value={postMeta._fd_unlock_price}
                onChange={(value) => setPostMeta({ _fd_unlock_price: parseFloat(value) })}
                help={__('设置用户单独购买此文章的价格。设置为0或留空表示不允许单独购买。', 'fd-member')}
                min="0"
                step="0.01"
            />
        </PluginDocumentSettingPanel>
    );
};

const enhance = compose([
    withSelect((select) => ({
        postMeta: select('core/editor').getEditedPostAttribute('meta'),
        postType: select('core/editor').getCurrentPostType(),
    })),
    withDispatch((dispatch) => ({
        setPostMeta(newMeta) {
            dispatch('core/editor').editPost({ meta: newMeta });
        },
    })),
]);

const EnhancedMemberAccessControl = enhance(MemberAccessControl);

registerPlugin('fd-member-content-access', {
    render: EnhancedMemberAccessControl,
    icon: 'groups',
}); 