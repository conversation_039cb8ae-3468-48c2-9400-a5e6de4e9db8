<?php
/**
 * 通用交互通知处理器
 *
 * 监听 `fd_reaction_added` 和 `fd_reaction_updated` 事件，
 * 并根据交互类型发送相应的通知给文章作者。
 *
 * @package FD\Member
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * 处理交互通知的核心函数
 *
 * @param string $type      交互类型: 'like', 'bookmark', 'recommend'
 * @param int    $user_id   执行交互的用户ID
 * @param int    $post_id   被交互的文章ID
 * @param int    $status    交互状态 (1=有效)
 */
function fd_handle_reaction_notification( $type, $user_id, $post_id, $status ) {
    // 仅在状态变为有效时发送通知
    if ( 1 !== $status ) {
        return;
    }

    // 确保通知核心功能可用
    if ( ! function_exists( 'fd_member_add_notification' ) ) {
        return;
    }

    $post = get_post( $post_id );
    if ( ! $post ) {
        return;
    }

    $author_id = (int) $post->post_author;
    $reactor   = get_userdata( $user_id );

    // 不给自己发通知，或交互者不存在
    if ( ! $reactor || $author_id === (int) $user_id ) {
        return;
    }

    // 根据类型定义通知内容
    $notification_data = [
        'like'      => [
            'title'   => '您的文章收到了新的点赞',
            'content' => sprintf( '%s 点赞了您的文章《%s》', $reactor->display_name, $post->post_title ),
        ],
        'bookmark'  => [
            'title'   => '您的文章被他人收藏',
            'content' => sprintf( '%s 收藏了您的文章《%s》', $reactor->display_name, $post->post_title ),
        ],
        'recommend' => [
            'title'   => '您的文章被他人推荐',
            'content' => sprintf( '%s 推荐了您的文章《%s》', $reactor->display_name, $post->post_title ),
        ],
    ];

    // 如果该交互类型没有定义通知，则不处理
    if ( ! isset( $notification_data[ $type ] ) ) {
        return;
    }

    // 发送通知
    fd_member_add_notification(
        $author_id,
        $notification_data[ $type ]['title'],
        $notification_data[ $type ]['content'],
        $type, // 将交互类型作为通知类型
        [
            'post_id'    => $post_id,
            'reactor_id' => $user_id,
        ]
    );
}

// 挂载到统一的事件钩子上
add_action( 'fd_reaction_added', 'fd_handle_reaction_notification', 10, 4 );
add_action( 'fd_reaction_updated', 'fd_handle_reaction_notification', 10, 4 ); 