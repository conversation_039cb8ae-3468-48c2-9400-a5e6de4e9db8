<?php
/**
 * 通用交互统计与缓存
 *
 * 统一处理点赞、收藏、推荐的计数缓存、后台列展示、
 * 数据清理及计划任务。
 *
 * @package FD\Member
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * 更新文章的指定类型交互数缓存
 *
 * @param string $type      交互类型: 'like', 'bookmark', 'recommend'
 * @param int    $post_id   文章ID
 * @return int 更新后的数量
 */
function fd_member_update_post_reaction_count_cache( $type, $post_id ) {
    $post_id = absint( $post_id );
    if ( ! $post_id ) {
        return 0;
    }
    $count = ReactionService::count( $type, $post_id );
    update_post_meta( $post_id, "_fd_{$type}_count", $count );
    return $count;
}

/**
 * 获取文章的指定类型交互数（优先从缓存读取）
 *
 * @param string $type      交互类型
 * @param int    $post_id   文章ID
 * @param bool   $refresh   是否强制刷新缓存
 * @return int
 */
function fd_member_get_post_reaction_count( $type, $post_id, $refresh = false ) {
    if ( $refresh ) {
        return fd_member_update_post_reaction_count_cache( $type, $post_id );
    }
    $cached_count = get_post_meta( $post_id, "_fd_{$type}_count", true );
    if ( '' === $cached_count ) {
        return fd_member_update_post_reaction_count_cache( $type, $post_id );
    }
    return (int) $cached_count;
}

/**
 * 为旧函数提供兼容性包装
 */
function fd_member_get_post_likes_count( $post_id, $refresh = false ) {
    return fd_member_get_post_reaction_count( 'like', $post_id, $refresh );
}
function fd_member_get_post_bookmarks_count( $post_id, $refresh = false ) {
    return fd_member_get_post_reaction_count( 'bookmark', $post_id, $refresh );
}
function fd_member_get_post_recommends_count( $post_id, $refresh = false ) {
    return fd_member_get_post_reaction_count( 'recommend', $post_id, $refresh );
}

/**
 * 当交互状态变更时，更新缓存
 */
function fd_member_update_cache_on_reaction_change( $type, $user_id, $post_id, $status ) {
    fd_member_update_post_reaction_count_cache( $type, $post_id );
}
add_action( 'fd_reaction_added', 'fd_member_update_cache_on_reaction_change', 10, 4 );
add_action( 'fd_reaction_updated', 'fd_member_update_cache_on_reaction_change', 10, 4 );

/**
 * 在文章列表中添加交互数量列
 */
function fd_member_add_reaction_columns( $columns ) {
    $columns['likes']      = '点赞数';
    $columns['bookmarks']  = '收藏数';
    $columns['recommends'] = '推荐数';
    return $columns;
}
add_filter( 'manage_posts_columns', 'fd_member_add_reaction_columns' );
add_filter( 'manage_pages_columns', 'fd_member_add_reaction_columns' );

/**
 * 显示文章交互数量
 */
function fd_member_show_reaction_columns( $column, $post_id ) {
    switch ( $column ) {
        case 'likes':
            echo fd_member_get_post_reaction_count( 'like', $post_id );
            break;
        case 'bookmarks':
            echo fd_member_get_post_reaction_count( 'bookmark', $post_id );
            break;
        case 'recommends':
            echo fd_member_get_post_reaction_count( 'recommend', $post_id );
            break;
    }
}
add_action( 'manage_posts_custom_column', 'fd_member_show_reaction_columns', 10, 2 );
add_action( 'manage_pages_custom_column', 'fd_member_show_reaction_columns', 10, 2 );

/**
 * 在文章删除前清理其所有交互记录
 */
function fd_member_cleanup_reactions_on_post_deletion( $post_id ) {
    global $wpdb;
    $wpdb->delete( $wpdb->prefix . 'reactions', [ 'post_id' => $post_id ], [ '%d' ] );
}
add_action( 'before_delete_post', 'fd_member_cleanup_reactions_on_post_deletion' );

/**
 * 在用户删除前清理其所有交互记录
 */
function fd_member_cleanup_reactions_on_user_deletion( $user_id ) {
    global $wpdb;
    $wpdb->delete( $wpdb->prefix . 'reactions', [ 'user_id' => $user_id ], [ '%d' ] );
}
add_action( 'deleted_user', 'fd_member_cleanup_reactions_on_user_deletion' ); 