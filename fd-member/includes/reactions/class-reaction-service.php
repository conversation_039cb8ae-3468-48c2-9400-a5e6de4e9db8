<?php
/**
 * ReactionService - 通用交互核心服务
 *
 * 封装了对 `fd_reactions` 表的所有数据库操作，
 * 并提供统一的事件钩子，供通知、统计等模块使用。
 *
 * @package FD\Member
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

class ReactionService {

    /**
     * 设置一个交互的状态 (添加/更新)
     *
     * @param string $type       交互类型: 'like', 'bookmark', 'recommend'
     * @param int    $user_id    用户ID
     * @param int    $post_id    文章ID
     * @param int    $status     状态: 1 = 有效, 0 = 取消
     *
     * @return bool|int 成功返回插入/更新的ID，失败返回false
     */
    public static function set( $type, $user_id, $post_id, $status = 1 ) {
        global $wpdb;

        $user_id = absint( $user_id );
        $post_id = absint( $post_id );
        $status  = absint( $status );

        if ( ! $user_id || ! $post_id || ! in_array( $type, [ 'like', 'bookmark', 'recommend' ] ) ) {
            return false;
        }

        $table_name = $wpdb->prefix . 'reactions';

        // 检查记录是否已存在
        $existing = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT id, status FROM {$table_name} WHERE type = %s AND user_id = %d AND post_id = %d",
                $type, $user_id, $post_id
            )
        );

        if ( $existing ) {
            // 如果状态没有变化，直接返回成功
            if ( (int) $existing->status === $status ) {
                return $existing->id;
            }

            // 更新现有记录
            $result = $wpdb->update(
                $table_name,
                [ 'status' => $status, 'updated_at' => current_time( 'mysql' ) ],
                [ 'id' => $existing->id ],
                [ '%d', '%s' ],
                [ '%d' ]
            );

            if ( false !== $result ) {
                // 触发状态更新事件
                do_action( 'fd_reaction_updated', $type, $user_id, $post_id, $status );
                return $existing->id;
            }

        } else {
            // 插入新记录
            $result = $wpdb->insert(
                $table_name,
                [
                    'type'       => $type,
                    'user_id'    => $user_id,
                    'post_id'    => $post_id,
                    'status'     => $status,
                    'created_at' => current_time( 'mysql' ),
                    'updated_at' => current_time( 'mysql' ),
                ],
                [ '%s', '%d', '%d', '%d', '%s', '%s' ]
            );

            if ( $result ) {
                $reaction_id = $wpdb->insert_id;
                // 触发新交互添加事件
                do_action( 'fd_reaction_added', $type, $user_id, $post_id, $status );
                return $reaction_id;
            }
        }

        return false;
    }

    /**
     * 获取用户对文章的交互状态
     *
     * @return bool|null true=已交互, false=未交互/已取消, null=记录不存在
     */
    public static function get_status( $type, $user_id, $post_id ) {
        global $wpdb;
        $status = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT status FROM {$wpdb->prefix}reactions WHERE type = %s AND user_id = %d AND post_id = %d",
                $type, absint( $user_id ), absint( $post_id )
            )
        );

        return ( null === $status ) ? null : (bool) $status;
    }

    /**
     * 检查用户是否执行了某个交互
     *
     * @return bool
     */
    public static function has( $type, $user_id, $post_id ) {
        return self::get_status( $type, $user_id, $post_id ) === true;
    }

    /**
     * 计算特定文章上特定交互类型的总数
     *
     * @return int
     */
    public static function count( $type, $post_id ) {
        global $wpdb;
        return (int) $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}reactions WHERE type = %s AND post_id = %d AND status = 1",
                $type, absint( $post_id )
            )
        );
    }

    /**
     * 获取用户的反应列表（用于个人中心显示）
     *
     * @param string $type     交互类型: 'like', 'bookmark', 'recommend'
     * @param int    $user_id  用户ID
     * @param int    $limit    限制数量
     * @param int    $offset   偏移量
     *
     * @return array 文章ID数组
     */
    public static function get_user_reactions( $type, $user_id, $limit = 10, $offset = 0 ) {
        global $wpdb;

        $user_id = absint( $user_id );
        $limit   = absint( $limit );
        $offset  = absint( $offset );

        if ( ! $user_id || ! in_array( $type, [ 'like', 'bookmark', 'recommend' ] ) ) {
            return [];
        }

        $table_name = $wpdb->prefix . 'reactions';

        $post_ids = $wpdb->get_col(
            $wpdb->prepare(
                "SELECT post_id FROM {$table_name}
                 WHERE type = %s AND user_id = %d AND status = 1
                 ORDER BY updated_at DESC
                 LIMIT %d OFFSET %d",
                $type, $user_id, $limit, $offset
            )
        );

        return array_map( 'absint', $post_ids );
    }

    /**
     * 获取用户反应的总数（用于分页）
     *
     * @param string $type     交互类型: 'like', 'bookmark', 'recommend'
     * @param int    $user_id  用户ID
     *
     * @return int
     */
    public static function get_user_reactions_count( $type, $user_id ) {
        global $wpdb;

        $user_id = absint( $user_id );

        if ( ! $user_id || ! in_array( $type, [ 'like', 'bookmark', 'recommend' ] ) ) {
            return 0;
        }

        $table_name = $wpdb->prefix . 'reactions';

        return (int) $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$table_name}
                 WHERE type = %s AND user_id = %d AND status = 1",
                $type, $user_id
            )
        );
    }
}