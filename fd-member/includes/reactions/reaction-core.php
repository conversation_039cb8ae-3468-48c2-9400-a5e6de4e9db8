<?php
/**
 * 通用交互（Reaction）核心文件
 * 仅包含数据库表创建逻辑；后续业务函数将在 ReactionService 中实现。
 */

// 防止直接访问
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * 创建通用交互表：wp_fd_reactions
 * 结构：id, type, user_id, post_id, status, created_at, updated_at
 */
function fd_member_reactions_create_table() {
    global $wpdb;

    // 前缀保持与站点一致
    $table_name      = $wpdb->prefix . 'reactions';
    $charset_collate = $wpdb->get_charset_collate();

    // 避免重复执行
    if ( $wpdb->get_var( $wpdb->prepare( "SHOW TABLES LIKE %s", $table_name ) ) === $table_name ) {
        return;
    }

    $sql = "CREATE TABLE {$table_name} (
        id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
        type ENUM('like','bookmark','recommend') NOT NULL,
        user_id BIGINT(20) UNSIGNED NOT NULL,
        post_id BIGINT(20) UNSIGNED NOT NULL,
        status TINYINT(1) NOT NULL DEFAULT 1,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY  (id),
        UNIQUE KEY user_post_type (type, user_id, post_id),
        KEY post_type_status (post_id, type, status),
        KEY user_type (user_id, type)
    ) {$charset_collate};";

    require_once ABSPATH . 'wp-admin/includes/upgrade.php';
    dbDelta( $sql );
}

// 插件激活时创建表（fd-member 主插件）
if ( function_exists( 'register_activation_hook' ) ) {
    register_activation_hook( dirname( dirname( __DIR__ ) ) . '/index.php', 'fd_member_reactions_create_table' );
}

// 为避免手动激活失败，可在首次 init 时兜底创建（仅限后台管理员）
add_action( 'init', function () {
    if ( is_admin() && current_user_can( 'manage_options' ) ) {
        fd_member_reactions_create_table();
    }
} ); 