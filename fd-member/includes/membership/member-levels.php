<?php
/**
 * 会员等级功能
 * 
 * 提供会员等级管理功能，包括添加、编辑、删除和获取会员等级
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

// 会员等级元数据键名
define('FD_MEMBER_LEVEL_META_KEY', 'fd_member_level');
define('FD_MEMBER_DEFAULT_LEVEL_OPTION', 'fd_member_default_level');
define('FD_MEMBER_LEVEL_EXPIRE_META_KEY', 'fd_member_level_expire');

/**
 * 获取所有会员等级
 * 
 * @return array 会员等级列表
 */
function fd_member_get_member_levels() {
    $levels = get_option('fd_member_levels', array());
    
    // 确保返回的是数组
    if (!is_array($levels)) {
        $levels = array();
    }
    
    return $levels;
}

/**
 * 获取单个会员等级
 *
 * @param int $level_id 等级ID
 * @return array|false 会员等级信息或false（如果不存在）
 */
function fd_member_get_member_level($level_id) {
    $levels = fd_member_get_member_levels();
    
    foreach ($levels as $level) {
        if (isset($level['id']) && $level['id'] == $level_id) {
            return $level;
        }
    }
    
    return false;
}

/**
 * 检查priority是否已被使用
 *
 * @param int $priority 优先级值
 * @param int $exclude_id 排除的等级ID（用于更新时）
 * @return bool 是否已被使用
 */
function fd_member_is_priority_used($priority, $exclude_id = null) {
    $levels = fd_member_get_member_levels();

    foreach ($levels as $level) {
        if (isset($level['priority']) && $level['priority'] == $priority) {
            // 如果是更新操作，排除当前等级
            if ($exclude_id && isset($level['id']) && $level['id'] == $exclude_id) {
                continue;
            }
            return true;
        }
    }

    return false;
}

/**
 * 添加新的会员等级
 *
 * @param array $level_data 会员等级数据
 * @return int|WP_Error 新会员等级ID或错误
 */
function fd_member_add_member_level($level_data) {
    if (empty($level_data['name'])) {
        return new WP_Error('missing_name', '会员等级名称不能为空');
    }

    // 验证priority唯一性（如果提供了priority）
    if (isset($level_data['priority']) && $level_data['priority'] !== '') {
        $priority = intval($level_data['priority']);
        if (fd_member_is_priority_used($priority)) {
            return new WP_Error('priority_exists', '优先级 ' . $priority . ' 已被其他等级使用，请选择不同的优先级');
        }
    }

    $levels = fd_member_get_member_levels();
    
    // 生成新的ID
    $max_id = 0;
    foreach ($levels as $level) {
        if (isset($level['id']) && $level['id'] > $max_id) {
            $max_id = $level['id'];
        }
    }
    $new_id = $max_id + 1;
    
    // 创建新等级
    $new_level = array(
        'id' => $new_id,
        'name' => sanitize_text_field($level_data['name']),
        'description' => isset($level_data['description']) ? sanitize_textarea_field($level_data['description']) : '',
        'priority' => isset($level_data['priority']) ? intval($level_data['priority']) : 0,
        'price' => isset($level_data['price']) ? floatval($level_data['price']) : 0,
        'duration' => isset($level_data['duration']) ? intval($level_data['duration']) : 0, // 默认为0表示永久有效
        'duration_unit' => isset($level_data['duration_unit']) ? sanitize_text_field($level_data['duration_unit']) : 'days' // 默认为天
    );
    
    $levels[] = $new_level;
    
    // 保存更新的等级列表
    update_option('fd_member_levels', $levels);
    
    return $new_id;
}

/**
 * 更新会员等级
 *
 * @param int $level_id 等级ID
 * @param array $level_data 更新的会员等级数据
 * @return bool|WP_Error 是否更新成功或错误
 */
function fd_member_update_member_level($level_id, $level_data) {
    if (empty($level_data['name'])) {
        return new WP_Error('missing_name', '会员等级名称不能为空');
    }

    // 验证priority唯一性（如果提供了priority）
    if (isset($level_data['priority']) && $level_data['priority'] !== '') {
        $priority = intval($level_data['priority']);
        if (fd_member_is_priority_used($priority, $level_id)) {
            return new WP_Error('priority_exists', '优先级 ' . $priority . ' 已被其他等级使用，请选择不同的优先级');
        }
    }

    $levels = fd_member_get_member_levels();
    $updated = false;
    
    foreach ($levels as $key => $level) {
        if (isset($level['id']) && $level['id'] == $level_id) {
            $levels[$key]['name'] = sanitize_text_field($level_data['name']);
            $levels[$key]['description'] = isset($level_data['description']) ? sanitize_textarea_field($level_data['description']) : '';
            $levels[$key]['priority'] = isset($level_data['priority']) ? intval($level_data['priority']) : 0;
            $levels[$key]['price'] = isset($level_data['price']) ? floatval($level_data['price']) : 0;
            $levels[$key]['duration'] = isset($level_data['duration']) ? intval($level_data['duration']) : 0;
            $levels[$key]['duration_unit'] = isset($level_data['duration_unit']) ? sanitize_text_field($level_data['duration_unit']) : 'days';
            $updated = true;
            break;
        }
    }
    
    if (!$updated) {
        return new WP_Error('level_not_found', '找不到指定的会员等级');
    }
    
    // 保存更新的等级列表
    update_option('fd_member_levels', $levels);
    
    return true;
}

/**
 * 删除会员等级
 *
 * @param int $level_id 等级ID
 * @return bool 是否删除成功
 */
function fd_member_delete_member_level($level_id) {
    $levels = fd_member_get_member_levels();
    $found = false;
    
    foreach ($levels as $key => $level) {
        if (isset($level['id']) && $level['id'] == $level_id) {
            unset($levels[$key]);
            $found = true;
            break;
        }
    }
    
    if ($found) {
        // 重新索引数组
        $levels = array_values($levels);
        
        // 保存更新的等级列表
        update_option('fd_member_levels', $levels);
        
        // 将使用此等级的用户重置为默认等级
        $users = get_users(array(
            'meta_key' => FD_MEMBER_LEVEL_META_KEY,
            'meta_value' => $level_id,
            'fields' => 'ID'
        ));
        
        // 获取默认等级
        $default_level_id = get_option(FD_MEMBER_DEFAULT_LEVEL_OPTION, 0);
        
        foreach ($users as $user_id) {
            if ($default_level_id) {
                update_user_meta($user_id, FD_MEMBER_LEVEL_META_KEY, $default_level_id);
            } else {
                delete_user_meta($user_id, FD_MEMBER_LEVEL_META_KEY);
            }
        }
        
        // 如果删除的是默认等级，则清除默认等级设置
        if (get_option(FD_MEMBER_DEFAULT_LEVEL_OPTION) == $level_id) {
            delete_option(FD_MEMBER_DEFAULT_LEVEL_OPTION);
        }
        
        return true;
    }
    
    return false;
}

/**
 * 设置用户的会员等级
 *
 * @param int $user_id 用户ID
 * @param int $level_id 等级ID
 * @param bool $reset_expiration 是否重置过期时间（默认为true）
 * @return bool 是否设置成功
 */
function fd_member_set_user_member_level($user_id, $level_id, $reset_expiration = true) {
    // 检查等级是否存在
    if ($level_id && !fd_member_get_member_level($level_id)) {
        return false;
    }
    
    // 设置用户等级
    if ($level_id) {
        // 如果需要重置过期时间
        if ($reset_expiration) {
            // 获取会员等级信息
            $level = fd_member_get_member_level($level_id);
            
            // 计算过期时间
            if ($level && isset($level['duration']) && $level['duration'] > 0) {
                $duration = intval($level['duration']);
                $duration_unit = isset($level['duration_unit']) ? $level['duration_unit'] : 'days';
                
                // 当前时间
                $now = current_time('timestamp');
                $expire_time = 0;
                
                // 根据不同时间单位计算过期时间
                switch ($duration_unit) {
                    case 'days':
                        $expire_time = strtotime("+{$duration} days", $now);
                        break;
                    case 'months':
                        $expire_time = strtotime("+{$duration} months", $now);
                        break;
                    case 'years':
                        $expire_time = strtotime("+{$duration} years", $now);
                        break;
                    default:
                        $expire_time = strtotime("+{$duration} days", $now);
                }
                
                // 保存过期时间
                update_user_meta($user_id, FD_MEMBER_LEVEL_EXPIRE_META_KEY, $expire_time);
            } else {
                // 如果没有设置有效期或无效，删除过期时间
                delete_user_meta($user_id, FD_MEMBER_LEVEL_EXPIRE_META_KEY);
            }
        }
        
        return update_user_meta($user_id, FD_MEMBER_LEVEL_META_KEY, $level_id);
    } else {
        delete_user_meta($user_id, FD_MEMBER_LEVEL_EXPIRE_META_KEY);
        return delete_user_meta($user_id, FD_MEMBER_LEVEL_META_KEY);
    }
}

/**
 * 获取用户的会员等级
 *
 * @param int $user_id 用户ID
 * @return array|false 用户的会员等级信息，如果没有则返回false
 */
function fd_member_get_user_member_level($user_id) {
    // 检查会员等级是否过期
    $expire_time = get_user_meta($user_id, FD_MEMBER_LEVEL_EXPIRE_META_KEY, true);
    
    // 如果设置了过期时间，检查是否已过期
    if ($expire_time && current_time('timestamp') > $expire_time) {
        // 过期了，尝试将用户降级到默认等级
        $default_level_id = get_option(FD_MEMBER_DEFAULT_LEVEL_OPTION, 0);
        if ($default_level_id) {
            // 降级到默认等级，不重置过期时间
            fd_member_set_user_member_level($user_id, $default_level_id, false);
            // 删除过期时间
            delete_user_meta($user_id, FD_MEMBER_LEVEL_EXPIRE_META_KEY);
        } else {
            // 没有默认等级，直接移除会员等级
            delete_user_meta($user_id, FD_MEMBER_LEVEL_META_KEY);
            delete_user_meta($user_id, FD_MEMBER_LEVEL_EXPIRE_META_KEY);
        }
    }
    
    $level_id = get_user_meta($user_id, FD_MEMBER_LEVEL_META_KEY, true);
    
    // 如果用户没有设置等级，尝试使用默认等级
    if (!$level_id) {
        $default_level_id = get_option(FD_MEMBER_DEFAULT_LEVEL_OPTION, 0);
        if ($default_level_id) {
            $level_id = $default_level_id;
        } else {
            return false;
        }
    }
    
    return fd_member_get_member_level($level_id);
}

/**
 * 获取用户的会员等级标识符 (slug or identifier).
 * 这是一个辅助函数，专门为JWT payload和WebSocket房间名设计。
 *
 * @param int $user_id 用户ID
 * @return string 用户的会员等级标识符, e.g., "level_1", "level_gold", or "default".
 */
function fd_member_get_user_level($user_id) {
    $level_info = fd_member_get_user_member_level($user_id);
    
    if ( ! $level_info || ! is_array($level_info) || ! isset($level_info['id']) ) {
        return 'default';
    }
    
    // 优先使用等级的ID作为标识符，格式为 "level_{id}"，确保唯一和稳定
    return 'level_' . $level_info['id'];
}

/**
 * 获取用户会员等级的过期时间
 *
 * @param int $user_id 用户ID
 * @return int|false 过期时间戳，如果没有设置或是永久会员，则返回false
 */
function fd_member_get_user_member_level_expiration($user_id) {
    return get_user_meta($user_id, FD_MEMBER_LEVEL_EXPIRE_META_KEY, true);
}

/**
 * 检查用户会员等级是否已过期
 *
 * @param int $user_id 用户ID
 * @return bool 是否已过期
 */
function fd_member_is_user_member_level_expired($user_id) {
    $expire_time = fd_member_get_user_member_level_expiration($user_id);
    
    // 如果没有设置过期时间，表示永久有效
    if (!$expire_time) {
        return false;
    }
    
    // 比较当前时间与过期时间
    return current_time('timestamp') > $expire_time;
}

/**
 * 格式化会员等级有效期显示
 *
 * @param array $level 会员等级信息
 * @return string 格式化后的有效期文本
 */
function fd_member_format_member_level_duration($level) {
    if (!isset($level['duration']) || $level['duration'] <= 0) {
        return '永久有效';
    }
    
    $duration = intval($level['duration']);
    $duration_unit = isset($level['duration_unit']) ? $level['duration_unit'] : 'days';
    
    switch ($duration_unit) {
        case 'days':
            return $duration . ' 天';
        case 'months':
            return $duration . ' 个月';
        case 'years':
            return $duration . ' 年';
        default:
            return $duration . ' 天';
    }
}

/**
 * 格式化用户会员到期时间显示
 *
 * @param int $user_id 用户ID
 * @return string 格式化后的到期时间文本
 */
function fd_member_format_user_member_expire_time($user_id) {
    $expire_time = fd_member_get_user_member_level_expiration($user_id);
    
    if (!$expire_time) {
        return '永久有效';
    }
    
    // 格式化日期
    return date_i18n('Y-m-d H:i:s', $expire_time);
}

/**
 * 设置默认会员等级
 *
 * @param int $level_id 等级ID
 * @return bool 是否设置成功
 */
function fd_member_set_default_member_level($level_id) {
    // 检查等级是否存在
    if ($level_id && !fd_member_get_member_level($level_id)) {
        return false;
    }
    
    if ($level_id) {
        return update_option(FD_MEMBER_DEFAULT_LEVEL_OPTION, $level_id);
    } else {
        return delete_option(FD_MEMBER_DEFAULT_LEVEL_OPTION);
    }
}

/**
 * 获取默认会员等级ID
 *
 * @return int|false 默认会员等级ID，如果没有设置则返回false
 */
function fd_member_get_default_member_level_id() {
    $default_level_id = get_option(FD_MEMBER_DEFAULT_LEVEL_OPTION, 0);
    return $default_level_id ? $default_level_id : false;
}

/**
 * 获取默认会员等级信息
 *
 * @return array|false 默认会员等级信息，如果没有设置则返回false
 */
function fd_member_get_default_member_level() {
    $default_level_id = fd_member_get_default_member_level_id();
    if (!$default_level_id) {
        return false;
    }
    
    return fd_member_get_member_level($default_level_id);
}

/**
 * 为新注册用户设置默认会员等级
 *
 * @param int $user_id 新用户ID
 */
function fd_member_set_default_level_for_new_user($user_id) {
    $default_level_id = fd_member_get_default_member_level_id();
    if ($default_level_id) {
        fd_member_set_user_member_level($user_id, $default_level_id);
    }
}
add_action('user_register', 'fd_member_set_default_level_for_new_user');

/**
 * 比较两个会员等级的优先级
 *
 * @param array $level1 第一个会员等级
 * @param array $level2 第二个会员等级
 * @return int 比较结果：1表示level1更高，-1表示level2更高，0表示相等
 */
function fd_member_compare_levels($level1, $level2) {
    $priority1 = isset($level1['priority']) ? intval($level1['priority']) : 0;
    $priority2 = isset($level2['priority']) ? intval($level2['priority']) : 0;

    if ($priority1 > $priority2) {
        return 1;
    } elseif ($priority1 < $priority2) {
        return -1;
    } else {
        return 0;
    }
}

/**
 * 检查用户是否可以升级到指定等级
 *
 * @param int $user_id 用户ID
 * @param int $target_level_id 目标等级ID
 * @return bool 是否可以升级
 */
function fd_member_can_upgrade_to_level($user_id, $target_level_id) {
    $current_level = fd_member_get_user_member_level($user_id);
    $target_level = fd_member_get_member_level($target_level_id);

    if (!$target_level) {
        return false;
    }

    // 如果用户没有当前等级，可以升级到任何等级
    if (!$current_level) {
        return true;
    }

    // 比较优先级
    return fd_member_compare_levels($target_level, $current_level) > 0;
}

/**
 * 获取等级层次描述
 *
 * @param int $priority 优先级
 * @return string 层次描述
 */
function fd_member_get_level_tier($priority) {
    if ($priority >= 100) return '钻石级';
    if ($priority >= 80) return '黄金级';
    if ($priority >= 50) return '白银级';
    if ($priority >= 20) return '青铜级';
    return '基础级';
}

/**
 * 获取所有会员等级（按优先级排序）
 *
 * @param bool $desc 是否降序排列（默认true，高优先级在前）
 * @return array 排序后的会员等级列表
 */
function fd_member_get_sorted_member_levels($desc = true) {
    $levels = fd_member_get_member_levels();

    // 按优先级排序
    usort($levels, function($a, $b) use ($desc) {
        $a_priority = isset($a['priority']) ? intval($a['priority']) : 0;
        $b_priority = isset($b['priority']) ? intval($b['priority']) : 0;

        if ($desc) {
            return $b_priority - $a_priority; // 降序排列，高优先级在前
        } else {
            return $a_priority - $b_priority; // 升序排列，低优先级在前
        }
    });

    return $levels;
}

/**
 * 检查会员等级配置的合理性
 *
 * @param array $level_data 会员等级数据
 * @param int $exclude_id 排除的等级ID（用于更新时）
 * @return array 检查结果 ['valid' => bool, 'warnings' => array, 'suggestions' => array]
 */
function fd_member_validate_level_config($level_data, $exclude_id = null) {
    $result = array(
        'valid' => true,
        'warnings' => array(),
        'suggestions' => array()
    );

    $priority = isset($level_data['priority']) ? intval($level_data['priority']) : 0;
    $price = isset($level_data['price']) ? floatval($level_data['price']) : 0;
    $duration = isset($level_data['duration']) ? intval($level_data['duration']) : 0;
    $duration_unit = isset($level_data['duration_unit']) ? $level_data['duration_unit'] : 'days';

    // 获取其他等级进行比较
    $other_levels = fd_member_get_member_levels();
    if ($exclude_id) {
        $other_levels = array_filter($other_levels, function($level) use ($exclude_id) {
            return isset($level['id']) && $level['id'] != $exclude_id;
        });
    }

    // 检查价格与优先级的关系
    foreach ($other_levels as $other_level) {
        $other_priority = isset($other_level['priority']) ? intval($other_level['priority']) : 0;
        $other_price = isset($other_level['price']) ? floatval($other_level['price']) : 0;

        // 如果优先级更高但价格更低，给出警告
        if ($priority > $other_priority && $price < $other_price && $price > 0 && $other_price > 0) {
            $result['warnings'][] = sprintf(
                '优先级 %d 高于现有等级"%s"(优先级 %d)，但价格 %.2f 元低于其价格 %.2f 元',
                $priority, $other_level['name'], $other_priority, $price, $other_price
            );
        }

        // 如果优先级更低但价格更高，给出警告
        if ($priority < $other_priority && $price > $other_price && $price > 0 && $other_price > 0) {
            $result['warnings'][] = sprintf(
                '优先级 %d 低于现有等级"%s"(优先级 %d)，但价格 %.2f 元高于其价格 %.2f 元',
                $priority, $other_level['name'], $other_priority, $price, $other_price
            );
        }
    }

    // 检查有效期的合理性
    if ($duration > 0) {
        // 转换为天数进行比较
        $duration_in_days = fd_member_convert_duration_to_days($duration, $duration_unit);

        foreach ($other_levels as $other_level) {
            $other_priority = isset($other_level['priority']) ? intval($other_level['priority']) : 0;
            $other_duration = isset($other_level['duration']) ? intval($other_level['duration']) : 0;
            $other_duration_unit = isset($other_level['duration_unit']) ? $other_level['duration_unit'] : 'days';

            if ($other_duration > 0) {
                $other_duration_in_days = fd_member_convert_duration_to_days($other_duration, $other_duration_unit);

                // 如果优先级更高但有效期更短，给出建议
                if ($priority > $other_priority && $duration_in_days < $other_duration_in_days) {
                    $result['suggestions'][] = sprintf(
                        '建议：优先级 %d 高于"%s"(优先级 %d)，可考虑设置更长的有效期（当前 %d %s，对比等级 %d %s）',
                        $priority, $other_level['name'], $other_priority,
                        $duration, $duration_unit, $other_duration, $other_duration_unit
                    );
                }
            }
        }
    }

    // 价格建议
    $tier = fd_member_get_level_tier($priority);
    $suggested_price_range = fd_member_get_suggested_price_range($priority);

    if ($price > 0 && ($price < $suggested_price_range['min'] || $price > $suggested_price_range['max'])) {
        $result['suggestions'][] = sprintf(
            '建议：%s 的价格建议范围为 %.0f-%.0f 元（当前 %.2f 元）',
            $tier, $suggested_price_range['min'], $suggested_price_range['max'], $price
        );
    }

    // 如果有警告，标记为需要注意
    if (!empty($result['warnings'])) {
        $result['valid'] = false;
    }

    return $result;
}

/**
 * 将有效期转换为天数
 *
 * @param int $duration 时长
 * @param string $unit 单位
 * @return int 天数
 */
function fd_member_convert_duration_to_days($duration, $unit) {
    switch ($unit) {
        case 'years':
            return $duration * 365;
        case 'months':
            return $duration * 30;
        case 'days':
        default:
            return $duration;
    }
}

/**
 * 根据优先级获取建议的价格范围
 *
 * @param int $priority 优先级
 * @return array ['min' => float, 'max' => float]
 */
function fd_member_get_suggested_price_range($priority) {
    if ($priority >= 100) {
        return array('min' => 500, 'max' => 2000);
    } elseif ($priority >= 80) {
        return array('min' => 200, 'max' => 500);
    } elseif ($priority >= 50) {
        return array('min' => 50, 'max' => 200);
    } elseif ($priority >= 20) {
        return array('min' => 10, 'max' => 50);
    } else {
        return array('min' => 0, 'max' => 10);
    }
}