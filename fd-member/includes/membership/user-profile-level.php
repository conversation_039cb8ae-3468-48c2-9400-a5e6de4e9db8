<?php
/**
 * 用户个人资料页会员等级字段
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 在用户个人资料页添加会员等级选择字段
 *
 * @param WP_User $user 当前被编辑的用户对象
 */
function fd_member_add_level_field_to_user_profile($user) {
    // 只有管理员可以编辑等级
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // 获取所有会员等级
    $levels = fd_member_get_member_levels();
    
    // 按优先级排序
    usort($levels, function($a, $b) {
        $a_priority = isset($a['priority']) ? intval($a['priority']) : 0;
        $b_priority = isset($b['priority']) ? intval($b['priority']) : 0;
        return $b_priority - $a_priority; // 降序排列，高优先级在前
    });
    
    // 获取当前用户的会员等级
    $current_level_id = get_user_meta($user->ID, FD_MEMBER_LEVEL_META_KEY, true);
    
    // 获取当前用户的会员等级到期时间
    $expire_time = fd_member_get_user_member_level_expiration($user->ID);
    
    ?>
    <h2>会员等级信息</h2>
    <table class="form-table">
        <tr>
            <th><label for="fd_member_level">会员等级</label></th>
            <td>
                <select name="fd_member_level" id="fd_member_level">
                    <option value="">-- 选择会员等级 --</option>
                    <?php foreach ($levels as $level): ?>
                        <option value="<?php echo esc_attr($level['id']); ?>" <?php selected($current_level_id, $level['id']); ?>>
                            <?php echo esc_html($level['name']); ?>
                            <?php if (!empty($level['description'])): ?>
                                (<?php echo esc_html($level['description']); ?>)
                            <?php endif; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <p class="description">为用户分配会员等级</p>
            </td>
        </tr>
        <?php if ($current_level_id): ?>
        <tr>
            <th>到期时间</th>
            <td>
                <?php if ($expire_time): ?>
                    <?php echo esc_html(date_i18n('Y-m-d H:i:s', $expire_time)); ?>
                    (<?php echo current_time('timestamp') > $expire_time ? '<span style="color:red;">已过期</span>' : '剩余 ' . human_time_diff(current_time('timestamp'), $expire_time); ?>)
                <?php else: ?>
                    永久有效
                <?php endif; ?>
                <p class="description">会员等级有效期</p>
            </td>
        </tr>
        <?php endif; ?>
    </table>
    <?php
}
add_action('show_user_profile', 'fd_member_add_level_field_to_user_profile');
add_action('edit_user_profile', 'fd_member_add_level_field_to_user_profile');

/**
 * 保存用户个人资料页的会员等级
 *
 * @param int $user_id 被编辑的用户ID
 */
function fd_member_save_level_field_to_user_profile($user_id) {
    // 检查当前用户是否有权限编辑用户
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // 检查是否存在会员等级字段
    if (isset($_POST['fd_member_level'])) {
        $level_id = sanitize_text_field($_POST['fd_member_level']);
        
        // 更新或删除会员等级
        if (!empty($level_id)) {
            fd_member_set_user_member_level($user_id, $level_id);
        } else {
            fd_member_set_user_member_level($user_id, null);
        }
    }
}
add_action('personal_options_update', 'fd_member_save_level_field_to_user_profile');
add_action('edit_user_profile_update', 'fd_member_save_level_field_to_user_profile');

/**
 * 在用户列表页添加会员等级列
 * 
 * @param array $columns 当前用户列表页的列
 * @return array 修改后的列
 */
function fd_member_add_level_column_to_users_list($columns) {
    $columns['fd_member_level'] = '会员等级';
    return $columns;
}
add_filter('manage_users_columns', 'fd_member_add_level_column_to_users_list');

/**
 * 在用户列表页显示会员等级
 * 
 * @param string $output 输出内容
 * @param string $column_name 列名
 * @param int $user_id 用户ID
 * @return string 列内容
 */
function fd_member_show_level_data_in_users_list($output, $column_name, $user_id) {
    if ($column_name !== 'fd_member_level') {
        return $output;
    }
    
    $level = fd_member_get_user_member_level($user_id);
    
    if ($level) {
        return esc_html($level['name']);
    } else {
        return '未设置';
    }
}
add_filter('manage_users_custom_column', 'fd_member_show_level_data_in_users_list', 10, 3);

/**
 * 在用户列表页添加会员等级筛选器
 */
function fd_member_add_level_filter_to_users_list() {
    if (isset($_GET['page']) || !is_admin() || !current_user_can('manage_options')) {
        return;
    }
    
    $current_screen = get_current_screen();
    
    if ($current_screen->id !== 'users') {
        return;
    }
    
    // 获取所有会员等级
    $levels = fd_member_get_member_levels();
    
    // 按优先级排序
    usort($levels, function($a, $b) {
        $a_priority = isset($a['priority']) ? intval($a['priority']) : 0;
        $b_priority = isset($b['priority']) ? intval($b['priority']) : 0;
        return $b_priority - $a_priority; // 降序排列，高优先级在前
    });
    
    // 如果没有会员等级，退出
    if (empty($levels)) {
        return;
    }
    
    // 获取当前选择的会员等级
    $current_level = isset($_GET['fd_member_level']) ? $_GET['fd_member_level'] : '';
    
    ?>
    <label class="screen-reader-text" for="fd_member_level_filter">按会员等级筛选</label>
    <select name="fd_member_level" id="fd_member_level_filter">
        <option value="">所有会员等级</option>
        <?php foreach ($levels as $level): ?>
            <option value="<?php echo esc_attr($level['id']); ?>" <?php selected($current_level, $level['id']); ?>>
                <?php echo esc_html($level['name']); ?>
            </option>
        <?php endforeach; ?>
    </select>
    <?php
}
add_action('restrict_manage_users', 'fd_member_add_level_filter_to_users_list');

/**
 * 处理用户列表页的会员等级筛选
 * 
 * @param WP_User_Query $query 用户查询对象
 */
function fd_member_filter_users_by_level($query) {
    global $pagenow;
    
    if ($pagenow !== 'users.php' || !isset($_GET['fd_member_level']) || empty($_GET['fd_member_level'])) {
        return;
    }
    
    $level_id = sanitize_text_field($_GET['fd_member_level']);
    
    // 添加元数据查询
    $query->query_vars['meta_key'] = FD_MEMBER_LEVEL_META_KEY;
    $query->query_vars['meta_value'] = $level_id;
}
add_action('pre_get_users', 'fd_member_filter_users_by_level'); 