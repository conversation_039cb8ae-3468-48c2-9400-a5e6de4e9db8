<?php
/**
 * 会员支付相关功能
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 注册会员支付相关的GraphQL字段和变更
 */
function fd_member_register_member_payment_graphql() {
    // 会员升级订单创建变更
    register_graphql_mutation('createMemberPaymentOrder', [
        'inputFields' => [
            'levelId' => [
                'type' => ['non_null' => 'Int'],
                'description' => '要升级到的会员等级ID'
            ]
        ],
        'outputFields' => [
            'status' => [
                'type' => 'Boolean',
                'description' => '操作是否成功'
            ],
            'message' => [
                'type' => 'String',
                'description' => '操作结果消息'
            ],
            'order' => [
                'type' => 'PaymentOrder',
                'description' => '创建的订单信息'
            ],
            'paymentUrl' => [
                'type' => 'String',
                'description' => '支付链接'
            ]
        ],
        'mutateAndGetPayload' => function($input) {
            // 检查用户是否已登录
            if (!is_user_logged_in()) {
                throw new \GraphQL\Error\UserError('您需要登录才能升级会员等级');
            }
            
            $user_id = get_current_user_id();
            $level_id = isset($input['levelId']) ? intval($input['levelId']) : 0;
            
            // 验证会员等级是否存在
            $level = fd_member_get_member_level($level_id);
            if (!$level) {
                throw new \GraphQL\Error\UserError('指定的会员等级不存在');
            }
            
            // 获取用户当前会员等级
            $current_level = fd_member_get_user_member_level($user_id);
            $current_level_id = $current_level ? $current_level['id'] : 0;
            
            // 检查是否需要升级（新等级ID大于当前等级ID）
            if ($current_level_id >= $level_id) {
                throw new \GraphQL\Error\UserError('您当前的会员等级已经高于或等于要升级的等级');
            }
            
            // 检查等级价格
            $price = isset($level['price']) ? floatval($level['price']) : 0;
            if ($price <= 0) {
                // 免费等级，直接升级
                fd_member_set_user_member_level($user_id, $level_id);
                
                return [
                    'status' => true,
                    'message' => '您已成功升级到免费会员等级: ' . $level['name'],
                    'order' => null,
                    'paymentUrl' => null
                ];
            }
            
            // 构建订单数据
            $metadata = [
                'level_id' => $level_id,
                'is_upgrade' => true,
                'from_level_id' => $current_level_id
            ];
            
            // 创建支付订单（调用fd-payment插件的API）
            if (function_exists('fd_create_payment_order')) {
                $order_data = [
                    'user_id' => $user_id,
                    'amount' => $price,
                    'title' => '会员升级: ' . $level['name'],
                    'description' => '升级至 ' . $level['name'] . ' 会员',
                    'product_type' => 'member_level',
                    'product_id' => $level_id,
                    'metadata' => json_encode($metadata)
                ];
                
                $order = fd_create_payment_order($order_data);
                
                if (is_wp_error($order)) {
                    throw new \GraphQL\Error\UserError('创建订单失败: ' . $order->get_error_message());
                }
                
                // 获取支付链接
                $payment_url = add_query_arg([
                    'order_id' => $order->id,
                    'return_url' => home_url('/payment/success')
                ], home_url('/payment'));
                
                return [
                    'status' => true,
                    'message' => '订单创建成功，请完成支付',
                    'order' => $order,
                    'paymentUrl' => $payment_url
                ];
            } else {
                throw new \GraphQL\Error\UserError('支付功能未启用，无法创建订单');
            }
        }
    ]);
}
add_action('graphql_register_types', 'fd_member_register_member_payment_graphql');

/**
 * 处理会员支付成功的回调
 */
function fd_member_process_member_payment_success($payment_data) {
    // 检查是否是会员相关订单
    if (!isset($payment_data['product_type']) || $payment_data['product_type'] !== 'member_level') {
        return; // 不是会员等级订单
    }
    
    $user_id = $payment_data['user_id'];
    $level_id = 0;

    // 优先从 product_id 获取 level_id
    if (!empty($payment_data['product_id'])) {
        $level_id = intval($payment_data['product_id']);
    }

    // 如果 product_id 中没有，则尝试从 metadata 中解析
    if (!$level_id && !empty($payment_data['metadata'])) {
        $metadata = json_decode($payment_data['metadata'], true);
        if (is_array($metadata)) {
            // 兼容新旧两种 metadata 格式
            if (isset($metadata['level_id'])) {
                $level_id = intval($metadata['level_id']);
            } elseif (isset($metadata['to_level_id'])) {
                $level_id = intval($metadata['to_level_id']);
            }
        }
    }
    
    if ($user_id && $level_id) {
        // 验证会员等级是否有效
        $level = fd_member_get_member_level($level_id);
        if ($level) {
            // 更新用户会员等级
            fd_member_set_user_member_level($user_id, $level_id);
            
            // 记录会员购买/升级日志
            do_action('fd_member_level_upgraded', $user_id, $level_id, $payment_data);
            
            // 发送邮件通知用户
            $user = get_user_by('id', $user_id);
            if ($user && $user->user_email) {
                $is_upgrade = !empty($payment_data['metadata']) && isset(json_decode($payment_data['metadata'], true)['is_upgrade']);
                $subject = '会员等级' . ($is_upgrade ? '升级' : '购买') . '成功';
                $message = "尊敬的 {$user->display_name}，\n\n";
                $message .= "您已成功" . ($is_upgrade ? '升级' : '购买') . "成为 {$level['name']} 会员。\n";
                $message .= "您现在可以享受更多会员特权。\n\n";
                $message .= "谢谢您的支持！\n";
                $message .= get_bloginfo('name');
                
                wp_mail($user->user_email, $subject, $message);
            }
        }
    }
}
add_action('fd_payment_success', 'fd_member_process_member_payment_success'); 