<?php
/**
 * 会员等级GraphQL功能
 * 
 * 提供GraphQL API支持，允许前端查询会员等级信息
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 注册会员等级GraphQL类型和字段
 */
function fd_member_register_member_level_graphql_types() {
    // 会员等级对象类型
    register_graphql_object_type('MemberLevel', [
        'description' => '会员等级信息',
        'fields' => [
            'id' => [
                'type' => 'Int',
                'description' => '会员等级ID'
            ],
            'name' => [
                'type' => 'String',
                'description' => '会员等级名称'
            ],
            'description' => [
                'type' => 'String',
                'description' => '会员等级描述'
            ],
            'priority' => [
                'type' => 'Int',
                'description' => '会员等级优先级'
            ],
            'price' => [
                'type' => 'Float',
                'description' => '会员等级价格'
            ],
            'duration' => [
                'type' => 'Int',
                'description' => '会员等级有效期时长'
            ],
            'durationUnit' => [
                'type' => 'String',
                'description' => '会员等级有效期单位(days, months, years)',
                'resolve' => function($level) {
                    return isset($level['duration_unit']) ? $level['duration_unit'] : 'days';
                }
            ],
            'tier' => [
                'type' => 'String',
                'description' => '会员等级层次描述',
                'resolve' => function($level) {
                    $priority = isset($level['priority']) ? intval($level['priority']) : 0;
                    return fd_member_get_level_tier($priority);
                }
            ]
        ]
    ]);
    
    // 在用户类型中添加会员等级字段
    register_graphql_field('User', 'memberLevel', [
        'type' => 'MemberLevel',
        'description' => '用户的会员等级信息',
        'resolve' => function($user, $args, $context, $info) {
            // 确保我们能获取到正确的用户ID
            $user_id = isset($user->ID) ? $user->ID : 0;
            
            // 如果$user是数据对象，尝试获取databaseId
            if (!$user_id && isset($user->databaseId)) {
                $user_id = $user->databaseId;
            }
            
            // 如果还是没有，尝试从数据对象中获取
            if (!$user_id && is_object($user) && method_exists($user, 'to_array')) {
                $user_data = $user->to_array();
                if (isset($user_data['ID'])) {
                    $user_id = $user_data['ID'];
                }
            }
            
            if (!$user_id) {
                return null;
            }
            
            $level = fd_member_get_user_member_level($user_id);
            
            if (!$level) {
                return null;
            }
            
            return [
                'id' => $level['id'],
                'name' => $level['name'],
                'description' => $level['description'],
                'priority' => isset($level['priority']) ? $level['priority'] : 0,
                'price' => isset($level['price']) ? $level['price'] : 0,
                'duration' => isset($level['duration']) ? $level['duration'] : 0,
                'duration_unit' => isset($level['duration_unit']) ? $level['duration_unit'] : 'days'
            ];
        }
    ]);
    
    // 添加会员到期时间字段
    register_graphql_field('User', 'memberExpiration', [
        'type' => 'String',
        'description' => '用户会员等级到期时间',
        'resolve' => function($user, $args, $context, $info) {
            // 确保我们能获取到正确的用户ID
            $user_id = isset($user->ID) ? $user->ID : 0;
            
            // 如果$user是数据对象，尝试获取databaseId
            if (!$user_id && isset($user->databaseId)) {
                $user_id = $user->databaseId;
            }
            
            // 如果还是没有，尝试从数据对象中获取
            if (!$user_id && is_object($user) && method_exists($user, 'to_array')) {
                $user_data = $user->to_array();
                if (isset($user_data['ID'])) {
                    $user_id = $user_data['ID'];
                }
            }
            
            if (!$user_id) {
                return null;
            }
            
            $expire_time = fd_member_get_user_member_level_expiration($user_id);
            
            if (!$expire_time) {
                return null; // 永久有效或未设置
            }
            
            return date('c', $expire_time); // ISO 8601格式日期
        }
    ]);
    
    // 查询所有会员等级
    register_graphql_field('RootQuery', 'memberLevels', [
        'type' => ['list_of' => 'MemberLevel'],
        'description' => '获取所有会员等级',
        'resolve' => function() {
            return fd_member_get_sorted_member_levels();
        }
    ]);
    
    // 查询特定会员等级
    register_graphql_field('RootQuery', 'memberLevel', [
        'type' => 'MemberLevel',
        'description' => '获取指定ID的会员等级',
        'args' => [
            'id' => [
                'type' => ['non_null' => 'Int'],
                'description' => '会员等级ID'
            ]
        ],
        'resolve' => function($source, $args) {
            $level = fd_member_get_member_level($args['id']);
            
            if (!$level) {
                return null;
            }
            
            return $level;
        }
    ]);
    
    // 查询所有会员等级（公开）
    register_graphql_field('RootQuery', 'allMemberLevels', [
        'type' => ['list_of' => 'MemberLevel'],
        'description' => '获取所有可用的会员等级',
        'resolve' => function() {
            return fd_member_get_sorted_member_levels();
        }
    ]);
    
    // 为管理员添加更新用户会员等级的变更
    register_graphql_mutation('updateUserMemberLevel', [
        'inputFields' => [
            'userId' => [
                'type' => ['non_null' => 'ID'],
                'description' => '要更新的用户ID'
            ],
            'levelId' => [
                'type' => 'Int',
                'description' => '会员等级ID，如果为null则移除用户的会员等级'
            ]
        ],
        'outputFields' => [
            'success' => [
                'type' => 'Boolean',
                'description' => '操作是否成功'
            ],
            'user' => [
                'type' => 'User',
                'description' => '更新后的用户'
            ]
        ],
        'mutateAndGetPayload' => function($input) {
            // 检查用户权限
            if (!current_user_can('manage_options')) {
                throw new \GraphQL\Error\UserError('您没有权限执行此操作');
            }
            
            $user_id = absint(Utils::get_database_id_from_id($input['userId']));
            $level_id = isset($input['levelId']) ? intval($input['levelId']) : null;
            
            // 验证用户是否存在
            $user = get_user_by('id', $user_id);
            if (!$user) {
                throw new \GraphQL\Error\UserError('用户不存在');
            }
            
            // 验证等级是否存在（如果提供）
            if ($level_id && !fd_member_get_member_level($level_id)) {
                throw new \GraphQL\Error\UserError('会员等级不存在');
            }
            
            // 更新用户会员等级
            $success = fd_member_set_user_member_level($user_id, $level_id);
            
            return [
                'success' => $success,
                'user' => $user
            ];
        }
    ]);

    // 检查用户是否可以升级到指定等级
    register_graphql_mutation('checkMemberLevelUpgrade', [
        'inputFields' => [
            'userId' => [
                'type' => ['non_null' => 'ID'],
                'description' => '用户ID'
            ],
            'targetLevelId' => [
                'type' => ['non_null' => 'Int'],
                'description' => '目标会员等级ID'
            ]
        ],
        'outputFields' => [
            'canUpgrade' => [
                'type' => 'Boolean',
                'description' => '是否可以升级'
            ],
            'currentLevel' => [
                'type' => 'MemberLevel',
                'description' => '当前会员等级'
            ],
            'targetLevel' => [
                'type' => 'MemberLevel',
                'description' => '目标会员等级'
            ],
            'message' => [
                'type' => 'String',
                'description' => '升级检查结果消息'
            ]
        ],
        'mutateAndGetPayload' => function($input) {
            $user_id = absint(Utils::get_database_id_from_id($input['userId']));
            $target_level_id = intval($input['targetLevelId']);

            // 验证用户是否存在
            $user = get_user_by('id', $user_id);
            if (!$user) {
                throw new \GraphQL\Error\UserError('用户不存在');
            }

            // 获取当前等级和目标等级
            $current_level = fd_member_get_user_member_level($user_id);
            $target_level = fd_member_get_member_level($target_level_id);

            if (!$target_level) {
                throw new \GraphQL\Error\UserError('目标会员等级不存在');
            }

            // 检查是否可以升级
            $can_upgrade = fd_member_can_upgrade_to_level($user_id, $target_level_id);

            // 生成消息
            $message = '';
            if (!$current_level) {
                $message = '您当前没有会员等级，可以升级到任何等级';
            } elseif ($can_upgrade) {
                $message = sprintf('可以从 %s 升级到 %s', $current_level['name'], $target_level['name']);
            } else {
                $current_priority = isset($current_level['priority']) ? $current_level['priority'] : 0;
                $target_priority = isset($target_level['priority']) ? $target_level['priority'] : 0;

                if ($current_priority == $target_priority) {
                    $message = '您已经拥有相同等级的会员身份';
                } else {
                    $message = sprintf('您当前的 %s 等级已经高于或等于目标等级 %s', $current_level['name'], $target_level['name']);
                }
            }

            return [
                'canUpgrade' => $can_upgrade,
                'currentLevel' => $current_level,
                'targetLevel' => $target_level,
                'message' => $message
            ];
        }
    ]);
}
add_action('graphql_register_types', 'fd_member_register_member_level_graphql_types');