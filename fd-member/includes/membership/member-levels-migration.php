<?php
/**
 * 会员等级数据迁移脚本
 * 
 * 为现有的会员等级设置合理的priority值
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 执行会员等级priority迁移
 * 
 * 这个函数会为没有设置priority的会员等级自动分配合理的priority值
 */
function fd_member_migrate_level_priorities() {
    $levels = fd_member_get_member_levels();
    
    if (empty($levels)) {
        return array('success' => true, 'message' => '没有需要迁移的会员等级');
    }
    
    $updated_count = 0;
    $errors = array();
    
    // 为没有priority的等级分配默认值
    foreach ($levels as $key => $level) {
        if (!isset($level['priority']) || $level['priority'] === '' || $level['priority'] === null) {
            // 根据等级名称和价格推测合理的priority
            $suggested_priority = fd_member_suggest_priority_by_name_and_price($level);
            
            // 确保priority唯一
            while (fd_member_is_priority_used($suggested_priority)) {
                $suggested_priority++;
            }
            
            $levels[$key]['priority'] = $suggested_priority;
            $updated_count++;
        }
    }
    
    // 保存更新后的等级列表
    if ($updated_count > 0) {
        $result = update_option('fd_member_levels', $levels);
        
        if ($result) {
            return array(
                'success' => true, 
                'message' => "成功为 {$updated_count} 个会员等级设置了priority值"
            );
        } else {
            return array(
                'success' => false, 
                'message' => '保存会员等级数据时出错'
            );
        }
    }
    
    return array('success' => true, 'message' => '所有会员等级都已设置priority值，无需迁移');
}

/**
 * 根据等级名称和价格推测合理的priority值
 * 
 * @param array $level 会员等级数据
 * @return int 建议的priority值
 */
function fd_member_suggest_priority_by_name_and_price($level) {
    $name = strtolower($level['name']);
    $price = isset($level['price']) ? floatval($level['price']) : 0;
    
    // 根据名称关键词判断
    if (strpos($name, '钻石') !== false || strpos($name, 'diamond') !== false || strpos($name, '至尊') !== false) {
        return 100;
    }
    
    if (strpos($name, '黄金') !== false || strpos($name, 'gold') !== false || strpos($name, '高级') !== false || strpos($name, 'premium') !== false) {
        return 80;
    }
    
    if (strpos($name, '白银') !== false || strpos($name, 'silver') !== false || strpos($name, '中级') !== false) {
        return 50;
    }
    
    if (strpos($name, '青铜') !== false || strpos($name, 'bronze') !== false || strpos($name, '初级') !== false) {
        return 20;
    }
    
    // 根据价格判断
    if ($price >= 1000) {
        return 90;
    } elseif ($price >= 500) {
        return 70;
    } elseif ($price >= 100) {
        return 40;
    } elseif ($price > 0) {
        return 10;
    }
    
    // 默认为基础级
    return 0;
}

/**
 * 检查是否需要执行迁移
 * 
 * @return bool 是否需要迁移
 */
function fd_member_needs_priority_migration() {
    $levels = fd_member_get_member_levels();
    
    foreach ($levels as $level) {
        if (!isset($level['priority']) || $level['priority'] === '' || $level['priority'] === null) {
            return true;
        }
    }
    
    return false;
}

/**
 * 在插件激活时自动执行迁移
 */
function fd_member_auto_migrate_on_activation() {
    if (fd_member_needs_priority_migration()) {
        $result = fd_member_migrate_level_priorities();
        
        // 记录迁移结果到日志
        if (function_exists('error_log')) {
            error_log('FD Member Levels Migration: ' . $result['message']);
        }
    }
}

// 在插件加载时检查是否需要迁移
add_action('init', function() {
    // 只在管理后台执行自动迁移检查
    if (is_admin() && fd_member_needs_priority_migration()) {
        // 添加管理通知
        add_action('admin_notices', function() {
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p><strong>FD Member:</strong> 检测到会员等级数据需要更新priority字段。';
            echo '<a href="' . admin_url('admin.php?page=fd-member-levels&migrate=1') . '" class="button button-primary" style="margin-left: 10px;">立即迁移</a>';
            echo '</p>';
            echo '</div>';
        });
    }
});

// 处理手动迁移请求
add_action('admin_init', function() {
    if (isset($_GET['page']) && $_GET['page'] === 'fd-member-levels' && isset($_GET['migrate']) && $_GET['migrate'] === '1') {
        if (current_user_can('manage_options')) {
            $result = fd_member_migrate_level_priorities();
            
            $message_type = $result['success'] ? 'migration_success' : 'migration_error';
            
            wp_safe_redirect(add_query_arg(array(
                'page' => 'fd-member-levels',
                'message' => $message_type,
                'migration_result' => urlencode($result['message'])
            ), admin_url('admin.php')));
            exit;
        }
    }
});
