<?php
/**
 * 头像相关扩展功能
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 替换Gravatar URL为自定义头像
 */
function fd_member_custom_avatar_url($url, $id_or_email, $args) {
    // 获取默认头像
    $default_avatar = 'https://img.futuredecade.com/s3/ai-generated-8388405_1280.jpg';
    
    // 检查是否是默认Gravatar
    if (strpos($url, 'gravatar.com') !== false) {
        // 获取用户自定义头像
        $user_id = 0;
        
        if (is_numeric($id_or_email)) {
            $user_id = $id_or_email;
        } elseif (is_string($id_or_email)) {
            $user = get_user_by('email', $id_or_email);
            if ($user) {
                $user_id = $user->ID;
            }
        } elseif (is_object($id_or_email)) {
            if (isset($id_or_email->user_id)) {
                $user_id = $id_or_email->user_id;
            } elseif (isset($id_or_email->ID)) {
                $user_id = $id_or_email->ID;
            }
        }
        
        // 检查用户是否有自定义头像
        if ($user_id) {
            $avatar_id = get_user_meta($user_id, 'fd_user_avatar_id', true);
            if ($avatar_id) {
                $avatar_url = wp_get_attachment_image_url($avatar_id, 'thumbnail');
                if ($avatar_url) {
                    return $avatar_url;
                }
            }
        }
        
        // 如果没有自定义头像，使用设置的默认头像
        $fd_default_avatar = get_option('fd_default_avatar');
        if ($fd_default_avatar) {
            $avatar_url = wp_get_attachment_image_url($fd_default_avatar, 'thumbnail');
            if ($avatar_url) {
                return $avatar_url;
            }
        }
        
        // 如果没有设置默认头像，使用硬编码的默认头像
        return $default_avatar;
    }
    
    return $url;
}
add_filter('get_avatar_url', 'fd_member_custom_avatar_url', 10, 3); 