<?php
/**
 * 密码重置相关的GraphQL接口
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

add_action('graphql_register_types', function() {
    
    // 发送密码重置验证码
    register_graphql_mutation('sendPasswordResetCode', [
        'inputFields' => [
            'email' => ['type' => ['non_null' => 'String'], 'description' => '用户邮箱'],
        ],
        'outputFields' => [
            'success' => ['type' => 'Boolean', 'description' => '操作是否成功'],
            'message' => ['type' => 'String', 'description' => '结果消息'],
        ],
        'mutateAndGetPayload' => function($input) {
            return fd_member_send_password_reset_email(sanitize_email($input['email']));
        }
    ]);

    // 通过邮箱验证码重置密码
    register_graphql_mutation('resetPasswordWithEmailCode', [
        'inputFields' => [
            'email'       => ['type' => ['non_null' => 'String'], 'description' => '用户邮箱'],
            'code'        => ['type' => ['non_null' => 'String'], 'description' => '验证码'],
            'newPassword' => ['type' => ['non_null' => 'String'], 'description' => '新密码'],
        ],
        'outputFields' => [
            'success' => ['type' => 'Boolean', 'description' => '操作是否成功'],
            'message' => ['type' => 'String', 'description' => '结果消息'],
        ],
        'mutateAndGetPayload' => function($input) {
            return fd_member_reset_password_with_email_code(
                sanitize_email($input['email']),
                sanitize_text_field($input['code']),
                $input['newPassword']
            );
        }
    ]);
    
    // 通过手机验证码重置密码
    register_graphql_mutation('resetPasswordWithPhoneCode', [
        'inputFields' => [
            'phone'       => ['type' => ['non_null' => 'String'], 'description' => '用户手机号'],
            'code'        => ['type' => ['non_null' => 'String'], 'description' => '验证码'],
            'newPassword' => ['type' => ['non_null' => 'String'], 'description' => '新密码'],
        ],
        'outputFields' => [
            'success' => ['type' => 'Boolean', 'description' => '操作是否成功'],
            'message' => ['type' => 'String', 'description' => '结果消息'],
        ],
        'mutateAndGetPayload' => function($input) {
            return fd_member_reset_password_with_phone_code(
                sanitize_text_field($input['phone']),
                sanitize_text_field($input['code']),
                $input['newPassword']
            );
        }
    ]);
}); 