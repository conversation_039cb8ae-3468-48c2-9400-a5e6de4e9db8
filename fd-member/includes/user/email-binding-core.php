<?php
/**
 * 邮箱绑定核心功能
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 发送邮箱绑定验证码
 *
 * @param int $user_id 当前用户ID
 * @param string $new_email 要绑定的新邮箱
 * @return array 操作结果
 */
function fd_member_send_email_binding_code($user_id, $new_email) {
    if (!is_email($new_email)) {
        return ['success' => false, 'message' => '请提供有效的邮箱地址'];
    }

    $current_user = get_user_by('id', $user_id);
    if (!$current_user) {
        return ['success' => false, 'message' => '无法获取当前用户信息'];
    }

    if ($current_user->user_email === $new_email) {
        return ['success' => false, 'message' => '新邮箱不能与当前邮箱相同'];
    }

    $existing_user_id = email_exists($new_email);
    if ($existing_user_id && $existing_user_id != $user_id) {
        return ['success' => false, 'message' => '该邮箱已被其他用户使用'];
    }

    $code = fd_member_generate_verification_code();
    $transient_key = 'email_binding_code_' . $user_id;
    set_transient($transient_key, ['code' => $code, 'email' => $new_email], 600);

    $site_name = get_bloginfo('name');
    $subject = sprintf('[%s] 邮箱绑定验证码', $site_name);
    $message = sprintf(
        "您好，\n\n您正在请求将此邮箱绑定到您在 %s 的账户。\n\n您的验证码是: %s\n\n此验证码将在10分钟内有效。\n\n%s 团队",
        $site_name, $code, $site_name
    );

    if (!wp_mail($new_email, $subject, $message, ['Content-Type: text/plain; charset=UTF-8'])) {
        return ['success' => false, 'message' => '发送验证码邮件失败，请稍后再试'];
    }

    return ['success' => true, 'message' => '验证码已发送到您的新邮箱，请查收'];
}


/**
 * 验证邮箱绑定验证码并更新邮箱
 *
 * @param int $user_id 当前用户ID
 * @param string $code 用户提交的验证码
 * @return array 操作结果
 */
function fd_member_verify_and_bind_email($user_id, $code) {
    $transient_key = 'email_binding_code_' . $user_id;
    $stored_data = get_transient($transient_key);

    if (empty($stored_data) || !is_array($stored_data) || $stored_data['code'] !== $code) {
        return ['success' => false, 'message' => '验证码不正确或已过期'];
    }
    
    // 验证成功，清除 transient
    delete_transient($transient_key);

    $new_email = $stored_data['email'];

    // 再次检查邮箱是否在验证期间被占用
    $existing_user_id = email_exists($new_email);
    if ($existing_user_id && $existing_user_id != $user_id) {
        return ['success' => false, 'message' => '操作超时，该邮箱已被其他用户使用'];
    }
    
    $result = wp_update_user(['ID' => $user_id, 'user_email' => $new_email]);

    if (is_wp_error($result)) {
        return ['success' => false, 'message' => $result->get_error_message()];
    }

    return ['success' => true, 'message' => '邮箱绑定成功'];
} 