<?php
/**
 * 邮箱绑定相关的GraphQL接口
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

add_action('graphql_register_types', function() {
    
    // 发送邮箱绑定验证码
    register_graphql_mutation('sendEmailBindingCode', [
        'inputFields' => [
            'email' => ['type' => ['non_null' => 'String'], 'description' => '要绑定的新邮箱'],
        ],
        'outputFields' => [
            'success' => ['type' => 'Boolean', 'description' => '操作是否成功'],
            'message' => ['type' => 'String', 'description' => '结果消息'],
        ],
        'mutateAndGetPayload' => function($input, $context) {
            if (!isset($context->user->ID)) {
                return ['success' => false, 'message' => '用户未登录'];
            }
            return fd_member_send_email_binding_code($context->user->ID, sanitize_email($input['email']));
        }
    ]);

    // 验证并绑定邮箱
    register_graphql_mutation('verifyAndBindEmail', [
        'inputFields' => [
            'code' => ['type' => ['non_null' => 'String'], 'description' => '邮箱中收到的验证码'],
        ],
        'outputFields' => [
            'success' => ['type' => 'Boolean', 'description' => '操作是否成功'],
            'message' => ['type' => 'String', 'description' => '结果消息'],
        ],
        'mutateAndGetPayload' => function($input, $context) {
            if (!isset($context->user->ID)) {
                return ['success' => false, 'message' => '用户未登录'];
            }
            return fd_member_verify_and_bind_email($context->user->ID, sanitize_text_field($input['code']));
        }
    ]);
}); 