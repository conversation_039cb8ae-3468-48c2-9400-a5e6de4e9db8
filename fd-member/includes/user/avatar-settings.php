<?php
/**
 * 用户头像后台设置
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 注册默认头像设置
 */
function fd_member_register_avatar_settings() {
    // 注册一个独立的设置选项，用于存储默认头像的附件ID
    register_setting(
        'fd_member_avatar_options_group', // 设置组，用于在表单中标识
        'fd_default_avatar',              // 选项名称
        array(
            'type'              => 'integer',
            'sanitize_callback' => 'absint', // 清理回调，确保是正整数
            'default'           => 0,
        )
    );
    
    // 添加一个新的设置区域到"会员设置"页面
    add_settings_section(
        'fd_member_avatar_settings_section', // 区域ID
        '默认头像设置',                      // 区域标题
        function() {                         // 区域描述回调
            echo '<p>在这里设置当用户没有自己的头像时，显示的默认头像。</p>';
        },
        'fd-member-avatar-settings'          // 页面别名
    );
    
    // 在新区域中添加设置字段
    add_settings_field(
        'fd_default_avatar_field',          // 字段ID
        '选择默认头像',                     // 字段标题
        'fd_member_default_avatar_field_callback', // 字段UI回调
        'fd-member-avatar-settings',        // 页面别名
        'fd_member_avatar_settings_section' // 所属区域ID
    );
}
add_action('admin_init', 'fd_member_register_avatar_settings');

/**
 * 默认头像设置字段的UI回调函数
 */
function fd_member_default_avatar_field_callback() {
    $avatar_id = get_option('fd_default_avatar', 0);
    $avatar_url = $avatar_id ? wp_get_attachment_image_url($avatar_id, 'thumbnail') : '';
    ?>
    
    <div class="avatar-preview" style="margin-bottom: 10px; width: 100px; height: 100px; border: 1px dashed #ccc; display: flex; align-items: center; justify-content: center; background-color: #f8f8f8;">
        <?php if ($avatar_url): ?>
            <img src="<?php echo esc_url($avatar_url); ?>" alt="默认头像预览" style="max-width: 100%; max-height: 100%;">
        <?php else: ?>
            <span style="color: #888;">无预览</span>
        <?php endif; ?>
    </div>
    
    <input type="hidden" name="fd_default_avatar" id="fd_default_avatar_id" value="<?php echo esc_attr($avatar_id); ?>">
    
    <button type="button" class="button button-secondary" id="fd-select-avatar-button">选择图片</button>
    <button type="button" class="button button-link-delete" id="fd-remove-avatar-button" style="<?php echo $avatar_id ? '' : 'display:none;'; ?>">移除</button>

    <p class="description">点击"选择图片"从媒体库中选择一张图片作为默认头像。</p>
    
    <script>
    jQuery(document).ready(function($) {
        // 确保wp.media可用
        if (typeof wp === 'undefined' || !wp.media) {
            $('#fd-select-avatar-button').prop('disabled', true).after('<p class="description" style="color: red;">媒体上传器无法加载，请刷新页面后重试。</p>');
            return;
        }

        var mediaUploader;

        // "选择图片"按钮点击事件
        $('#fd-select-avatar-button').on('click', function(e) {
            e.preventDefault();
            
            if (mediaUploader) {
                mediaUploader.open();
                return;
            }
            
            mediaUploader = wp.media({
                title: '选择默认用户头像',
                button: {
                    text: '设置为默认头像'
                },
                multiple: false,
                library: {
                    type: 'image'
                }
            });
            
            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                $('#fd_default_avatar_id').val(attachment.id);
                $('.avatar-preview').html('<img src="' + attachment.sizes.thumbnail.url + '" alt="默认头像预览" style="max-width: 100%; max-height: 100%;">');
                $('#fd-remove-avatar-button').show();
            });
            
            mediaUploader.open();
        });
        
        // "移除"按钮点击事件
        $('#fd-remove-avatar-button').on('click', function(e) {
            e.preventDefault();
            $('#fd_default_avatar_id').val('');
            $('.avatar-preview').html('<span style="color: #888;">无预览</span>');
            $(this).hide();
        });
    });
    </script>
    <?php
} 