<?php
/**
 * 密码重置核心功能
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 发送密码重置邮件
 * 
 * @param string $email 用户邮箱
 * @return array 操作结果
 */
function fd_member_send_password_reset_email($email) {
    if (!is_email($email)) {
        return ['success' => false, 'message' => '无效的邮箱地址'];
    }

    $template_data = [
        'subject' => sprintf('[%s] 您的密码重置验证码', get_bloginfo('name')),
        'title'   => '密码重置',
        'intro'   => sprintf('您好，您正在为 %s 的账户请求重置密码。', get_bloginfo('name')),
        'expire_notice' => '此验证码将在10分钟内有效。如果您没有请求重置密码，请忽略此邮件。'
    ];

    return fd_member_send_email_verification_code($email, 'password_reset', $template_data);
}

/**
 * 使用邮箱和验证码重置密码
 * 
 * @param string $email 用户邮箱
 * @param string $code 验证码
 * @param string $new_password 新密码
 * @return array 操作结果
 */
function fd_member_reset_password_with_email_code($email, $code, $new_password) {
    if (strlen($new_password) < 8) {
        return ['success' => false, 'message' => '新密码必须至少包含8个字符'];
    }

    $verification_result = fd_member_verify_email_verification_code($email, $code, 'password_reset', true);

    if (!$verification_result['isValid']) {
        return ['success' => false, 'message' => $verification_result['message']];
    }

    $user = get_user_by('email', $email);
    if (!$user) {
        return ['success' => false, 'message' => '未找到对应的用户'];
    }

    reset_password($user, $new_password);
    
    // 发送密码已重置通知
    wp_send_new_user_notifications($user->ID, 'user');

    return ['success' => true, 'message' => '密码重置成功，请使用新密码登录'];
}

/**
 * 使用手机和验证码重置密码
 * 
 * @param string $phone 手机号
 * @param string $code 验证码
 * @param string $new_password 新密码
 * @return array 操作结果
 */
function fd_member_reset_password_with_phone_code($phone, $code, $new_password) {
    if (strlen($new_password) < 8) {
        return ['success' => false, 'message' => '新密码必须至少包含8个字符'];
    }

    $is_valid = fd_member_verify_sms_code($phone, $code);
    if (!$is_valid) {
        return ['success' => false, 'message' => '验证码错误或已过期'];
    }

    $user_id = fd_member_phone_exists($phone);
    if (!$user_id) {
        return ['success' => false, 'message' => '未找到与此手机号关联的用户'];
    }

    $user = get_user_by('id', $user_id);
    reset_password($user, $new_password);
    
    // 发送密码已重置通知
    wp_send_new_user_notifications($user->ID, 'user');

    return ['success' => true, 'message' => '密码重置成功，请使用新密码登录'];
} 