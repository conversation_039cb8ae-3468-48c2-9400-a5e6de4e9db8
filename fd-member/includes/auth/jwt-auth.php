<?php
/**
 * JWT认证相关功能
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 增加JWT令牌过期时间
 * 将JWT令牌有效期从默认的300秒(5分钟)改为7天
 */
function fd_member_jwt_expiration_time() {
    return time() + (DAY_IN_SECONDS * 7);
}
add_filter('graphql_jwt_auth_expire', 'fd_member_jwt_expiration_time', 99);

/**
 * 设置JWT刷新令牌有效期
 * 将刷新令牌有效期设置为30天
 */
function fd_member_jwt_refresh_expiration_time() {
    return time() + (DAY_IN_SECONDS * 30);
}
add_filter('graphql_jwt_auth_refresh_expire', 'fd_member_jwt_refresh_expiration_time', 99);

/**
 * 直接修改JWT有效负载，设置一个更合理的过期时间（基于iat，不叠加）
 * 同时，增加用户的会员等级信息
 */
function fd_member_modify_jwt_payload($payload) {
    // 增加会员等级信息
    if (isset($payload['data']['user']['id'])) {
        $user_id = $payload['data']['user']['id'];
        
        // 假设 fd_member_get_user_level() 是获取用户等级的函数
        if (function_exists('fd_member_get_user_level')) {
            $user_level = fd_member_get_user_level($user_id);
            $payload['data']['user']['level'] = $user_level ? $user_level : 'default';
        } else {
            // 如果获取不到等级函数，给一个默认值
            $payload['data']['user']['level'] = 'default';
        }
    }

    // 判断是否是刷新令牌
    if (isset($payload['data']['user']['user_secret'])) {
        // 刷新令牌：30天
        $payload['exp'] = $payload['iat'] + (DAY_IN_SECONDS * 30);
    } else {
        // 访问令牌：7天
        $payload['exp'] = $payload['iat'] + (DAY_IN_SECONDS * 7);
    }
    return $payload;
}
add_filter('graphql_jwt_auth_token_before_sign', 'fd_member_modify_jwt_payload', 999);
add_filter('graphql_refresh_jwt_auth_token_payload', 'fd_member_modify_jwt_payload', 999); 