<?php
/**
 * Submission System - GraphQL API
 *
 * @package FD Member
 */

if (!defined('ABSPATH')) {
    exit; // Prevent direct access
}

add_action('graphql_register_types', function() {
    register_graphql_mutation(
        'createPostSubmission',
        [
            'inputFields'         => [
                'title'       => [
                    'type'        => ['non_null' => 'String'],
                    'description' => __('The title of the post.', 'fd-member'),
                ],
                'contentHtml' => [
                    'type'        => ['non_null' => 'String'],
                    'description' => __('The post content in HTML format.', 'fd-member'),
                ],
                'contentJson' => [
                    'type'        => ['non_null' => 'String'],
                    'description' => __('The post content in JSON format (from TipTap).', 'fd-member'),
                ],
            ],
            'outputFields'        => [
                'success'    => [
                    'type'        => ['non_null' => 'Boolean'],
                    'description' => __('Whether the submission was successful.', 'fd-member'),
                ],
                'postId'     => [
                    'type'        => 'ID',
                    'description' => __('The database ID of the newly created post.', 'fd-member'),
                ],
                'postStatus' => [
                    'type'        => 'String',
                    'description' => __('The status of the newly created post (e.g., "pending").', 'fd-member'),
                ],
            ],
            'mutateAndGetPayload' => function ($input) {
                // Permission Check: Allow any logged-in user to submit.
                if (!is_user_logged_in()) {
                    throw new \GraphQL\Error\UserError(__('You must be logged in to submit a post.', 'fd-member'));
                }

                // Sanitize Data
                $title = sanitize_text_field($input['title']);
                $content_html = wp_kses_post($input['contentHtml']);
                
                // Basic Validation
                if (empty($title)) {
                    throw new \GraphQL\Error\UserError(__('A title is required.', 'fd-member'));
                }
                if (empty($content_html)) {
                    throw new \GraphQL\Error\UserError(__('Content cannot be empty.', 'fd-member'));
                }

                $post_data = [
                    'post_title'   => $title,
                    'post_content' => $content_html,
                    'post_status'  => 'pending', // All submissions are pending review initially.
                    'post_author'  => get_current_user_id(),
                ];

                $post_id = wp_insert_post($post_data, true);

                if (is_wp_error($post_id)) {
                    throw new \GraphQL\Error\UserError($post_id->get_error_message());
                }

                // Store the JSON version of the content in post meta
                if (!empty($input['contentJson'])) {
                    update_post_meta($post_id, '_post_content_json', wp_slash($input['contentJson']));
                }

                return [
                    'success'    => true,
                    'postId'     => $post_id,
                    'postStatus' => get_post_status($post_id),
                ];
            },
        ]
    );

    // --- Mutation: Withdraw Post Submission ---
    register_graphql_mutation(
        'withdrawPostSubmission',
        [
            'inputFields'         => [
                'id' => [
                    'type'        => ['non_null' => 'ID'],
                    'description' => __('The global ID of the post to withdraw.', 'fd-member'),
                ],
            ],
            'outputFields'        => [
                'post' => [
                    'type'        => 'Post',
                    'description' => __('The updated post object.', 'fd-member'),
                ]
            ],
            'mutateAndGetPayload' => function ($input, $context) {
                if (!is_user_logged_in()) {
                    throw new \GraphQL\Error\UserError(__('You must be logged in.', 'fd-member'));
                }

                $post_id = \GraphQLRelay\Relay::fromGlobalId($input['id'])['id'];
                $post = get_post($post_id);

                // Security checks
                if (!$post || (int)$post->post_author !== get_current_user_id()) {
                    throw new \GraphQL\Error\UserError(__('You do not have permission to modify this post.', 'fd-member'));
                }
                if ($post->post_status !== 'pending') {
                    throw new \GraphQL\Error\UserError(__('Only pending submissions can be withdrawn.', 'fd-member'));
                }

                $result = wp_update_post(['ID' => $post_id, 'post_status' => 'draft'], true);
                if (is_wp_error($result)) {
                    throw new \GraphQL\Error\UserError($result->get_error_message());
                }

                return ['post' => $context->get_loader('post')->load_deferred($post_id)];
            },
        ]
    );

    // --- Mutation: Update Post Submission ---
    register_graphql_mutation(
        'updatePostSubmission',
        [
            'inputFields'         => [
                'id' => [
                    'type'        => ['non_null' => 'ID'],
                    'description' => __('The global ID of the post to update.', 'fd-member'),
                ],
                'title'       => ['type' => 'String'],
                'contentHtml' => ['type' => 'String'],
                'contentJson' => ['type' => 'String'],
            ],
            'outputFields'        => [
                'post' => [
                    'type'        => 'Post',
                    'description' => __('The updated post object.', 'fd-member'),
                ],
            ],
            'mutateAndGetPayload' => function ($input, $context) {
                if (!is_user_logged_in()) {
                    throw new \GraphQL\Error\UserError(__('You must be logged in.', 'fd-member'));
                }

                $post_id = \GraphQLRelay\Relay::fromGlobalId($input['id'])['id'];
                $post = get_post($post_id);
                
                // Security checks
                if (!$post || (int)$post->post_author !== get_current_user_id()) {
                    throw new \GraphQL\Error\UserError(__('You do not have permission to modify this post.', 'fd-member'));
                }
                if (!in_array($post->post_status, ['draft', 'pending'])) {
                     throw new \GraphQL\Error\UserError(__('Only draft or pending posts can be edited.', 'fd-member'));
                }

                $post_data = ['ID' => $post_id];
                if (!empty($input['title'])) {
                    $post_data['post_title'] = sanitize_text_field($input['title']);
                }
                if (!empty($input['contentHtml'])) {
                    $post_data['post_content'] = wp_kses_post($input['contentHtml']);
                }
                
                // When updating, always set status back to pending for re-review
                $post_data['post_status'] = 'pending';

                $result = wp_update_post($post_data, true);
                if (is_wp_error($result)) {
                    throw new \GraphQL\Error\UserError($result->get_error_message());
                }

                if (!empty($input['contentJson'])) {
                    update_post_meta($post_id, '_post_content_json', wp_slash($input['contentJson']));
                }
                
                return ['post' => $context->get_loader('post')->load_deferred($post_id)];
            },
        ]
    );
});

/**
 * 注册 RootQuery:userSubmissions —— 获取用户的投稿列表
 */
add_action('graphql_register_types', function() {
    if (!function_exists('register_graphql_field')) {
        return;
    }

    register_graphql_field('RootQuery', 'userSubmissions', [
        'type'        => ['list_of' => 'Post'],
        'description' => __('Get a list of posts submitted by the given user (any post_status).', 'fd-member'),
        'args'        => [
            'userId' => [
                'type'        => 'Int',
                'description' => __('User ID, default current user', 'fd-member'),
            ],
            'limit' => [
                'type'         => 'Int',
                'description'  => __('How many items to return', 'fd-member'),
                'defaultValue' => 10,
            ],
            'offset' => [
                'type'         => 'Int',
                'description'  => __('Offset for pagination', 'fd-member'),
                'defaultValue' => 0,
            ],
            'status' => [
                'type'        => 'String',
                'description' => __('Filter by post_status (publish|pending|draft etc). Default all', 'fd-member'),
            ],
        ],
        'resolve' => function ($root, $args) {
            $user_id = isset($args['userId']) ? absint($args['userId']) : get_current_user_id();
            if (!$user_id) {
                throw new \GraphQL\Error\UserError(__('Please login first.', 'fd-member'));
            }

            $limit  = isset($args['limit'])  ? absint($args['limit'])  : 10;
            $offset = isset($args['offset']) ? absint($args['offset']) : 0;
            $status = isset($args['status']) && $args['status'] ? sanitize_key($args['status']) : 'any';

            $query_args = [
                'author'         => $user_id,
                'post_status'    => $status,
                'posts_per_page' => $limit,
                'offset'         => $offset,
                'orderby'        => 'date',
                'order'          => 'DESC',
            ];

            $wp_query = new WP_Query($query_args);
            if (!$wp_query->have_posts()) {
                return [];
            }

            $posts   = [];
            $context = \WPGraphQL::get_app_context();
            foreach ($wp_query->posts as $post) {
                $resolved = \WPGraphQL\Data\DataSource::resolve_post_object($post->ID, $context);
                if ($resolved) {
                    $posts[] = $resolved;
                }
            }

            // 清理全局 $post
            wp_reset_postdata();
            return $posts;
        },
    ]);
});

/**
 * Filter to allow authors to see their own pending posts in GraphQL queries.
 * By default, WPGraphQL would return null for pending posts for non-admin users.
 */
add_filter('graphql_post_object_can_view', function ($can_view, $post_model, $post, $context, $info = null) {
    // 已经允许，直接返回
    if ($can_view) {
        return true;
    }

    // 允许作者查看自己处于待审或草稿状态的文章
    if (in_array($post->post_status, ['pending', 'draft'], true)) {
        $current_user = get_current_user_id();
        if ($current_user && (int) $post->post_author === $current_user) {
            return true;
        }
    }

    return $can_view;
}, 99, 5); 