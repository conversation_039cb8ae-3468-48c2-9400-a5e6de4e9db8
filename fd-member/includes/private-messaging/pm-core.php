<?php
/**
 * 私信功能 - 核心文件
 * 提供数据库操作和核心功能
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

global $wpdb;
define('FD_PM_CONVERSATIONS_TABLE', $wpdb->prefix . 'member_pm_conversations');
define('FD_PM_MESSAGES_TABLE', $wpdb->prefix . 'member_pm_messages');


/**
 * 在插件激活时创建私信功能所需的数据库表
 */
function fd_member_pm_create_tables() {
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

    // 1. 创建会话表
    $conversations_table_name = FD_PM_CONVERSATIONS_TABLE;
    if ($wpdb->get_var("SHOW TABLES LIKE '{$conversations_table_name}'") != $conversations_table_name) {
        $sql_conversations = "CREATE TABLE $conversations_table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            user_one BIGINT(20) UNSIGNED NOT NULL,
            user_two BIGINT(20) UNSIGNED NOT NULL,
            subject VARCHAR(255) NOT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_conversation (user_one, user_two),
            KEY user_one (user_one),
            KEY user_two (user_two),
            KEY updated_at (updated_at)
        ) $charset_collate;";
        dbDelta($sql_conversations);
    }

    // 2. 创建消息表
    $messages_table_name = FD_PM_MESSAGES_TABLE;
    if ($wpdb->get_var("SHOW TABLES LIKE '{$messages_table_name}'") != $messages_table_name) {
        $sql_messages = "CREATE TABLE $messages_table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            conversation_id BIGINT(20) UNSIGNED NOT NULL,
            sender_id BIGINT(20) UNSIGNED NOT NULL,
            recipient_id BIGINT(20) UNSIGNED NOT NULL,
            content TEXT NOT NULL,
            sent_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            is_read TINYINT(1) NOT NULL DEFAULT 0,
            PRIMARY KEY (id),
            KEY conversation_id (conversation_id),
            KEY sender_id (sender_id),
            KEY recipient_id (recipient_id),
            KEY is_read (is_read)
        ) $charset_collate;";
        dbDelta($sql_messages);
    }
}

/**
 * 获取或创建一个会话
 * @param array $data 包含 sender_id, recipient_id, subject
 * @return int The conversation ID.
 */
function fd_member_get_or_create_conversation($data) {
    global $wpdb;

    $p1 = min($data['sender_id'], $data['recipient_id']);
    $p2 = max($data['sender_id'], $data['recipient_id']);

    $conversation_id = $wpdb->get_var($wpdb->prepare(
        "SELECT id FROM " . FD_PM_CONVERSATIONS_TABLE . " WHERE user_one = %d AND user_two = %d",
        $p1,
        $p2
    ));

    if ($conversation_id) {
        return (int) $conversation_id;
    }

    $wpdb->insert(
        FD_PM_CONVERSATIONS_TABLE,
        [
            'user_one'   => $p1,
            'user_two'   => $p2,
            'subject'    => $data['subject'],
            'updated_at' => current_time('mysql', 1),
        ],
        ['%d', '%d', '%s', '%s']
    );

    return (int) $wpdb->insert_id;
}


/**
 * 发送一条私信
 * @param array $message_data 包含 sender_id, recipient_id, subject, content
 * @return WP_Error|int Message ID on success.
 */
function fd_member_send_pm($message_data) {
    global $wpdb;

    $conversation_id = fd_member_get_or_create_conversation($message_data);

    if (!$conversation_id) {
        return new WP_Error('conversation_error', __('Could not create or find conversation.', 'fd-member'));
    }

    $result = $wpdb->insert(
        FD_PM_MESSAGES_TABLE,
        [
            'conversation_id' => $conversation_id,
            'sender_id'       => $message_data['sender_id'],
            'recipient_id'    => $message_data['recipient_id'],
            'content'         => $message_data['content'],
            'sent_at'         => current_time('mysql', 1),
        ],
        ['%d', '%d', '%d', '%s', '%s']
    );

    if (!$result) {
        return new WP_Error('send_error', __('Could not send the message.', 'fd-member'));
    }
    
    $message_id = (int) $wpdb->insert_id;

    // 更新会话的 updated_at 时间
    $wpdb->update(
        FD_PM_CONVERSATIONS_TABLE,
        ['updated_at' => current_time('mysql', 1)],
        ['id' => $conversation_id],
        ['%s'],
        ['%d']
    );
    
    do_action('fd_member_pm_message_sent', $message_id, $message_data);

    return $message_id;
}

/**
 * 根据ID获取单条私信
 * @param int $message_id
 * @return object|null
 */
function fd_member_get_pm($message_id) {
    global $wpdb;
    return $wpdb->get_row($wpdb->prepare("SELECT * FROM " . FD_PM_MESSAGES_TABLE . " WHERE id = %d", $message_id));
}

/**
 * 获取一个用户的所有会话
 * @param int $user_id
 * @return array
 */
function fd_member_get_user_conversations($user_id) {
    global $wpdb;
    return $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM " . FD_PM_CONVERSATIONS_TABLE . " WHERE user_one = %d OR user_two = %d ORDER BY updated_at DESC",
        $user_id,
        $user_id
    ));
}

/**
 * 获取会话中的所有消息
 * @param int $conversation_id
 * @param int $limit 0 or less to fetch all.
 * @return array
 */
function fd_member_get_conversation_messages($conversation_id, $limit = 20) {
    global $wpdb;

    $query = $wpdb->prepare(
        "SELECT * FROM " . FD_PM_MESSAGES_TABLE . " WHERE conversation_id = %d ORDER BY sent_at DESC",
        $conversation_id
    );

    if ($limit > 0) {
        $query .= $wpdb->prepare(" LIMIT %d", $limit);
    }

    return $wpdb->get_results($query);
}

/**
 * 获取会话的最后一条消息
 * @param int $conversation_id
 * @return object|null
 */
function fd_member_get_conversation_last_message($conversation_id) {
    global $wpdb;
    return $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM " . FD_PM_MESSAGES_TABLE . " WHERE conversation_id = %d ORDER BY sent_at DESC LIMIT 1",
        $conversation_id
    ));
}

/**
 * 根据ID获取一个会话
 * @param int $conversation_id
 * @param int $user_id
 * @return object|null
 */
function fd_member_get_conversation_by_id($conversation_id, $user_id) {
    global $wpdb;
    return $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM " . FD_PM_CONVERSATIONS_TABLE . " WHERE id = %d AND (user_one = %d OR user_two = %d)",
        $conversation_id,
        $user_id,
        $user_id
    ));
}

/**
 * 将会话标记为已读
 * @param int $conversation_id
 * @param int $user_id
 * @return bool|int
 */
function fd_member_mark_conversation_as_read($conversation_id, $user_id) {
    global $wpdb;
    return $wpdb->update(
        FD_PM_MESSAGES_TABLE,
        ['is_read' => 1],
        ['conversation_id' => $conversation_id, 'recipient_id' => $user_id, 'is_read' => 0],
        ['%d'],
        ['%d', '%d', '%d']
    );
}

/**
 * 获取用户的未读消息总数
 * @param int $user_id
 * @return int
 */
function fd_member_get_unread_message_count($user_id) {
    global $wpdb;
    return (int) $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM " . FD_PM_MESSAGES_TABLE . " WHERE recipient_id = %d AND is_read = 0",
        $user_id
    ));
}

/**
 * 删除一个会话
 * @param int $conversation_id
 * @param int $user_id
 * @return WP_Error|bool
 */
function fd_member_delete_conversation($conversation_id, $user_id) {
    global $wpdb;

    $conversation = fd_member_get_conversation_by_id($conversation_id, $user_id);
    if (!$conversation) {
        return new WP_Error('not_found', __('Conversation not found or you do not have permission to delete it.', 'fd-member'));
    }

    $wpdb->query($wpdb->prepare("DELETE FROM " . FD_PM_MESSAGES_TABLE . " WHERE conversation_id = %d", $conversation_id));
    $wpdb->query($wpdb->prepare("DELETE FROM " . FD_PM_CONVERSATIONS_TABLE . " WHERE id = %d", $conversation_id));

    return true;
}

/**
 * Deletes all private messaging data associated with a user upon their deletion.
 *
 * This function is hooked into the 'delete_user' action.
 *
 * @param int $user_id The ID of the user being deleted.
 */
function fd_member_pm_delete_user_data($user_id) {
    global $wpdb;

    // 1. Find all conversation IDs this user is a part of.
    $conversation_ids = $wpdb->get_col($wpdb->prepare(
        "SELECT id FROM " . FD_PM_CONVERSATIONS_TABLE . " WHERE user_one = %d OR user_two = %d",
        $user_id,
        $user_id
    ));

    if (!empty($conversation_ids)) {
        // Prepare a string of placeholders for the IN clause
        $placeholders = implode(', ', array_fill(0, count($conversation_ids), '%d'));

        // 2. Delete all messages within those conversations.
        $wpdb->query($wpdb->prepare(
            "DELETE FROM " . FD_PM_MESSAGES_TABLE . " WHERE conversation_id IN ($placeholders)",
            $conversation_ids
        ));

        // 3. Delete the conversations themselves.
        $wpdb->query($wpdb->prepare(
            "DELETE FROM " . FD_PM_CONVERSATIONS_TABLE . " WHERE id IN ($placeholders)",
            $conversation_ids
        ));
    }
}
add_action('delete_user', 'fd_member_pm_delete_user_data', 10, 1);
