<?php
/**
 * 私信功能 - GraphQL API 接口
 *
 * @package FD Member
 * @version 2.1.0
 * @description
 *   This file registers the GraphQL schema for the Private Messaging feature.
 *   Key Change (v2.1.0): Introduced a custom `ConversationParticipant` type
 *   to replace the core `User` type for `otherUser`, `sender`, and `recipient` fields.
 *   This resolves a permission issue where non-admin users could not resolve
 *   the non-nullable fields of the core `User` type for other users,
 *   causing the query to fail.
 */

if (!defined('ABSPATH')) {
    exit; // Prevent direct access
}

add_action('graphql_register_types', 'fd_member_register_pm_graphql_types');

/**
 * Registers all GraphQL types, queries, and mutations for the PM feature.
 */
function fd_member_register_pm_graphql_types() {

    // =========================================================================
    // 1. OBJECT TYPES
    // =========================================================================

    // NEW (v2.1.0): A simplified user type for conversations to avoid permission issues.
    register_graphql_object_type('ConversationParticipant', [
        'description' => __('A participant in a private message conversation.', 'fd-member'),
        'fields'      => [
            'id' => [
                'type' => ['non_null' => 'ID'],
                'description' => __('The global ID of the user.', 'fd-member'),
                'resolve' => fn($user) => \GraphQLRelay\Relay::toGlobalId('user', $user->ID),
            ],
            'databaseId' => [
                'type' => ['non_null' => 'Int'],
                'description' => __('The database ID of the user.', 'fd-member'),
                'resolve' => fn($user) => $user->ID,
            ],
            'name' => [
                'type' => ['non_null' => 'String'],
                'description' => __("The user's display name.", 'fd-member'),
                'resolve' => fn($user) => $user->display_name,
            ],
            'avatar' => [
                'type' => 'Avatar', // Using the core Avatar type from WPGraphQL
                'description' => __("The user's avatar.", 'fd-member'),
                'resolve' => function ($user) {
                    $avatar_data = get_avatar_data($user->ID);
                    // Manually construct an object that matches what the Avatar resolver expects.
                    return !empty($avatar_data['url']) ? ['url' => $avatar_data['url'], 'size' => 96] : null;
                },
            ],
        ],
    ]);

    // Note: Connection-related types (Edge, PageInfo, Connection) are defined first
    // as they are referenced by the main object types.

    register_graphql_object_type('PrivateMessagePageInfo', [
        'description' => __('Information about pagination in a connection.', 'fd-member'),
        'fields'      => [
            'hasNextPage' => ['type' => ['non_null' => 'Boolean'], 'description' => __('Indicates if there are more items available.', 'fd-member')],
            'endCursor'   => ['type' => 'String', 'description' => __('The cursor for the last item in the current set.', 'fd-member')],
        ],
    ]);

    register_graphql_object_type('PrivateMessageEdge', [
        'description' => __('An edge in a PrivateMessage connection.', 'fd-member'),
        'fields'      => [
            'node'   => ['type' => 'PrivateMessage', 'description' => __('The item at the end of the edge.', 'fd-member')],
            'cursor' => ['type' => ['non_null' => 'String'], 'description' => __('A cursor for use in pagination.', 'fd-member')],
        ],
    ]);

    register_graphql_object_type('PrivateMessageConnection', [
        'description' => __('A connection to a list of PrivateMessage objects.', 'fd-member'),
        'fields'      => [
            'edges'    => ['type' => ['list_of' => 'PrivateMessageEdge'], 'description' => __('A list of edges.', 'fd-member')],
            'pageInfo' => ['type' => ['non_null' => 'PrivateMessagePageInfo'], 'description' => __('Information to aid in pagination.', 'fd-member')],
        ],
    ]);

    register_graphql_object_type('PrivateMessageConversationEdge', [
        'description' => __('An edge in a PrivateMessageConversation connection.', 'fd-member'),
        'fields'      => [
            'node'   => ['type' => 'PrivateMessageConversation', 'description' => __('The item at the end of the edge.', 'fd-member')],
            'cursor' => ['type' => ['non_null' => 'String'], 'description' => __('A cursor for use in pagination.', 'fd-member')],
        ],
    ]);

    register_graphql_object_type('PrivateMessageConversationConnection', [
        'description' => __('A connection to a list of PrivateMessageConversation objects.', 'fd-member'),
        'fields'      => [
            'edges'    => ['type' => ['list_of' => 'PrivateMessageConversationEdge'], 'description' => __('A list of edges.', 'fd-member')],
            'pageInfo' => ['type' => ['non_null' => 'PrivateMessagePageInfo'], 'description' => __('Information to aid in pagination.', 'fd-member')],
        ],
    ]);

    register_graphql_object_type('PrivateMessage', [
        'description' => __('A private message between two users.', 'fd-member'),
        'fields'      => [
            'id'             => [
                'type'        => ['non_null' => 'ID'],
                'description' => __('The global ID of the message.', 'fd-member'),
                'resolve'     => fn($msg) => \GraphQLRelay\Relay::toGlobalId('pm_message', $msg->id),
            ],
            'databaseId'     => [
                'type'        => ['non_null' => 'Int'],
                'description' => __('The unique database ID of the message.', 'fd-member'),
                'resolve'     => fn($msg) => (int) $msg->id,
            ],
            'content'        => [
                'type'        => ['non_null' => 'String'],
                'description' => __('The content of the message.', 'fd-member'),
                'resolve'     => fn($msg) => (string) $msg->content,
            ],
            'sentAt'         => [
                'type'        => ['non_null' => 'String'],
                'description' => __('The date the message was sent, in RFC3339 format.', 'fd-member'),
                'resolve'     => fn($msg) => wp_date(DATE_RFC3339, strtotime($msg->sent_at)),
            ],
            'isRead'         => [
                'type'        => ['non_null' => 'Boolean'],
                'description' => __('Whether the message has been read by the recipient.', 'fd-member'),
                'resolve'     => fn($msg) => (bool) $msg->is_read,
            ],
            'sender'         => [
                'type'        => 'ConversationParticipant', // UPDATED (v2.1.0)
                'description' => __('The user who sent the message.', 'fd-member'),
                'resolve'     => function ($message) {
                    if (empty($message->sender_id)) return null;
                    return get_userdata($message->sender_id);
                },
            ],
            'recipient'      => [
                'type'        => 'ConversationParticipant', // UPDATED (v2.1.0)
                'description' => __('The user who received the message.', 'fd-member'),
                'resolve'     => function ($message) {
                    if (empty($message->recipient_id)) return null;
                    return get_userdata($message->recipient_id);
                },
            ],
        ],
        'interfaces'  => ['Node'],
    ]);

    register_graphql_object_type('PrivateMessageConversation', [
        'description' => __('A conversation thread between two users.', 'fd-member'),
        'fields'      => [
            'id'           => [
                'type'        => ['non_null' => 'ID'],
                'description' => __('The global ID of the conversation.', 'fd-member'),
                'resolve'     => fn($convo) => \GraphQLRelay\Relay::toGlobalId('pm_conversation', $convo->id),
            ],
            'databaseId'   => [
                'type'        => ['non_null' => 'Int'],
                'description' => __('The unique database ID of the conversation.', 'fd-member'),
                'resolve'     => fn($convo) => (int) $convo->id,
            ],
            'updatedAt'    => [
                'type'        => ['non_null' => 'String'],
                'description' => __('The date of the last activity in the conversation.', 'fd-member'),
                'resolve'     => fn($convo) => wp_date(DATE_RFC3339, strtotime($convo->updated_at)),
            ],
            'unreadCount'  => [
                'type'        => ['non_null' => 'Int'],
                'description' => __('Number of unread messages for the current user in this conversation.', 'fd-member'),
                'resolve'     => function($convo) {
                    global $wpdb;
                    $current_user_id = get_current_user_id();
                    return (int) $wpdb->get_var($wpdb->prepare(
                        "SELECT COUNT(*) FROM " . FD_PM_MESSAGES_TABLE . " WHERE conversation_id = %d AND recipient_id = %d AND is_read = 0",
                        $convo->id,
                        $current_user_id
                    ));
                }
            ],
            'otherUser'    => [
                'type'        => 'ConversationParticipant', // UPDATED (v2.1.0)
                'description' => __('The other participant in the conversation.', 'fd-member'),
                'resolve'     => function ($convo) {
                    $current_user_id = get_current_user_id();
                    $other_user_id   = ($current_user_id == $convo->user_one) ? $convo->user_two : $convo->user_one;
                    return get_userdata($other_user_id);
                },
            ],
            'lastMessage'  => [
                'type'        => 'PrivateMessage',
                'description' => __('The most recent message in the conversation.', 'fd-member'),
                'resolve'     => fn($convo) => fd_member_get_conversation_last_message($convo->id),
            ],
            'messages'     => [
                'type'        => ['non_null' => 'PrivateMessageConnection'],
                'description' => __('The messages within the conversation.', 'fd-member'),
                'args'        => [
                    'first' => ['type' => 'Int', 'defaultValue' => 20],
                    'after' => ['type' => 'String'],
                ],
                'resolve'     => function ($conversation, $args) {
                    // Check if current user is part of the conversation before proceeding.
                    $current_user_id = get_current_user_id();
                    $convo_data = fd_member_get_conversation_by_id($conversation->id, $current_user_id);
                    if (!$convo_data) {
                         throw new \GraphQL\Error\UserError(__('You do not have permission to view messages in this conversation.', 'fd-member'));
                    }

                    // Use limit=0 to fetch all messages for robust manual pagination.
                    $all_messages = fd_member_get_conversation_messages($conversation->id, 0);

                    $after_offset = 0;
                    if (!empty($args['after'])) {
                        $decoded_cursor = base64_decode($args['after']);
                        if ($decoded_cursor && strpos($decoded_cursor, 'msg_cursor:') === 0) {
                            $after_offset = (int) substr($decoded_cursor, 11) + 1;
                        }
                    }

                    $total          = count($all_messages);
                    $messages_slice = array_slice($all_messages, $after_offset, $args['first']);

                    $edges = array_map(function($message, $index) use ($after_offset) {
                        return [
                            'cursor' => base64_encode('msg_cursor:' . ($after_offset + $index)),
                            'node'   => $message,
                        ];
                    }, $messages_slice, array_keys($messages_slice));

                    $has_next_page = ($after_offset + count($messages_slice)) < $total;
                    $end_cursor    = !empty($edges) ? end($edges)['cursor'] : null;

                    return [
                        'edges'    => $edges,
                        'pageInfo' => ['hasNextPage' => $has_next_page, 'endCursor' => $end_cursor],
                    ];
                },
            ],
        ],
        'interfaces'  => ['Node'],
    ]);

    // =========================================================================
    // 2. QUERY FIELDS
    // =========================================================================

    register_graphql_field('RootQuery', 'privateConversation', [
        'type'        => 'PrivateMessageConversation',
        'description' => __('Retrieves a single private conversation.', 'fd-member'),
        'args'        => [
            'id' => [
                'type'        => ['non_null' => 'ID'],
                'description' => __('The Global Relay ID of the conversation.', 'fd-member'),
            ],
        ],
        'resolve'     => function ($root, $args) {
            if (!is_user_logged_in()) {
                throw new \GraphQL\Error\UserError(__('Authentication required.', 'fd-member'));
            }
            $id_parts = \GraphQLRelay\Relay::fromGlobalId($args['id']);
            if (empty($id_parts['type']) || 'pm_conversation' !== $id_parts['type']) {
                throw new \GraphQL\Error\UserError(__('Invalid conversation ID.', 'fd-member'));
            }
            return fd_member_get_conversation_by_id($id_parts['id'], get_current_user_id());
        },
    ]);

    register_graphql_field('User', 'privateConversations', [
        'type'        => ['non_null' => 'PrivateMessageConversationConnection'],
        'description' => __("The user's private message conversations.", 'fd-member'),
        'args'        => [
            'first' => ['type' => 'Int', 'defaultValue' => 10],
            'after' => ['type' => 'String'],
        ],
        'resolve'     => function ($user, $args) {
            if (empty($user->databaseId)) return null;

            // Security check: Only the user themselves or an admin can see their conversations.
            if (get_current_user_id() !== $user->databaseId && !current_user_can('edit_users')) {
                 throw new \GraphQL\Error\UserError(__('You do not have permission to view these conversations.', 'fd-member'));
            }

            $all_conversations = fd_member_get_user_conversations($user->databaseId);
            
            // Filter out conversations where the other user does not exist.
            $valid_conversations = array_filter($all_conversations, function($convo) use ($user) {
                $other_user_id = ($user->databaseId == $convo->user_one) ? $convo->user_two : $convo->user_one;
                return (bool) get_userdata($other_user_id);
            });

            // Note: The original pagination logic was basic and could be improved.
            // For now, we will apply it to the filtered conversations.
            $after_offset = 0;
            if (!empty($args['after'])) {
                $decoded_cursor = base64_decode($args['after']);
                if ($decoded_cursor && strpos($decoded_cursor, 'convo_cursor:') === 0) {
                    $after_offset = (int) substr($decoded_cursor, 13) + 1;
                }
            }

            $total = count($valid_conversations);
            // Use array_values to re-index the array after filtering
            $conversations_slice = array_slice(array_values($valid_conversations), $after_offset, $args['first']);

            $edges = array_map(function($convo, $index) use ($after_offset) {
                return [
                    'cursor' => base64_encode('convo_cursor:' . ($after_offset + $index)),
                    'node'   => $convo,
                ];
            }, $conversations_slice, array_keys($conversations_slice));

            $has_next_page = ($after_offset + count($conversations_slice)) < $total;
            $end_cursor    = !empty($edges) ? end($edges)['cursor'] : null;

            return [
                'edges'    => $edges,
                'pageInfo' => ['hasNextPage' => $has_next_page, 'endCursor' => $end_cursor],
            ];
        },
    ]);

    register_graphql_field('User', 'unreadMessageCount', [
        'type'        => ['non_null' => 'Int'],
        'description' => __("The user's total number of unread private messages.", 'fd-member'),
        'resolve'     => function ($user) {
            if (empty($user->databaseId)) return 0;
            // Security check
            if (get_current_user_id() !== $user->databaseId) {
                return 0;
            }
            return fd_member_get_unread_message_count($user->databaseId);
        },
    ]);

    // 新增：搜索可私信的用户（轻量级信息）
    register_graphql_field('RootQuery', 'searchableUsers', [
        'type'        => ['list_of' => 'ConversationParticipant'],
        'description' => __('Search users you can start a conversation with.', 'fd-member'),
        'args'        => [
            'search' => [
                'type'        => ['non_null' => 'String'],
                'description' => __('Keyword to search in user_login / user_nicename / display_name.', 'fd-member'),
            ],
            'first'  => [
                'type'         => 'Int',
                'description'  => __('Maximum number of results to return.', 'fd-member'),
                'defaultValue' => 10,
            ],
        ],
        'resolve'     => function ($root, $args) {
            if (!is_user_logged_in()) {
                throw new \GraphQL\Error\UserError(__('Authentication required.', 'fd-member'));
            }

            $current_user = get_current_user_id();
            $keyword      = sanitize_text_field($args['search']);
            $limit        = !empty($args['first']) ? (int) $args['first'] : 10;

            $query = new WP_User_Query([
                'search'         => '*' . $keyword . '*',
                'search_columns' => ['user_login', 'user_nicename', 'display_name', 'user_email'],
                'number'         => $limit,
                'orderby'        => 'display_name',
                'order'          => 'ASC',
                'exclude'        => [$current_user], // 不返回当前用户自己
            ]);

            // 返回 WP_User 对象数组，已被 ConversationParticipant 的字段解析器所支持
            return $query->get_results();
        },
    ]);

    // =========================================================================
    // 3. MUTATIONS
    // =========================================================================

    // 3.1. Send Private Message
    register_graphql_mutation(
        'sendPrivateMessage',
        [
            'inputFields'         => [
                'recipientId' => [
                    'type'        => ['non_null' => 'ID'],
                    'description' => __('The Global Relay ID of the recipient.', 'fd-member'),
                ],
                'content'     => [
                    'type'        => ['non_null' => 'String'],
                    'description' => __('The message content.', 'fd-member'),
                ],
            ],
            'outputFields'        => [
                'success'       => ['type' => 'Boolean', 'description' => __('Whether the message was sent successfully.', 'fd-member')],
                'sentMessage'   => ['type' => 'PrivateMessage', 'description' => __('The message that was sent.', 'fd-member')],
                'conversation'  => ['type' => 'PrivateMessageConversation', 'description' => __('The conversation the message was added to.', 'fd-member')],
            ],
            'mutateAndGetPayload' => function ($input) {
                if (!is_user_logged_in()) {
                    throw new \GraphQL\Error\UserError(__('Authentication required.', 'fd-member'));
                }
                $sender_id = get_current_user_id();

                $recipient_id_parts = \GraphQLRelay\Relay::fromGlobalId($input['recipientId']);
                if (empty($recipient_id_parts['type']) || 'user' !== strtolower($recipient_id_parts['type'])) {
                    throw new \GraphQL\Error\UserError(__('Invalid recipient ID.', 'fd-member'));
                }
                $recipient_db_id = (int) $recipient_id_parts['id'];

                if ($sender_id === $recipient_db_id) {
                    throw new \GraphQL\Error\UserError(__('You cannot send a message to yourself.', 'fd-member'));
                }
                if (false === get_userdata($recipient_db_id)) {
                    throw new \GraphQL\Error\UserError(__('Recipient user does not exist.', 'fd-member'));
                }

                $message_data = [
                    'sender_id'    => $sender_id,
                    'recipient_id' => $recipient_db_id,
                    'subject'      => 'Private Message', // Subject is handled internally
                    'content'      => sanitize_textarea_field($input['content']),
                ];

                $message_id = fd_member_send_pm($message_data);

                if (is_wp_error($message_id)) {
                    throw new \GraphQL\Error\UserError($message_id->get_error_message());
                }

                $sent_message = fd_member_get_pm($message_id);

                return [
                    'success'       => true,
                    'sentMessage'   => $sent_message,
                    'conversation'  => fd_member_get_conversation_by_id($sent_message->conversation_id, $sender_id),
                ];
            },
        ]
    );

    // 3.2. Mark Conversation as Read
    register_graphql_mutation(
        'markConversationAsRead',
        [
            'inputFields'         => [
                'conversationId' => [
                    'type'        => ['non_null' => 'ID'],
                    'description' => __('The Global Relay ID of the conversation to mark as read.', 'fd-member'),
                ],
            ],
            'outputFields'        => [
                'success'      => ['type' => 'Boolean', 'description' => __('Whether the operation was successful.', 'fd-member')],
                'conversation' => ['type' => 'PrivateMessageConversation', 'description' => __('The updated conversation.', 'fd-member')],
            ],
            'mutateAndGetPayload' => function ($input) {
                if (!is_user_logged_in()) {
                    throw new \GraphQL\Error\UserError(__('Authentication required.', 'fd-member'));
                }
                $user_id = get_current_user_id();

                $id_parts = \GraphQLRelay\Relay::fromGlobalId($input['conversationId']);
                if (empty($id_parts['type']) || 'pm_conversation' !== $id_parts['type']) {
                    throw new \GraphQL\Error\UserError(__('Invalid conversation ID.', 'fd-member'));
                }
                $conversation_id = (int) $id_parts['id'];

                $result = fd_member_mark_conversation_as_read($conversation_id, $user_id);
                if (is_wp_error($result)) {
                    throw new \GraphQL\Error\UserError($result->get_error_message());
                }

                return [
                    'success'      => true,
                    'conversation' => fd_member_get_conversation_by_id($conversation_id, $user_id),
                ];
            },
        ]
    );

    // 3.3. Delete Conversation
    register_graphql_mutation(
        'deleteConversation',
        [
            'inputFields'         => [
                'conversationId' => [
                    'type'        => ['non_null' => 'ID'],
                    'description' => __('The Global Relay ID of the conversation to delete.', 'fd-member'),
                ],
            ],
            'outputFields'        => [
                'success'               => ['type' => 'Boolean', 'description' => __('Whether the deletion was successful.', 'fd-member')],
                'deletedConversationId' => ['type' => 'ID', 'description' => __('The Global Relay ID of the deleted conversation.', 'fd-member')],
            ],
            'mutateAndGetPayload' => function ($input) {
                if (!is_user_logged_in()) {
                    throw new \GraphQL\Error\UserError(__('Authentication required.', 'fd-member'));
                }
                $user_id = get_current_user_id();
                
                $id_parts = \GraphQLRelay\Relay::fromGlobalId($input['conversationId']);
                if (empty($id_parts['type']) || 'pm_conversation' !== $id_parts['type']) {
                    throw new \GraphQL\Error\UserError(__('Invalid conversation ID.', 'fd-member'));
                }
                $conversation_id = (int) $id_parts['id'];

                $result = fd_member_delete_conversation($conversation_id, $user_id);
                if (is_wp_error($result)) {
                    throw new \GraphQL\Error\UserError($result->get_error_message());
                }

                return [
                    'success'               => true,
                    'deletedConversationId' => $input['conversationId'], // Return the same global ID that was passed in
                ];
            },
        ]
    );
}
