<?php
/**
 * Grant minimal post‐editing capabilities to Subscriber role
 * so that authors can query their own pending/draft posts via WPGraphQL.
 *
 * Why not use filter? WPGraphQL ultimately使用 `current_user_can( 'edit_post' )`,
 * 所以直接授予能力是最简单且不会影响前端权限/UI 的方式。
 */
add_action( 'init', function () {
    $role = get_role( 'subscriber' );
    if ( $role && ! $role->has_cap( 'edit_posts' ) ) {
        $role->add_cap( 'edit_posts' );           // 允许创建/编辑自己的文章
        $role->add_cap( 'edit_published_posts' ); // 可在发布后修正错字
    }
}, 20 ); 