<?php
/**
 * 通知系统GraphQL API
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 注册通知相关GraphQL类型和字段
 */
function fd_member_notification_register_graphql_types() {
    if (!function_exists('register_graphql_object_type') || !function_exists('register_graphql_field')) {
        return;
    }
    
    // 注册通知类型
    register_graphql_object_type('UserNotification', [
        'description' => '用户通知',
        'fields' => [
            'id' => [
                'type' => 'ID',
                'description' => '通知ID'
            ],
            'userId' => [
                'type' => 'Int',
                'description' => '用户ID',
                'resolve' => function($notification) {
                    return $notification->user_id;
                }
            ],
            'title' => [
                'type' => 'String',
                'description' => '通知标题'
            ],
            'content' => [
                'type' => 'String',
                'description' => '通知内容'
            ],
            'type' => [
                'type' => 'String',
                'description' => '通知类型'
            ],
            'typeName' => [
                'type' => 'String',
                'description' => '通知类型名称',
                'resolve' => function($notification) {
                    $types = fd_member_notification_get_types();
                    return isset($types[$notification->type]) ? $types[$notification->type] : $notification->type;
                }
            ],
            'status' => [
                'type' => 'String',
                'description' => '通知状态'
            ],
            'createdAt' => [
                'type' => 'String',
                'description' => '创建时间',
                'resolve' => function($notification) {
                    return $notification->created_at;
                }
            ],
            'readAt' => [
                'type' => 'String',
                'description' => '阅读时间',
                'resolve' => function($notification) {
                    return $notification->read_at;
                }
            ]
        ]
    ]);
    
    // 注册查询用户通知
    register_graphql_field('RootQuery', 'userNotifications', [
        'type' => ['list_of' => 'UserNotification'],
        'description' => '获取当前用户的通知',
        'args' => [
            'status' => [
                'type' => 'String',
                'description' => '通知状态，如 unread, read'
            ],
            'type' => [
                'type' => 'String',
                'description' => '通知类型'
            ],
            'perPage' => [
                'type' => 'Int',
                'description' => '每页数量',
                'defaultValue' => 10
            ],
            'page' => [
                'type' => 'Int',
                'description' => '页码',
                'defaultValue' => 1
            ]
        ],
        'resolve' => function($root, $args) {
            if (!is_user_logged_in()) {
                return [];
            }
            
            $user_id = get_current_user_id();
            $status = isset($args['status']) ? $args['status'] : null;
            $type = isset($args['type']) ? $args['type'] : null;
            $per_page = isset($args['perPage']) ? $args['perPage'] : 10;
            $page = isset($args['page']) ? $args['page'] : 1;
            
            return fd_member_notification_get_user_notifications($user_id, $status, $type, $per_page, $page);
        }
    ]);
    
    // 注册查询单个通知
    register_graphql_field('RootQuery', 'userNotification', [
        'type' => 'UserNotification',
        'description' => '获取单个通知详情',
        'args' => [
            'id' => [
                'type' => ['non_null' => 'ID'],
                'description' => '通知ID'
            ]
        ],
        'resolve' => function($root, $args) {
            if (!is_user_logged_in()) {
                return null;
            }
            
            $user_id = get_current_user_id();
            $notification_id = absint($args['id']);
            
            global $wpdb;
            $table_name = $wpdb->prefix . 'member_notifications';
            
            return $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $table_name WHERE id = %d AND user_id = %d",
                $notification_id,
                $user_id
            ));
        }
    ]);
    
    // 注册查询未读通知数
    register_graphql_field('RootQuery', 'unreadNotificationCount', [
        'type' => 'Int',
        'description' => '获取当前用户未读通知数',
        'resolve' => function() {
            if (!is_user_logged_in()) {
                return 0;
            }
            
            $user_id = get_current_user_id();
            return fd_member_notification_get_unread_count($user_id);
        }
    ]);
    
    // 注册标记通知已读变更
    register_graphql_mutation('markNotificationRead', [
        'inputFields' => [
            'id' => [
                'type' => ['non_null' => 'ID'],
                'description' => '通知ID'
            ]
        ],
        'outputFields' => [
            'success' => [
                'type' => 'Boolean',
                'description' => '是否成功',
                'resolve' => function($payload) {
                    return $payload['success'];
                }
            ],
            'notification' => [
                'type' => 'UserNotification',
                'description' => '已更新的通知',
                'resolve' => function($payload) {
                    return $payload['notification'];
                }
            ]
        ],
        'mutateAndGetPayload' => function($input) {
            if (!is_user_logged_in()) {
                throw new \GraphQL\Error\UserError('请先登录');
            }
            
            $user_id = get_current_user_id();
            $notification_id = absint($input['id']);
            
            $result = fd_member_notification_mark_read($notification_id, $user_id);
            
            if (!$result) {
                throw new \GraphQL\Error\UserError('标记通知已读失败');
            }
            
            global $wpdb;
            $table_name = $wpdb->prefix . 'member_notifications';
            $notification = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $table_name WHERE id = %d",
                $notification_id
            ));
            
            return [
                'success' => true,
                'notification' => $notification
            ];
        }
    ]);
    
    // 注册标记所有通知已读变更
    register_graphql_mutation('markAllNotificationsRead', [
        'outputFields' => [
            'success' => [
                'type' => 'Boolean',
                'description' => '是否成功',
                'resolve' => function($payload) {
                    return $payload['success'];
                }
            ],
            'count' => [
                'type' => 'Int',
                'description' => '标记已读的通知数量',
                'resolve' => function($payload) {
                    return $payload['count'];
                }
            ]
        ],
        'mutateAndGetPayload' => function() {
            if (!is_user_logged_in()) {
                throw new \GraphQL\Error\UserError('请先登录');
            }
            
            $user_id = get_current_user_id();
            
            // 获取标记前的未读数量
            $before_count = fd_member_notification_get_unread_count($user_id);
            
            $result = fd_member_notification_mark_all_read($user_id);
            
            if (!$result) {
                throw new \GraphQL\Error\UserError('标记所有通知已读失败');
            }
            
            return [
                'success' => true,
                'count' => $before_count
            ];
        }
    ]);
    
    // 注册删除通知变更
    register_graphql_mutation('deleteNotification', [
        'inputFields' => [
            'id' => [
                'type' => ['non_null' => 'ID'],
                'description' => '通知ID'
            ]
        ],
        'outputFields' => [
            'success' => [
                'type' => 'Boolean',
                'description' => '是否成功',
                'resolve' => function($payload) {
                    return $payload['success'];
                }
            ],
            'deletedId' => [
                'type' => 'ID',
                'description' => '已删除的通知ID',
                'resolve' => function($payload) {
                    return $payload['deletedId'];
                }
            ]
        ],
        'mutateAndGetPayload' => function($input) {
            if (!is_user_logged_in()) {
                throw new \GraphQL\Error\UserError('请先登录');
            }
            
            $user_id = get_current_user_id();
            $notification_id = absint($input['id']);
            
            $result = fd_member_notification_delete($notification_id, $user_id);
            
            if (!$result) {
                throw new \GraphQL\Error\UserError('删除通知失败');
            }
            
            return [
                'success' => true,
                'deletedId' => $notification_id
            ];
        }
    ]);
}
add_action('graphql_register_types', 'fd_member_notification_register_graphql_types'); 