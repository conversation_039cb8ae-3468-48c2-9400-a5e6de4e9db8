<?php
/**
 * 通知系统钩子函数
 * 将现有邮件场景同步为系统通知
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 注册成功通知
 * 用户完成注册后发送通知
 * 
 * @param int $user_id 用户ID
 */
function fd_member_notification_registration_success($user_id) {
    $user = get_userdata($user_id);
    
    if (!$user) return;
    
    $title = '欢迎注册成功';
    $content = sprintf('尊敬的 %s，感谢您成功注册我们的网站。您现在可以享受更多网站功能和服务。', $user->display_name);
    
    fd_member_notification_create($user_id, $title, $content, 'account');
}
add_action('user_register', 'fd_member_notification_registration_success');

/**
 * 会员升级成功通知
 * 用户成功升级会员后发送通知
 * 
 * @param int $user_id 用户ID
 * @param int $level_id 会员等级ID
 * @param array $payment_data 支付数据
 */
function fd_member_notification_upgrade_success($user_id, $level_id, $payment_data) {
    $level = fd_member_get_member_level($level_id);
    
    if (!$level) return;
    
    $title = '会员升级成功';
    $content = sprintf('恭喜您已成功升级为 %s 会员，即刻可享受更多会员特权。', $level['name']);
    
    // 如果会员有有效期，添加到通知内容中
    $expiration = fd_member_get_user_member_level_expiration($user_id);
    if ($expiration) {
        $expiration_date = date('Y-m-d', $expiration);
        $content .= sprintf('您的会员有效期至 %s。', $expiration_date);
    }
    
    fd_member_notification_create($user_id, $title, $content, 'member');
}
add_action('fd_member_level_upgraded', 'fd_member_notification_upgrade_success', 10, 3);

/**
 * 密码重置成功通知
 * 用户密码重置成功后发送通知
 * 
 * @param WP_User $user 用户对象
 */
function fd_member_notification_password_reset($user) {
    $user_id = $user->ID;
    
    $title = '密码已重置';
    $content = '您的账户密码已成功重置。如果这不是您本人的操作，请立即联系管理员。';
    
    fd_member_notification_create($user_id, $title, $content, 'account');
}
add_action('after_password_reset', 'fd_member_notification_password_reset');

/**
 * 邮箱绑定成功通知
 * 用户成功绑定新邮箱后发送通知
 * 
 * @param array $result 绑定结果
 */
function fd_member_notification_email_binding($result) {
    if (!isset($result['success']) || !$result['success']) return;
    
    $user_id = $result['user_id'];
    
    $title = '邮箱绑定成功';
    $content = '您的账户已成功绑定新的邮箱地址。';
    
    fd_member_notification_create($user_id, $title, $content, 'account');
}

// 创建邮箱绑定成功的钩子
add_action('fd_email_binding_success', 'fd_member_notification_email_binding');

/**
 * 支付成功通知
 * 用户完成支付后发送通知
 * 
 * @param array $payment_data 支付数据
 */
function fd_member_notification_payment_success($payment_data) {
    if (!isset($payment_data['user_id'])) return;
    
    $user_id = $payment_data['user_id'];
    $amount = isset($payment_data['amount']) ? $payment_data['amount'] : 0;
    $order_id = isset($payment_data['order_id']) ? $payment_data['order_id'] : 0;
    
    $title = '支付成功';
    $content = sprintf('您的订单 #%s 支付成功，金额：￥%.2f。', $order_id, $amount);
    
    fd_member_notification_create($user_id, $title, $content, 'payment');
}
add_action('fd_payment_success', 'fd_member_notification_payment_success');

/**
 * 会员到期提醒通知
 * 会员即将到期时发送提醒
 * 
 * @param int $user_id 用户ID
 * @param int $days_left 剩余天数
 */
function fd_member_notification_member_expiring_soon($user_id, $days_left, $level_id) {
    $level = fd_member_get_member_level($level_id);
    
    if (!$level) return;
    
    $title = '会员即将到期';
    $content = sprintf('您的 %s 会员身份将在 %d 天后到期，为了不影响您的会员权益，请及时续费。', 
        $level['name'], 
        $days_left
    );
    
    fd_member_notification_create($user_id, $title, $content, 'member');
}
add_action('fd_member_expiring_soon', 'fd_member_notification_member_expiring_soon', 10, 3);

/**
 * 会员已到期通知
 * 会员到期时发送通知
 * 
 * @param int $user_id 用户ID
 * @param int $expired_level_id 过期的会员等级ID
 */
function fd_member_notification_member_expired($user_id, $expired_level_id) {
    $level = fd_member_get_member_level($expired_level_id);
    
    if (!$level) return;
    
    $title = '会员已到期';
    $content = sprintf('您的 %s 会员身份已经到期，部分会员权益将无法使用。您可以随时续费恢复会员身份。', 
        $level['name']
    );
    
    fd_member_notification_create($user_id, $title, $content, 'member');
}
add_action('fd_member_expired', 'fd_member_notification_member_expired', 10, 2); 