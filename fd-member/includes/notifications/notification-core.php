<?php
/**
 * 通知系统核心功能
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 初始化通知系统数据库
 */
function fd_member_notification_init_db() {
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();
    $table_name = $wpdb->prefix . 'member_notifications';
    
    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        user_id bigint(20) unsigned NOT NULL,
        title varchar(255) NOT NULL,
        content text NOT NULL,
        type varchar(50) NOT NULL DEFAULT 'system',
        status varchar(20) NOT NULL DEFAULT 'unread',
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        read_at datetime DEFAULT NULL,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY status (status),
        KEY type (type)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbD<PERSON>ta($sql);
}

/**
 * 创建通知
 * 
 * @param int $user_id 用户ID
 * @param string $title 通知标题
 * @param string $content 通知内容
 * @param string $type 通知类型
 * @return int|false 成功返回通知ID，失败返回false
 */
function fd_member_notification_create($user_id, $title, $content, $type = 'system') {
    global $wpdb;
    $table_name = $wpdb->prefix . 'member_notifications';
    
    $result = $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'title' => $title,
            'content' => $content,
            'type' => $type,
            'status' => 'unread',
            'created_at' => current_time('mysql')
        ),
        array('%d', '%s', '%s', '%s', '%s', '%s')
    );
    
    return $result ? $wpdb->insert_id : false;
}

/**
 * 获取用户通知
 * 
 * @param int $user_id 用户ID
 * @param string|null $status 通知状态（可选）
 * @param string|null $type 通知类型（可选）
 * @param int $per_page 每页数量
 * @param int $page 页码
 * @return array 通知列表
 */
function fd_member_notification_get_user_notifications($user_id, $status = null, $type = null, $per_page = 10, $page = 1) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'member_notifications';
    
    $sql = $wpdb->prepare("SELECT * FROM $table_name WHERE user_id = %d", $user_id);
    
    if ($status) {
        $sql .= $wpdb->prepare(" AND status = %s", $status);
    }
    
    if ($type) {
        $sql .= $wpdb->prepare(" AND type = %s", $type);
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    // 添加分页
    $offset = ($page - 1) * $per_page;
    $sql .= $wpdb->prepare(" LIMIT %d, %d", $offset, $per_page);
    
    return $wpdb->get_results($sql);
}

/**
 * 获取用户未读通知数量
 * 
 * @param int $user_id 用户ID
 * @return int 未读通知数
 */
function fd_member_notification_get_unread_count($user_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'member_notifications';
    
    $sql = $wpdb->prepare("SELECT COUNT(*) FROM $table_name WHERE user_id = %d AND status = 'unread'", $user_id);
    return (int)$wpdb->get_var($sql);
}

/**
 * 标记通知为已读
 * 
 * @param int $notification_id 通知ID
 * @param int|null $user_id 用户ID（可选，为安全验证）
 * @return bool 操作是否成功
 */
function fd_member_notification_mark_read($notification_id, $user_id = null) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'member_notifications';
    
    $where = array('id' => $notification_id);
    if ($user_id) {
        $where['user_id'] = $user_id;
    }
    
    $result = $wpdb->update(
        $table_name,
        array(
            'status' => 'read',
            'read_at' => current_time('mysql')
        ),
        $where,
        array('%s', '%s'),
        array('%d', '%d')
    );
    
    return $result !== false;
}

/**
 * 批量创建通知
 * 
 * @param array $user_ids 用户ID数组
 * @param string $title 通知标题
 * @param string $content 通知内容
 * @param string $type 通知类型
 * @return bool 操作是否成功
 */
function fd_member_notification_create_bulk($user_ids, $title, $content, $type = 'system') {
    if (empty($user_ids)) {
        return false;
    }
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'member_notifications';
    $values = array();
    $place_holders = array();
    $current_time = current_time('mysql');
    
    foreach ($user_ids as $user_id) {
        array_push($values, $user_id, $title, $content, $type, 'unread', $current_time);
        $place_holders[] = "(%d, %s, %s, %s, %s, %s)";
    }
    
    $query = "INSERT INTO $table_name (user_id, title, content, type, status, created_at) VALUES ";
    $query .= implode(', ', $place_holders);
    
    $result = $wpdb->query($wpdb->prepare($query, $values));
    
    return $result !== false;
}

/**
 * 获取通知类型列表
 * 
 * @return array 通知类型及其显示名称
 */
function fd_member_notification_get_types() {
    return apply_filters('fd_member_notification_types', array(
        'system' => '系统通知',
        'account' => '账户通知',
        'payment' => '支付通知',
        'member' => '会员通知'
    ));
}

/**
 * 删除通知
 * 
 * @param int $notification_id 通知ID
 * @param int|null $user_id 用户ID（可选，为安全验证）
 * @return bool 操作是否成功
 */
function fd_member_notification_delete($notification_id, $user_id = null) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'member_notifications';
    
    $where = array('id' => $notification_id);
    if ($user_id) {
        $where['user_id'] = $user_id;
    }
    
    $result = $wpdb->delete(
        $table_name,
        $where,
        array('%d', '%d')
    );
    
    return $result !== false;
}

/**
 * 清空用户的所有通知
 * 
 * @param int $user_id 用户ID
 * @return bool 操作是否成功
 */
function fd_member_notification_clear_user($user_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'member_notifications';
    
    $result = $wpdb->delete(
        $table_name,
        array('user_id' => $user_id),
        array('%d')
    );
    
    return $result !== false;
}

/**
 * 标记用户所有通知为已读
 * 
 * @param int $user_id 用户ID
 * @return bool 操作是否成功
 */
function fd_member_notification_mark_all_read($user_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'member_notifications';
    
    $result = $wpdb->update(
        $table_name,
        array(
            'status' => 'read',
            'read_at' => current_time('mysql')
        ),
        array(
            'user_id' => $user_id,
            'status' => 'unread'
        ),
        array('%s', '%s'),
        array('%d', '%s')
    );
    
    return $result !== false;
}

/**
 * 迁移旧表数据到新表
 * 删除错误前缀的表并迁移数据
 */
function fd_member_notification_migrate_table() {
    global $wpdb;
    $old_table = $wpdb->prefix . 'fd_member_notifications';
    $new_table = $wpdb->prefix . 'member_notifications';
    
    // 检查旧表是否存在
    $old_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$old_table}'") == $old_table;
    if (!$old_table_exists) {
        return false; // 旧表不存在，无需迁移
    }
    
    // 先删除可能已存在的新表
    $wpdb->query("DROP TABLE IF EXISTS {$new_table}");
    
    // 创建新表
    fd_member_notification_init_db();
    
    // 拷贝数据
    $result = $wpdb->query("INSERT INTO {$new_table} SELECT * FROM {$old_table}");
    
    // 删除旧表
    if ($result !== false) {
        $wpdb->query("DROP TABLE IF EXISTS {$old_table}");
    }
    
    return $result !== false;
} 