<?php
/**
 * 通知系统管理界面
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 添加管理菜单
 */
function fd_member_notification_add_menu() {
    add_submenu_page(
        'fd-member',           // 父菜单别名
        '用户通知',           // 页面标题
        '用户通知',           // 菜单标题
        'manage_options',      // 所需权限
        'fd-member-notifications', // 菜单别名
        'fd_member_notification_admin_page' // 回调函数
    );
}
add_action('admin_menu', 'fd_member_notification_add_menu', 20); // 优先级设为20，确保在主菜单添加后执行

/**
 * 添加调试输出
 */
function fd_member_notification_debug_output() {
    if (isset($_GET['page']) && $_GET['page'] == 'fd-member-notifications') {
        echo "<!-- Notification page loading -->";
        if (WP_DEBUG) {
            echo "<!-- Debug mode enabled -->";
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
        }
    }
}
add_action('admin_notices', 'fd_member_notification_debug_output');

/**
 * 确保通知列表类可用
 */
function fd_member_notification_load_list_table() {
    if (isset($_GET['page']) && $_GET['page'] == 'fd-member-notifications') {
        if (!class_exists('WP_List_Table')) {
            require_once(ABSPATH . 'wp-admin/includes/class-wp-list-table.php');
        }
        
        // 加载通知表格类
        $list_file = FD_MEMBER_DIR . 'admin/notifications/notification-list.php';
        if (file_exists($list_file)) {
            require_once($list_file);
        } else {
            wp_die('通知列表文件不存在: ' . $list_file);
        }
    }
}
add_action('admin_init', 'fd_member_notification_load_list_table');

/**
 * 通知管理主页面
 */
function fd_member_notification_admin_page() {
    echo '<div class="wrap">';
    echo '<h1>用户通知系统</h1>';
    
    try {
        $action = isset($_REQUEST['action']) ? sanitize_text_field($_REQUEST['action']) : 'list';
        
        // 确保WP_List_Table类已加载
        if (!class_exists('WP_List_Table')) {
            require_once(ABSPATH . 'wp-admin/includes/class-wp-list-table.php');
        }
        
        // 再次确保通知列表类加载
        $list_file = FD_MEMBER_DIR . 'admin/notifications/notification-list.php';
        if (file_exists($list_file)) {
            require_once($list_file);
        } else {
            throw new Exception('通知列表文件不存在: ' . $list_file);
        }
        
        // 检查通知列表类是否存在
        if (!class_exists('FD_Notification_List_Table')) {
            throw new Exception('通知列表类不存在');
        }
        
        switch ($action) {
            case 'new':
                fd_member_notification_edit_page();
                break;
            case 'edit':
                fd_member_notification_edit_page();
                break;
            default:
                fd_member_notification_list_page();
                break;
        }
    } catch (Exception $e) {
        echo '<div class="error"><p>错误: ' . esc_html($e->getMessage()) . '</p></div>';
        echo '<pre>' . esc_html($e->getTraceAsString()) . '</pre>';
    }
    
    echo '</div>';
}

/**
 * 通知列表页面
 */
function fd_member_notification_list_page() {
    // 创建通知列表类的实例
    $list_table = new FD_Notification_List_Table();
    $list_table->prepare_items();
    
    ?>
    <div class="wrap">
        <h1 class="wp-heading-inline">用户通知</h1>
        <a href="<?php echo admin_url('admin.php?page=fd-member-notifications&action=new'); ?>" class="page-title-action">添加通知</a>
        <hr class="wp-header-end">
        
        <?php
        if (isset($_REQUEST['message'])) {
            if ($_REQUEST['message'] == '1') {
                echo '<div class="updated notice is-dismissible"><p>通知已成功保存。</p></div>';
            } elseif ($_REQUEST['message'] == '2') {
                echo '<div class="updated notice is-dismissible"><p>通知已成功删除。</p></div>';
            }
        }
        ?>
        
        <form method="post">
            <?php
            $list_table->display();
            ?>
        </form>
    </div>
    <?php
}

/**
 * 通知编辑页面
 */
function fd_member_notification_edit_page() {
    $notification_id = isset($_REQUEST['id']) ? absint($_REQUEST['id']) : 0;
    $notification = null;
    
    if ($notification_id > 0) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'member_notifications';
        $notification = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $notification_id));
    }
    
    ?>
    <div class="wrap">
        <h1 class="wp-heading-inline">
            <?php echo $notification ? '编辑通知' : '添加通知'; ?>
        </h1>
        <a href="<?php echo admin_url('admin.php?page=fd-member-notifications'); ?>" class="page-title-action">返回列表</a>
        <hr class="wp-header-end">
        
        <form method="post" action="<?php echo admin_url('admin-post.php'); ?>">
            <?php wp_nonce_field('fd_notification_save', 'fd_notification_nonce'); ?>
            <input type="hidden" name="action" value="fd_save_notification" />
            <?php if ($notification): ?>
            <input type="hidden" name="notification_id" value="<?php echo esc_attr($notification->id); ?>" />
            <?php endif; ?>
            
            <table class="form-table">
                <tr>
                    <th scope="row"><label for="notification_title">通知标题</label></th>
                    <td>
                        <input type="text" name="notification_title" id="notification_title" class="regular-text" value="<?php echo $notification ? esc_attr($notification->title) : ''; ?>" required />
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="notification_content">通知内容</label></th>
                    <td>
                        <textarea name="notification_content" id="notification_content" class="large-text" rows="5" required><?php echo $notification ? esc_textarea($notification->content) : ''; ?></textarea>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="notification_type">通知类型</label></th>
                    <td>
                        <select name="notification_type" id="notification_type">
                            <?php
                            $types = fd_member_notification_get_types();
                            foreach ($types as $type => $name) {
                                $selected = $notification && $notification->type == $type ? 'selected' : '';
                                echo '<option value="' . esc_attr($type) . '" ' . $selected . '>' . esc_html($name) . '</option>';
                            }
                            ?>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="notification_recipient">接收者</label></th>
                    <td>
                        <select name="notification_recipient" id="notification_recipient">
                            <option value="all">所有用户</option>
                            <?php
                            // 获取所有角色
                            $roles = wp_roles()->get_names();
                            echo '<optgroup label="按角色">';
                            foreach ($roles as $role_id => $role_name) {
                                echo '<option value="role:' . esc_attr($role_id) . '">角色: ' . esc_html($role_name) . '</option>';
                            }
                            echo '</optgroup>';
                            
                            // 获取最近注册的用户
                            $users = get_users(array('number' => 20, 'orderby' => 'registered', 'order' => 'DESC'));
                            if (!empty($users)) {
                                echo '<optgroup label="选择用户">';
                                foreach ($users as $user) {
                                    echo '<option value="' . esc_attr($user->ID) . '">' . esc_html($user->display_name) . ' (' . esc_html($user->user_email) . ')</option>';
                                }
                                echo '</optgroup>';
                            }
                            ?>
                        </select>
                        <p class="description">选择接收此通知的用户或用户组</p>
                    </td>
                </tr>
            </table>
            
            <p class="submit">
                <input type="submit" name="submit" id="submit" class="button button-primary" value="保存通知" />
            </p>
        </form>
    </div>
    <?php
}

/**
 * 处理通知保存
 */
function fd_member_notification_handle_save() {
    if (!isset($_POST['fd_notification_nonce']) || !wp_verify_nonce($_POST['fd_notification_nonce'], 'fd_notification_save')) {
        wp_die('安全验证失败', '错误', array('back_link' => true));
    }
    
    if (!current_user_can('manage_options')) {
        wp_die('您没有权限执行此操作', '权限错误', array('back_link' => true));
    }
    
    $notification_id = isset($_POST['notification_id']) ? absint($_POST['notification_id']) : 0;
    $title = sanitize_text_field($_POST['notification_title']);
    $content = wp_kses_post($_POST['notification_content']);
    $type = sanitize_text_field($_POST['notification_type']);
    $recipient = sanitize_text_field($_POST['notification_recipient']);
    
    if (empty($title) || empty($content)) {
        wp_die('请填写通知标题和内容', '输入错误', array('back_link' => true));
    }
    
    // 处理通知接收者
    if ($recipient == 'all') {
        // 获取所有用户ID
        $users = get_users(array('fields' => 'ID'));
        fd_member_notification_create_bulk($users, $title, $content, $type);
    } else if (strpos($recipient, 'role:') === 0) {
        // 按角色发送通知
        $role = str_replace('role:', '', $recipient);
        $users = get_users(array('role' => $role, 'fields' => 'ID'));
        fd_member_notification_create_bulk($users, $title, $content, $type);
    } else {
        // 发送给单个用户
        $user_id = absint($recipient);
        fd_member_notification_create($user_id, $title, $content, $type);
    }
    
    wp_redirect(admin_url('admin.php?page=fd-member-notifications&message=1'));
    exit;
}
add_action('admin_post_fd_save_notification', 'fd_member_notification_handle_save');

/**
 * 处理批量操作
 */
function fd_member_notification_handle_bulk_actions() {
    $page = isset($_GET['page']) ? $_GET['page'] : '';
    
    if ($page != 'fd-member-notifications') {
        return;
    }
    
    if (!isset($_REQUEST['action']) || $_REQUEST['action'] == -1) {
        return;
    }
    
    if (!isset($_REQUEST['notification']) || !is_array($_REQUEST['notification'])) {
        return;
    }
    
    if (!current_user_can('manage_options')) {
        wp_die('您没有权限执行此操作', '权限错误', array('back_link' => true));
    }
    
    $action = $_REQUEST['action'];
    $notification_ids = array_map('absint', $_REQUEST['notification']);
    
    switch ($action) {
        case 'delete':
            global $wpdb;
            $table_name = $wpdb->prefix . 'member_notifications';
            
            $ids_placeholder = implode(',', array_fill(0, count($notification_ids), '%d'));
            $wpdb->query($wpdb->prepare(
                "DELETE FROM $table_name WHERE id IN ($ids_placeholder)",
                $notification_ids
            ));
            
            wp_redirect(admin_url('admin.php?page=fd-member-notifications&message=2'));
            exit;
            break;
    }
}
add_action('admin_init', 'fd_member_notification_handle_bulk_actions'); 