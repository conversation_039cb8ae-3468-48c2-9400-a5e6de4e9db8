<?php
/**
 * Editor-related functions for Content Access Control.
 * (<PERSON><PERSON><PERSON> and Classic Editor)
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

require_once __DIR__ . '/../membership/member-levels.php';

// Compatibility wrapper: ensure unified function name across modules.
if (!function_exists('fd_member_get_all_member_levels')) {
    /**
     * 获取所有会员等级（兼容旧代码）
     *
     * @return array
     */
    function fd_member_get_all_member_levels() {
        if (function_exists('fd_member_get_member_levels')) {
            return fd_member_get_member_levels();
        }
        return array();
    }
}

// Explicitly require the core file to ensure constants and functions are available.
require_once __DIR__ . '/cac-core.php';

/**
 * Register post meta for the access level.
 * This makes it available to the REST API for Gutenberg.
 */
function fd_member_register_post_meta() {
    foreach (fd_member_get_restricted_post_types() as $ptype) {
        $meta_args_level = [
            'show_in_rest' => true,
            'single' => true,
            'type' => 'integer',
            'auth_callback' => function() {
                return current_user_can('edit_posts');
            }
        ];
        register_post_meta($ptype, FD_POST_ACCESS_LEVEL_META_KEY, $meta_args_level);

        // 注册解锁价格元数据
        register_post_meta($ptype, FD_POST_UNLOCK_PRICE_META_KEY, [
            'show_in_rest' => true,
            'single' => true,
            'type' => 'number',
            'auth_callback' => function() {
                return current_user_can('edit_posts');
            }
        ]);

        // preview mode (excerpt / first_chars / custom)
        register_post_meta($ptype, '_fd_preview_mode', [
            'show_in_rest' => true,
            'single' => true,
            'type' => 'string',
            'auth_callback' => function() {
                return current_user_can('edit_posts');
            }
        ]);

        register_post_meta($ptype, '_fd_preview_value', [
            'show_in_rest' => true,
            'single' => true,
            'type' => 'string',
            'auth_callback' => function() {
                return current_user_can('edit_posts');
            }
        ]);
    }
}
add_action('init', 'fd_member_register_post_meta');

/**
 * Add the meta box for the Classic Editor (all CPTs).
 */
function fd_member_add_access_control_meta_box() {
    foreach (fd_member_get_restricted_post_types() as $ptype) {
        add_meta_box(
            'fd-member-access-control',
            __('Content Access', 'fd-member'),
            'fd_member_render_access_control_meta_box',
            $ptype,
            'side',
            'high'
        );
    }
}
add_action('add_meta_boxes', 'fd_member_add_access_control_meta_box');

/**
 * Render the content of the meta box.
 *
 * @param WP_Post $post The post object.
 */
function fd_member_render_access_control_meta_box($post) {
    wp_nonce_field('fd_member_access_control_save', 'fd_member_access_nonce');

    $required_level_id = get_post_meta($post->ID, FD_POST_ACCESS_LEVEL_META_KEY, true);
    $unlock_price = get_post_meta($post->ID, FD_POST_UNLOCK_PRICE_META_KEY, true);
    $all_levels = fd_member_get_sorted_member_levels(false);
    $preview_mode  = get_post_meta($post->ID, '_fd_preview_mode', true) ?: 'excerpt';
    $preview_value = get_post_meta($post->ID, '_fd_preview_value', true);

    ?>
    <p>
        <label for="fd_required_member_level"><?php _e('Required Member Level', 'fd-member'); ?></label>
    </p>
    <select name="fd_required_member_level" id="fd_required_member_level" style="width: 100%;">
        <option value="0" <?php selected($required_level_id, 0); ?>>
            <?php _e('Public (Everyone)', 'fd-member'); ?>
        </option>
        <option value="-1" <?php selected($required_level_id, -1); ?>>
            <?php _e('Logged-in Users', 'fd-member'); ?>
        </option>
        <optgroup label="<?php _e('Member Levels', 'fd-member'); ?>">
            <?php foreach ($all_levels as $level) : ?>
                <option value="<?php echo esc_attr($level['id']); ?>" <?php selected($required_level_id, $level['id']); ?>>
                    <?php echo esc_html($level['name']); ?>
                </option>
            <?php endforeach; ?>
        </optgroup>
    </select>
    <p class="howto">
        <?php _e('Select the minimum member level required to view this post\'s full content.', 'fd-member'); ?>
    </p>

    <hr />
    <p>
        <label for="fd_unlock_price"><strong><?php _e('单独解锁价格', 'fd-member'); ?></strong></label>
    </p>
    <input type="number" name="fd_unlock_price" id="fd_unlock_price" value="<?php echo esc_attr($unlock_price); ?>" min="0" step="0.01" style="width: 100%;" />
    <p class="howto">
        <?php _e('设置用户单独购买此文章的价格。设置为0或留空表示不允许单独购买。', 'fd-member'); ?>
    </p>

    <hr />
    <p>
        <label for="fd_preview_mode"><strong><?php _e('Preview Mode', 'fd-member'); ?></strong></label>
    </p>
    <select name="fd_preview_mode" id="fd_preview_mode" style="width:100%;">
        <option value="excerpt" <?php selected($preview_mode, 'excerpt'); ?>><?php _e('Excerpt (default)', 'fd-member'); ?></option>
        <option value="first_chars" <?php selected($preview_mode, 'first_chars'); ?>><?php _e('First N characters', 'fd-member'); ?></option>
        <option value="custom" <?php selected($preview_mode, 'custom'); ?>><?php _e('Custom HTML/Text', 'fd-member'); ?></option>
    </select>

    <p class="howto">
        <?php _e('If "First N characters", enter a number; if "Custom",可填写任意HTML。', 'fd-member'); ?>
    </p>

    <textarea name="fd_preview_value" id="fd_preview_value" rows="3" style="width:100%;"><?php echo esc_textarea($preview_value); ?></textarea>
    <?php
}

/**
 * Save the meta box data.
 *
 * @param int $post_id The ID of the post being saved.
 */
function fd_member_save_access_control_meta_data($post_id) {
    // Check nonce
    if (!isset($_POST['fd_member_access_nonce']) || !wp_verify_nonce($_POST['fd_member_access_nonce'], 'fd_member_access_control_save')) {
        return;
    }

    // Check user permissions
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    if (isset($_POST['fd_required_member_level'])) {
        $level_id = intval($_POST['fd_required_member_level']);
        update_post_meta($post_id, FD_POST_ACCESS_LEVEL_META_KEY, $level_id);
    }

    if (isset($_POST['fd_unlock_price'])) {
        $price = floatval($_POST['fd_unlock_price']);
        update_post_meta($post_id, FD_POST_UNLOCK_PRICE_META_KEY, $price);
    }

    if (isset($_POST['fd_preview_mode'])) {
        $mode = sanitize_text_field($_POST['fd_preview_mode']);
        update_post_meta($post_id, '_fd_preview_mode', $mode);
    }

    if (isset($_POST['fd_preview_value'])) {
        $val = wp_kses_post($_POST['fd_preview_value']);
        update_post_meta($post_id, '_fd_preview_value', $val);
    }
}
add_action('save_post', 'fd_member_save_access_control_meta_data');


/**
 * Enqueue assets for the Gutenberg editor.
 */
function fd_member_enqueue_editor_assets() {
    $screen = get_current_screen();
    if (!$screen || $screen->base !== 'post' || !in_array($screen->post_type, fd_member_get_restricted_post_types(), true)) {
        return;
    }

    $asset_file_path = FD_MEMBER_DIR . 'assets/js/editor.js';
    
    // Get all member levels sorted by priority
    $levels = fd_member_get_sorted_member_levels(false); // false for ascending
    $formatted_levels = array_map(function($level){
        return [
            'id' => intval($level['id']),
            'name' => $level['name'],
        ];
    }, $levels);

    wp_enqueue_script(
        'fd-member-editor-script',
        FD_MEMBER_URI . 'assets/js/editor.js',
        ['wp-plugins', 'wp-edit-post', 'wp-element', 'wp-components', 'wp-data', 'wp-compose', 'wp-i18n'],
        filemtime($asset_file_path),
        true // Load in footer
    );

    // Set up script translations
    wp_set_script_translations('fd-member-editor-script', 'fd-member', FD_MEMBER_DIR . 'lang');

    // Pass member levels to the script
    wp_localize_script('fd-member-editor-script', 'fdMemberLevels', $formatted_levels);
}
add_action('enqueue_block_editor_assets', 'fd_member_enqueue_editor_assets');

/**
 * Get all member levels formatted for the editor.
 *
 * @return array
 */
function fd_member_get_all_member_levels_for_editor() {
    $levels = fd_member_get_all_member_levels();
    $options = [
        ['value' => 0, 'label' => __('Public Access', 'fd-member')],
    ];
    foreach ($levels as $level) {
        if (is_array($level)) {
            $options[] = [
                'value' => intval($level['id']),
                'label' => $level['name'],
            ];
        }
    }
    return $options;
}


// --- Bulk Actions for Post List Tables ---

/**
 * Add bulk actions for setting member access level to the posts list table.
 */
function fd_member_cac_add_bulk_actions_to_init() {
    $post_types = fd_member_get_restricted_post_types();
    foreach ($post_types as $post_type) {
        add_filter("bulk_actions-edit-{$post_type}", 'fd_member_cac_add_bulk_actions');
        add_filter("handle_bulk_actions-edit-{$post_type}", 'fd_member_cac_handle_bulk_action', 10, 3);
    }
}
add_action('admin_init', 'fd_member_cac_add_bulk_actions_to_init');


/**
 * Callback to add custom bulk actions to the dropdown.
 *
 * @param array $bulk_actions Existing bulk actions.
 * @return array Modified bulk actions.
 */
function fd_member_cac_add_bulk_actions($bulk_actions) {
    $levels = fd_member_get_all_member_levels();

    $bulk_actions['fd_member_set_level_0'] = __('Set Access: Public', 'fd-member');

    if (!empty($levels)) {
        foreach ($levels as $level) {
            if (is_array($level)) {
                $bulk_actions['fd_member_set_level_' . $level['id']] = sprintf(__('Set Access: %s', 'fd-member'), $level['name']);
            }
        }
    }

    return $bulk_actions;
}

/**
 * Handle the custom bulk action.
 *
 * @param string $redirect_to The redirect URL.
 * @param string $action The action being performed.
 * @param array  $post_ids The IDs of the posts to be modified.
 * @return string The redirect URL.
 */
function fd_member_cac_handle_bulk_action($redirect_to, $action, $post_ids) {
    if (strpos($action, 'fd_member_set_level_') === false) {
        return $redirect_to;
    }

    $level_id = (int) str_replace('fd_member_set_level_', '', $action);
    $updated_count = 0;

    foreach ($post_ids as $post_id) {
        update_post_meta($post_id, '_fd_required_member_level', $level_id);
        $updated_count++;
    }

    return add_query_arg([
        'fd_member_level_updated' => $updated_count,
    ], $redirect_to);
}

/**
 * Display an admin notice after the bulk action has been performed.
 */
function fd_member_cac_bulk_action_admin_notice() {
    if (!empty($_REQUEST['fd_member_level_updated'])) {
        $count = (int) $_REQUEST['fd_member_level_updated'];
        printf(
            '<div class="notice notice-success is-dismissible"><p>%s</p></div>',
            sprintf(
                _n(
                    '%d post access level updated.',
                    '%d posts access levels updated.',
                    $count,
                    'fd-member'
                ),
                number_format_i18n($count)
            )
        );
    }
}
add_action('admin_notices', 'fd_member_cac_bulk_action_admin_notice');

// ==============================
//   Bulk Actions: Unlock Price
// ==============================

/**
 * 添加"批量设置解锁价格"下拉菜单选项。
 *
 * @param array $bulk_actions
 * @return array
 */
function fd_member_cac_add_bulk_unlock_price_actions($bulk_actions) {
    $bulk_actions['set_unlock_price_custom'] = __('Set Unlock Price…', 'fd-member');
    return $bulk_actions;
}

/**
 * 处理"批量设置解锁价格"动作。
 *
 * @param string $redirect_to
 * @param string $doaction
 * @param array  $post_ids
 * @return string
 */
function fd_member_cac_handle_bulk_unlock_price_action($redirect_to, $doaction, $post_ids) {
    if ($doaction !== 'set_unlock_price_custom') {
        return $redirect_to;
    }

    if (!isset($_REQUEST['fd_bulk_unlock_price'])) {
        return $redirect_to;
    }

    $new_price = floatval($_REQUEST['fd_bulk_unlock_price']);

    $updated = 0;
    foreach ($post_ids as $post_id) {
        // 仅更新受保护的内容类型
        update_post_meta($post_id, FD_POST_UNLOCK_PRICE_META_KEY, $new_price);
        $updated++;
    }

    // 在重定向 URL 上附加结果参数
    $redirect_to = add_query_arg([
        'unlock_price_updated' => $updated,
        'unlock_price_value'   => $new_price,
    ], $redirect_to);

    return $redirect_to;
}

/**
 * 在后台显示批量设置解锁价格完成后的通知。
 */
function fd_member_cac_bulk_unlock_price_admin_notice() {
    if (!empty($_GET['unlock_price_updated'])) {
        $count = intval($_GET['unlock_price_updated']);
        $price = esc_html($_GET['unlock_price_value']);
        printf(
            '<div class="notice notice-success is-dismissible"><p>' .
            /* translators: 1: updated count, 2: price */
            __('%1$s posts have been updated. Unlock price set to ¥%2$s.', 'fd-member') .
            '</p></div>',
            $count,
            $price
        );
    }
}

// 针对所有受保护的文章类型挂钩
add_action('admin_init', function () {
    // Enqueue a dummy script as carrier to attach inline JS and ensure jQuery loads
    wp_enqueue_script('fd-member-bulk-unlock', FD_MEMBER_URI . 'assets/js/blank.js', ['jquery'], FD_MEMBER_VERSION, true);
    $prompt = esc_js(__('Enter unlock price (leave empty or 0 to disable):', 'fd-member'));
    $js = <<<JS
(function($){
    $(document).ready(function(){
        $('#doaction, #doaction2').on('click', function(){
            var actionSel = ($(this).attr('id') === 'doaction') ? 'select[name="action"]' : 'select[name="action2"]';
            var action = $(actionSel).val();
            if(action === 'set_unlock_price_custom'){
                var price = window.prompt('{$prompt}');
                if(price === null){ return false; }
                $('<input>').attr({type:'hidden', name:'fd_bulk_unlock_price', value:price}).appendTo('#posts-filter');
            }
        });
    });
})(jQuery);
JS;
    wp_add_inline_script('fd-member-bulk-unlock', $js);

    foreach (fd_member_get_restricted_post_types() as $ptype) {
        add_filter("bulk_actions-edit-{$ptype}", 'fd_member_cac_add_bulk_unlock_price_actions');
        add_filter("handle_bulk_actions-edit-{$ptype}", 'fd_member_cac_handle_bulk_unlock_price_action', 10, 3);
    }
});
add_action('admin_notices', 'fd_member_cac_bulk_unlock_price_admin_notice'); 