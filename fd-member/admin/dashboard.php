<?php
/**
 * 会员管理插件仪表盘页面
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 会员仪表盘页面回调
 */
function fd_member_dashboard_page() {
    // 检查用户权限
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // 获取统计数据
    $total_users = count_users()['total_users'];
    $new_users_week = fd_member_get_new_users_count(7);
    $total_likes = function_exists('fd_member_likes_get_total_likes') ? fd_member_likes_get_total_likes() : 'N/A';
    $levels_stats = function_exists('fd_member_get_user_counts_by_level') ? fd_member_get_user_counts_by_level() : [];

    ?>
    <div class="wrap fd-member-dashboard">
        <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

        <div id="dashboard-widgets-wrap">
            <div id="dashboard-widgets" class="metabox-holder">

                <!-- Column 1 -->
                <div id="postbox-container-1" class="postbox-container">
                    <div class="postbox">
                        <h2 class="hndle"><span><span class="dashicons dashicons-chart-bar"></span> 会员统计</span></h2>
                        <div class="inside">
                            <div class="main">
                                <ul class="fd-stats-list">
                                    <li>
                                        <span class="dashicons dashicons-groups"></span>
                                        <strong>总会员数:</strong>
                                        <span><?php echo number_format_i18n($total_users); ?></span>
                                    </li>
                                    <li>
                                        <span class="dashicons dashicons-star-filled"></span>
                                        <strong>近7日新增:</strong>
                                        <span><?php echo number_format_i18n($new_users_week); ?></span>
                                    </li>
                                    <li>
                                        <span class="dashicons dashicons-heart"></span>
                                        <strong>总点赞数:</strong>
                                        <span><?php echo is_numeric($total_likes) ? number_format_i18n($total_likes) : $total_likes; ?></span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="postbox">
                        <h2 class="hndle"><span><span class="dashicons dashicons-admin-links"></span> 快速操作</span></h2>
                        <div class="inside">
                            <div class="main">
                                <ul class="fd-quick-links">
                                    <li><a href="<?php echo admin_url('users.php'); ?>" class="button button-primary">所有用户</a></li>
                                    <li><a href="<?php echo admin_url('admin.php?page=fd-member-settings'); ?>" class="button">会员设置</a></li>
                                    <li><a href="<?php echo admin_url('admin.php?page=fd-member-levels'); ?>" class="button">会员等级</a></li>
                                    <li><a href="<?php echo admin_url('admin.php?page=fd-likes-manager'); ?>" class="button">点赞管理</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Column 2 -->
                <div id="postbox-container-2" class="postbox-container">
                    <div class="postbox">
                        <h2 class="hndle"><span><span class="dashicons dashicons-awards"></span> 会员等级分布</span></h2>
                        <div class="inside">
                            <div class="main">
                                <?php if (!empty($levels_stats)) : ?>
                                    <ul class="fd-stats-list">
                                        <?php foreach ($levels_stats as $stat) : ?>
                                            <li>
                                                <span class="dashicons dashicons-businessman"></span>
                                                <strong><?php echo esc_html($stat->level_name); ?>:</strong>
                                                <span><?php echo number_format_i18n($stat->user_count); ?> 人</span>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php else: ?>
                                    <p>暂无会员等级数据。</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <?php
}

/**
 * 获取指定天数内的新用户数量
 *
 * @param int $days 天数
 * @return int 新用户数量
 */
function fd_member_get_new_users_count($days = 7) {
    global $wpdb;
    $date = date('Y-m-d H:i:s', strtotime("-{$days} days"));
    $count = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT COUNT(ID) FROM {$wpdb->users} WHERE user_registered >= %s",
            $date
        )
    );
    return (int)$count;
} 