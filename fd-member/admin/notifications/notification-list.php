<?php
/**
 * 通知系统列表表格类
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 通知列表表格类
 */
class FD_Notification_List_Table extends WP_List_Table {
    
    /**
     * 构造函数
     */
    public function __construct() {
        parent::__construct([
            'singular' => '通知',
            'plural'   => '通知',
            'ajax'     => false
        ]);
    }
    
    /**
     * 获取列定义
     *
     * @return array
     */
    public function get_columns() {
        $columns = [
            'cb'         => '<input type="checkbox" />',
            'title'      => '标题',
            'content'    => '内容',
            'type'       => '类型',
            'recipient'  => '接收者',
            'status'     => '状态',
            'created_at' => '创建时间'
        ];
        
        return $columns;
    }
    
    /**
     * 获取可排序列
     *
     * @return array
     */
    protected function get_sortable_columns() {
        $sortable_columns = [
            'title'      => ['title', false],
            'type'       => ['type', false],
            'status'     => ['status', false],
            'created_at' => ['created_at', true]
        ];
        
        return $sortable_columns;
    }
    
    /**
     * 批量操作选项
     *
     * @return array
     */
    protected function get_bulk_actions() {
        $actions = [
            'delete' => '删除'
        ];
        
        return $actions;
    }
    
    /**
     * 默认列渲染
     *
     * @param object $item 当前行数据
     * @param string $column_name 列名
     *
     * @return string
     */
    protected function column_default($item, $column_name) {
        switch ($column_name) {
            case 'content':
                return '<div class="notification-content">' . wp_trim_words(esc_html($item->content), 10, '...') . '</div>';
            case 'type':
                $types = fd_member_notification_get_types();
                return isset($types[$item->type]) ? esc_html($types[$item->type]) : esc_html($item->type);
            case 'status':
                return $item->status == 'unread' ? '<span class="status-unread">未读</span>' : '<span class="status-read">已读</span>';
            case 'created_at':
                return esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($item->created_at)));
            case 'recipient':
                $user = get_userdata($item->user_id);
                return $user ? esc_html($user->display_name) . ' (' . esc_html($user->user_email) . ')' : '用户ID: ' . esc_html($item->user_id);
            default:
                return esc_html(print_r($item, true)); // 显示我们未处理的内容
        }
    }
    
    /**
     * 渲染标题列
     *
     * @param object $item 当前行数据
     *
     * @return string
     */
    protected function column_title($item) {
        // 构建操作链接
        $actions = [
            'edit'   => sprintf('<a href="%s">编辑</a>', admin_url('admin.php?page=fd-member-notifications&action=edit&id=' . $item->id)),
            'delete' => sprintf('<a href="%s" onclick="return confirm(\'确定要删除此通知吗?\');">删除</a>', admin_url('admin.php?page=fd-member-notifications&action=delete&notification[]=' . $item->id))
        ];
        
        // 返回标题加操作链接
        return sprintf('%1$s %2$s', esc_html($item->title), $this->row_actions($actions));
    }
    
    /**
     * 渲染复选框列
     *
     * @param object $item 当前行数据
     *
     * @return string
     */
    protected function column_cb($item) {
        return sprintf(
            '<input type="checkbox" name="notification[]" value="%s" />',
            $item->id
        );
    }
    
    /**
     * 准备表格数据
     */
    public function prepare_items() {
        $this->_column_headers = [
            $this->get_columns(),
            [],
            $this->get_sortable_columns()
        ];
        
        $per_page = 20;
        $current_page = $this->get_pagenum();
        $offset = ($current_page - 1) * $per_page;
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'member_notifications';
        
        // 排序
        $orderby = !empty($_REQUEST['orderby']) ? sanitize_sql_orderby($_REQUEST['orderby']) : 'created_at';
        $order = !empty($_REQUEST['order']) ? sanitize_text_field($_REQUEST['order']) : 'desc';
        
        // 搜索
        $search = isset($_REQUEST['s']) ? sanitize_text_field($_REQUEST['s']) : '';
        $where = '';
        if (!empty($search)) {
            $where = $wpdb->prepare(" WHERE title LIKE %s OR content LIKE %s", "%{$search}%", "%{$search}%");
        }
        
        // 获取总数据量
        $total_items = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}{$where}");
        
        // 获取分页数据
        $this->items = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM {$table_name}{$where} ORDER BY {$orderby} {$order} LIMIT %d, %d",
                $offset,
                $per_page
            )
        );
        
        // 设置分页参数
        $this->set_pagination_args([
            'total_items' => $total_items,
            'per_page'    => $per_page,
            'total_pages' => ceil($total_items / $per_page)
        ]);
    }
    
    /**
     * 无数据提示信息
     */
    public function no_items() {
        echo '暂无通知数据';
    }
}

// 添加样式
add_action('admin_head', function() {
    echo '<style>
        .column-content { width: 30%; }
        .column-title { width: 20%; }
        .status-unread { color: #d54e21; font-weight: bold; }
        .status-read { color: #999; }
        .notification-content { max-height: 60px; overflow: hidden; }
    </style>';
}); 