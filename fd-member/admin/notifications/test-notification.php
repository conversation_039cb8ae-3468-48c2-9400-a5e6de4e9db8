<?php
/**
 * 通知系统测试页面
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

// 注册测试页面
function fd_member_notification_add_test_menu() {
    add_submenu_page(
        'fd-member',
        '测试通知',
        '测试通知',
        'manage_options',
        'fd-member-notification-test',
        'fd_member_notification_test_page'
    );
}
add_action('admin_menu', 'fd_member_notification_add_test_menu', 30);

// 测试页面内容
function fd_member_notification_test_page() {
    $message = '';
    $success = false;
    
    // 处理表单提交
    if (isset($_POST['test_notification']) && wp_verify_nonce($_POST['notification_test_nonce'], 'notification_test')) {
        $user_id = get_current_user_id();
        $title = '测试通知';
        $content = '这是一条测试通知，发送时间: ' . current_time('mysql');
        
        $result = fd_member_notification_create($user_id, $title, $content, 'system');
        
        if ($result) {
            $message = '测试通知创建成功，通知ID: ' . $result;
            $success = true;
        } else {
            $message = '测试通知创建失败';
        }
    }
    
    // 显示测试页面
    ?>
    <div class="wrap">
        <h1>通知系统测试</h1>
        
        <?php if ($message): ?>
            <div class="<?php echo $success ? 'updated' : 'error'; ?> notice is-dismissible">
                <p><?php echo esc_html($message); ?></p>
            </div>
        <?php endif; ?>
        
        <div class="card">
            <h2>发送测试通知</h2>
            <p>点击下面的按钮向当前用户发送一条测试通知。</p>
            
            <form method="post">
                <?php wp_nonce_field('notification_test', 'notification_test_nonce'); ?>
                <p>
                    <input type="submit" name="test_notification" class="button button-primary" value="发送测试通知" />
                </p>
            </form>
        </div>
        
        <div class="card">
            <h2>数据库诊断</h2>
            <p><strong>当前数据库前缀:</strong> <code><?php echo $wpdb->prefix; ?></code></p>
            <p><strong>正确的通知表名:</strong> <code><?php echo $table_name; ?></code></p>
            <p><strong>可能错误的表名:</strong> <code><?php echo $wpdb->prefix; ?>fd_member_notifications</code></p>
            
            <h3>代码中表名引用检查</h3>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>文件</th>
                        <th>表名引用</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $files_to_check = array(
                        'includes/notifications/notification-core.php',
                        'includes/notifications/notification-admin.php',
                        'includes/notifications/notification-api.php',
                        'admin/notifications/notification-list.php',
                        'admin/notifications/test-notification.php'
                    );
                    
                    foreach ($files_to_check as $file) {
                        $full_path = FD_MEMBER_DIR . $file;
                        $content = file_exists($full_path) ? file_get_contents($full_path) : '';
                        $wrong_ref = strpos($content, "'fd_member_notifications'") !== false || 
                                    strpos($content, '"fd_member_notifications"') !== false;
                        $correct_ref = strpos($content, "'member_notifications'") !== false || 
                                      strpos($content, '"member_notifications"') !== false;
                        
                        echo '<tr>';
                        echo '<td>' . esc_html($file) . '</td>';
                        echo '<td>';
                        if ($wrong_ref) {
                            echo '<span style="color: red;">发现错误表名引用 fd_member_notifications</span><br>';
                        }
                        if ($correct_ref) {
                            echo '<span style="color: green;">发现正确表名引用 member_notifications</span>';
                        }
                        if (!$wrong_ref && !$correct_ref) {
                            echo '<span style="color: gray;">未找到表名引用</span>';
                        }
                        echo '</td>';
                        echo '<td>' . ($wrong_ref ? '❌ 需要修复' : '✅ 正常') . '</td>';
                        echo '</tr>';
                    }
                    ?>
                </tbody>
            </table>
        </div>
        
        <div class="card">
            <h2>数据库状态</h2>
            <?php
            global $wpdb;
            $table_name = $wpdb->prefix . 'member_notifications';
            $old_table_name = $wpdb->prefix . 'fd_member_notifications';
            
            // 检查表是否存在
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") == $table_name;
            $old_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$old_table_name}'") == $old_table_name;
            
            // 处理迁移
            if (isset($_POST['migrate_table']) && wp_verify_nonce($_POST['migration_nonce'], 'migrate_table')) {
                if (function_exists('fd_member_notification_migrate_table')) {
                    $migrate_result = fd_member_notification_migrate_table();
                    if ($migrate_result) {
                        echo '<div class="updated notice is-dismissible"><p>表迁移成功！</p></div>';
                    } else {
                        echo '<div class="error notice is-dismissible"><p>表迁移失败或无需迁移。</p></div>';
                    }
                    echo '<script>window.location.reload();</script>';
                } else {
                    echo '<div class="error notice is-dismissible"><p>迁移函数不可用。</p></div>';
                }
            }
            
            if ($old_table_exists) {
                echo '<div class="card" style="border-left: 4px solid #dc3232; padding: 10px;">';
                echo '<h3>检测到旧表结构!</h3>';
                echo '<p>发现表 <code>' . esc_html($old_table_name) . '</code> 存在，这是由于表前缀重复造成的错误表结构。</p>';
                echo '<p>点击下面的按钮将数据迁移到正确的表 <code>' . esc_html($table_name) . '</code> 并删除旧表。</p>';
                
                echo '<form method="post">';
                wp_nonce_field('migrate_table', 'migration_nonce');
                echo '<p><input type="submit" name="migrate_table" class="button button-primary" value="迁移数据库表" /></p>';
                echo '</form>';
                echo '</div>';
            }
            
            if ($table_exists) {
                echo '<p style="color:green">✓ 通知表已创建</p>';
                
                // 获取通知数量
                $count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
                echo '<p>系统中共有 ' . $count . ' 条通知记录</p>';
                
                // 获取最新5条通知
                if ($count > 0) {
                    $notifications = $wpdb->get_results("SELECT * FROM {$table_name} ORDER BY created_at DESC LIMIT 5");
                    echo '<h3>最新5条通知</h3>';
                    echo '<table class="wp-list-table widefat fixed striped">';
                    echo '<thead><tr><th>ID</th><th>用户ID</th><th>标题</th><th>类型</th><th>状态</th><th>创建时间</th></tr></thead>';
                    echo '<tbody>';
                    foreach ($notifications as $note) {
                        echo '<tr>';
                        echo '<td>' . esc_html($note->id) . '</td>';
                        echo '<td>' . esc_html($note->user_id) . '</td>';
                        echo '<td>' . esc_html($note->title) . '</td>';
                        echo '<td>' . esc_html($note->type) . '</td>';
                        echo '<td>' . esc_html($note->status) . '</td>';
                        echo '<td>' . esc_html($note->created_at) . '</td>';
                        echo '</tr>';
                    }
                    echo '</tbody>';
                    echo '</table>';
                }
            } else {
                echo '<p style="color:red">✗ 通知表未创建</p>';
                echo '<p>请尝试重新激活插件以创建数据表。</p>';
                
                // 手动创建表按钮
                if (isset($_POST['create_table']) && wp_verify_nonce($_POST['table_create_nonce'], 'create_table')) {
                    fd_member_notification_init_db();
                    echo '<p>正在尝试创建数据表...</p>';
                    echo '<script>window.location.reload();</script>';
                }
                
                echo '<form method="post">';
                wp_nonce_field('create_table', 'table_create_nonce');
                echo '<p><input type="submit" name="create_table" class="button" value="手动创建数据表" /></p>';
                echo '</form>';
            }
            ?>
        </div>
    </div>
    <?php
} 