<?php
/**
 * 会员管理插件菜单配置
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 添加会员管理主菜单
 */
function fd_member_add_admin_menu() {
    add_menu_page(
        'FD Member', // 页面标题
        'FD Member', // 菜单标题
        'manage_options', // 权限
        'fd-member', // 菜单别名
        'fd_member_dashboard_page', // 回调函数
        'dashicons-groups', // 图标
        30 // 位置
    );
    
    // 添加仪表盘子菜单
    add_submenu_page(
        'fd-member', // 父菜单别名
        '会员统计', // 页面标题
        '会员统计', // 菜单标题
        'manage_options', // 权限
        'fd-member', // 菜单别名（与主菜单相同，作为默认页面）
        'fd_member_dashboard_page' // 回调函数
    );
    
    // 添加会员等级管理子菜单
    add_submenu_page(
        'fd-member', // 父菜单别名
        '会员等级管理', // 页面标题
        '会员等级管理', // 菜单标题
        'manage_options', // 权限
        'fd-member-levels', // 菜单别名
        'fd_member_levels_page' // 回调函数
    );

    // 添加等级配置检查工具子菜单
    add_submenu_page(
        'fd-member', // 父菜单别名
        '配置检查', // 页面标题
        '配置检查', // 菜单标题
        'manage_options', // 权限
        'fd-member-config-checker', // 菜单别名
        'fd_member_level_config_checker_page' // 回调函数
    );
}
add_action('admin_menu', 'fd_member_add_admin_menu');