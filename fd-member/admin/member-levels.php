<?php
/**
 * 会员等级管理页面
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 在admin_init钩子中处理会员等级的删除和更新操作
 */
function fd_member_levels_admin_init() {
    // 只在会员等级管理页面处理
    if (!isset($_GET['page']) || $_GET['page'] !== 'fd-member-levels') {
        return;
    }
    
    // 处理设置默认等级
    if (isset($_POST['set_default_level']) && isset($_POST['_wpnonce']) && wp_verify_nonce($_POST['_wpnonce'], 'fd_set_default_level')) {
        $default_level_id = isset($_POST['default_level_id']) ? intval($_POST['default_level_id']) : 0;
        $current_default_level_id = get_option(FD_MEMBER_DEFAULT_LEVEL_OPTION, 0);
        
        // 检查提交的值是否与当前设置的默认值相同
        if ($default_level_id == $current_default_level_id) {
            // 值相同，无需更新，返回成功消息
            wp_safe_redirect(add_query_arg(array(
                'page' => 'fd-member-levels',
                'message' => 'default_level_unchanged'
            ), admin_url('admin.php')));
            exit;
        }
        
        if (fd_member_set_default_member_level($default_level_id)) {
            wp_safe_redirect(add_query_arg(array(
                'page' => 'fd-member-levels',
                'message' => 'default_level_success'
            ), admin_url('admin.php')));
            exit;
        } else {
            wp_safe_redirect(add_query_arg(array(
                'page' => 'fd-member-levels',
                'message' => 'default_level_error'
            ), admin_url('admin.php')));
            exit;
        }
    }
    
    // 删除等级
    if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['level_id']) && isset($_GET['_wpnonce']) && wp_verify_nonce($_GET['_wpnonce'], 'fd_delete_level_' . $_GET['level_id'])) {
        $level_id = intval($_GET['level_id']);
        
        if (fd_member_delete_member_level($level_id)) {
            // 重定向到列表页，并添加成功消息参数
            wp_safe_redirect(add_query_arg(array(
                'page' => 'fd-member-levels',
                'message' => 'delete_success'
            ), admin_url('admin.php')));
            exit;
        } else {
            // 重定向到列表页，并添加失败消息参数
            wp_safe_redirect(add_query_arg(array(
                'page' => 'fd-member-levels',
                'message' => 'delete_error'
            ), admin_url('admin.php')));
            exit;
        }
    }
    
    // 更新等级（通过POST提交）
    if (isset($_POST['update_level']) && isset($_POST['_wpnonce']) && wp_verify_nonce($_POST['_wpnonce'], 'fd_update_level')) {
        $level_id = isset($_POST['level_id']) ? intval($_POST['level_id']) : 0;

        $level_data = array(
            'name' => isset($_POST['level_name']) ? $_POST['level_name'] : '',
            'description' => isset($_POST['level_description']) ? $_POST['level_description'] : '',
            'priority' => isset($_POST['level_priority']) ? $_POST['level_priority'] : 0,
            'price' => isset($_POST['level_price']) ? $_POST['level_price'] : 0,
            'duration' => isset($_POST['level_duration']) ? $_POST['level_duration'] : 0,
            'duration_unit' => isset($_POST['level_duration_unit']) ? $_POST['level_duration_unit'] : 'days'
        );

        // 检查配置合理性
        $validation = fd_member_validate_level_config($level_data, $level_id);

        $result = fd_member_update_member_level($level_id, $level_data);

        if (is_wp_error($result)) {
            wp_safe_redirect(add_query_arg(array(
                'page' => 'fd-member-levels',
                'message' => 'update_error',
                'error' => urlencode($result->get_error_message())
            ), admin_url('admin.php')));
            exit;
        } else {
            $redirect_args = array(
                'page' => 'fd-member-levels',
                'message' => 'update_success'
            );

            // 如果有警告或建议，添加到URL参数中
            if (!empty($validation['warnings']) || !empty($validation['suggestions'])) {
                $redirect_args['validation_warnings'] = urlencode(json_encode($validation['warnings']));
                $redirect_args['validation_suggestions'] = urlencode(json_encode($validation['suggestions']));
            }

            wp_safe_redirect(add_query_arg($redirect_args, admin_url('admin.php')));
            exit;
        }
    }
    
    // 添加新等级（通过POST提交）
    if (isset($_POST['add_level']) && isset($_POST['_wpnonce']) && wp_verify_nonce($_POST['_wpnonce'], 'fd_add_level')) {
        $level_data = array(
            'name' => isset($_POST['level_name']) ? $_POST['level_name'] : '',
            'description' => isset($_POST['level_description']) ? $_POST['level_description'] : '',
            'priority' => isset($_POST['level_priority']) ? $_POST['level_priority'] : 0,
            'price' => isset($_POST['level_price']) ? $_POST['level_price'] : 0,
            'duration' => isset($_POST['level_duration']) ? $_POST['level_duration'] : 0,
            'duration_unit' => isset($_POST['level_duration_unit']) ? $_POST['level_duration_unit'] : 'days'
        );

        // 检查配置合理性
        $validation = fd_member_validate_level_config($level_data);

        $result = fd_member_add_member_level($level_data);

        if (is_wp_error($result)) {
            wp_safe_redirect(add_query_arg(array(
                'page' => 'fd-member-levels',
                'message' => 'add_error',
                'error' => urlencode($result->get_error_message())
            ), admin_url('admin.php')));
            exit;
        } else {
            $redirect_args = array(
                'page' => 'fd-member-levels',
                'message' => 'add_success'
            );

            // 如果有警告或建议，添加到URL参数中
            if (!empty($validation['warnings']) || !empty($validation['suggestions'])) {
                $redirect_args['validation_warnings'] = urlencode(json_encode($validation['warnings']));
                $redirect_args['validation_suggestions'] = urlencode(json_encode($validation['suggestions']));
            }

            wp_safe_redirect(add_query_arg($redirect_args, admin_url('admin.php')));
            exit;
        }
    }
}
add_action('admin_init', 'fd_member_levels_admin_init');

/**
 * 会员等级设置页面回调函数
 */
function fd_member_levels_page() {
    // 检查用户权限
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // 处理消息
    $message = '';
    $message_type = '';
    
    // 处理通过URL参数传递的消息
    if (isset($_GET['message'])) {
        switch ($_GET['message']) {
            case 'delete_success':
                $message = '会员等级删除成功。';
                $message_type = 'success';
                break;
            case 'delete_error':
                $message = '删除会员等级失败。';
                $message_type = 'error';
                break;
            case 'update_success':
                $message = '会员等级更新成功。';
                $message_type = 'success';
                break;
            case 'update_error':
                $message = '更新会员等级失败。';
                if (isset($_GET['error'])) {
                    $message .= ' ' . urldecode($_GET['error']);
                }
                $message_type = 'error';
                break;
            case 'add_success':
                $message = '会员等级添加成功。';
                $message_type = 'success';
                break;
            case 'add_error':
                $message = '添加会员等级失败。';
                if (isset($_GET['error'])) {
                    $message .= ' ' . urldecode($_GET['error']);
                }
                $message_type = 'error';
                break;
            case 'default_level_success':
                $message = '默认会员等级设置成功。';
                $message_type = 'success';
                break;
            case 'default_level_unchanged':
                $message = '默认会员等级保持不变，无需重复保存。';
                $message_type = 'info';
                break;
            case 'default_level_error':
                $message = '默认会员等级设置失败。';
                $message_type = 'error';
                break;
            case 'migration_success':
                $message = '会员等级priority迁移成功。';
                if (isset($_GET['migration_result'])) {
                    $message .= ' ' . urldecode($_GET['migration_result']);
                }
                $message_type = 'success';
                break;
            case 'migration_error':
                $message = '会员等级priority迁移失败。';
                if (isset($_GET['migration_result'])) {
                    $message .= ' ' . urldecode($_GET['migration_result']);
                }
                $message_type = 'error';
                break;
        }
    }
    
    // 获取所有会员等级（已排序）
    $levels = fd_member_get_sorted_member_levels();
    
    // 获取默认会员等级ID
    $default_level_id = fd_member_get_default_member_level_id();
    
    // 编辑模式
    $edit_mode = false;
    $edit_level = array();
    
    if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['level_id'])) {
        $level_id = intval($_GET['level_id']);
        $edit_level = fd_member_get_member_level($level_id);
        
        if ($edit_level) {
            $edit_mode = true;
        }
    }
    
    ?>
    <div class="wrap">
        <h1><?php echo esc_html('会员等级管理'); ?></h1>
        
        <?php if ($message): ?>
            <div class="notice notice-<?php
                if ($message_type === 'success') {
                    echo 'success';
                } elseif ($message_type === 'info') {
                    echo 'info';
                } else {
                    echo 'error';
                }
            ?> is-dismissible">
                <p><?php echo esc_html($message); ?></p>
            </div>
        <?php endif; ?>

        <?php
        // 显示验证警告
        if (isset($_GET['validation_warnings'])) {
            $warnings = json_decode(urldecode($_GET['validation_warnings']), true);
            if (!empty($warnings)) {
                echo '<div class="notice notice-warning is-dismissible">';
                echo '<p><strong>配置警告：</strong></p>';
                echo '<ul>';
                foreach ($warnings as $warning) {
                    echo '<li>' . esc_html($warning) . '</li>';
                }
                echo '</ul>';
                echo '</div>';
            }
        }

        // 显示验证建议
        if (isset($_GET['validation_suggestions'])) {
            $suggestions = json_decode(urldecode($_GET['validation_suggestions']), true);
            if (!empty($suggestions)) {
                echo '<div class="notice notice-info is-dismissible">';
                echo '<p><strong>配置建议：</strong></p>';
                echo '<ul>';
                foreach ($suggestions as $suggestion) {
                    echo '<li>' . esc_html($suggestion) . '</li>';
                }
                echo '</ul>';
                echo '</div>';
            }
        }
        ?>
        
        <!-- 会员等级配置指南 -->
        <div class="fd-member-config-guide-container">
            <div class="fd-config-guide-header" onclick="toggleConfigGuide()">
                <h2>
                    <span class="dashicons dashicons-info"></span>
                    会员等级配置指南
                    <span class="dashicons dashicons-arrow-down-alt2" id="guide-arrow"></span>
                </h2>
            </div>
            <div class="fd-config-guide-content" id="config-guide-content" style="display: none;">
                <div class="fd-guide-section">
                    <h3>🎯 配置原则</h3>
                    <p>为确保会员体系的合理性，请遵循以下原则：</p>
                    <ul>
                        <li><strong>优先级与价格正相关</strong>：优先级越高，价格应该越高</li>
                        <li><strong>优先级与权益正相关</strong>：优先级越高，享受的权益应该越多</li>
                        <li><strong>有效期设置合理</strong>：高等级会员可以设置更长的有效期作为额外权益</li>
                    </ul>
                </div>

                <div class="fd-guide-section">
                    <h3>📊 推荐配置模板</h3>
                    <table class="fd-guide-table">
                        <thead>
                            <tr>
                                <th>等级层次</th>
                                <th>优先级范围</th>
                                <th>价格建议</th>
                                <th>有效期建议</th>
                                <th>示例</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>钻石级</td>
                                <td>100+</td>
                                <td>500+ 元</td>
                                <td>1年或永久</td>
                                <td>钻石会员：优先级100，价格999元，1年</td>
                            </tr>
                            <tr>
                                <td>黄金级</td>
                                <td>80-99</td>
                                <td>200-500 元</td>
                                <td>6-12个月</td>
                                <td>黄金会员：优先级80，价格299元，6个月</td>
                            </tr>
                            <tr>
                                <td>白银级</td>
                                <td>50-79</td>
                                <td>50-200 元</td>
                                <td>3-6个月</td>
                                <td>白银会员：优先级50，价格99元，3个月</td>
                            </tr>
                            <tr>
                                <td>青铜级</td>
                                <td>20-49</td>
                                <td>10-50 元</td>
                                <td>1-3个月</td>
                                <td>青铜会员：优先级20，价格29元，1个月</td>
                            </tr>
                            <tr>
                                <td>基础级</td>
                                <td>0-19</td>
                                <td>免费或低价</td>
                                <td>永久或短期</td>
                                <td>普通会员：优先级0，免费，永久</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="fd-guide-section">
                    <h3>⚠️ 常见配置问题</h3>
                    <div class="fd-warning-box">
                        <p><strong>避免以下不合理配置：</strong></p>
                        <ul>
                            <li>❌ 高优先级但低价格（如：优先级80，价格10元）</li>
                            <li>❌ 低优先级但高价格（如：优先级10，价格500元）</li>
                            <li>❌ 高优先级但短有效期（如：优先级100，1天有效期）</li>
                            <li>❌ 相同优先级的多个等级</li>
                        </ul>
                    </div>
                </div>

                <div class="fd-guide-section">
                    <h3>💡 最佳实践建议</h3>
                    <ul>
                        <li>先规划整体会员体系，再设置具体参数</li>
                        <li>定期检查等级配置的合理性</li>
                        <li>考虑用户升级路径的连贯性</li>
                        <li>根据业务需求调整价格和有效期</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 默认会员等级设置 -->
        <div class="fd-member-default-level-container">
            <h2>默认会员等级设置</h2>
            <p>新注册用户将自动分配到默认会员等级。未指定等级的用户在前端也将显示为默认等级。</p>
            
            <form method="post" action="">
                <?php wp_nonce_field('fd_set_default_level'); ?>
                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="default_level_id">默认会员等级</label></th>
                        <td>
                            <select name="default_level_id" id="default_level_id">
                                <option value="0" <?php selected($default_level_id, 0); ?>>-- 不设置默认等级 --</option>
                                <?php foreach ($levels as $level): ?>
                                    <option value="<?php echo esc_attr($level['id']); ?>" <?php selected($default_level_id, $level['id']); ?>>
                                        <?php echo esc_html($level['name']); ?>
                                        <?php if (!empty($level['description'])): ?>
                                            (<?php echo esc_html($level['description']); ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="set_default_level" class="button button-primary" value="保存默认等级">
                </p>
            </form>
        </div>
        
        <div class="fd-member-levels-container">
            <div class="fd-member-levels-list">
                <h2>当前会员等级</h2>
                <?php if (empty($levels)): ?>
                    <p>目前没有定义任何会员等级。请添加第一个会员等级。</p>
                <?php else: ?>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名称</th>
                                <th>描述</th>
                                <th>价格</th>
                                <th>有效期</th>
                                <th>优先级</th>
                                <th>等级层次</th>
                                <th>配置状态</th>
                                <th>默认</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($levels as $level):
                                // 检查当前等级的配置合理性
                                $validation = fd_member_validate_level_config($level, $level['id']);
                            ?>
                                <tr>
                                    <td><?php echo esc_html($level['id']); ?></td>
                                    <td><?php echo esc_html($level['name']); ?></td>
                                    <td><?php echo esc_html($level['description']); ?></td>
                                    <td><?php echo isset($level['price']) ? esc_html(number_format($level['price'], 2)) . ' 元' : '0.00 元'; ?></td>
                                    <td>
                                        <?php 
                                        if (isset($level['duration']) && $level['duration'] > 0) {
                                            $unit_text = '';
                                            switch (isset($level['duration_unit']) ? $level['duration_unit'] : 'days') {
                                                case 'days':
                                                    $unit_text = '天';
                                                    break;
                                                case 'months':
                                                    $unit_text = '个月';
                                                    break;
                                                case 'years':
                                                    $unit_text = '年';
                                                    break;
                                                default:
                                                    $unit_text = '天';
                                            }
                                            echo esc_html($level['duration'] . ' ' . $unit_text);
                                        } else {
                                            echo '永久';
                                        }
                                        ?>
                                    </td>
                                    <td><?php echo esc_html(isset($level['priority']) ? $level['priority'] : 0); ?></td>
                                    <td>
                                        <span class="fd-level-tier-badge">
                                            <?php echo esc_html(fd_member_get_level_tier(isset($level['priority']) ? $level['priority'] : 0)); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (!empty($validation['warnings'])): ?>
                                            <span class="fd-config-status fd-config-warning" title="<?php echo esc_attr(implode('; ', $validation['warnings'])); ?>">
                                                <span class="dashicons dashicons-warning"></span> 需要注意
                                            </span>
                                        <?php elseif (!empty($validation['suggestions'])): ?>
                                            <span class="fd-config-status fd-config-suggestion" title="<?php echo esc_attr(implode('; ', $validation['suggestions'])); ?>">
                                                <span class="dashicons dashicons-info"></span> 可优化
                                            </span>
                                        <?php else: ?>
                                            <span class="fd-config-status fd-config-good">
                                                <span class="dashicons dashicons-yes"></span> 配置合理
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($default_level_id && $default_level_id == $level['id']): ?>
                                            <span class="dashicons dashicons-yes" style="color: green;"></span>
                                        <?php else: ?>
                                            <span class="dashicons dashicons-no" style="color: #ccc;"></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?php echo esc_url(add_query_arg(array('action' => 'edit', 'level_id' => $level['id']))); ?>" class="button button-small">编辑</a>
                                        <a href="<?php echo esc_url(wp_nonce_url(add_query_arg(array('action' => 'delete', 'level_id' => $level['id'])), 'fd_delete_level_' . $level['id'])); ?>" class="button button-small" onclick="return confirm('确定要删除该会员等级吗？这将移除所有分配了该等级的用户的等级信息。');">删除</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
            
            <div class="fd-member-level-form">
                <h2><?php echo $edit_mode ? '编辑会员等级' : '添加新会员等级'; ?></h2>
                <form method="post" action="">
                    <?php if ($edit_mode): ?>
                        <input type="hidden" name="level_id" value="<?php echo esc_attr($edit_level['id']); ?>">
                        <?php wp_nonce_field('fd_update_level'); ?>
                    <?php else: ?>
                        <?php wp_nonce_field('fd_add_level'); ?>
                    <?php endif; ?>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><label for="level_name">等级名称</label></th>
                            <td>
                                <input type="text" id="level_name" name="level_name" class="regular-text" value="<?php echo $edit_mode ? esc_attr($edit_level['name']) : ''; ?>" required>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="level_description">等级描述</label></th>
                            <td>
                                <textarea id="level_description" name="level_description" class="regular-text" rows="3"><?php echo $edit_mode ? esc_textarea($edit_level['description']) : ''; ?></textarea>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="level_price">等级价格</label></th>
                            <td>
                                <input type="number" id="level_price" name="level_price" class="regular-text" step="0.01" min="0" value="<?php echo $edit_mode ? esc_attr(isset($edit_level['price']) ? $edit_level['price'] : 0) : '0'; ?>"> 元
                                <p class="description">设置为0表示免费等级</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="level_priority">优先级</label></th>
                            <td>
                                <input type="number" id="level_priority" name="level_priority" class="small-text" value="<?php echo $edit_mode ? esc_attr(isset($edit_level['priority']) ? $edit_level['priority'] : 0) : '0'; ?>">
                                <p class="description">
                                    数字越大优先级越高，用于排序。每个等级的优先级必须唯一。<br>
                                    <strong>建议值：</strong> 钻石级(100+)、黄金级(80-99)、白银级(50-79)、青铜级(20-49)、基础级(0-19)
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="level_duration">会员有效期</label></th>
                            <td>
                                <input type="number" id="level_duration" name="level_duration" class="small-text" min="0" value="<?php echo $edit_mode ? esc_attr(isset($edit_level['duration']) ? $edit_level['duration'] : 0) : '0'; ?>">
                                <select name="level_duration_unit" id="level_duration_unit">
                                    <option value="days" <?php echo $edit_mode && isset($edit_level['duration_unit']) && $edit_level['duration_unit'] == 'days' ? 'selected' : ''; ?>>天</option>
                                    <option value="months" <?php echo $edit_mode && isset($edit_level['duration_unit']) && $edit_level['duration_unit'] == 'months' ? 'selected' : ''; ?>>月</option>
                                    <option value="years" <?php echo $edit_mode && isset($edit_level['duration_unit']) && $edit_level['duration_unit'] == 'years' ? 'selected' : ''; ?>>年</option>
                                </select>
                                <p class="description">设置为0表示永久有效</p>
                            </td>
                        </tr>
                    </table>
                    
                    <p class="submit">
                        <?php if ($edit_mode): ?>
                            <input type="submit" name="update_level" class="button button-primary" value="更新会员等级">
                            <a href="<?php echo esc_url(remove_query_arg(array('action', 'level_id'))); ?>" class="button">取消</a>
                        <?php else: ?>
                            <input type="submit" name="add_level" class="button button-primary" value="添加会员等级">
                        <?php endif; ?>
                    </p>
                </form>
            </div>
        </div>
    </div>
    
    <style>
    /* 配置指南样式 */
    .fd-member-config-guide-container {
        background: #fff;
        border: 1px solid #ccd0d4;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
        margin-bottom: 20px;
    }
    .fd-config-guide-header {
        padding: 15px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
        background: #f9f9f9;
    }
    .fd-config-guide-header:hover {
        background: #f0f0f0;
    }
    .fd-config-guide-header h2 {
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .fd-config-guide-content {
        padding: 20px;
    }
    .fd-guide-section {
        margin-bottom: 25px;
    }
    .fd-guide-section h3 {
        margin-top: 0;
        margin-bottom: 10px;
        color: #23282d;
    }
    .fd-guide-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
    }
    .fd-guide-table th,
    .fd-guide-table td {
        padding: 8px 12px;
        border: 1px solid #ddd;
        text-align: left;
    }
    .fd-guide-table th {
        background: #f9f9f9;
        font-weight: 600;
    }
    .fd-warning-box {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 15px;
        margin-top: 10px;
    }
    .fd-warning-box ul {
        margin: 10px 0 0 20px;
    }

    /* 默认等级容器样式 */
    .fd-member-default-level-container {
        background: #fff;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid #ccd0d4;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
    }

    /* 等级列表容器样式 */
    .fd-member-levels-container {
        display: flex;
        flex-wrap: wrap;
        margin-top: 20px;
    }
    .fd-member-levels-list {
        flex: 2;
        margin-right: 30px;
        min-width: 500px;
    }
    .fd-member-level-form {
        flex: 1;
        min-width: 300px;
        background: #fff;
        padding: 15px;
        border: 1px solid #ccd0d4;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
    }

    /* 等级层次徽章样式 */
    .fd-level-tier-badge {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
        background: #f0f0f1;
        color: #50575e;
    }

    /* 配置状态样式 */
    .fd-config-status {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        padding: 2px 6px;
        border-radius: 3px;
    }
    .fd-config-good {
        color: #0073aa;
        background: #e5f5fa;
    }
    .fd-config-warning {
        color: #b32d2e;
        background: #fbeaea;
    }
    .fd-config-suggestion {
        color: #826200;
        background: #fff8e1;
    }

    @media (max-width: 960px) {
        .fd-member-levels-container {
            flex-direction: column;
        }
        .fd-member-levels-list {
            margin-right: 0;
            margin-bottom: 30px;
        }
    }
    </style>

    <script>
    function toggleConfigGuide() {
        var content = document.getElementById('config-guide-content');
        var arrow = document.getElementById('guide-arrow');

        if (content.style.display === 'none') {
            content.style.display = 'block';
            arrow.className = 'dashicons dashicons-arrow-up-alt2';
        } else {
            content.style.display = 'none';
            arrow.className = 'dashicons dashicons-arrow-down-alt2';
        }
    }
    </script>
    <?php
}