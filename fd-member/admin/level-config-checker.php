<?php
/**
 * 会员等级配置检查工具
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 会员等级配置检查页面
 */
function fd_member_level_config_checker_page() {
    // 检查用户权限
    if (!current_user_can('manage_options')) {
        return;
    }
    
    $levels = fd_member_get_sorted_member_levels();
    $overall_issues = array();
    $level_validations = array();
    
    // 检查每个等级的配置
    foreach ($levels as $level) {
        $validation = fd_member_validate_level_config($level, $level['id']);
        $level_validations[$level['id']] = $validation;
        
        if (!empty($validation['warnings'])) {
            $overall_issues = array_merge($overall_issues, $validation['warnings']);
        }
    }
    
    ?>
    <div class="wrap">
        <h1>会员等级配置检查</h1>
        <p>此工具帮助您检查所有会员等级配置的合理性，识别潜在问题并提供优化建议。</p>
        
        <?php if (empty($overall_issues)): ?>
            <div class="notice notice-success">
                <p><strong>✅ 配置检查通过</strong> - 所有会员等级的配置都是合理的！</p>
            </div>
        <?php else: ?>
            <div class="notice notice-warning">
                <p><strong>⚠️ 发现 <?php echo count($overall_issues); ?> 个配置问题</strong> - 建议您查看下方详细信息并进行调整。</p>
            </div>
        <?php endif; ?>
        
        <!-- 配置概览 -->
        <div class="fd-config-overview">
            <h2>配置概览</h2>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>等级名称</th>
                        <th>优先级</th>
                        <th>等级层次</th>
                        <th>价格</th>
                        <th>有效期</th>
                        <th>配置状态</th>
                        <th>问题数量</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($levels as $level): 
                        $validation = $level_validations[$level['id']];
                        $issue_count = count($validation['warnings']) + count($validation['suggestions']);
                    ?>
                        <tr>
                            <td><strong><?php echo esc_html($level['name']); ?></strong></td>
                            <td><?php echo esc_html(isset($level['priority']) ? $level['priority'] : 0); ?></td>
                            <td>
                                <span class="fd-level-tier-badge">
                                    <?php echo esc_html(fd_member_get_level_tier(isset($level['priority']) ? $level['priority'] : 0)); ?>
                                </span>
                            </td>
                            <td><?php echo isset($level['price']) ? esc_html(number_format($level['price'], 2)) . ' 元' : '免费'; ?></td>
                            <td>
                                <?php 
                                if (isset($level['duration']) && $level['duration'] > 0) {
                                    $unit_text = '';
                                    switch (isset($level['duration_unit']) ? $level['duration_unit'] : 'days') {
                                        case 'days': $unit_text = '天'; break;
                                        case 'months': $unit_text = '个月'; break;
                                        case 'years': $unit_text = '年'; break;
                                        default: $unit_text = '天';
                                    }
                                    echo esc_html($level['duration'] . ' ' . $unit_text);
                                } else {
                                    echo '永久';
                                }
                                ?>
                            </td>
                            <td>
                                <?php if (!empty($validation['warnings'])): ?>
                                    <span class="fd-config-status fd-config-warning">
                                        <span class="dashicons dashicons-warning"></span> 需要注意
                                    </span>
                                <?php elseif (!empty($validation['suggestions'])): ?>
                                    <span class="fd-config-status fd-config-suggestion">
                                        <span class="dashicons dashicons-info"></span> 可优化
                                    </span>
                                <?php else: ?>
                                    <span class="fd-config-status fd-config-good">
                                        <span class="dashicons dashicons-yes"></span> 配置合理
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($issue_count > 0): ?>
                                    <span class="fd-issue-count"><?php echo $issue_count; ?> 个问题</span>
                                <?php else: ?>
                                    <span class="fd-no-issues">无问题</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- 详细问题报告 -->
        <?php if (!empty($overall_issues)): ?>
            <div class="fd-detailed-report">
                <h2>详细问题报告</h2>
                <?php foreach ($levels as $level): 
                    $validation = $level_validations[$level['id']];
                    if (empty($validation['warnings']) && empty($validation['suggestions'])) {
                        continue;
                    }
                ?>
                    <div class="fd-level-report">
                        <h3><?php echo esc_html($level['name']); ?> (ID: <?php echo $level['id']; ?>)</h3>
                        
                        <?php if (!empty($validation['warnings'])): ?>
                            <div class="fd-warnings">
                                <h4><span class="dashicons dashicons-warning"></span> 配置警告</h4>
                                <ul>
                                    <?php foreach ($validation['warnings'] as $warning): ?>
                                        <li><?php echo esc_html($warning); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($validation['suggestions'])): ?>
                            <div class="fd-suggestions">
                                <h4><span class="dashicons dashicons-info"></span> 优化建议</h4>
                                <ul>
                                    <?php foreach ($validation['suggestions'] as $suggestion): ?>
                                        <li><?php echo esc_html($suggestion); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <p>
                            <a href="<?php echo esc_url(add_query_arg(array('page' => 'fd-member-levels', 'action' => 'edit', 'level_id' => $level['id']), admin_url('admin.php'))); ?>" class="button button-primary">
                                编辑此等级
                            </a>
                        </p>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <!-- 优化建议 -->
        <div class="fd-optimization-tips">
            <h2>通用优化建议</h2>
            <div class="fd-tips-grid">
                <div class="fd-tip-card">
                    <h3>🎯 价格策略</h3>
                    <p>确保高优先级等级的价格高于低优先级等级，形成合理的价格梯度。</p>
                </div>
                <div class="fd-tip-card">
                    <h3>⏰ 有效期设置</h3>
                    <p>高等级会员可以享受更长的有效期作为额外权益，但要避免过短的有效期。</p>
                </div>
                <div class="fd-tip-card">
                    <h3>📊 等级层次</h3>
                    <p>建议设置3-5个等级，层次分明，避免等级过多导致用户选择困难。</p>
                </div>
                <div class="fd-tip-card">
                    <h3>🔄 升级路径</h3>
                    <p>确保用户有清晰的升级路径，每个等级都有明确的价值主张。</p>
                </div>
            </div>
        </div>
        
        <p>
            <a href="<?php echo esc_url(admin_url('admin.php?page=fd-member-levels')); ?>" class="button button-primary">
                返回会员等级管理
            </a>
        </p>
    </div>
    
    <style>
    .fd-config-overview {
        background: #fff;
        padding: 20px;
        margin: 20px 0;
        border: 1px solid #ccd0d4;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
    }
    
    .fd-detailed-report {
        margin: 20px 0;
    }
    
    .fd-level-report {
        background: #fff;
        padding: 20px;
        margin-bottom: 20px;
        border: 1px solid #ccd0d4;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
    }
    
    .fd-level-report h3 {
        margin-top: 0;
        color: #23282d;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }
    
    .fd-warnings, .fd-suggestions {
        margin: 15px 0;
    }
    
    .fd-warnings h4 {
        color: #b32d2e;
        margin-bottom: 10px;
    }
    
    .fd-suggestions h4 {
        color: #826200;
        margin-bottom: 10px;
    }
    
    .fd-warnings ul, .fd-suggestions ul {
        margin-left: 20px;
    }
    
    .fd-optimization-tips {
        margin: 30px 0;
    }
    
    .fd-tips-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }
    
    .fd-tip-card {
        background: #fff;
        padding: 20px;
        border: 1px solid #ccd0d4;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
        border-radius: 4px;
    }
    
    .fd-tip-card h3 {
        margin-top: 0;
        margin-bottom: 10px;
        color: #23282d;
    }
    
    .fd-issue-count {
        color: #b32d2e;
        font-weight: 600;
    }
    
    .fd-no-issues {
        color: #0073aa;
    }
    </style>
    <?php
}
