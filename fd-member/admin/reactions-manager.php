<?php
/**
 * 通用交互管理界面
 *
 * 提供一个统一的后台列表，用于查看和管理所有类型的交互数据。
 *
 * @package FD\Member
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * 添加通用交互管理菜单
 */
function fd_member_reactions_add_admin_menu() {
    add_submenu_page(
        'fd-member',
        __( '交互管理', 'fd-member' ),
        __( '交互管理', 'fd-member' ),
        'manage_options',
        'fd-reactions-manager',
        'fd_member_reactions_admin_page'
    );
}
add_action( 'admin_menu', 'fd_member_reactions_add_admin_menu', 20 );

/**
 * 渲染通用交互管理页面
 */
function fd_member_reactions_admin_page() {
    $current_type = isset( $_GET['type'] ) ? sanitize_key( $_GET['type'] ) : 'like';
    $page         = isset( $_GET['paged'] ) ? absint( $_GET['paged'] ) : 1;
    $per_page     = 20;

    $reactions_data = fd_member_reactions_get_admin_data( $current_type, $page, $per_page );
    $reactions      = $reactions_data['reactions'];
    $total          = $reactions_data['total'];
    $total_pages    = ceil( $total / $per_page );
    ?>
    <div class="wrap">
        <h1 class="wp-heading-inline"><?php _e( '交互管理', 'fd-member' ); ?></h1>
        <hr class="wp-header-end">

        <ul class="subsubsub">
            <li><a href="?page=fd-reactions-manager&type=like" class="<?php echo 'like' === $current_type ? 'current' : ''; ?>">点赞</a> |</li>
            <li><a href="?page=fd-reactions-manager&type=bookmark" class="<?php echo 'bookmark' === $current_type ? 'current' : ''; ?>">收藏</a> |</li>
            <li><a href="?page=fd-reactions-manager&type=recommend" class="<?php echo 'recommend' === $current_type ? 'current' : ''; ?>">推荐</a></li>
        </ul>

        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>用户</th>
                    <th>文章</th>
                    <th>操作</th>
                    <th>创建时间</th>
                    <th>更新时间</th>
                </tr>
            </thead>
            <tbody>
                <?php if ( ! empty( $reactions ) ) : ?>
                    <?php foreach ( $reactions as $reaction ) : ?>
                        <tr>
                            <td><?php echo esc_html( $reaction->id ); ?></td>
                            <td>
                                <?php
                                $user = get_user_by( 'id', $reaction->user_id );
                                if ( $user ) {
                                    echo esc_html( $user->display_name ) . ' (ID:' . absint( $user->ID ) . ')';
                                } else {
                                    echo '—';
                                }
                                ?>
                            </td>
                            <td>
                                <?php
                                $post = get_post( $reaction->post_id );
                                if ( $post ) {
                                    $edit_link = get_edit_post_link( $post->ID );
                                    printf( '<a href="%s" target="_blank">%s</a>', esc_url( $edit_link ), esc_html( get_the_title( $post ) ) );
                                } else {
                                    echo '—';
                                }
                                ?>
                            </td>
                            <td>
                                <?php
                                // 根据类型+状态输出动作中文
                                $actions_map = [
                                    'like'      => [ 1 => '点赞',   0 => '取消点赞' ],
                                    'bookmark'  => [ 1 => '收藏',   0 => '取消收藏' ],
                                    'recommend' => [ 1 => '推荐',   0 => '取消推荐' ],
                                ];
                                $type_key = strtolower( $reaction->type );
                                echo isset( $actions_map[ $type_key ][ $reaction->status ] )
                                    ? esc_html( $actions_map[ $type_key ][ $reaction->status ] )
                                    : esc_html( $reaction->status );
                                ?>
                            </td>
                            <td><?php echo esc_html( $reaction->created_at ); ?></td>
                            <td><?php echo esc_html( $reaction->updated_at ); ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php else : ?>
                    <tr><td colspan="5">暂无数据</td></tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    <?php
}

/**
 * 获取交互管理数据
 */
function fd_member_reactions_get_admin_data( $type, $page, $per_page ) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'reactions';
    $offset     = ( $page - 1 ) * $per_page;

    $total = $wpdb->get_var( $wpdb->prepare( "SELECT COUNT(*) FROM {$table_name} WHERE type = %s", $type ) );
    $reactions = $wpdb->get_results( $wpdb->prepare(
        "SELECT * FROM {$table_name} WHERE type = %s ORDER BY id DESC LIMIT %d OFFSET %d",
        $type, $per_page, $offset
    ) );

    return [ 'reactions' => $reactions, 'total' => $total ];
} 