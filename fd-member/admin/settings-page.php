<?php
/**
 * 手机验证 - 后台设置页面UI
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 添加用户设置子菜单
 */
function fd_member_add_user_settings_submenu() {
    $hook = add_submenu_page(
        'fd-member', // 父菜单别名，使用已经定义的会员管理主菜单
        '会员设置', // 页面标题
        '会员设置', // 菜单标题
        'manage_options', // 权限
        'fd-member-settings', // 菜单别名
        'fd_member_user_settings_page' // 回调函数
    );
    
    // 在这个页面加载时，确保加载媒体上传脚本，并加载我们的自定义脚本
    add_action("admin_print_scripts-{$hook}", 'fd_member_phone_admin_enqueue_scripts');
}
add_action('admin_menu', 'fd_member_add_user_settings_submenu', 30);

/**
 * 加载此页面所需的脚本
 */
function fd_member_phone_admin_enqueue_scripts() {
    // WordPress自带的媒体上传脚本
    wp_enqueue_media();

    // 我们的自定义JS
    $script_path = FD_MEMBER_URI . 'includes/verification/phone-verification/admin/assets/js/phone-auth-admin.js';
    $script_asset_path = FD_MEMBER_DIR . 'includes/verification/phone-verification/admin/assets/js/phone-auth-admin.js';
    $version = file_exists($script_asset_path) ? filemtime($script_asset_path) : '1.0';
    
    wp_enqueue_script(
        'fd-phone-auth-admin-script',
        $script_path,
        array('jquery'),
        $version,
        true
    );

    // 通过 wp_localize_script 向JS传递PHP变量
    wp_localize_script(
        'fd-phone-auth-admin-script',
        'fdPhoneAuthAdmin', // JS中的对象名
        array(
            'restUrl' => esc_url_raw(rest_url('fd-member/v1/')),
            'nonce' => wp_create_nonce('wp_rest')
        )
    );
}


/**
 * 用户设置页面回调
 */
function fd_member_user_settings_page() {
    // 检查用户权限
    if (!current_user_can('manage_options')) {
        return;
    }

    // 获取当前活动的标签页
    $active_tab = isset($_GET['tab']) ? sanitize_key($_GET['tab']) : 'phone_verification';
    
    ?>
    <div class="wrap">
        <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
        
        <?php 
        // 显示所有设置错误/成功消息
        settings_errors(); 
        ?>
        
        <h2 class="nav-tab-wrapper">
            <a href="?page=fd-member-settings&tab=phone_verification" class="nav-tab <?php echo $active_tab == 'phone_verification' ? 'nav-tab-active' : ''; ?>">手机验证</a>
            <a href="?page=fd-member-settings&tab=avatar" class="nav-tab <?php echo $active_tab == 'avatar' ? 'nav-tab-active' : ''; ?>">头像设置</a>
            <a href="?page=fd-member-settings&tab=promotion" class="nav-tab <?php echo $active_tab == 'promotion' ? 'nav-tab-active' : ''; ?>">晋升体系</a>
            <a href="?page=fd-member-settings&tab=paywall" class="nav-tab <?php echo $active_tab == 'paywall' ? 'nav-tab-active' : ''; ?>">付费墙链接</a>
        </h2>
        
        <form action="options.php" method="post">
            <?php
            if ($active_tab == 'phone_verification') {
                settings_fields('fd_member_options_group');
                do_settings_sections('fd-member-phone-auth-settings');
            } elseif ($active_tab == 'avatar') {
                settings_fields('fd_member_avatar_options_group');
                do_settings_sections('fd-member-avatar-settings');
            } elseif ($active_tab == 'promotion') {
                settings_fields('fd_member_promotion_options_group');
                do_settings_sections('fd-member-promotion-settings');
            } elseif ($active_tab == 'paywall') {
                settings_fields('fd_member_paywall_group');
                do_settings_sections('fd-member-paywall-settings');
            }
            submit_button('保存设置');
            ?>
        </form>

        <?php
        // 仅在手机验证标签页显示测试区域
        if ($active_tab == 'phone_verification') :
            // 检查是否启用了手机验证功能
            $options = get_option('fd_member_options', array());
            $phone_enabled = isset($options['enable_phone']) && $options['enable_phone'] === '1';
            
            if ($phone_enabled) :
                // 测试区域
                ?>
                <div class="fd-settings-section" style="margin-top: 20px; border-top: 1px solid #ddd; padding-top: 20px;">
                    <h2>手机验证测试</h2>
                    <p>使用以下表单测试手机验证码功能。</p>
                    
                    <div id="fd-phone-test-area">
                        <div class="fd-test-field" style="margin-bottom: 15px;">
                            <label for="fd-test-phone" style="display: block; margin-bottom: 5px; font-weight: bold;">手机号码</label>
                            <input type="text" id="fd-test-phone" class="regular-text" style="margin-right: 10px;" placeholder="请输入手机号" pattern="^1[3-9]\d{9}$" />
                            <button type="button" id="fd-send-code-btn" class="button button-secondary">发送验证码</button>
                        </div>
                        
                        <div class="fd-test-field" style="margin-bottom: 15px; display: none;" id="fd-code-field">
                            <label for="fd-test-code" style="display: block; margin-bottom: 5px; font-weight: bold;">验证码</label>
                            <input type="text" id="fd-test-code" class="regular-text" style="margin-right: 10px;" placeholder="请输入6位验证码" maxlength="6" />
                            <button type="button" id="fd-verify-code-btn" class="button button-primary">验证</button>
                        </div>
                        
                        <div id="fd-test-result" style="margin-top: 15px; padding: 10px; border-left: 4px solid #ccc; background: #f8f8f8; display: none;"></div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
    <?php
} 

/**
 * 注册付费墙链接设置
 */
function fd_member_paywall_settings_init() {
    // Register settings
    register_setting('fd_member_paywall_group', 'fd_member_login_url');
    register_setting('fd_member_paywall_group', 'fd_member_register_url');
    register_setting('fd_member_paywall_group', 'fd_member_upgrade_url');
    register_setting('fd_member_paywall_group', 'fd_member_paywall_variant');

    add_settings_section(
        'fd_member_paywall_section',
        '付费墙链接与样式',
        function() { echo '<p>自定义登录、注册、升级会员页面的前端链接，并选择全局付费墙UI样式。</p>'; },
        'fd-member-paywall-settings'
    );

    add_settings_field(
        'fd_member_login_url',
        '登录页面 URL',
        function() {
            $val = esc_url(get_option('fd_member_login_url', ''));
            echo '<input type="text" class="regular-text" name="fd_member_login_url" value="' . $val . '" placeholder="https://www.futuredecade.com/auth/login" />';
        },
        'fd-member-paywall-settings',
        'fd_member_paywall_section'
    );

    add_settings_field(
        'fd_member_register_url',
        '注册页面 URL',
        function() {
            $val = esc_url(get_option('fd_member_register_url', ''));
            echo '<input type="text" class="regular-text" name="fd_member_register_url" value="' . $val . '" placeholder="https://www.futuredecade.com/auth/register" />';
        },
        'fd-member-paywall-settings',
        'fd_member_paywall_section'
    );

    add_settings_field(
        'fd_member_upgrade_url',
        '升级会员页面 URL',
        function() {
            $val = esc_url(get_option('fd_member_upgrade_url', ''));
            echo '<input type="text" class="regular-text" name="fd_member_upgrade_url" value="' . $val . '" placeholder="https://www.futuredecade.com/checkout" />';
        },
        'fd-member-paywall-settings',
        'fd_member_paywall_section'
    );

    add_settings_field(
        'fd_member_paywall_variant',
        '付费墙UI样式',
        function() {
            $val = get_option('fd_member_paywall_variant', 'default');
            ?>
            <select name="fd_member_paywall_variant">
                <option value="default" <?php selected($val, 'default'); ?>>
                    默认样式 (Default)
                </option>
                <option value="compact" <?php selected($val, 'compact'); ?>>
                    紧凑样式 (Compact)
                </option>
            </select>
            <p class="description">为全站的付费墙选择一个统一的显示风格。</p>
            <?php
        },
        'fd-member-paywall-settings',
        'fd_member_paywall_section'
    );
}
add_action('admin_init', 'fd_member_paywall_settings_init'); 