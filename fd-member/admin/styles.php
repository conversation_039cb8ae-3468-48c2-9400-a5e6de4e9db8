<?php
/**
 * 加载管理界面样式和脚本
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 加载管理界面样式和内联CSS
 */
function fd_member_admin_styles($hook) {
    // 只在会员管理插件页面加载样式
    if (strpos($hook, 'fd-member') === false) {
        return;
    }
    
    // 加载外部CSS文件
    wp_enqueue_style(
        'fd-member-admin-style',
        FD_MEMBER_URI . 'admin/assets/css/admin.css',
        array(),
        FD_MEMBER_VERSION
    );

    // 为仪表盘页面添加内联样式
    $current_screen = get_current_screen();
    if ($current_screen->id === 'toplevel_page_fd-member') {
        $custom_css = "
            .fd-member-dashboard #dashboard-widgets-wrap {
                margin-top: 20px;
            }
            .fd-member-dashboard .postbox-container {
                min-width: 280px !important;
            }
            .fd-stats-list, .fd-quick-links {
                margin: 0;
                padding: 0;
                list-style: none;
            }
            .fd-stats-list li {
                display: flex;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #eee;
            }
            .fd-stats-list li:last-child {
                border-bottom: none;
            }
            .fd-stats-list .dashicons {
                margin-right: 10px;
                color: #72777c;
            }
            .fd-stats-list strong {
                flex-grow: 1;
            }
            .fd-quick-links li {
                display: inline-block;
                margin-right: 10px;
                margin-bottom: 10px;
            }
        ";
        wp_add_inline_style('fd-member-admin-style', $custom_css);
    }
}
add_action('admin_enqueue_scripts', 'fd_member_admin_styles'); 