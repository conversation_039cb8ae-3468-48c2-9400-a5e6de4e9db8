<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员内容访问控制 - 后端实现文档</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; line-height: 1.6; color: #333; max-width: 960px; margin: 20px auto; padding: 0 20px; }
        h1, h2, h3 { color: #222; border-bottom: 1px solid #eaecef; padding-bottom: 0.3em; }
        h1 { font-size: 2em; }
        h2 { font-size: 1.5em; }
        h3 { font-size: 1.25em; }
        code { font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace; background-color: #f6f8fa; padding: 0.2em 0.4em; margin: 0; font-size: 85%; border-radius: 3px; }
        pre { background-color: #f6f8fa; padding: 16px; overflow: auto; border-radius: 6px; }
        pre code { padding: 0; margin: 0; font-size: 100%; background-color: transparent; }
        ul, ol { padding-left: 2em; }
        li { margin-bottom: 0.5em; }
        strong { font-weight: 600; }
        .file-path { color: #0366d6; font-weight: 600; }
    </style>
</head>
<body>

    <h1>会员内容访问控制 - 后端实现文档</h1>
    <p>本文档详细说明了在 <code>fd-member</code> 插件中实现的会员内容访问控制功能的后端架构和核心逻辑。</p>

    <h2>1. 目标</h2>
    <p>允许网站管理员为单篇文章设置最低访问权限，只有达到指定会员等级的用户才能查看完整内容，从而实现内容的精细化运营。</p>

    <h2>2. 文件结构</h2>
    <p>为了保持代码的模块化和整洁，所有相关功能都被组织在新的目录中：</p>
    <ul>
        <li><code class="file-path">/includes/content-access-control/</code>
            <ul>
                <li><strong><code>cac-core.php</code></strong>: 存放功能的核心逻辑，包括权限判断函数和前端内容过滤钩子。此文件在任何情况下都会加载。</li>
                <li><strong><code>cac-editor.php</code></strong>: 存放与文章编辑器（经典编辑器和古腾堡编辑器）相关的所有代码，包括 Meta Box 的注册、Gutenberg 侧边栏的渲染、以及编辑器脚本的加载。此文件仅在后台（<code>is_admin()</code> 为 true）时加载，以优化性能。</li>
                <li><strong><code>cac-graphql.php</code></strong>: 存放所有与 GraphQL API 相关的功能，包括字段注册和内容解析器覆写。</li>
            </ul>
        </li>
        <li><code class="file-path">/assets/js/editor.js</code>: 用于渲染古腾堡编辑器侧边栏设置面板的 React 组件。</li>
        <li><code class="file-path">/lang/fd-member-zh_CN.po</code>: 包含所有新增 UI 文本的中文翻译文件。</li>
    </ul>

    <h2>3. 数据模型</h2>
    <p>文章的访问权限通过一个单独的文章元数据（Post Meta）进行存储。</p>
    <ul>
        <li><strong>Meta 键名:</strong> <code>_fd_required_member_level</code></li>
        <li><strong>值类型:</strong> 整数 (Integer)</li>
        <li><strong>值的含义:</strong>
            <ul>
                <li><code>0</code>: 公开访问，所有人可见（默认值）。</li>
                <li><code>-1</code>: 仅限登录用户访问。</li>
                <li><code>> 0</code>: 具体的会员等级 ID。只有用户会员等级的<strong>优先级 (priority)</strong> 大于或等于该等级的优先级时，才能访问。</li>
            </ul>
        </li>
    </ul>

    <h2>4. 核心逻辑实现</h2>
    <h3>4.1. 权限判断函数</h3>
    <p>所有权限检查都通过一个中心函数完成，以确保逻辑统一。</p>
    <pre><code>function fd_member_user_can_view_post($post_id = null)</code></pre>
    <p>该函数执行以下检查：</p>
    <ol>
        <li>获取文章所需的会员等级 ID (<code>_fd_required_member_level</code>)。</li>
        <li>如果等级为 0 (公开)，直接返回 <code>true</code>。</li>
        <li>获取当前用户信息。如果文章需要特定等级但用户未登录，返回 <code>false</code>。</li>
        <li>如果等级为 -1 (仅登录)，检查用户是否登录即可。</li>
        <li>获取用户当前会员等级的优先级和文章所需等级的优先级。</li>
        <li>比较两个优先级，如果用户优先级 <strong>大于或等于</strong> 所需优先级，则返回 <code>true</code>，否则返回 <code>false</code>。</li>
    </ol>

    <h3>4.2. 内容安全</h3>
    <p>为了从根本上阻止未授权用户看到受限内容，我们在服务器端通过 GraphQL 解析器和 <code>the_content</code> 过滤器来控制内容输出。</p>
    
    <h4>4.2.1. GraphQL API</h4>
    <p>这是主要的保护途径，因为我们的前端应用 (<code>fd-frontend</code>) 完全通过 GraphQL 获取数据。</p>
    <ul>
        <li>在 <code>register_graphql_field</code> 注册 <code>content</code> 字段时，我们提供一个自定义的解析器 (<code>resolve</code>) 函数。</li>
        <li>此函数内部会调用 <code>fd_member_user_can_view_post()</code> 进行权限检查。</li>
        <li>如果用户有权限，解析器返回完整的 <code>post_content</code>。</li>
        <li>如果用户无权限，解析器返回文章的摘要 (<code>post_excerpt</code>)，如果摘要为空，则返回 <code>null</code>。</li>
    </ul>
    <p><strong>安全性说明:</strong> 这种后端机制确保了受限的完整内容绝不会通过 API 发送到无权限用户的浏览器，从而杜绝了通过前端手段绕过限制的可能。</p>

    <h4>4.2.2. 传统主题 (the_content 过滤器)</h4>
    <p>为了兼容传统的 WordPress 主题，我们依然保留了 <code>the_content</code> 过滤器作为备用方案。</p>
    <pre><code>add_filter('the_content', 'fd_member_filter_the_content', 10);</code></pre>
    <p>此过滤器函数的工作流程如下：</p>
    <ol>
        <li>在非后台、非 Feed、且为详情页（<code>is_singular()</code>）的页面触发。</li>
        <li>调用 <code>fd_member_user_can_view_post()</code> 进行权限检查。</li>
        <li>如果用户有权限，返回原始内容。</li>
        <li>如果用户无权限，则返回文章摘要，并在其后附加一个提示框，引导用户登录或升级会员。</li>
    </ol>

    <h2>5. 编辑器集成</h2>
    <h3>5.1. 经典编辑器</h3>
    <p>通过标准的 <code>add_meta_box()</code> 函数，在文章编辑页的侧边栏添加了一个"内容访问权限"的设置框。该设置框是一个下拉菜单，包含了所有可用的访问级别。</p>

    <h3>5.2. 区块编辑器 (Gutenberg)</h3>
    <p>古腾堡编辑器的集成更为现代化：</p>
    <ol>
        <li><strong>REST API 注册:</strong> 首先通过 <code>register_post_meta()</code> 将 <code>_fd_required_member_level</code> 字段注册到 REST API，使其能在编辑器环境中被读写。</li>
        <li><strong>React 组件:</strong> <code class="file-path">/assets/js/editor.js</code> 文件定义了一个 React 组件，它使用 <code>wp.editPost.PluginDocumentSettingPanel</code> 将设置面板无缝嵌入到古腾堡的"文档"侧边栏中。</li>
        <li><strong>数据通信:</strong>
            <ul>
                <li>PHP 通过 <code>wp_enqueue_script()</code> 加载 <code>editor.js</code>。</li>
                <li>使用 <code>wp_localize_script()</code> 将所有会员等级的列表（ID 和名称）从后端传递给 JavaScript。</li>
                <li>使用 <code>wp_set_script_translations()</code> 为 JavaScript 组件加载翻译，实现国际化。</li>
            </ul>
        </li>
    </ol>

    <h3>5.3. 文章列表批量操作</h3>
    <p>为了提升内容管理效率，我们在文章和自定义文章类型的列表页面中，为"批量操作"下拉菜单添加了快速设置访问权限的功能。</p>
    <ul>
        <li><strong>功能:</strong> 管理员可以勾选多篇文章，然后从"批量操作"菜单中选择一个会员等级（或设为公开），一键更新所有选中文章的访问权限。</li>
        <li><strong>实现挂钩 (Hooks):</strong>
            <ul>
                <li><code>bulk_actions-edit-{$post_type}</code>: 用于动态地将所有会员等级作为新选项添加到下拉菜单中。</li>
                <li><code>handle_bulk_actions-edit-{$post_type}</code>: 用于处理批量更新请求，接收选中的文章 ID 并更新其 <code>_fd_required_member_level</code> 元数据。</li>
            </ul>
        </li>
        <li><strong>用户反馈:</strong> 操作完成后，页面顶部会显示一个通知，告知管理员有多少篇文章的权限被成功修改。</li>
    </ul>

    <h2>6. 自定义文章类型（CPT）支持</h2>
    <p>新版实现已对 <strong>所有</strong> 公开且在后台可编辑的自定义文章类型自动生效，无需额外配置。</p>
    <h3>6.1. 工作机制</h3>
    <ul>
        <li><strong>动态类型列表:</strong> 通过 <code>fd_member_get_restricted_post_types()</code> 在运行时搜集满足 <code>public=true</code> 且 <code>show_ui=true</code> 的 <code>post_type</code>，并排除 <code>attachment / revision / nav_menu_item</code> 等系统类型。</li>
        <li><strong>核心函数:</strong> 权限判断与 <code>the_content</code> 过滤逻辑改为调用该动态列表，而不再硬编码为 <code>post</code>。</li>
        <li><strong>后台编辑器:</strong> <code>register_post_meta()</code>、经典编辑器 MetaBox 以及 Gutenberg 侧边栏面板均在循环中为每个受保护的 CPT 注入，管理员在任何内容类型下都可设置访问权限。</li>
        <li><strong>GraphQL:</strong> 在 <code>graphql_register_types</code> 钩子内遍历所有 <code>show_in_graphql=true</code> 的类型，为每个类型统一注册 <code>requiredMemberLevel</code>、<code>isMembersOnly</code> 字段，并覆写 <code>content</code> 解析器。</li>
        <li><strong>前端脚本:</strong> 移除了限定 <code>postType !== 'post'</code> 的早期返回，确保侧边栏 UI 在所有 CPT 中显示。</li>
    </ul>
    <h3>6.2. 黑名单 / 过滤器</h3>
    <p>若需排除某些特定 <code>post_type</code>，开发者可在主题或站点插件中使用以下钩子：</p>
    <pre><code>add_filter( 'fd_member_restricted_post_types', function( $types ) {
    return array_diff( $types, [ 'your_cpt_slug' ] );
} );</code></pre>
    <h3>6.3. 测试要点</h3>
    <ol>
        <li>在后台创建 / 编辑一个自定义内容条目，确认侧边栏出现"内容访问权限"下拉，并可保存。</li>
        <li>以游客和具有不同会员等级的用户，通过前端或 GraphQL 分别读取该内容，验证访问控制逻辑与普通文章一致。</li>
    </ol>

    <h2>7. 高级注意事项和未来优化</h2>
    <p>为了确保系统的健壮性、安全性和性能，在生产环境中部署时，还应考虑以下几点：</p>

    <h3>7.1. HTTP 缓存头策略</h3>
    <ul>
        <li><strong>目的:</strong> 防止 CDN 或共享缓存（如 Varnish）错误地缓存了授权用户看到的完整内容，然后将其提供给未授权的游客。</li>
        <li><strong>风险:</strong> 如果不做处理，公开的缓存层可能会将一份完整的受限内容缓存起来，导致所有后续访问者（无论权限如何）都能看到全文，造成内容泄露。</li>
        <li><strong>实现建议:</strong> 在 PHP 代码中，当判断出当前内容是受限的时，应主动发送特定的 HTTP 头，指示下游缓存这是私有内容。
            <pre><code>header('Cache-Control: private, no-store');
header('Vary: Cookie'); // 告知缓存，响应内容因 Cookie（通常包含登录状态）而异</code></pre>
        </li>
    </ul>

    <h3>7.2. 边缘场景处理</h3>
    <ul>
        <li><strong>私密/密码保护文章:</strong> 需要确保我们的访问控制逻辑与 WordPress 内置的"私密(Private)"和"密码保护(Password Protected)"状态能和谐共存，避免逻辑冲突。通常，WordPress 自身的检查应该优先。</li>
        <li><strong>作者/管理员预览:</strong> 必须保证文章作者或管理员在任何情况下都能预览和编辑文章，即使用户角色本身不满足前台的会员等级要求。这可以通过在权限检查函数中加入 <code>current_user_can('edit_post', $post_id)</code> 这样的能力判断来实现。</li>
    </ul>

    <h3>7.3. 自动化测试</h3>
    <ul>
        <li><strong>目的:</strong> 保证后续的功能迭代或代码重构不会意外引入安全漏洞（特别是内容泄露风险）。</li>
        <li><strong>实现建议:</strong> 编写单元测试或集成测试，覆盖核心的权限判断函数 <code>fd_member_user_can_view_post()</code>。至少应包含以下测试用例：
            <ol>
                <li>游客访问公开文章。</li>
                <li>游客访问受限文章。</li>
                <li>低等级会员访问高等级文章。</li>
                <li>高等级会员访问低等级文章。</li>
                <li>作者访问自己设置了限制的文章。</li>
            </ol>
        </li>
        <li><strong>价值:</strong> 即使只有少量核心测试用例，也能在持续集成（CI）流程中自动守护系统的安全边界。</li>
    </ul>

    <h3>7.4. 预览策略优化</h3>
    <ul>
        <li><strong>现状:</strong> 当前的策略是未授权用户只能看到摘要。对于没有摘要的文章，用户看不到任何引导性内容，这可能不足以吸引他们付费或升级。</li>
        <li><strong>优化建议:</strong>
            <ul>
                <li><strong>内容截断:</strong> 允许后台设置一个"预览字数"，自动截取文章开头的一部分作为预览。</li>
                <li><strong>"更多内容"标签:</strong> 类似于 WordPress 的 `<!--more-->` 标签，允许编辑手动指定预览部分的结束位置。</li>
            </ul>
        </li>
        <li><strong>影响:</strong> 这需要对后端逻辑和前端组件进行相应的修改，但能显著提升用户体验和转化率。</li>
    </ul>

    <h3>7.5. 性能与监控</h3>
    <ul>
        <li><strong>挑战:</strong> 随着会员数量和网站流量的增长，每次页面加载都去数据库查询用户等级信息，可能会成为性能瓶颈。</li>
        <li><strong>优化方案:</strong>
            <ul>
                <li><strong>对象缓存:</strong> 使用 WordPress 的 Object Cache (如 Redis 或 Memcached)，将用户的会员等级信息缓存起来，避免重复的数据库查询。用户的等级信息在变更时（如升级、过期）需要主动失效缓存。</li>
                <li><strong>JWT 存储:</strong> 如果系统使用 JWT (JSON Web Tokens) 进行认证，可以将用户的会员等级或权限标识直接编码进 JWT 的 Payload 中。这样，API 在收到请求时，无需查询数据库即可从 Token 中解析出权限信息。</li>
            </ul>
        </li>
        <li><strong>监控:</strong> 配置慢查询日志，监控与会员权限相关的数据库查询，及时发现并解决性能问题。</li>
    </ul>

    <h2>8. 付费墙链接自定义</h2>
    <p>自 v1.1.0 起，插件新增后台 <strong>"付费墙链接"</strong> 设置，允许站点管理员为"登录 / 注册 / 升级会员"三大按钮填写前端专属 URL。这样，前端跳转不再依赖 WordPress 默认后台地址。</p>
    <h3>8.1 设置入口</h3>
    <p>路径：<code>会员管理 → 会员设置 → 付费墙链接</code> 标签页。</p>
    <ul>
        <li><strong>登录页面 URL</strong>（<code>fd_member_login_url</code>）</li>
        <li><strong>注册页面 URL</strong>（<code>fd_member_register_url</code>）</li>
        <li><strong>升级会员页面 URL</strong>（<code>fd_member_upgrade_url</code>）</li>
    </ul>
    <h3>8.2 代码实现</h3>
    <ul>
        <li>后台 UI 定义于 <code>admin/settings-page.php</code>，通过 <code>register_setting()</code> 存储 3 个 <code>option</code>。</li>
        <li><code>cac-core.php</code> 与 <code>cac-graphql.php</code> 在渲染付费墙时读取上述 <code>option</code>，若为空则回退到 WordPress 默认地址。</li>
    </ul>
    <h3>8.3 安全注意</h3>
    <ul>
        <li>插件未对输入做强制校验，请确保填写的链接为可信同站 URL。</li>
        <li>若需恢复默认行为，只需留空对应输入框并保存即可。</li>
    </ul>

</body>
</html> 