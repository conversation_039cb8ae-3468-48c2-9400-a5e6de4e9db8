<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>私信功能后端实现文档 (v2.1)</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        h1 {
            font-size: 2.5em;
            text-align: center;
            border-bottom: none;
        }
        pre {
            background-color: #2d2d2d;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        code {
            font-family: "Fira Code", "Consolas", "Monaco", monospace;
        }
        .container {
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .note {
            background-color: #e7f3fe;
            border-left: 6px solid #2196F3;
            padding: 15px;
            margin: 20px 0;
        }
        .warning {
            background-color: #fffbe6;
            border-left: 6px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
        }
        .success {
            background-color: #e8f5e9;
            border-left: 6px solid #4CAF50;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>私信 (Private Messaging) API文档 (v2.1)</h1>
        <p class="note">本文档详细介绍了私信功能的GraphQL API。v2.1版本修复了一个重要的权限问题，并对部分GraphQL类型进行了重构，以确保非管理员用户可以正常使用私信功能。</p>

        <h2>核心理念</h2>
        <ul>
            <li><strong>全局ID (Global ID)</strong>: 所有对象和输入都使用GraphQL的全局中继ID (Global Relay ID)。</li>
            <li><strong>权限控制</strong>: 所有查询和变更都会验证用户登录状态和操作权限。</li>
            <li><strong>用户中心 (Viewer)</strong>: 大部分针对当前用户的查询都挂载在 <code>viewer</code> 对象下。</li>
            <li><strong>标准化分页</strong>: 所有列表均采用标准的光标分页 (Cursor-based Pagination)。</li>
        </ul>

        <h2 id="permission-issue">权限问题与解决方案</h2>
        <div class="warning">
            <h4>问题背景：WPGraphQL的严格权限策略</h4>
            <p>在测试中发现，普通用户（如订阅者角色）在获取会话列表时会遇到 <code>Internal server error: "Cannot return null for non-nullable field \"User.id\"."</code> 错误。</p>
            <p>根本原因在于：</p>
            <ol>
                <li>WPGraphQL 默认严格遵守 WordPress 的权限模型。普通用户没有权限通过顶层查询 (如 <code>users</code>, <code>user(id: ...)</code>) 查看其他用户的详细信息。</li>
                <li>旧版API中，会话的 <code>otherUser</code> 字段返回的是核心的 <code>User</code> 类型。当一个普通用户查询会话列表时，GraphQL引擎尝试解析对方用户的信息。</li>
                <li>由于权限不足，引擎无法获取对方用户的任何字段，包括被定义为 <code>ID!</code> (不可为空) 的 <code>id</code> 字段。因此解析结果为 `null`，与 Schema 的非空定义冲突，导致查询失败。</li>
            </ol>
        </div>
        <div class="success">
            <h4>解决方案：解耦用户类型</h4>
            <p>为了解决此问题，我们不再直接暴露核心的 <code>User</code> 类型给私信会话。而是创建了一个新的、轻量级的GraphQL类型 <code>ConversationParticipant</code>，专门用于表示会话参与者。</p>
            <pre><code># 新增的、用于替代User类型的轻量级参与者类型
type ConversationParticipant {
    id: ID!
    databaseId: Int!
    name: String!
    avatar: Avatar
}</code></pre>
            <p>这个新类型的解析器被设计为直接从数据库获取必要的公开信息（ID, name, avatar），绕过了WPGraphQL对核心 <code>User</code> 对象的权限检查。我们将所有返回用户的地方（如 `otherUser`, `sender`）都替换为这个新类型，从而解决了权限冲突。</p>
        </div>


        <h2>1. GraphQL 类型定义 (v2.1)</h2>
        
        <h3>1.1 主要对象类型</h3>
        
        <h4><code>ConversationParticipant</code> (新增)</h4>
        <p>代表一个轻量级的会话参与者，只包含公开信息。</p>
        <pre><code>type ConversationParticipant {
    id: ID!
    databaseId: Int!
    name: String!
    avatar: Avatar
}</code></pre>

        <h4><code>PrivateMessage</code> (已更新)</h4>
        <p>代表单条私信。<code>sender</code> 和 <code>recipient</code> 字段已更新。</p>
        <pre><code>type PrivateMessage implements Node {
    id: ID!
    databaseId: Int!
    content: String!
    sentAt: String!
    isRead: Boolean!
    sender: ConversationParticipant
    recipient: ConversationParticipant
}</code></pre>

        <h4><code>PrivateMessageConversation</code> (已更新)</h4>
        <p>代表一个会话。<code>otherUser</code> 字段已更新。</p>
        <pre><code>type PrivateMessageConversation implements Node {
    id: ID!
    databaseId: Int!
    updatedAt: String!
    unreadCount: Int!
    otherUser: ConversationParticipant
    lastMessage: PrivateMessage
    messages(first: Int, after: String): PrivateMessageConnection!
}</code></pre>
        <p class="warning"><strong>重要</strong>: <code>messages</code> 字段返回的是一个分页连接 (Connection)，需要按分页格式进行查询。</p>


        <h3>1.2 分页辅助类型</h3>
        <p>这些是实现标准光标分页所需的辅助类型。</p>
        <pre><code># 消息分页
type PrivateMessageConnection {
    edges: [PrivateMessageEdge]
    pageInfo: PrivateMessagePageInfo!
}
type PrivateMessageEdge {
    cursor: String!
    node: PrivateMessage
}

# 会话分页
type PrivateMessageConversationConnection {
    edges: [PrivateMessageConversationEdge]
    pageInfo: PrivateMessagePageInfo!
}
type PrivateMessageConversationEdge {
    cursor: String!
    node: PrivateMessageConversation
}

# 通用分页信息
type PrivateMessagePageInfo {
    hasNextPage: Boolean!
    endCursor: String
}</code></pre>

        <h2>2. 查询 (Queries)</h2>

        <h3>2.1 获取会话列表 (<code>viewer.privateConversations</code>)</h3>
        <p>获取当前登录用户的所有会话列表。注意 <code>otherUser</code> 内部字段的变化。</p>
        <pre><code># 查询语句
query GetMyConversations($first: Int = 10, $after: String) {
    viewer {
        id
        privateConversations(first: $first, after: $after) {
            edges {
                cursor
                node {
                    id
                    databaseId
                    updatedAt
                    unreadCount
                    otherUser {
                        id
                        databaseId
                        name
                        avatar { url }
                    }
                    lastMessage {
                        content
                        sentAt
                    }
                }
            }
            pageInfo {
                hasNextPage
                endCursor
            }
        }
    }
}</code></pre>

        <h3>2.2 获取单个会话详情 (<code>privateConversation</code>)</h3>
        <p>根据会话的全局ID获取其详细信息。注意 <code>otherUser</code> 和 <code>sender</code> 字段的变化。</p>
        <pre><code># 查询语句
query GetConversationMessages($id: ID!, $first: Int = 20, $after: String) {
    privateConversation(id: $id) {
        id
        databaseId
        otherUser {
            id
            name
        }
        messages(first: $first, after: $after) {
            edges {
                node {
                    id
                    content
                    sentAt
                    isRead
                    sender {
                        id
                        name
                    }
                }
            }
            pageInfo {
                hasNextPage
                endCursor
            }
        }
    }
}

# 查询变量示例
# { "id": "cG1fY29udmVyc2F0aW9uOjI=" }</code></pre>

        <h3>2.3 获取未读消息总数 (<code>viewer.unreadMessageCount</code>)</h3>
        <p>获取当前登录用户的全站未读私信总数。</p>
        <pre><code># 查询语句
query GetUnreadMessageCount {
    viewer {
        unreadMessageCount
    }
}</code></pre>

        <h2>3. 变更 (Mutations)</h2>

        <h3>3.1 发送私信 (<code>sendPrivateMessage</code>)</h3>
        <p>向指定用户发送一条私信。</p>
        <pre><code># 变更语句
mutation SendMessage($recipientId: ID!, $content: String!) {
    sendPrivateMessage(input: {
        recipientId: $recipientId,
        content: $content,
        clientMutationId: "unique-mutation-id"
    }) {
        success
        sentMessage {
            id
            content
        }
        conversation {
            id
        }
    }
}

# 变更变量示例
# {
#   "recipientId": "dXNlcjo0NTY=", # 用户的全局ID
#   "content": "你好，这是通过v2.1 API发送的消息。"
# }</code></pre>

        <h3>3.2 标记会话已读 (<code>markConversationAsRead</code>)</h3>
        <p>将一个会话中的所有未读消息标记为已读。</p>
        <pre><code># 变更语句
mutation MarkAsRead($conversationId: ID!) {
    markConversationAsRead(input: {
        conversationId: $conversationId,
        clientMutationId: "unique-mutation-id"
    }) {
        success
        conversation {
            id
            unreadCount
        }
    }
}

# 变更变量示例
# { "conversationId": "cG1fY29udmVyc2F0aW9uOjE=" }</code></pre>

        <h3>3.3 删除会话 (<code>deleteConversation</code>)</h3>
        <p>删除一个会话及其包含的所有消息。</p>
        <pre><code># 变更语句
mutation DeleteConversation($conversationId: ID!) {
    deleteConversation(input: {
        conversationId: $conversationId,
        clientMutationId: "unique-mutation-id"
    }) {
        success
        deletedConversationId
    }
}

# 变更变量示例
# { "conversationId": "cG1fY29udmVyc2F0aW9uOjE=" }</code></pre>

    </div>
</body>
</html>