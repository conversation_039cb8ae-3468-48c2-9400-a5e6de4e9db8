<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>单篇文章付费解锁功能实现文档</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f8f9fa;
    }
    h1 {
      color: #2c3e50;
      border-bottom: 3px solid #3498db;
      padding-bottom: 10px;
      margin-bottom: 30px;
    }
    h2 {
      color: #34495e;
      margin-top: 40px;
      margin-bottom: 20px;
      padding-left: 10px;
      border-left: 4px solid #3498db;
    }
    h3 {
      color: #2c3e50;
      margin-top: 25px;
      margin-bottom: 15px;
    }
    pre {
      background: #f8f9fa;
      border: 1px solid #e1e4e8;
      border-radius: 3px;
      padding: 15px;
      overflow-x: auto;
    }
    code {
      font-family: Consolas, Monaco, 'Andale Mono', monospace;
      font-size: 14px;
    }
    .note {
      background: #fff3cd;
      border-left: 4px solid #ffeeba;
      padding: 15px;
      margin: 20px 0;
    }
    .important {
      background: #d1ecf1;
      border-left: 4px solid #0c5460;
      padding: 15px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <h1>单篇文章付费解锁功能实现文档</h1>

  <h2>1. 功能概述</h2>
  <p>本功能允许用户在会员等级不足以查看文章全文时，通过一次性支付解锁单篇文章的访问权限。支付完成后，用户永久拥有该文章的阅读权限。</p>

  <h2>2. 数据模型</h2>
  <h3>2.1 文章元数据</h3>
  <ul>
    <li><code>_fd_unlock_price</code> (float) — 解锁价格，&gt;0 时启用付费解锁。</li>
  </ul>
  <h3>2.2 用户解锁记录表</h3>
  <pre><code>CREATE TABLE {prefix}unlocked_posts (
  id BIGINT(20) AUTO_INCREMENT PRIMARY KEY,
  user_id BIGINT(20) NOT NULL,
  post_id BIGINT(20) NOT NULL,
  unlocked_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY user_id (user_id),
  KEY post_id (post_id),
  UNIQUE KEY user_post (user_id, post_id)
);</code></pre>
  <p>表名前缀将自动替换为站点实际设置（如 <code>fd_</code>、<code>wp_</code>）。</p>

  <h2>3. 权限检查流程</h2>
  <ol>
    <li>公开文章 (requiredLevel = 0) 直接放行。</li>
    <li>作者/管理员预览直接放行。</li>
    <li>已单独解锁的用户直接放行（查询 <code>unlocked_posts</code> 表）。</li>
    <li>会员等级比较，满足优先级则放行。</li>
    <li>否则显示付费墙。</li>
  </ol>

  <h2>4. 后台编辑器集成</h2>
  <h3>4.1 经典编辑器</h3>
  <p>在"内容访问权限"元框下新增"单独解锁价格"输入框，单位同站点货币。</p>
  <h3>4.2 古腾堡编辑器</h3>
  <p>侧边栏 <code>PluginDocumentSettingPanel</code> 中新增 <code>TextControl</code>，字段绑定 <code>_fd_unlock_price</code>。</p>
  <h3>4.3 批量设置解锁价格</h3>
  <p>在文章列表页（所有受保护的内容类型）中，可通过"批量操作"直接输入解锁价格并一次性应用到多篇文章。</p>
  <ol>
    <li>在勾选目标文章后，于批量操作下拉菜单选择 <code>Set Unlock Price…</code>。</li>
    <li>点击"应用"后浏览器将弹出输入框，录入任意价格（支持小数；留空或 0 表示取消单篇解锁）。</li>
    <li>提交后，所选文章的 <code>_fd_unlock_price</code> 元数据被统一更新，并在顶部显示操作结果通知。</li>
  </ol>
  <pre><code>// JavaScript 提示逻辑（简化）
$('#doaction, #doaction2').on('click', function(){
  if ($(this).val() === 'set_unlock_price_custom') {
    var price = window.prompt('Enter unlock price …');
    $('<input>').attr({type:'hidden', name:'fd_bulk_unlock_price', value:price}).appendTo('#posts-filter');
  }
});</code></pre>

  <h2>5. GraphQL 扩展</h2>
  <h3>5.1 新增字段</h3>
  <ul>
    <li><code>unlockPrice: Float</code></li>
    <li><code>isUnlockedByCurrentUser: Boolean</code></li>
  </ul>
  <h3>5.2 新增 Mutation</h3>
  <pre><code>mutation CreateUnlockOrder($postId: ID!) {
  createUnlockOrder(input: { postId: $postId }) {
    success
    message
    orderId
  }
}</code></pre>

  <h2>6. 支付流程</h2>
  <ol>
    <li>前端调用 <code>createUnlockOrder</code> 创建订单。</li>
    <li>用户在通用支付页完成支付。</li>
    <li>支付成功触发 <code>fd_payment_order_completed</code>，后端记录解锁。</li>
    <li>再次请求文章，字段 <code>isUnlockedByCurrentUser</code> 返回 <code>true</code>，<code>content</code> 返回全文。</li>
  </ol>

  <h2>7. 测试示例</h2>
  <h3>7.1 查询文章解锁信息</h3>
  <pre><code>query($id: ID!) {
  post(id: $id, idType: DATABASE_ID) {
    title
    unlockPrice
    isUnlockedByCurrentUser
  }
}</code></pre>

  <h3>7.2 创建解锁订单</h3>
  <pre><code>mutation($postId: ID!) {
  createUnlockOrder(input: { postId: $postId }) {
    success
    orderId
  }
}</code></pre>

  <div class="important">
    <strong>注意：</strong> Mutation 调用需在已认证的用户上下文执行（JWT / Cookie）。
  </div>

  <h2>8. 版本信息</h2>
  <ul>
    <li>功能引入版本：<strong>v1.1.0</strong></li>
    <li>文档更新时间：2025-07-02</li>
  </ul>

  <h2>9. v1.2.0 兼容性更新 (2025-07-04)</h2>
  <div class="important">
    <strong>核心变更：</strong> 此版本旨在确保单篇解锁功能与 <code>fd-payment</code> 支付插件 <strong>v2.0.0+</strong> 版本完全兼容。
  </div>
  <h3>9.1 订单状态字段对齐</h3>
  <p>为匹配 <code>fd-payment</code> v2 的数据结构，解锁回调函数中的逻辑已调整：</p>
  <ul>
    <li>订单完成状态的检查值从 <code>'completed'</code> 修正为 <code>'paid'</code>。</li>
    <li>读取订单状态的字段从 <code>$order->status</code> 修正为 <code>$order->payment_status</code>。</li>
  </ul>
  <pre><code>// fd-member/includes/content-access-control/cac-core.php
if ($order->payment_status !== 'paid') {
    return;
}</code></pre>

  <h3>9.2 订单元数据读取</h3>
  <p><code>fd-payment</code> v2 将订单元数据从序列化数组 (<code>meta</code> 列) 改为 JSON 字符串 (<code>metadata</code> 列)。为同时兼容新旧版本，增加了健壮的读取逻辑：</p>
  <pre><code>// cac-core.php - 兼容 JSON / serialized array
$meta_raw = $order->metadata ?? $order->meta ?? '';
$meta = is_string($meta_raw) ? json_decode($meta_raw, true) : $meta_raw;
if (!is_array($meta)) {
    $meta = maybe_unserialize($meta_raw);
}
if (!is_array($meta) || $meta['order_type'] !== 'post_unlock' || ...) {
   return;
}</code></pre>
  <p>此修改确保无论支付插件版本如何，<code>post_unlock</code> 类型的订单都能被正确识别，从而触发解锁记录的写入。</p>

  <h3>9.3 版本信息</h3>
  <ul>
    <li>功能补丁版本：<strong>v1.2.0</strong></li>
    <li>发布时间：2025-07-04</li>
  </ul>

</body>
</html> 