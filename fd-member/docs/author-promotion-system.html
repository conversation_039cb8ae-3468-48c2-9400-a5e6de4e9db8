<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作者自动晋升体系 - 实现文档</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; line-height: 1.6; color: #333; max-width: 960px; margin: 20px auto; padding: 0 20px; }
        h1, h2, h3 { color: #2c3e50; border-bottom: 2px solid #eaecef; padding-bottom: 0.3em; }
        h1 { font-size: 2.2em; }
        h2 { font-size: 1.8em; }
        code { background-color: #f6f8fa; padding: 0.2em 0.4em; margin: 0; font-size: 85%; border-radius: 3px; font-family: "SFMono-Regular", <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace; }
        pre { background-color: #f6f8fa; padding: 16px; overflow: auto; border-radius: 6px; }
        pre code { padding: 0; margin: 0; font-size: 100%; }
        .container { background-color: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .note { background-color: #e6f7ff; border-left: 4px solid #1890ff; padding: 12px 16px; margin: 20px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>作者自动晋升体系 - 实现文档</h1>
        <p><strong>版本:</strong> 1.0.0</p>
        <p><strong>日期:</strong> 2025-06-21</p>

        <h2>1. 功能概述</h2>
        <p>
            本功能为 <code>fd-member</code> 插件增加了一个自动化的用户成长机制。管理员可以在后台设置一个文章发布数量的阈值，当用户的已发布文章总数达到这个阈值时，系统会自动将其角色从"订阅者 (Subscriber)"提升为"作者 (Author)"，从而赋予其自行发布文章的权限。
        </p>
        <div class="note">
            <strong>核心目标:</strong> 激励用户贡献高质量内容，并通过自动化的方式减轻管理员手动晋升用户的工作量，构建一个良性的社区生态。
        </div>

        <h2>2. 后台设置实现</h2>
        <p>
            为了让管理员能够方便地控制晋升条件，我们在"会员管理" -> "会员设置"页面下新增了一个"晋升体系"标签页。
        </p>
        
        <h3>2.1. 文件结构</h3>
        <ul>
            <li><code>/includes/user/author-promotion.php</code>: 包含本功能所有核心逻辑，包括设置项注册、后台UI渲染及晋升检测函数。</li>
            <li><code>/admin/settings-page.php</code>: 修改此文件以添加新的标签页。</li>
            <li><code>/index.php</code>: 修改主插件文件以引入 <code>author-promotion.php</code>。</li>
        </ul>

        <h3>2.2. 设置项注册 (Settings API)</h3>
        <p>我们使用 WordPress 内置的 Settings API 来创建和管理设置项，确保数据安全和标准化。</p>
        <ol>
            <li><strong>注册设置组:</strong> 通过 <code>register_setting</code> 函数注册了一个名为 <code>fd_member_promotion_options_group</code> 的设置组，其数据保存在 <code>wp_options</code> 表的 <code>fd_member_promotion_options</code> 字段中。</li>
            <li><strong>添加设置区域和字段:</strong> 使用 <code>add_settings_section</code> 和 <code>add_settings_field</code> 创建了设置区域和具体的输入字段。
                <ul>
                    <li><strong>字段ID:</strong> <code>promotion_publish_count_threshold</code></li>
                    <li><strong>标题:</strong> "升级为作者所需文章数"</li>
                    <li><strong>UI:</strong> 一个 HTML5 的 <code>&lt;input type="number"&gt;</code> 输入框。</li>
                </ul>
            </li>
            <li><strong>数据清理:</strong> 为 <code>register_setting</code> 提供了 sanitize 回调函数 <code>fd_member_promotion_options_sanitize</code>，使用 <code>absint()</code> 确保用户输入的值始终是一个非负整数。</li>
        </ol>
        <pre><code class="language-php">
// author-promotion.php

// 注册设置
add_action('admin_init', 'fd_member_register_promotion_settings');

function fd_member_register_promotion_settings() {
    register_setting(
        'fd_member_promotion_options_group',
        'fd_member_promotion_options',
        'fd_member_promotion_options_sanitize'
    );
    // ... add_settings_section and add_settings_field ...
}

// 渲染输入框
function fd_member_promotion_threshold_field_html() {
    $options = get_option('fd_member_promotion_options');
    $threshold = isset($options['promotion_publish_count_threshold']) ? $options['promotion_publish_count_threshold'] : 10;
    // ... HTML for input field ...
}
        </code></pre>
        
        <h2>3. 核心晋升逻辑</h2>
        <p>晋升的核心逻辑由 <code>fd_member_check_and_promote_user($user_id)</code> 函数负责。</p>
        
        <h3>3.1. 函数执行步骤</h3>
        <ol>
            <li><strong>获取用户对象:</strong> 根据传入的 <code>$user_id</code> 获取 <code>WP_User</code> 对象。</li>
            <li><strong>检查角色:</strong> 验证该用户的角色是否为 `subscriber`。如果不是，则直接返回，避免将管理员等其他角色降级。</li>
            <li><strong>获取阈值:</strong> 从数据库中读取管理员设置的晋升阈值。如果未设置或为0，则不执行任何操作。</li>
            <li><strong>统计文章数:</strong> 使用 WordPress 核心函数 <code>count_user_posts($user_id, 'post', true)</code> 来高效地统计该用户已发布 (publish) 的文章总数。</li>
            <li><strong>比较并晋升:</strong> 如果用户的已发布文章数大于或等于阈值，则创建一个新的 <code>WP_User</code> 实例，并调用其 <code>set_role('author')</code> 方法，将用户的角色更新为"作者"。</li>
        </ol>
        <pre><code class="language-php">
// author-promotion.php

function fd_member_check_and_promote_user($user_id) {
    $user = get_user_by('ID', $user_id);

    if (!$user || !in_array('subscriber', $user->roles, true)) {
        return;
    }
    
    $options = get_option('fd_member_promotion_options');
    $threshold = isset($options['promotion_publish_count_threshold']) ? absint($options['promotion_publish_count_threshold']) : 0;
    
    if ($threshold <= 0) {
        return;
    }

    $published_posts_count = count_user_posts($user_id, 'post', true);

    if ($published_posts_count >= $threshold) {
        $user_obj = new WP_User($user_id);
        $user_obj->set_role('author');
    }
}
        </code></pre>

        <h2>4. 触发机制</h2>
        <p>
            选择正确的触发时机至关重要。我们不应该在每次用户登录或每次页面加载时都去检查，这样效率太低。最佳的检查点是"**当一篇文章被发布的那一刻**"。
        </p>
        <p>
            我们使用了 WordPress 的 <code>transition_post_status</code> 钩子，它在一个文章的状态发生任何变化时都会被调用。
        </p>
        <ul>
            <li><strong>钩子挂载:</strong> <code>add_action('transition_post_status', 'fd_member_on_post_publish', 10, 3);</code></li>
            <li><strong>回调函数 (`fd_member_on_post_publish`):</strong>
                <ul>
                    <li>接收三个参数: <code>$new_status</code>, <code>$old_status</code>, <code>$post</code>。</li>
                    <li>通过判断 <code>$new_status === 'publish' && $old_status !== 'publish'</code>，我们确保只有当文章是**首次被发布**时才执行逻辑，避免了在更新已发布文章时重复触发。</li>
                    <li>从 <code>$post</code> 对象中获取作者ID，然后调用上面描述的核心函数 <code>fd_member_check_and_promote_user()</code> 来执行检查和晋升操作。</li>
                </ul>
            </li>
        </ul>

        <h2>5. 完整工作流</h2>
        <ol>
            <li>网站管理员进入后台 **会员管理 -> 会员设置 -> 晋升体系** 页面，将"升级为作者所需文章数"设置为一个值（例如 `10`）并保存。</li>
            <li>某个"订阅者"角色的用户通过前端投稿系统提交了其第10篇文章。</li>
            <li>管理员在后台审核通过了这第10篇文章，点击"发布"。</li>
            <li>文章状态从 `pending` 变为 `publish`，触发了 `transition_post_status` 钩子。</li>
            <li>我们的回调函数被执行，它获取了该文章作者的ID。</li>
            <li><code>fd_member_check_and_promote_user</code> 函数被调用，它发现该用户的已发布文章数 `10` 达到了阈值 `10`。</li>
            <li>系统立即将该用户的角色从 `subscriber` 更新为 `author`。</li>
            <li>该用户下次登录时，就拥有了作者的所有权限，例如可以直接发布文章而无需审核。整个过程完全自动化。</li>
        </ol>
    </div>
</body>
</html> 