<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FD Member 文档 V1.0</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .docs-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .docs-header h1 {
            font-size: 2.4em;
            margin-bottom: 10px;
        }
        .docs-meta {
            color: #666;
            font-style: italic;
        }
        .docs-section {
            margin-bottom: 40px;
        }
        .docs-section h2 {
            font-size: 1.8em;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            margin-bottom: 20px;
        }
        .docs-section h3 {
            font-size: 1.4em;
            margin-top: 25px;
            margin-bottom: 15px;
        }
        .feature-list li {
            margin-bottom: 20px;
        }
        .feature-list h3 {
            margin-top: 0;
            margin-bottom: 10px;
        }
        .code-block {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 15px;
            overflow: auto;
            margin: 15px 0;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        .docs-footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            font-size: 0.9em;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="docs-header">
        <h1>FD Member 文档 V1.0</h1>
        <p class="docs-meta">版本：1.0.0 | 作者：未来学人</p>
    </div>
    
    <div class="docs-section">
        <h2>插件介绍</h2>
        <p>FD Member 是一个功能丰富的会员管理插件，提供用户管理、资料维护、登录注册等基础功能。该插件支持通过GraphQL API与NextJS前端集成，实现现代化的会员系统。</p>
    </div>
    
    <div class="docs-section">
        <h2>核心功能</h2>
        <ul class="feature-list">
            <li>
                <h3>用户管理</h3>
                <p>提供完整的用户管理功能，包括注册、登录、密码重置等操作。</p>
            </li>
            <li>
                <h3>用户资料</h3>
                <p>支持自定义用户资料字段，用户可以完善个人信息。</p>
            </li>
            <li>
                <h3>社交登录</h3>
                <p>支持第三方社交账号登录，如微信、QQ等。</p>
            </li>
            <li>
                <h3>用户关系</h3>
                <p>支持用户之间的关注/粉丝关系。</p>
            </li>
            <li>
                <h3>消息通知</h3>
                <p>提供站内消息和通知系统。</p>
            </li>
        </ul>
    </div>
    
    <div class="docs-section">
        <h2>GraphQL API</h2>
        <p>此插件提供了GraphQL API，可用于与NextJS等前端框架集成。</p>
        
        <h3>主要类型</h3>
        <div class="code-block">
<pre>
type MemberProfile {
  userId: ID!
  nickname: String
  avatar: String
  bio: String
  website: String
  followersCount: Int
  followingCount: Int
}
</pre>
        </div>
        
        <h3>主要查询</h3>
        <div class="code-block">
<pre>
# 获取会员资料
query GetMemberProfile($userId: ID) {
  memberProfile(userId: $userId) {
    userId
    nickname
    avatar
    bio
    website
    followersCount
    followingCount
  }
}
</pre>
        </div>
        
        <h3>主要变更操作</h3>
        <div class="code-block">
<pre>
# 更新会员资料
mutation UpdateProfile($nickname: String, $bio: String, $website: String) {
  updateMemberProfile(input: {
    nickname: $nickname
    bio: $bio
    website: $website
  }) {
    success
    message
    profile {
      userId
      nickname
      avatar
      bio
      website
    }
  }
}
</pre>
        </div>
    </div>
    
    <div class="docs-section">
        <h2>用户认证流程</h2>
        <h3>基本登录流程</h3>
        <ol>
            <li>用户访问登录页面</li>
            <li>输入用户名和密码</li>
            <li>系统验证用户信息</li>
            <li>登录成功后，生成认证令牌</li>
            <li>重定向到用户中心或指定页面</li>
        </ol>
        
        <h3>社交登录流程</h3>
        <ol>
            <li>用户点击社交登录按钮</li>
            <li>重定向到对应的社交平台授权页面</li>
            <li>用户授权后，社交平台回调到系统</li>
            <li>系统验证授权信息，创建或更新用户账号</li>
            <li>完成登录过程</li>
        </ol>
        
        <h3>示例代码</h3>
        <div class="code-block">
<pre>
// 登录示例代码
const login = async (username, password) => {
  const response = await graphql({
    query: LOGIN_MUTATION,
    variables: {
      username,
      password
    }
  });
  
  if (response.data.login.success) {
    // 存储认证令牌
    localStorage.setItem('authToken', response.data.login.token);
    // 重定向到用户中心
    window.location.href = '/user-center';
  } else {
    // 显示错误信息
    showError(response.data.login.message);
  }
};
</pre>
        </div>
    </div>
    
    <div class="docs-section">
        <h2>用户资料管理</h2>
        <p>FD Member 插件提供了全面的用户资料管理功能。</p>
        
        <h3>可自定义的用户资料字段</h3>
        <ul>
            <li><strong>基础信息</strong>：昵称、头像、简介等</li>
            <li><strong>联系方式</strong>：电子邮件、电话、社交账号等</li>
            <li><strong>个人资料</strong>：性别、生日、地区等</li>
            <li><strong>自定义字段</strong>：支持添加任意自定义字段</li>
        </ul>
        
        <h3>资料隐私设置</h3>
        <p>用户可以设置每个资料字段的可见性：</p>
        <ul>
            <li><strong>公开</strong>：所有人可见</li>
            <li><strong>仅关注者</strong>：只有关注者可见</li>
            <li><strong>仅自己</strong>：只有自己可见</li>
        </ul>
    </div>
    
    <div class="docs-section">
        <h2>开发者文档</h2>
        <p>FD Member 插件提供了多种挂钩和过滤器，允许开发者扩展其功能。</p>
        
        <h3>常用动作挂钩</h3>
        <ul>
            <li><code>fd_member_after_login</code> - 用户登录后触发</li>
            <li><code>fd_member_after_register</code> - 用户注册后触发</li>
            <li><code>fd_member_profile_update</code> - 用户资料更新后触发</li>
        </ul>
        
        <h3>常用过滤器</h3>
        <ul>
            <li><code>fd_member_profile_fields</code> - 自定义用户资料字段</li>
            <li><code>fd_member_login_redirect</code> - 自定义登录后重定向URL</li>
            <li><code>fd_member_avatar_url</code> - 自定义用户头像URL</li>
        </ul>
        
        <h3>添加自定义资料字段</h3>
        <div class="code-block">
<pre>
add_filter('fd_member_profile_fields', 'add_custom_profile_fields');
function add_custom_profile_fields($fields) {
    $fields['company'] = array(
        'label' => '公司',
        'type' => 'text',
        'required' => false,
        'privacy' => 'public'
    );
    $fields['position'] = array(
        'label' => '职位',
        'type' => 'text',
        'required' => false,
        'privacy' => 'public'
    );
    return $fields;
}
</pre>
        </div>
    </div>
    
    <div class="docs-footer">
        <p>如需技术支持，请访问：<a href="https://www.futuredecade.com" target="_blank">www.futuredecade.com</a></p>
    </div>
</body>
</html> 