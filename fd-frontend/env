# 应用配置
NODE_ENV=production
# WordPress GraphQL API端点
NEXT_PUBLIC_WORDPRESS_API_URL=https://admin.futuredecade.com/graphql
# WordPress主站点URL（用于REST API调用）
NEXT_PUBLIC_WORDPRESS_URL=https://admin.futuredecade.com
# WordPress应用密码（用于前端微信公众号图片抓取）
WP_APP_USER=conglin
WP_APP_PASS=mN2gON8Sbbn6RUVhlk4Y5rda
WP_BASE_URL=https://admin.futuredecade.com
# 社交分享
NEXT_PUBLIC_SITE_URL=https://www.futuredecade.com
NEXT_PUBLIC_WEBSOCKET_URL=wss://ws.futuredecade.com
# 缓存失效密钥（与WordPress wp-config.php中的REVALIDATE_SECRET保持一致）
REVALIDATE_SECRET=8c2e9b0d1a4f3e6a7b8d9c0e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0
# 前端手动缓存失效密钥（用于WebSocket事件处理）
NEXT_PUBLIC_REVALIDATE_SECRET=8c2e9b0d1a4f3e6a7b8d9c0e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0