# 支付功能实现指南

本文档介绍如何在前端应用中集成基于GraphQL的支付功能。

## 前置依赖

首先，需要安装以下依赖：

```bash
# 安装qrcode.react用于生成微信支付二维码
npm install qrcode.react --save
```

## TypeScript类型定义

如果在开发过程中遇到TypeScript类型错误，请在项目根目录创建或修改以下类型定义文件：

### 文件: src/types/payment.d.ts

```typescript
// 支付相关类型定义
type PaymentMethod = {
  id: string;
  title: string;
  icon: string;
  color: string;
};

type PaymentOrder = {
  id: string;
  orderNumber: string;
  title: string;
  description: string;
  amount: number;
  userId: number;
  paymentMethod: string;
  paymentStatus: string;
  createdAt: string;
  updatedAt: string;
};
```

### 文件: src/types/apollo.d.ts (如果需要)

```typescript
// Apollo相关类型定义，解决useMutation和useQuery的类型问题
declare module '@apollo/client' {
  export interface MutationResult<TData = any> {
    data?: TData;
    error?: ApolloError;
    loading: boolean;
    called: boolean;
    client: ApolloClient<any>;
  }

  export interface QueryResult<TData = any, TVariables = OperationVariables> {
    data?: TData;
    error?: ApolloError;
    loading: boolean;
    called: boolean;
    refetch: (variables?: TVariables) => Promise<ApolloQueryResult<TData>>;
    fetchMore: FetchMoreOptions<TData, TVariables> & Promise<ApolloQueryResult<TData>>;
    networkStatus: NetworkStatus;
    client: ApolloClient<any>;
  }
}
```

## 项目组件结构

支付功能包括以下组件：

1. `PaymentMethodSelector` - 支付方式选择器
2. `OrderForm` - 订单创建表单
3. `PaymentProcessor` - 支付处理器
4. `BalancePaymentForm` - 余额支付表单
5. `PaymentPage` - 支付主页面
6. `OrdersPage` - 订单列表页面
7. `PayOrderPage` - 单个订单支付页面

## 使用方法

### 1. 添加支付路由

在你的应用路由中添加以下路由：

- `/payment` - 支付中心
- `/payment/orders` - 订单列表
- `/payment/pay/[id]` - 支付特定订单

### 2. 在页面中使用支付功能

示例：在产品页面添加"立即购买"按钮

```jsx
import { useRouter } from 'next/navigation';

export default function ProductPage({ product }) {
  const router = useRouter();
  
  const handleBuyNow = () => {
    router.push(`/payment?product=${product.id}&title=${product.title}&price=${product.price}`);
  };
  
  return (
    <div>
      <h1>{product.title}</h1>
      <button onClick={handleBuyNow}>立即购买</button>
    </div>
  );
}
```

### 3. 处理支付状态回调

在支付成功后，可以注册一个自定义事件来处理跨组件通信：

```jsx
// 发送支付成功事件
const sendPaymentSuccessEvent = (orderId) => {
  const event = new CustomEvent('payment_success', { detail: { orderId } });
  window.dispatchEvent(event);
};

// 监听支付成功事件
useEffect(() => {
  const handlePaymentSuccess = (event) => {
    const { orderId } = event.detail;
    console.log(`订单 ${orderId} 支付成功`);
    // 处理后续逻辑
  };
  
  window.addEventListener('payment_success', handlePaymentSuccess);
  return () => {
    window.removeEventListener('payment_success', handlePaymentSuccess);
  };
}, []);
```

## 安全注意事项

1. 使用 `dangerouslySetInnerHTML` 渲染支付表单时注意XSS风险，确保内容来源可信
2. 在支付成功页面避免直接显示敏感信息
3. 所有支付相关的状态变更都应在后端进行二次验证
4. 使用HTTPS确保支付过程的安全性

## 问题排查

1. **支付二维码不显示** - 确认微信支付正确配置，检查qrcode.react是否正确安装
2. **支付成功后未跳转** - 检查支付轮询状态检查是否正常工作
3. **API错误** - 查看控制台错误日志，确认GraphQL查询和变更格式正确
4. **余额显示异常** - 确认用户已登录且后端API正确返回余额信息

## 扩展与自定义

此实现可以根据需要进行扩展：

1. 添加更多支付方式
2. 自定义支付流程页面
3. 添加订单管理功能
4. 集成积分系统
5. 添加支付通知功能 