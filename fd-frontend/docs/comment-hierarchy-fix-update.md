# 评论层级显示修复 - 更新

## 问题定位

通过调试日志发现了普通文章页面评论层级显示问题的根本原因：

### 调试日志分析

**自定义类型文章**（工作正常）：
```
parentId: "Y29tbWVudDoxMDE="
parentKey: "Y29tbWVudDoxMDE="
```

**普通文章**（有问题）：
```
Child comments found:
- parentId: "Y29tbWVudDo0Mg=="  (GraphQL ID格式)
- parentId: "Y29tbWVudDozNQ=="  (GraphQL ID格式)
- parentId: "Y29tbWVudDoxMDE="  (GraphQL ID格式)

Parent comments:
- id: "Y29tbWVudDo0Mg=="       (GraphQL ID格式)
- databaseId: 42               (数字格式)
```

### 问题根因

在回复查找逻辑中，我们使用的键的优先级顺序有问题：

**修复前**：
```typescript
const possibleKeys = [
  comment.databaseId?.toString(),  // "42" - 优先使用数字ID
  comment.id,                      // "Y29tbWVudDo0Mg==" - GraphQL ID
  // ...
];
```

**问题**：
- 子评论的parentId是GraphQL ID格式：`"Y29tbWVudDo0Mg=="`
- 但我们优先用数字ID `"42"` 作为键来查找
- 导致无法匹配，回复评论显示为根评论

## 修复方案

### 调整键匹配优先级

**修复后**：
```typescript
const possibleKeys = [
  comment.id,                      // "Y29tbWVudDo0Mg==" - 优先使用GraphQL ID
  comment.databaseId?.toString(),  // "42" - 备用数字ID
  comment.id.includes(':') ? comment.id.split(':')[1] : null
].filter(Boolean);
```

### 修复逻辑

1. **优先匹配GraphQL ID**：因为parentId通常是GraphQL ID格式
2. **备用数字ID匹配**：兼容可能的数字ID情况
3. **Base64解码匹配**：处理特殊编码情况

## 修复的文件

- `fd-frontend/src/components/comments/CommentList.tsx`

## 预期效果

修复后，普通文章页面应该能够：
- ✅ 正确显示现有的回复评论层级
- ✅ 新提交的回复评论立即显示在正确位置
- ✅ 与自定义类型文章保持一致的用户体验

## 验证步骤

1. 刷新普通文章页面
2. 检查现有回复评论是否正确显示在父评论下方
3. 提交新的回复评论，验证乐观更新效果
4. 查看控制台日志：`[CommentList] Found replies for parent:`

## 技术要点

- **ID格式统一**：WordPress GraphQL返回的ID格式在不同查询中保持一致
- **键匹配策略**：优先使用最可能匹配的键格式
- **调试友好**：保留详细日志帮助问题排查
