<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>付费墙前端UI组件架构文档</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1e40af;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #1f2937;
            margin-top: 40px;
            margin-bottom: 20px;
            padding-left: 15px;
            border-left: 4px solid #3b82f6;
        }
        h3 {
            color: #374151;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        .component-card {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .component-title {
            color: #1e40af;
            font-weight: bold;
            font-size: 1.2em;
            margin-bottom: 10px;
        }
        .features {
            background: #ecfdf5;
            border-left: 4px solid #10b981;
            padding: 15px;
            margin: 15px 0;
        }
        .usage-scenario {
            background: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            margin: 15px 0;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
        }
        .props-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .props-table th,
        .props-table td {
            border: 1px solid #d1d5db;
            padding: 12px;
            text-align: left;
        }
        .props-table th {
            background: #f3f4f6;
            font-weight: bold;
        }
        .flow-diagram {
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .architecture-box {
            display: inline-block;
            background: white;
            border: 2px solid #3b82f6;
            border-radius: 6px;
            padding: 10px 15px;
            margin: 5px;
            font-weight: bold;
        }
        .arrow {
            color: #3b82f6;
            font-size: 1.5em;
            margin: 0 10px;
        }
        .highlight {
            background: #fef3c7;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        .warning {
            background: #fef2f2;
            border-left: 4px solid #ef4444;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background: #f0fdf4;
            border-left: 4px solid #22c55e;
            padding: 15px;
            margin: 15px 0;
        }
        .toc {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
        }
        .toc a {
            color: #3b82f6;
            text-decoration: none;
        }
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 付费墙前端UI组件架构文档</h1>
        
        <div class="success">
            <strong>📅 文档版本：</strong> v1.0.0<br>
            <strong>🗓️ 更新时间：</strong> 2025-07-03<br>
            <strong>👨‍💻 开发者：</strong> Augment Agent<br>
            <strong>🎯 目标：</strong> 将简陋的HTML付费墙升级为精致的卡片式UI组件
        </div>

        <div class="toc">
            <h3>📋 目录</h3>
            <ul>
                <li><a href="#overview">1. 项目概览</a></li>
                <li><a href="#architecture">2. 组件架构</a></li>
                <li><a href="#components">3. 核心组件详解</a></li>
                <li><a href="#data-flow">4. 数据流向分析</a></li>
                <li><a href="#usage">5. 使用方法</a></li>
                <li><a href="#customization">6. 定制化配置</a></li>
                <li><a href="#demo">7. 演示和测试</a></li>
            </ul>
        </div>

        <h2 id="overview">1. 项目概览</h2>
        
        <h3>🎯 项目目标</h3>
        <p>将后端生成的简陋HTML付费墙：</p>
        <div class="code-block">
&lt;div class="fd-member-access-denied"&gt;
  &lt;h3&gt;Members Only Content&lt;/h3&gt;
  &lt;p&gt;This content is reserved for a higher membership level.&lt;/p&gt;
  &lt;a href="/upgrade" class="button"&gt;Upgrade Your Membership&lt;/a&gt;
&lt;/div&gt;
        </div>
        
        <p>升级为精致的现代化卡片式UI组件，提供：</p>
        <div class="features">
            <ul>
                <li>🎨 <strong>精美设计</strong>：渐变背景、动画效果、现代化视觉</li>
                <li>📱 <strong>响应式布局</strong>：完美适配各种设备尺寸</li>
                <li>🌙 <strong>深色模式</strong>：自动适应用户主题偏好</li>
                <li>⚡ <strong>智能渲染</strong>：自动解析HTML并替换为精美卡片</li>
                <li>🔄 <strong>多样式支持</strong>：完整版和紧凑版两种样式</li>
                <li>✨ <strong>流畅动画</strong>：悬停效果、加载状态、过渡动画</li>
            </ul>
        </div>

        <h2 id="architecture">2. 组件架构</h2>
        
        <div class="flow-diagram">
            <h3>🏗️ 组件层次结构</h3>
            <div style="margin: 20px 0;">
                <div class="architecture-box" style="background: #e1f5fe;">PostContentSmart</div>
                <div class="arrow">↓</div>
                <div class="architecture-box" style="background: #f3e5f5;">PaywallRenderer</div>
                <div class="arrow">↓</div>
                <div style="margin: 10px 0;">
                    <div class="architecture-box" style="background: #e8f5e8;">PaywallCard</div>
                    <span style="margin: 0 20px;">或</span>
                    <div class="architecture-box" style="background: #fff3e0;">PaywallCardCompact</div>
                </div>
            </div>
        </div>

        <h3>📊 数据流向</h3>
        <div class="code-block">
文章模板 → PostContentSmart → GraphQL查询 → PaywallRenderer → 卡片组件 → 用户交互
    ↓              ↓                ↓              ↓              ↓           ↓
  传入数据      状态管理        获取权限信息    HTML解析      UI展示      回调处理
        </div>

        <h2 id="components">3. 核心组件详解</h2>

        <div class="component-card">
            <div class="component-title">🧠 PostContentSmart.tsx - 智能控制中心</div>
            <p><strong>职责：</strong>整个付费墙系统的智能控制中心，负责状态管理和数据获取。</p>
            
            <div class="features">
                <strong>核心功能：</strong>
                <ul>
                    <li>🔍 智能检测初始内容是否包含付费墙标记</li>
                    <li>🔄 根据用户登录状态动态重新获取内容</li>
                    <li>📊 管理文章数据和付费墙相关信息</li>
                    <li>🎛️ 控制何时显示付费墙，何时显示完整内容</li>
                </ul>
            </div>

            <div class="code-block">
const PostContentSmart: React.FC&lt;PostContentSmartProps&gt; = ({ 
  postId, 
  initialContent,
  postTitle = '',
  unlockPrice = 0,
  requiredMemberLevel = 0,
  isUnlocked = false,
  paywallVariant = 'default'
}) =&gt; {
  const { isAuthenticated } = useAuthContext();
  const hasPaywall = initialContent.includes('fd-member-access-denied');

  const { data, loading } = useQuery(POST_CONTENT_QUERY, {
    variables: { id: postId },
    skip: !isAuthenticated || !hasPaywall,
    fetchPolicy: 'network-only',
  });
            </div>
        </div>

        <div class="component-card">
            <div class="component-title">🔄 PaywallRenderer.tsx - HTML解析器</div>
            <p><strong>职责：</strong>解析后端HTML并决定如何渲染付费墙组件。</p>
            
            <div class="features">
                <strong>核心功能：</strong>
                <ul>
                    <li>🔍 解析后端返回的付费墙HTML结构</li>
                    <li>🔗 提取登录、注册、升级等链接信息</li>
                    <li>🧹 移除原始付费墙HTML，保留预览内容</li>
                    <li>🎯 根据variant参数选择合适的卡片组件</li>
                </ul>
            </div>

            <div class="code-block">
// 自动检测和解析付费墙HTML
const hasPaywallDiv = content.includes('fd-member-access-denied');
const parser = new DOMParser();
const doc = parser.parseFromString(content, 'text/html');

// 提取链接信息
const links = paywallDiv.querySelectorAll('a');
// 移除原始HTML
const contentWithoutPaywall = content.replace(/&lt;div class="fd-member-access-denied"&gt;[\s\S]*?&lt;\/div&gt;/g, '');
            </div>
        </div>

        <div class="component-card">
            <div class="component-title">🎨 PaywallCard.tsx - 完整版卡片</div>
            <p><strong>职责：</strong>提供最丰富的视觉效果和交互体验的付费墙卡片。</p>
            
            <div class="features">
                <strong>设计特色：</strong>
                <ul>
                    <li>🌈 精美的渐变背景和装饰元素</li>
                    <li>✨ 丰富的动画效果（悬停、脉冲、旋转等）</li>
                    <li>👑 支持会员升级和单篇解锁两种选项</li>
                    <li>📱 完整的响应式设计</li>
                    <li>🌙 深色模式支持</li>
                </ul>
            </div>

            <div class="usage-scenario">
                <strong>适用场景：</strong>
                <ul>
                    <li>文章详情页的主要付费墙展示</li>
                    <li>需要强调付费内容价值的重要位置</li>
                    <li>有充足展示空间的页面区域</li>
                </ul>
            </div>
        </div>

        <div class="component-card">
            <div class="component-title">📦 PaywallCardCompact.tsx - 紧凑版卡片</div>
            <p><strong>职责：</strong>在空间有限的场景下提供简洁而有效的付费墙展示。</p>
            
            <div class="features">
                <strong>设计特色：</strong>
                <ul>
                    <li>📏 简洁的横向布局设计</li>
                    <li>🎯 保留核心功能和视觉效果</li>
                    <li>💾 更小的占用空间</li>
                    <li>📱 优化的移动端显示</li>
                </ul>
            </div>

            <div class="usage-scenario">
                <strong>适用场景：</strong>
                <ul>
                    <li>文章列表中的付费墙提示</li>
                    <li>侧边栏或小部件中的付费墙</li>
                    <li>移动端优化显示</li>
                    <li>需要节省垂直空间的场景</li>
                </ul>
            </div>
        </div>

        <h2 id="data-flow">4. 数据流向分析</h2>

        <h3>🔄 初始化阶段</h3>
        <div class="code-block">
1. 文章模板传入初始数据 → PostContentSmart
   - postId, initialContent, unlockPrice等

2. PostContentSmart检测付费墙标记
   - 检查 initialContent.includes('fd-member-access-denied')

3. 如果用户已登录且有付费墙 → 发起GraphQL查询
   - 获取完整内容和权限信息
        </div>

        <h3>🎯 渲染决策阶段</h3>
        <div class="code-block">
1. PostContentSmart → PaywallRenderer
   - 传递内容和配置参数

2. PaywallRenderer解析HTML内容
   - 检测付费墙标记
   - 提取链接信息
   - 清理HTML内容

3. 选择合适的卡片组件
   - variant='default' → PaywallCard
   - variant='compact' → PaywallCardCompact
        </div>

        <h3>👆 用户交互阶段</h3>
        <div class="code-block">
1. 用户点击解锁按钮 → PaywallCard
2. 触发onUnlock回调 → PostContentSmart
3. 调用createUnlockOrder API (待实现)
4. 跳转到支付页面
        </div>

        <h2 id="usage">5. 使用方法</h2>

        <h3>📝 基础使用</h3>
        <div class="code-block">
import PaywallCard from '@/components/post/PaywallCard';

&lt;PaywallCard
  postId={123}
  postTitle="文章标题"
  requiredMemberLevel={2}
  unlockPrice={9.99}
  isUnlocked={false}
  onUnlock={handleUnlock}
/&gt;
        </div>

        <h3>🏗️ 在文章模板中使用</h3>
        <div class="code-block">
import PostContentSmart from '@/components/post/PostContentSmart';

&lt;PostContentSmart 
  postId={post.databaseId} 
  initialContent={post.content}
  postTitle={post.title}
  unlockPrice={post.unlockPrice}
  requiredMemberLevel={post.requiredMemberLevel}
  isUnlocked={post.isUnlockedByCurrentUser}
  paywallVariant="default" // 或 "compact"
/&gt;
        </div>

        <h3>⚙️ 属性配置表</h3>
        <table class="props-table">
            <thead>
                <tr>
                    <th>属性</th>
                    <th>类型</th>
                    <th>默认值</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>postId</td>
                    <td>number</td>
                    <td>-</td>
                    <td>文章ID（必需）</td>
                </tr>
                <tr>
                    <td>postTitle</td>
                    <td>string</td>
                    <td>-</td>
                    <td>文章标题（必需）</td>
                </tr>
                <tr>
                    <td>requiredMemberLevel</td>
                    <td>number</td>
                    <td>0</td>
                    <td>所需会员等级</td>
                </tr>
                <tr>
                    <td>unlockPrice</td>
                    <td>number</td>
                    <td>0</td>
                    <td>单篇解锁价格</td>
                </tr>
                <tr>
                    <td>isUnlocked</td>
                    <td>boolean</td>
                    <td>false</td>
                    <td>是否已解锁</td>
                </tr>
                <tr>
                    <td>paywallVariant</td>
                    <td>'default' | 'compact'</td>
                    <td>'default'</td>
                    <td>付费墙样式变体</td>
                </tr>
                <tr>
                    <td>onUnlock</td>
                    <td>function</td>
                    <td>-</td>
                    <td>解锁回调函数</td>
                </tr>
            </tbody>
        </table>

        <h2 id="customization">6. 定制化配置</h2>

        <h3>🎨 样式定制</h3>
        <p>组件使用Tailwind CSS构建，支持通过CSS变量进行定制：</p>
        <div class="code-block">
/* 自定义渐变色 */
.paywall-card {
  background: linear-gradient(135deg, var(--paywall-bg-start), var(--paywall-bg-end));
}

/* 自定义动画时长 */
.paywall-card {
  transition-duration: var(--paywall-transition-duration, 300ms);
}
        </div>

        <h3>🔗 自定义链接</h3>
        <div class="code-block">
&lt;PaywallCard
  loginUrl="/custom/login"
  registerUrl="/custom/register"
  upgradeUrl="/custom/upgrade"
/&gt;
        </div>

        <h3>🎭 主题配置</h3>
        <div class="code-block">
interface PaywallTheme {
  colors: {
    primary: string;
    secondary: string;
    background: string;
  };
  animations: {
    duration: number;
    easing: string;
  };
}
        </div>

        <h2 id="demo">7. 演示和测试</h2>

        <div class="success">
            <strong>🎮 演示页面：</strong> <code>/demo/paywall</code><br>
            <strong>📁 组件位置：</strong> <code>fd-frontend/src/components/post/</code><br>
            <strong>📖 详细文档：</strong> <code>fd-frontend/src/components/post/README.md</code>
        </div>

        <h3>🧪 测试场景</h3>
        <div class="features">
            <ul>
                <li>✅ 未登录用户看到登录/注册选项</li>
                <li>✅ 已登录用户看到升级/解锁选项</li>
                <li>✅ 响应式布局在各设备正常显示</li>
                <li>✅ 深色模式自动适配</li>
                <li>✅ 动画效果流畅运行</li>
                <li>✅ HTML解析和替换正确执行</li>
            </ul>
        </div>

        <h3>🚀 下一步开发计划</h3>
        <div class="warning">
            <strong>待实现功能：</strong>
            <ul>
                <li>🔧 实现 <code>createUnlockOrder</code> GraphQL变更</li>
                <li>💳 集成支付流程处理</li>
                <li>🔄 添加实际的解锁功能</li>
                <li>⚠️ 完善错误处理和加载状态</li>
                <li>♿ 添加无障碍访问支持</li>
                <li>⚡ 性能优化和代码分割</li>
            </ul>
        </div>

        <div class="success">
            <strong>🎉 项目成果：</strong><br>
            成功将简陋的HTML付费墙升级为现代化的精致卡片UI，大幅提升了用户体验和视觉效果。
            组件架构清晰，扩展性强，为后续的支付功能集成奠定了坚实基础。
        </div>
    </div>
</body>
</html>
