<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Future Decade - 文章详情页路由修改文档 V1.6.6</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3, h4 {
            color: #1a73e8;
            margin-top: 24px;
        }
        h1 {
            font-size: 28px;
            border-bottom: 2px solid #eaecef;
            padding-bottom: 12px;
        }
        h2 {
            font-size: 24px;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 8px;
        }
        code {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            background-color: #f6f8fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 0.9em;
        }
        pre {
            background-color: #f6f8fa;
            border-radius: 6px;
            padding: 16px;
            overflow: auto;
            line-height: 1.45;
        }
        pre code {
            background-color: transparent;
            padding: 0;
        }
        .code-block {
            position: relative;
            margin: 16px 0;
        }
        .code-header {
            background-color: #e1e4e8;
            color: #24292e;
            border-radius: 6px 6px 0 0;
            padding: 8px 16px;
            font-weight: 600;
            font-size: 14px;
        }
        .code-block pre {
            margin-top: 0;
            border-top-left-radius: 0;
            border-top-right-radius: 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
        }
        th, td {
            border: 1px solid #dfe2e5;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f6f8fa;
            font-weight: 600;
        }
        tr:nth-child(even) {
            background-color: #f6f8fa;
        }
        .note {
            background-color: #e1f5fe;
            border-left: 4px solid #03a9f4;
            padding: 12px 16px;
            margin: 16px 0;
            border-radius: 0 4px 4px 0;
        }
        .warning {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 12px 16px;
            margin: 16px 0;
            border-radius: 0 4px 4px 0;
        }
        .success {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 12px 16px;
            margin: 16px 0;
            border-radius: 0 4px 4px 0;
        }
    </style>
</head>
<body>
    <h1>文章详情页路由修改 - V1.6.6</h1>
    <p>
        <strong>版本:</strong> 1.6.6<br>
        <strong>更新日期:</strong> <span id="current-date"></span><br>
    </p>

    <div class="note">
        <p><strong>摘要</strong>：本文档详细说明了将文章详情页路由从 <code>/[prefix]/[uuid]/[slug]</code> 修改为 <code>/post/[uuid]/[slug]</code> 的实现细节，保持了与后端设置的兼容性。</p>
    </div>

    <h2>1. 背景与目标</h2>
    <p>之前的文章路由结构使用动态前缀，导致以下问题：</p>
    <ul>
        <li>URL结构不一致（根据后台设置前缀可能不同）</li>
        <li>SEO不友好</li>
        <li>用户难以记忆</li>
    </ul>
    <p>新的路由方案目标：</p>
    <ul>
        <li>统一使用 <code>/post/[uuid]/[slug]</code> 格式</li>
        <li>保留与后台设置的兼容（内部仍然处理前缀）</li>
        <li>支持从旧路由重定向到新路由</li>
        <li>保持所有现有功能正常工作</li>
    </ul>

    <h2>2. 实现方案</h2>
    <p>实现分为以下几个部分：</p>
    <ol>
        <li>创建新的路由结构</li>
        <li>修改中间件以支持URL重写</li>
        <li>更新URL构建函数</li>
        <li>更新引用文章URL的页面</li>
        <li>删除旧路由</li>
    </ol>

    <h2>3. 详细实现</h2>

    <h3>3.1 创建新的路由结构</h3>
    <p>创建新的文章详情页组件：</p>
    <div class="code-block">
        <div class="code-header">fd-frontend/src/app/post/[uuid]/[slug]/page.tsx</div>
<pre><code>'use client';

import React from 'react';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { usePost, useRoutePrefixes } from '@/hooks';

// 设置重新验证时间（1小时）
export const revalidate = 3600;

// 比较两个slug是否匹配
const slugsMatch = (slug1: string, slug2: string) => {
  // 标准化和解码slug
  const normalizeSlug = (slug: string) => decodeURIComponent(slug).toLowerCase().replace(/-/g, '');
  
  // 对于中文字符，允许更宽松的比较
  const containsChineseChar = (text: string) => /[\u4e00-\u9fa5]/.test(text);
  
  if (containsChineseChar(slug1) || containsChineseChar(slug2)) {
    // 中文URL允许部分匹配
    return normalizeSlug(slug1).includes(normalizeSlug(slug2)) || 
           normalizeSlug(slug2).includes(normalizeSlug(slug1));
  }
  
  // 非中文URL执行精确匹配
  return normalizeSlug(slug1) === normalizeSlug(slug2);
};

// 生成页面元数据
export async function generateMetadata({ params }) {
  const { uuid, slug } = params;
  const post = await fetchPostByUuid(uuid);
  
  if (!post) return { title: '文章未找到' };
  
  return {
    title: post.title,
    description: post.excerpt?.replace(/<[^>]*>?/gm, '').substring(0, 160) || '',
    openGraph: {
      title: post.title,
      description: post.excerpt?.replace(/<[^>]*>?/gm, '').substring(0, 160) || '',
      url: `/post/${uuid}/${slug}`,
      type: 'article',
      images: post.featuredImage?.node?.sourceUrl ? [
        { url: post.featuredImage.node.sourceUrl }
      ] : [],
    }
  };
}

// 文章详情页组件
export default function ArticlePage({ params }: { params: { uuid: string; slug: string } }) {
  const { uuid, slug } = params;
  const { post, loading, error } = usePost({ uuid });
  const { prefixes } = useRoutePrefixes();
  
  // 加载中状态
  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 animate-pulse">
        <div className="h-8 bg-gray-200 rounded w-3/4 mb-6"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/3 mb-6"></div>
        <div className="h-64 bg-gray-200 rounded mb-6"></div>
        <div className="h-4 bg-gray-200 rounded mb-2"></div>
        <div className="h-4 bg-gray-200 rounded mb-2"></div>
        <div className="h-4 bg-gray-200 rounded mb-2"></div>
      </div>
    );
  }
  
  // 错误状态
  if (error) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="bg-red-50 text-red-500 p-4 rounded-lg">
          <h2 className="text-lg font-medium mb-2">加载出错</h2>
          <p>{error.message}</p>
        </div>
      </div>
    );
  }
  
  // 文章不存在
  if (!post) {
    notFound();
  }
  
  // 校验slug是否匹配，不匹配则返回404
  if (!slugsMatch(post.slug, slug)) {
    notFound();
  }
  
  return (
    <article className="container mx-auto py-8 px-4">
      <header className="mb-8">
        <h1 className="text-3xl font-bold mb-4">{post.title}</h1>
        <div className="flex flex-wrap items-center text-gray-600 mb-4">
          <time dateTime={post.date} className="mr-4">
            {new Date(post.date).toLocaleDateString('zh-CN')}
          </time>
          
          {post.author?.node && (
            <span className="mr-4">
              作者: {post.author.node.name}
            </span>
          )}
          
          {post.categories?.nodes?.length > 0 && (
            <div className="mr-4">
              分类：
              {post.categories.nodes.map((category, index) => (
                <span key={category.id}>
                  <Link href={`/category/${category.slug}`} className="text-blue-600 hover:text-blue-800">
                    {category.name}
                  </Link>
                  {index < post.categories.nodes.length - 1 && ", "}
                </span>
              ))}
            </div>
          )}
        </div>
      </header>
      
      {post.featuredImage?.node?.sourceUrl && (
        <div className="mb-8">
          <img
            src={post.featuredImage.node.sourceUrl}
            alt={post.featuredImage.node.altText || post.title}
            className="w-full h-auto rounded-lg"
          />
        </div>
      )}
      
      <div 
        className="prose prose-lg max-w-none"
        dangerouslySetInnerHTML={{ __html: post.content }}
      />
      
      {post.tags?.nodes?.length > 0 && (
        <div className="mt-8 pt-6 border-t border-gray-200">
          <h3 className="text-lg font-medium mb-3">标签</h3>
          <div className="flex flex-wrap gap-2">
            {post.tags.nodes.map(tag => (
              <Link 
                key={tag.id}
                href={`/tag/${tag.slug}`}
                className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-md text-sm"
              >
                {tag.name}
              </Link>
            ))}
          </div>
        </div>
      )}
    </article>
  );
}</code></pre>
    </div>

    <h3>3.2 修改中间件</h3>
    <p>更新中间件以支持URL重写和重定向：</p>
    <div class="code-block">
        <div class="code-header">fd-frontend/src/middleware.ts</div>
<pre><code>import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  try {
    const url = request.nextUrl.clone();
    const { pathname } = url;
    const segments = pathname.split('/').filter(Boolean);

    // 读取环境变量获取配置
    const postPrefix = process.env.NEXT_PUBLIC_POST_PREFIX || 'article';
    const categoryPrefix = process.env.NEXT_PUBLIC_CATEGORY_PREFIX || 'category';
    const tagPrefix = process.env.NEXT_PUBLIC_TAG_PREFIX || 'tag';
    const categoryIndexRoute = process.env.NEXT_PUBLIC_CATEGORY_INDEX_ROUTE || 'categories';
    const tagIndexRoute = process.env.NEXT_PUBLIC_TAG_INDEX_ROUTE || 'tags';

    // 有效的UUID模式（8个字符的十六进制数字或短UUID）
    const isValidUuid = (str: string) => /^[0-9a-f]{8}$/.test(str);

    // 处理自定义路由

    // 1. 处理分类索引页URL重写
    if (pathname === `/${categoryIndexRoute}`) {
      // 将 /categories 内部重写为 /category-index
      url.pathname = '/category-index';
      return NextResponse.rewrite(url);
    }

    // 2. 处理标签索引页URL重写
    if (pathname === `/${tagIndexRoute}`) {
      // 将 /tags 内部重写为 /tag-index
      url.pathname = '/tag-index';
      return NextResponse.rewrite(url);
    }

    // 3. 处理 /category-index 重定向到自定义路由
    if (pathname === '/category-index') {
      url.pathname = `/${categoryIndexRoute}`;
      return NextResponse.redirect(url);
    }

    // 4. 处理 /tag-index 重定向到自定义路由
    if (pathname === '/tag-index') {
      url.pathname = `/${tagIndexRoute}`;
      return NextResponse.redirect(url);
    }

    // 5. 处理 /post/[uuid]/[slug] 内部重写到 /[prefix]/[uuid]/[slug]
    if (segments.length === 3 && segments[0] === 'post' && isValidUuid(segments[1])) {
      // 将 /post/[uuid]/[slug] 重写为 /[prefix]/[uuid]/[slug]
      url.pathname = `/${postPrefix}/${segments[1]}/${segments[2]}`;
      return NextResponse.rewrite(url);
    }

    // 6. 处理 /[prefix]/[uuid]/[slug] 重定向到 /post/[uuid]/[slug]
    if (segments.length === 3 && segments[0] === postPrefix && isValidUuid(segments[1])) {
      // 将 /[prefix]/[uuid]/[slug] 重定向到 /post/[uuid]/[slug]
      url.pathname = `/post/${segments[1]}/${segments[2]}`;
      return NextResponse.redirect(url);
    }

  } catch (error) {
    console.error("错误发生在中间件中:", error);
  }

  // 如果没有匹配的路由处理，继续请求
  return NextResponse.next();
}</code></pre>
    </div>

    <h3>3.3 更新URL构建函数</h3>
    <p>更新URL构建函数以支持新的路由结构：</p>
    <div class="code-block">
        <div class="code-header">fd-frontend/src/utils/url-builder.ts</div>
<pre><code>import { RoutePrefixes } from '../types/routes';

/**
 * 构建文章详情页URL
 * 用于外部显示的用户友好URL
 * @param uuid 文章UUID
 * @param slug 文章slug
 * @param prefixes 路由前缀配置
 * @returns 文章详情页URL
 */
export const buildPostUrl = (uuid: string, slug: string, prefixes?: RoutePrefixes) => {
  // 统一使用内部路由URL /post/[uuid]/[slug]
  return `/post/${uuid}/${slug}`;
};

/**
 * 构建内部路由文章详情页URL
 * 仅用于内部路由，不直接显示给用户
 * @param uuid 文章UUID
 * @param slug 文章slug
 * @param prefixes 路由前缀配置
 * @returns 内部文章详情页URL
 */
export const buildInternalPostUrl = (uuid: string, slug: string, prefixes?: RoutePrefixes) => {
  const prefix = prefixes?.postPrefix || 'article';
  return `/${prefix}/${uuid}/${slug}`;
};

/**
 * 构建分类详情页URL
 * @param slug 分类slug
 * @param prefixes 路由前缀配置
 * @returns 分类详情页URL
 */
export const buildCategoryUrl = (slug: string, prefixes?: RoutePrefixes) => {
  const prefix = prefixes?.categoryPrefix || 'category';
  return `/${prefix}/${slug}`;
};

/**
 * 构建标签详情页URL
 * @param slug 标签slug
 * @param prefixes 路由前缀配置
 * @returns 标签详情页URL
 */
export const buildTagUrl = (slug: string, prefixes?: RoutePrefixes) => {
  const prefix = prefixes?.tagPrefix || 'tag';
  return `/${prefix}/${slug}`;
};</code></pre>
    </div>

    <h3>3.4 更新页面组件</h3>
    <p>更新引用文章URL的页面组件（Category、Tag等页面）：</p>
    <div class="code-block">
        <div class="code-header">更新文章链接示例</div>
<pre><code>// 更新前
const getPostUrl = (postSlug: string, shortUuid: string) => {
  return prefixes?.postPrefix 
    ? `/${prefixes.postPrefix}/${shortUuid}/${postSlug}`
    : `/${shortUuid}/${postSlug}`;
};

// 更新后 - 使用统一的URL构建函数
import { buildPostUrl } from '@/utils/url-builder';

const getPostUrl = (postSlug: string, shortUuid: string) => {
  return buildPostUrl(shortUuid, postSlug, prefixes);
};</code></pre>
    </div>

    <h3>3.5 删除旧路由</h3>
    <p>删除旧的路由文件，并创建脚本自动化此过程：</p>
    <div class="code-block">
        <div class="code-header">remove-old-routes.sh</div>
<pre><code>#!/bin/bash

# 确保在正确目录执行
echo "正在切换到项目目录..."
cd "$(dirname "$0")"

# 确认删除操作
echo "警告：此操作将删除旧的文章路由目录"
echo "请确认新的路由 '/post/[uuid]/[slug]' 已正常工作"
echo "确认删除? (y/n)"
read -r confirm

if [ "$confirm" != "y" ]; then
  echo "操作已取消"
  exit 0
fi

# 执行删除操作
echo "开始删除旧路由..."
rm -rf fd-frontend/src/app/\[prefix\]/\[uuid\]/\[slug\]/page.tsx
rmdir fd-frontend/src/app/\[prefix\]/\[uuid\]/\[slug\] 2>/dev/null || true
rmdir fd-frontend/src/app/\[prefix\]/\[uuid\] 2>/dev/null || true
rmdir fd-frontend/src/app/\[prefix\] 2>/dev/null || true

echo "旧路由已删除"
echo "请重启开发服务器以确保路由更新生效"</code></pre>
    </div>

    <h2>4. 路由流程</h2>
    <p>新的路由流程如下：</p>
    <ol>
        <li><strong>用户访问</strong>：用户访问 <code>/post/[uuid]/[slug]</code> URL</li>
        <li><strong>中间件处理</strong>：中间件检测URL模式，将 <code>/post/[uuid]/[slug]</code> 内部重写为 <code>/[prefix]/[uuid]/[slug]</code></li>
        <li><strong>页面渲染</strong>：Next.js 渲染 <code>/post/[uuid]/[slug]</code> 页面组件</li>
        <li><strong>数据获取</strong>：页面组件使用 UUID 从 GraphQL API 获取文章数据</li>
        <li><strong>页面输出</strong>：返回渲染后的页面给用户</li>
    </ol>

    <p>对于旧的URL结构 <code>/[prefix]/[uuid]/[slug]</code>：</p>
    <ol>
        <li><strong>用户访问</strong>：用户访问 <code>/[prefix]/[uuid]/[slug]</code> URL</li>
        <li><strong>中间件处理</strong>：中间件检测URL模式，将 <code>/[prefix]/[uuid]/[slug]</code> 重定向到 <code>/post/[uuid]/[slug]</code></li>
        <li><strong>页面渲染</strong>：流程同上</li>
    </ol>

    <h2>5. 注意事项</h2>
    <div class="warning">
        <p><strong>重要</strong>：所有显示文章链接的组件都需要使用 <code>buildPostUrl</code> 函数构建URL。</p>
    </div>
    <ul>
        <li>强烈建议使用 <code>url-builder.ts</code> 中的函数构建所有URL，避免硬编码URL格式</li>
        <li>新的URL格式在所有地方都使用 <code>/post/</code> 作为前缀，不再使用环境变量中的前缀配置</li>
        <li>中间件处理会保持与旧URL格式的兼容，允许旧链接正常工作</li>
        <li>后端仍然可以通过设置自定义文章前缀，内部路由会处理映射关系</li>
    </ul>

    <h2>6. 性能与SEO考虑</h2>
    <p>新的路由结构带来以下好处：</p>
    <ul>
        <li><strong>SEO优化</strong>：统一的URL结构更利于搜索引擎索引</li>
        <li><strong>性能改进</strong>：使用URL重写代替重定向，减少HTTP请求</li>
        <li><strong>更好的用户体验</strong>：简洁一致的URL格式，易于分享和记忆</li>
    </ul>

    <script>
        // 获取当前日期并显示在文档中
        document.getElementById('current-date').textContent = new Date().toISOString().split('T')[0];
    </script>
</body>
</html> 