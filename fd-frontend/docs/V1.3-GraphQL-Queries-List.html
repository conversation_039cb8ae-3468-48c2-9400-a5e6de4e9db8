<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GraphQL查询列表 - Future Decade</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #2c3e50;
      border-bottom: 2px solid #eee;
      padding-bottom: 10px;
    }
    h2 {
      color: #3498db;
      margin-top: 20px;
      border-bottom: 1px solid #eee;
      padding-bottom: 5px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px 12px;
      text-align: left;
    }
    th {
      background-color: #f8f8f8;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    a {
      color: #3498db;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
    .back-to-top {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #3498db;
      color: white;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-decoration: none;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
  </style>
</head>
<body>
  <a href="#top" class="back-to-top">↑</a>
  
  <h1 id="top">GraphQL查询列表</h1>
  <p>本文档列出了Future Decade前端项目中使用的所有GraphQL片段和查询。详细的查询定义可在<a href="V1.3-GraphQL-Queries-Reference.html">完整参考文档</a>中找到。</p>
  
  <h2 id="fragments">片段定义</h2>
  <table>
    <thead>
      <tr>
        <th>片段名称</th>
        <th>描述</th>
        <th>类型</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>POST_FRAGMENT</td>
        <td>文章基本信息片段</td>
        <td>Post</td>
      </tr>
      <tr>
        <td>POST_DETAIL_FRAGMENT</td>
        <td>文章详细信息片段</td>
        <td>Post</td>
      </tr>
      <tr>
        <td>CATEGORY_FRAGMENT</td>
        <td>分类信息片段</td>
        <td>Category</td>
      </tr>
      <tr>
        <td>TAG_FRAGMENT</td>
        <td>标签信息片段</td>
        <td>Tag</td>
      </tr>
      <tr>
        <td>USER_FRAGMENT</td>
        <td>用户信息片段</td>
        <td>User</td>
      </tr>
      <tr>
        <td>MEDIA_FRAGMENT</td>
        <td>媒体文件信息片段</td>
        <td>MediaItem</td>
      </tr>
      <tr>
        <td>MENU_ITEM_FRAGMENT</td>
        <td>菜单项信息片段</td>
        <td>MenuItem</td>
      </tr>
      <tr>
        <td>CUSTOM_POST_FRAGMENT</td>
        <td>自定义文章类型信息片段</td>
        <td>ContentNode</td>
      </tr>
      <tr>
        <td>COMMENT_FRAGMENT</td>
        <td>评论信息片段</td>
        <td>Comment</td>
      </tr>
    </tbody>
  </table>
  
  <h2 id="post-queries">文章查询</h2>
  <table>
    <thead>
      <tr>
        <th>查询名称</th>
        <th>描述</th>
        <th>主要参数</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>GET_LATEST_POSTS</td>
        <td>获取最新文章列表</td>
        <td>first: Int</td>
      </tr>
      <tr>
        <td>GET_POST_BY_SLUG</td>
        <td>通过slug获取单个文章</td>
        <td>slug: ID!</td>
      </tr>
      <tr>
        <td>GET_POST_BY_ID</td>
        <td>通过ID获取单个文章</td>
        <td>id: ID!</td>
      </tr>
      <tr>
        <td>SEARCH_POSTS</td>
        <td>搜索文章</td>
        <td>search: String!, first: Int</td>
      </tr>
      <tr>
        <td>GET_POSTS_BY_CATEGORY</td>
        <td>获取指定分类下的文章</td>
        <td>categoryId: Int!</td>
      </tr>
      <tr>
        <td>GET_POSTS_BY_TAG</td>
        <td>获取指定标签下的文章</td>
        <td>tagId: String!</td>
      </tr>
      <tr>
        <td>GET_POSTS_BY_AUTHOR</td>
        <td>获取指定作者的文章</td>
        <td>authorId: Int!, first: Int</td>
      </tr>
      <tr>
        <td>GET_HOME_DATA</td>
        <td>获取首页数据</td>
        <td>featuredPostsCount: Int, recentPostsCount: Int</td>
      </tr>
    </tbody>
  </table>
  
  <h2 id="page-queries">页面查询</h2>
  <table>
    <thead>
      <tr>
        <th>查询名称</th>
        <th>描述</th>
        <th>主要参数</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>GET_PAGES</td>
        <td>获取所有页面</td>
        <td>first: Int</td>
      </tr>
      <tr>
        <td>GET_PAGE_BY_SLUG</td>
        <td>通过slug获取单个页面</td>
        <td>slug: ID!</td>
      </tr>
      <tr>
        <td>GET_PAGE_BY_ID</td>
        <td>通过ID获取单个页面</td>
        <td>id: ID!</td>
      </tr>
    </tbody>
  </table>
  
  <h2 id="taxonomy-queries">分类法查询</h2>
  <table>
    <thead>
      <tr>
        <th>查询名称</th>
        <th>描述</th>
        <th>主要参数</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>GET_CATEGORIES</td>
        <td>获取所有分类</td>
        <td>-</td>
      </tr>
      <tr>
        <td>GET_CATEGORY_BY_SLUG</td>
        <td>通过slug获取单个分类</td>
        <td>slug: ID!</td>
      </tr>
      <tr>
        <td>GET_TAGS</td>
        <td>获取所有标签</td>
        <td>-</td>
      </tr>
      <tr>
        <td>GET_TAXONOMIES</td>
        <td>获取所有分类法</td>
        <td>-</td>
      </tr>
      <tr>
        <td>GET_TAXONOMY_TERMS</td>
        <td>获取指定分类法下的所有项</td>
        <td>taxonomy: TaxonomyEnum!</td>
      </tr>
      <tr>
        <td>GET_TAXONOMY_TERM_BY_ID</td>
        <td>通过ID获取分类法条目</td>
        <td>id: ID!, taxonomy: TaxonomyEnum!</td>
      </tr>
      <tr>
        <td>GET_TAXONOMY_TERM_BY_SLUG</td>
        <td>通过slug获取分类法条目</td>
        <td>slug: [String]!, taxonomy: TaxonomyEnum!</td>
      </tr>
      <tr>
        <td>GET_POSTS_BY_TAXONOMY</td>
        <td>获取指定分类法条目下的文章</td>
        <td>taxonomy: TaxonomyEnum!, termId: ID!, first: Int</td>
      </tr>
      <tr>
        <td>GET_TAG_DETAIL</td>
        <td>获取标签详情及其文章</td>
        <td>slug: ID!, first: Int</td>
      </tr>
      <tr>
        <td>GET_CATEGORY_DETAIL</td>
        <td>获取分类详情及其文章</td>
        <td>slug: ID!, first: Int</td>
      </tr>
    </tbody>
  </table>
  
  <h2 id="user-queries">用户查询</h2>
  <table>
    <thead>
      <tr>
        <th>查询名称</th>
        <th>描述</th>
        <th>主要参数</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>GET_USER</td>
        <td>通过ID获取用户详情</td>
        <td>id: ID!</td>
      </tr>
      <tr>
        <td>GET_USERS</td>
        <td>获取用户列表</td>
        <td>first: Int</td>
      </tr>
    </tbody>
  </table>
  
  <h2 id="media-queries">媒体查询</h2>
  <table>
    <thead>
      <tr>
        <th>查询名称</th>
        <th>描述</th>
        <th>主要参数</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>GET_MEDIA</td>
        <td>通过ID获取媒体详情</td>
        <td>id: ID!</td>
      </tr>
    </tbody>
  </table>
  
  <h2 id="menu-queries">菜单查询</h2>
  <table>
    <thead>
      <tr>
        <th>查询名称</th>
        <th>描述</th>
        <th>主要参数</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>GET_MENUS</td>
        <td>获取所有菜单及其菜单项</td>
        <td>-</td>
      </tr>
      <tr>
        <td>GET_MENU_BY_LOCATION</td>
        <td>获取指定位置的菜单</td>
        <td>location: MenuLocationEnum!</td>
      </tr>
    </tbody>
  </table>
  
  <h2 id="custom-post-queries">自定义内容类型查询</h2>
  <table>
    <thead>
      <tr>
        <th>查询名称</th>
        <th>描述</th>
        <th>主要参数</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>GET_CUSTOM_POSTS</td>
        <td>获取指定自定义内容类型的列表</td>
        <td>type: ContentTypeEnum!, first: Int</td>
      </tr>
      <tr>
        <td>GET_CUSTOM_POST_BY_SLUG</td>
        <td>通过slug获取自定义内容类型详情</td>
        <td>type: ContentTypeEnum!, slug: String!</td>
      </tr>
      <tr>
        <td>GET_CUSTOM_POST_BY_ID</td>
        <td>通过ID获取自定义内容类型详情</td>
        <td>type: ContentTypeEnum!, id: ID!</td>
      </tr>
    </tbody>
  </table>
  
  <h2 id="settings-queries">设置查询</h2>
  <table>
    <thead>
      <tr>
        <th>查询名称</th>
        <th>描述</th>
        <th>主要参数</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>GET_ALL_SETTINGS</td>
        <td>获取所有WordPress设置</td>
        <td>-</td>
      </tr>
      <tr>
        <td>GET_GENERAL_SETTINGS</td>
        <td>获取WordPress一般设置</td>
        <td>-</td>
      </tr>
      <tr>
        <td>GET_READING_SETTINGS</td>
        <td>获取WordPress阅读设置</td>
        <td>-</td>
      </tr>
      <tr>
        <td>GET_DISCUSSION_SETTINGS</td>
        <td>获取WordPress讨论设置</td>
        <td>-</td>
      </tr>
      <tr>
        <td>GET_WRITING_SETTINGS</td>
        <td>获取WordPress写作设置</td>
        <td>-</td>
      </tr>
    </tbody>
  </table>
  
  <h2 id="comment-queries">评论查询</h2>
  <table>
    <thead>
      <tr>
        <th>查询名称</th>
        <th>描述</th>
        <th>主要参数</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>GET_POST_COMMENTS</td>
        <td>获取文章的评论列表</td>
        <td>postId: ID!, first: Int</td>
      </tr>
      <tr>
        <td>GET_COMMENT</td>
        <td>通过ID获取评论详情</td>
        <td>id: ID!</td>
      </tr>
      <tr>
        <td>GET_COMMENT_REPLIES</td>
        <td>获取评论的回复列表</td>
        <td>id: ID!, first: Int</td>
      </tr>
      <tr>
        <td>GET_COMMENTS_BY_STATUS</td>
        <td>按状态获取评论列表</td>
        <td>status: [CommentStatusEnum], first: Int</td>
      </tr>
    </tbody>
  </table>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 平滑滚动
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
          });
        });
      });
    });
  </script>
</body>
</html> 