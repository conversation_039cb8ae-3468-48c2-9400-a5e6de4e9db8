<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V1.8.0 - 中间件URL处理系统重构</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1100px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3, h4 {
            color: #1a1a1a;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        h1 {
            font-size: 2.2em;
            text-align: center;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            font-size: 1.8em;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 5px;
        }
        h3 {
            font-size: 1.5em;
        }
        h4 {
            font-size: 1.3em;
        }
        p, li {
            font-size: 16px;
            margin-bottom: 1em;
        }
        code {
            font-family: '<PERSON><PERSON>', 'Monaco', 'Courier New', monospace;
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 0.9em;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow: auto;
            font-size: 0.9em;
            margin: 15px 0;
            line-height: 1.4;
        }
        pre code {
            background-color: transparent;
            padding: 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        blockquote {
            border-left: 4px solid #ddd;
            padding: 10px 15px;
            margin: 15px 0;
            background-color: #f9f9f9;
        }
        .important {
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        .warning {
            background-color: #f8d7da;
            padding: 10px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
        }
        .info {
            background-color: #d1ecf1;
            padding: 10px;
            border-left: 4px solid #17a2b8;
            margin: 15px 0;
        }
        .success {
            background-color: #d4edda;
            padding: 10px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        .route-table {
            width: 100%;
            border-collapse: collapse;
        }
        .route-table th,
        .route-table td {
            border: 1px solid #ddd;
            padding: 10px;
        }
        .route-table th {
            background-color: #f2f2f2;
        }
        .route-type {
            font-weight: bold;
            min-width: 100px;
        }
        .route-path {
            font-family: monospace;
            background-color: #f5f5f5;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .route-arrow {
            text-align: center;
            font-weight: bold;
            font-size: 1.2em;
            width: 50px;
        }
        .route-behavior {
            font-style: italic;
            color: #666;
        }
        .example {
            background-color: #eef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>V1.8.0 - 中间件URL处理系统重构</h1>
    
    <div class="info">
        <p><strong>版本:</strong> V1.8.0</p>
        <p><strong>日期:</strong> 2023年10月</p>
        <p><strong>功能:</strong> 实现更加简洁、用户友好的URL结构，处理自定义文章类型、分类法和归档页的路由</p>
    </div>

    <h2>1. 概述</h2>
    <p>本文档详细说明了V1.8.0版本中中间件的URL处理系统。该系统通过Next.js的中间件功能实现了高度灵活的URL路由处理，包括URL重写（内部路径映射）和重定向（对外可见的URL变化）。</p>
    
    <p>主要设计目标包括：</p>
    <ul>
        <li>为用户提供简洁、友好的URL结构</li>
        <li>内部保持一致的路由处理逻辑</li>
        <li>支持自定义文章类型、分类法和索引页</li>
        <li>去除冗余的路径前缀，如<code>/post-type/</code></li>
        <li>保持向后兼容性，确保旧的URL模式仍然有效</li>
    </ul>

    <h2>2. 完整路由结构</h2>
    
    <h3>2.1 自定义文章类型路由</h3>
    <table class="route-table">
        <tr>
            <th>操作类型</th>
            <th>来源路径</th>
            <th>目标路径</th>
            <th>行为</th>
            <th>说明</th>
        </tr>
        <tr>
            <td class="route-type">重定向</td>
            <td class="route-path">/post-type/[type]</td>
            <td class="route-path">/[type]</td>
            <td class="route-behavior">用户可见URL变化</td>
            <td>将自定义文章类型列表页的路径简化，去除/post-type/前缀</td>
        </tr>
        <tr>
            <td class="route-type">重写</td>
            <td class="route-path">/[type]</td>
            <td class="route-path">/post-type/[type]</td>
            <td class="route-behavior">内部映射，用户不可见</td>
            <td>在内部仍然使用原有的路由结构处理自定义文章类型列表页</td>
        </tr>
        <tr>
            <td class="route-type">重定向</td>
            <td class="route-path">/post-type/[type]/[uuid]/[slug]</td>
            <td class="route-path">/[type]/[uuid]/[slug]</td>
            <td class="route-behavior">用户可见URL变化</td>
            <td>将自定义文章类型详情页的路径简化，去除/post-type/前缀</td>
        </tr>
        <tr>
            <td class="route-type">重写</td>
            <td class="route-path">/[type]/[uuid]/[slug]</td>
            <td class="route-path">/post-type/[type]/[uuid]/[slug]</td>
            <td class="route-behavior">内部映射，用户不可见</td>
            <td>在内部仍然使用原有的路由结构处理自定义文章类型详情页</td>
        </tr>
    </table>
    
    <div class="example">
        <p><strong>示例:</strong></p>
        <p>旧URL: <code>/post-type/news</code> → 新URL: <code>/news</code>（文章类型列表页）</p>
        <p>旧URL: <code>/post-type/news/230921-123456/example-title</code> → 新URL: <code>/news/230921-123456/example-title</code>（文章详情页）</p>
    </div>

    <h3>2.2 文章路由</h3>
    <table class="route-table">
        <tr>
            <th>操作类型</th>
            <th>来源路径</th>
            <th>目标路径</th>
            <th>行为</th>
            <th>说明</th>
        </tr>
        <tr>
            <td class="route-type">重定向</td>
            <td class="route-path">/post/[uuid]/[slug]</td>
            <td class="route-path">/[prefix]/[uuid]/[slug]</td>
            <td class="route-behavior">用户可见URL变化</td>
            <td>将内部文章路径重定向到使用配置前缀的URL</td>
        </tr>
        <tr>
            <td class="route-type">重写</td>
            <td class="route-path">/[prefix]/[uuid]/[slug]</td>
            <td class="route-path">/post/[uuid]/[slug]</td>
            <td class="route-behavior">内部映射，用户不可见</td>
            <td>将带前缀的URL内部映射到post路径处理，前缀不匹配则返回404</td>
        </tr>
    </table>
    
    <div class="example">
        <p><strong>示例:</strong></p>
        <p>内部URL: <code>/post/230921-123456/example-title</code> → 配置前缀URL: <code>/article/230921-123456/example-title</code></p>
        <p>错误前缀URL: <code>/wrong-prefix/230921-123456/example-title</code> → 404错误</p>
    </div>

    <h3>2.3 分类和标签路由</h3>
    <table class="route-table">
        <tr>
            <th>操作类型</th>
            <th>来源路径</th>
            <th>目标路径</th>
            <th>行为</th>
            <th>说明</th>
        </tr>
        <tr>
            <td class="route-type">重定向</td>
            <td class="route-path">/category-index</td>
            <td class="route-path">/[categoryIndexRoute]</td>
            <td class="route-behavior">用户可见URL变化</td>
            <td>将内部分类索引页路径重定向到配置的自定义路径</td>
        </tr>
        <tr>
            <td class="route-type">重写</td>
            <td class="route-path">/[categoryIndexRoute]</td>
            <td class="route-path">/category-index</td>
            <td class="route-behavior">内部映射，用户不可见</td>
            <td>将自定义分类索引页路径内部映射到category-index处理</td>
        </tr>
        <tr>
            <td class="route-type">重定向</td>
            <td class="route-path">/tag-index</td>
            <td class="route-path">/[tagIndexRoute]</td>
            <td class="route-behavior">用户可见URL变化</td>
            <td>将内部标签索引页路径重定向到配置的自定义路径</td>
        </tr>
        <tr>
            <td class="route-type">重写</td>
            <td class="route-path">/[tagIndexRoute]</td>
            <td class="route-path">/tag-index</td>
            <td class="route-behavior">内部映射，用户不可见</td>
            <td>将自定义标签索引页路径内部映射到tag-index处理</td>
        </tr>
        <tr>
            <td class="route-type">重写</td>
            <td class="route-path">/[categoryPrefix]/[slug]</td>
            <td class="route-path">/category/[slug]</td>
            <td class="route-behavior">内部映射，用户不可见</td>
            <td>将带分类前缀的URL内部映射到category路径处理</td>
        </tr>
        <tr>
            <td class="route-type">重写</td>
            <td class="route-path">/[tagPrefix]/[slug]</td>
            <td class="route-path">/tag/[slug]</td>
            <td class="route-behavior">内部映射，用户不可见</td>
            <td>将带标签前缀的URL内部映射到tag路径处理</td>
        </tr>
        <tr>
            <td class="route-type">重定向</td>
            <td class="route-path">/category/[slug]</td>
            <td class="route-path">/[categoryPrefix]/[slug] 或 /[slug]</td>
            <td class="route-behavior">用户可见URL变化</td>
            <td>将内部分类路径重定向到带前缀URL或单级路径（无前缀时）</td>
        </tr>
        <tr>
            <td class="route-type">重定向</td>
            <td class="route-path">/tag/[slug]</td>
            <td class="route-path">/[tagPrefix]/[slug] 或 /[slug]</td>
            <td class="route-behavior">用户可见URL变化</td>
            <td>将内部标签路径重定向到带前缀URL或单级路径（无前缀时）</td>
        </tr>
    </table>
    
    <div class="example">
        <p><strong>示例:</strong></p>
        <p>内部URL: <code>/category-index</code> → 自定义URL: <code>/categories</code>（分类索引页）</p>
        <p>内部URL: <code>/tag-index</code> → 自定义URL: <code>/tags</code>（标签索引页）</p>
        <p>分类URL: <code>/cat/technology</code> → 内部处理: <code>/category/technology</code></p>
        <p>标签URL: <code>/tag/javascript</code> → 内部处理: <code>/tag/javascript</code></p>
    </div>

    <h3>2.4 自定义分类法路由</h3>
    <table class="route-table">
        <tr>
            <th>操作类型</th>
            <th>来源路径</th>
            <th>目标路径</th>
            <th>行为</th>
            <th>说明</th>
        </tr>
        <tr>
            <td class="route-type">重定向</td>
            <td class="route-path">/taxonomy/[taxonomy]/[slug]</td>
            <td class="route-path">/[taxonomy]/[slug]</td>
            <td class="route-behavior">用户可见URL变化</td>
            <td>将内部自定义分类法条目路径重定向到简化的URL</td>
        </tr>
        <tr>
            <td class="route-type">重写</td>
            <td class="route-path">/[taxonomy]/[slug]</td>
            <td class="route-path">/taxonomy/[taxonomy]/[slug]</td>
            <td class="route-behavior">内部映射，用户不可见</td>
            <td>将简化的自定义分类法条目URL内部映射到taxonomy路径处理</td>
        </tr>
        <tr>
            <td class="route-type">重定向</td>
            <td class="route-path">/taxonomy/[taxonomy]</td>
            <td class="route-path">/[taxonomy]</td>
            <td class="route-behavior">用户可见URL变化</td>
            <td>将内部自定义分类法归档页路径重定向到简化的URL</td>
        </tr>
        <tr>
            <td class="route-type">重写</td>
            <td class="route-path">/[taxonomy]</td>
            <td class="route-path">/taxonomy/[taxonomy]</td>
            <td class="route-behavior">内部映射，用户不可见</td>
            <td>当slug映射表返回taxonomy_archive类型时，将单级路径重写到taxonomy路径处理</td>
        </tr>
    </table>
    
    <div class="example">
        <p><strong>示例:</strong></p>
        <p>内部URL: <code>/taxonomy/region/asia</code> → 用户友好URL: <code>/region/asia</code></p>
        <p>内部URL: <code>/taxonomy/region</code> → 用户友好URL: <code>/region</code></p>
    </div>

    <h3>2.5 页面路由</h3>
    <table class="route-table">
        <tr>
            <th>操作类型</th>
            <th>来源路径</th>
            <th>目标路径</th>
            <th>行为</th>
            <th>说明</th>
        </tr>
        <tr>
            <td class="route-type">重定向</td>
            <td class="route-path">/page/[slug]</td>
            <td class="route-path">/[slug]</td>
            <td class="route-behavior">用户可见URL变化</td>
            <td>将内部页面路径重定向到单级路径</td>
        </tr>
        <tr>
            <td class="route-type">重写</td>
            <td class="route-path">/[slug]</td>
            <td class="route-path">/page/[slug]</td>
            <td class="route-behavior">内部映射，用户不可见</td>
            <td>当slug映射表返回page类型时，将单级路径重写到page路径处理</td>
        </tr>
    </table>
    
    <div class="example">
        <p><strong>示例:</strong></p>
        <p>内部URL: <code>/page/about</code> → 用户友好URL: <code>/about</code></p>
        <p>内部URL: <code>/page/contact</code> → 用户友好URL: <code>/contact</code></p>
    </div>

    <h3>2.6 单级路径解析</h3>
    <p>对于单级路径（如<code>/news</code>或<code>/about</code>），系统会通过GraphQL查询<code>resolveSinglePathSlug</code>解析其类型，然后将其映射到相应的内部路径：</p>
    <table class="route-table">
        <tr>
            <th>解析类型</th>
            <th>来源路径</th>
            <th>目标路径</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>page</td>
            <td class="route-path">/[slug]</td>
            <td class="route-path">/page/[slug]</td>
            <td>页面类型重写到page路径</td>
        </tr>
        <tr>
            <td>category</td>
            <td class="route-path">/[slug]</td>
            <td class="route-path">/category/[slug]</td>
            <td>分类类型重写到category路径</td>
        </tr>
        <tr>
            <td>tag</td>
            <td class="route-path">/[slug]</td>
            <td class="route-path">/tag/[slug]</td>
            <td>标签类型重写到tag路径</td>
        </tr>
        <tr>
            <td>post_type</td>
            <td class="route-path">/[slug]</td>
            <td class="route-path">/post-type/[slug]</td>
            <td>自定义文章类型重写到post-type路径</td>
        </tr>
        <tr>
            <td>taxonomy_archive</td>
            <td class="route-path">/[slug]</td>
            <td class="route-path">/taxonomy/[taxonomy]</td>
            <td>自定义分类法归档页重写到taxonomy路径</td>
        </tr>
    </table>

    <h2>3. 中间件代码实现</h2>
    <p>中间件通过Next.js的中间件功能实现，主要处理以下几种URL操作：</p>
    <ul>
        <li><strong>重写 (Rewrite)</strong>: 内部路径映射，用户在浏览器中看不到URL变化</li>
        <li><strong>重定向 (Redirect)</strong>: 显式的URL变化，用户在浏览器中可以看到地址栏变化</li>
    </ul>

    <p>核心实现逻辑包括：</p>
    <pre><code>// 中间件入口函数
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const pathSegments = pathname.split('/').filter(Boolean);
  
  // UUID验证正则表达式 (YYMMDD-123456)
  const UUID_REGEX = /^\d{6}-\d{6}$/;
  
  try {
    // 获取路由前缀配置
    const prefixes = await fetchRoutePrefixes();
    
    // 处理自定义类型文章详情页 URL简化
    if (pathSegments.length === 4 && pathSegments[0] === 'post-type') {
      const [_, type, uuid, slug] = pathSegments;
      if (UUID_REGEX.test(uuid)) {
        return NextResponse.redirect(
          new URL(`/${type}/${uuid}/${slug}`, request.url)
        );
      }
    }
    
    // 处理简化后的自定义类型文章详情页 内部重写
    if (pathSegments.length === 3) {
      const [type, uuid, slug] = pathSegments;
      if (UUID_REGEX.test(uuid)) {
        const resolvedType = await resolveTypeSlug(type);
        if (resolvedType?.type === 'post_type') {
          return NextResponse.rewrite(
            new URL(`/post-type/${type}/${uuid}/${slug}`, request.url)
          );
        }
      }
    }
    
    // 处理自定义类型列表页 URL简化
    if (pathSegments.length === 2 && pathSegments[0] === 'post-type') {
      const [_, type] = pathSegments;
      return NextResponse.redirect(
        new URL(`/${type}`, request.url)
      );
    }
    
    // 处理简化后的自定义类型列表页 内部重写
    if (pathSegments.length === 1) {
      const potentialType = pathSegments[0];
      if (!isSpecialPath(potentialType)) {
        const resolvedSlug = await resolveSlug(potentialType);
        if (resolvedSlug?.type === 'post_type') {
          return NextResponse.rewrite(
            new URL(`/post-type/${potentialType}`, request.url)
          );
        }
      }
    }
    
    // 其他路由处理逻辑...
  } catch (error) {
    console.error('Error in middleware:', error);
  }
  
  return NextResponse.next();
}</code></pre>

    <h2>4. 升级说明</h2>
    <div class="info">
        <p>此版本对URL结构进行了重大改进，使用户界面更加友好并符合现代Web应用的URL设计惯例。主要变化包括：</p>
        <ul>
            <li>移除了<code>/post-type/</code>前缀，使自定义文章类型的URL更简洁</li>
            <li>保持了向后兼容性，旧的URL格式仍然有效</li>
            <li>统一了URL处理逻辑，使系统更加一致和可维护</li>
        </ul>
    </div>

    <h3>4.1 注意事项</h3>
    <div class="warning">
        <p><strong>重要提示:</strong></p>
        <ul>
            <li>实施此升级时，请确保先测试旧URL的重定向功能，再推广新的URL格式</li>
            <li>对于已被搜索引擎索引的URL，请考虑添加适当的<code>rel="canonical"</code>标签</li>
            <li>监控服务器日志，确保没有意外的404错误</li>
        </ul>
    </div>

    <h2>5. 测试要点</h2>
    <p>在实施此中间件升级时，请确保测试以下关键功能点：</p>
    <ol>
        <li>旧格式URL访问测试：确保所有旧格式的URL能正确重定向到新格式</li>
        <li>新格式URL访问测试：确保所有新格式的URL能正确加载相应内容</li>
        <li>单级路径解析测试：确保系统能正确识别和处理各种类型的单级路径</li>
        <li>自定义分类法路径测试：确保自定义分类法的URL能正确工作</li>
        <li>错误路径处理测试：确保对无效路径返回正确的404响应</li>
        <li>性能测试：确保中间件的多次解析和重写不会显著影响页面加载性能</li>
    </ol>

    <h2>6. 结论</h2>
    <p>V1.8.0版本的中间件实现为系统提供了更加灵活、用户友好的URL结构，同时保持了内部路由处理的一致性。这一升级大大改善了用户体验和系统的可维护性。</p>
    <p>在未来的版本中，我们将继续优化URL结构和路由处理逻辑，可能的改进方向包括：</p>
    <ul>
        <li>进一步优化slug映射表的查询性能</li>
        <li>增强错误处理和日志记录功能</li>
        <li>添加更细粒度的权限控制</li>
        <li>考虑实现URL国际化支持</li>
    </ul>

    <div class="success">
        <p><strong>总结:</strong></p>
        <p>通过本次升级，系统实现了更简洁、直观的URL结构，特别是去除了<code>/post-type/</code>前缀，使得自定义文章类型的URL更加友好。同时，通过精心设计的重写和重定向机制，确保了向后兼容性和系统的一致性。</p>
    </div>
</body>
</html> 