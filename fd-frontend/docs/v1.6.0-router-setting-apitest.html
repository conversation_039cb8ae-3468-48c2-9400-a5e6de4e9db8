<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Future Decade Frontend v1.6.0 文档</title>
    <style>
        body {
            font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 1.5em;
        }
        h1 {
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        h2 {
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        code {
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            font-size: 0.9em;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        pre code {
            background-color: transparent;
            padding: 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .version-badge {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8em;
            margin-left: 10px;
        }
        .notice {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 10px 15px;
            margin: 20px 0;
        }
        .important {
            background-color: #ffedef;
            border-left: 4px solid #f44336;
            padding: 10px 15px;
            margin: 20px 0;
        }
        .method {
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border: 1px solid #eee;
        }
        .method h4 {
            margin-top: 0;
        }
        .method-signature {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            margin-bottom: 10px;
        }
        .parameters {
            margin-left: 20px;
        }
        .returns {
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <h1>Future Decade Frontend <span class="version-badge">v1.6.0</span></h1>
    <p>本文档提供Future Decade前端应用的功能说明和使用指南。该应用基于Next.js构建，用于消费WordPress GraphQL API。</p>
    
    <h2>目录</h2>
    <ul>
        <li><a href="#introduction">介绍</a></li>
        <li><a href="#hooks">自定义Hooks</a>
            <ul>
                <li><a href="#useRoutePrefixes">useRoutePrefixes</a> <span class="version-badge">新增</span></li>
                <li><a href="#content-hooks">内容相关Hooks</a></li>
                <li><a href="#taxonomy-hooks">分类法相关Hooks</a></li>
            </ul>
        </li>
        <li><a href="#route-url-structure">路由与URL结构</a> <span class="version-badge">更新</span></li>
        <li><a href="#testing">测试页面</a>
            <ul>
                <li><a href="#route-test">路由前缀测试</a> <span class="version-badge">新增</span></li>
                <li><a href="#graphql-test">GraphQL测试</a></li>
            </ul>
        </li>
        <li><a href="#version-history">版本历史</a></li>
    </ul>
    
    <h2 id="introduction">介绍</h2>
    <p>Future Decade Frontend是一个基于Next.js框架构建的WordPress无头CMS前端应用。它利用GraphQL API从WordPress后端获取数据，提供现代化、高性能的用户体验。</p>
    
    <div class="notice">
        <p><strong>注意：</strong> 本应用需要配合Future Decade Theme（v1.2.5或更高版本）和WPGraphQL插件一起使用。</p>
    </div>
    
    <h2 id="hooks">自定义Hooks</h2>
    <p>应用提供了一系列自定义React Hooks，用于从WordPress GraphQL API获取数据。</p>
    
    <h3 id="useRoutePrefixes">useRoutePrefixes <span class="version-badge">新增</span></h3>
    <p>此Hook用于获取路由前缀设置，支持在前端动态生成符合后台设置的URL结构。</p>
    
    <div class="method">
        <h4>用法</h4>
        <pre><code>const { prefixes, loading, error } = useRoutePrefixes();</code></pre>
        
        <h4>返回值</h4>
        <ul>
            <li><code>prefixes</code> - 包含路由前缀的对象</li>
            <ul>
                <li><code>categoryPrefix</code> - 分类页面URL前缀，null表示无前缀</li>
                <li><code>tagPrefix</code> - 标签页面URL前缀，null表示无前缀</li>
                <li><code>postPrefix</code> - 文章页面URL前缀</li>
            </ul>
            <li><code>loading</code> - 加载状态</li>
            <li><code>error</code> - 错误信息，如果有</li>
        </ul>
        
        <h4>示例</h4>
        <pre><code>import { useRoutePrefixes } from '@/hooks';

// 在组件中使用
function MyComponent() {
  const { prefixes } = useRoutePrefixes();
  
  // 生成分类URL
  const categoryUrl = prefixes.categoryPrefix 
    ? `/${prefixes.categoryPrefix}/news` 
    : `/news`;
    
  // 生成标签URL
  const tagUrl = prefixes.tagPrefix 
    ? `/${prefixes.tagPrefix}/wordpress` 
    : `/wordpress`;
    
  // 生成文章URL
  const postUrl = `/${prefixes.postPrefix}/abc123/sample-article`;
  
  return (
    <div>
      <a href={categoryUrl}>新闻分类</a>
      <a href={tagUrl}>WordPress标签</a>
      <a href={postUrl}>示例文章</a>
    </div>
  );
}</code></pre>
    </div>
    
    <h3 id="content-hooks">内容相关Hooks</h3>
    <p>这些Hooks用于获取和操作WordPress内容。</p>
    
    <table>
        <thead>
            <tr>
                <th>Hook名称</th>
                <th>用途</th>
                <th>文件路径</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td><code>usePosts</code></td>
                <td>获取文章列表，支持分页和按分类/标签筛选</td>
                <td>hooks/usePosts.ts</td>
            </tr>
            <tr>
                <td><code>usePost</code></td>
                <td>获取单篇文章详情，支持通过ID或slug获取</td>
                <td>hooks/usePost.ts</td>
            </tr>
            <tr>
                <td><code>useCustomPosts</code></td>
                <td>获取自定义内容类型列表，支持分页</td>
                <td>hooks/useCustomPost.ts</td>
            </tr>
            <tr>
                <td><code>useCustomPost</code></td>
                <td>获取单个自定义内容详情，支持通过ID或slug获取</td>
                <td>hooks/useCustomPost.ts</td>
            </tr>
        </tbody>
    </table>
    
    <h3 id="taxonomy-hooks">分类法相关Hooks</h3>
    <p>这些Hooks用于获取和操作WordPress分类法。</p>
    
    <table>
        <thead>
            <tr>
                <th>Hook名称</th>
                <th>用途</th>
                <th>文件路径</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td><code>useCategories</code></td>
                <td>获取所有分类列表</td>
                <td>hooks/useCategory.ts</td>
            </tr>
            <tr>
                <td><code>useCategory</code></td>
                <td>获取单个分类详情，通过slug获取</td>
                <td>hooks/useCategory.ts</td>
            </tr>
            <tr>
                <td><code>useTaxonomies</code></td>
                <td>获取所有分类法列表</td>
                <td>hooks/useTaxonomy.ts</td>
            </tr>
            <tr>
                <td><code>useTaxonomy</code></td>
                <td>获取单个分类法详情，通过name获取</td>
                <td>hooks/useTaxonomy.ts</td>
            </tr>
        </tbody>
    </table>
    
    <h2 id="route-url-structure">路由与URL结构 <span class="version-badge">更新</span></h2>
    <p>应用现在支持通过WordPress后台配置的路由前缀动态生成URL。路由结构如下：</p>
    
    <table>
        <thead>
            <tr>
                <th>内容类型</th>
                <th>URL格式</th>
                <th>示例（默认设置）</th>
                <th>示例（自定义设置）</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>分类页面</td>
                <td><code>/[categoryPrefix]/[slug]</code><br/>或<code>/[slug]</code>（无前缀）</td>
                <td>/technology（无前缀）</td>
                <td>/category/technology</td>
            </tr>
            <tr>
                <td>标签页面</td>
                <td><code>/[tagPrefix]/[slug]</code><br/>或<code>/[slug]</code>（无前缀）</td>
                <td>/topics/wordpress</td>
                <td>/tag/wordpress</td>
            </tr>
            <tr>
                <td>文章详情页</td>
                <td><code>/[postPrefix]/[uuid]/[slug]</code></td>
                <td>/articles/a1b2c3d4e5/hello-world</td>
                <td>/posts/a1b2c3d4e5/hello-world</td>
            </tr>
        </tbody>
    </table>
    
    <div class="important">
        <p><strong>重要提示：</strong> 路由前缀变更会影响SEO，请谨慎修改。建议在网站初始设置时确定，后续尽量避免更改。</p>
    </div>
    
    <h2 id="testing">测试页面</h2>
    <p>应用提供了一些测试页面，用于验证功能正常工作。</p>
    
    <h3 id="route-test">路由前缀测试 <span class="version-badge">新增</span></h3>
    <p>路由前缀测试页面位于<code>/route-test</code>，用于验证路由前缀设置是否正确获取，并显示各种URL的示例。</p>
    
    <p>此页面显示：</p>
    <ul>
        <li>当前分类前缀设置及示例URL</li>
        <li>当前标签前缀设置及示例URL</li>
        <li>当前文章前缀设置及示例URL</li>
    </ul>
    
    <h3 id="graphql-test">GraphQL测试</h3>
    <p>GraphQL测试页面位于<code>/graphql-test</code>，用于测试各种GraphQL查询和响应。</p>
    
    <h2 id="version-history">版本历史</h2>
    <h3>v1.6.0 <small>(2025-04-22)</small></h3>
    <ul>
        <li>新增useRoutePrefixes钩子，支持获取后台设置的路由前缀</li>
        <li>添加路由前缀测试页面</li>
        <li>支持动态URL生成，适配后台路由前缀设置</li>
    </ul>
    
    <h3>v1.5.5 <small>(2025-03-28)</small></h3>
    <ul>
        <li>优化GraphQL查询缓存策略</li>
        <li>修复usePosts钩子加载更多功能的问题</li>
    </ul>
    
    <h3>v1.5.0 <small>(2025-03-10)</small></h3>
    <ul>
        <li>重构GraphQL查询结构，提高代码复用性</li>
        <li>添加新的GraphQL测试页面</li>
    </ul>
    
    <h3>v1.0.0 <small>(2025-01-15)</small></h3>
    <ul>
        <li>初始版本发布</li>
        <li>基本内容显示功能</li>
        <li>基础GraphQL查询支持</li>
    </ul>
    
    <hr>
    <footer>
        <p>&copy; 2025 Future Decade. 保留所有权利。</p>
    </footer>
</body>
</html> 