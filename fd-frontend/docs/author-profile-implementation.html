<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V2.1.0 - 创作者主页功能实现文档</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 900px;
            margin: auto;
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #e7e7e7;
            padding-bottom: 10px;
            margin-top: 20px;
        }
        h1 { font-size: 2em; }
        h2 { font-size: 1.5em; }
        h3 { font-size: 1.2em; border-bottom: 1px solid #eee; }
        code {
            background-color: #e8eaed;
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 3px;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
        }
        pre {
            background: #2d2d2d;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
        }
        pre code {
            background: none;
            padding: 0;
            color: inherit;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-info {
            color: #31708f;
            background-color: #d9edf7;
            border-color: #bce8f1;
        }
        .alert-success {
            color: #3c763d;
            background-color: #dff0d8;
            border-color: #d6e9c6;
        }
        .alert-danger {
            color: #a94442;
            background-color: #f2dede;
            border-color: #ebccd1;
        }
        .file-path {
            font-weight: bold;
            color: #555;
            margin-bottom: 5px;
            display: block;
        }
        ul {
            list-style-type: disc;
            margin-left: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>V2.1.0 - 创作者主页功能实现文档</h1>
        <p class="alert alert-info">本文档详细记录了为项目添加"创作者主页"功能的完整流程，包括后端API设计、前端页面开发，以及在此过程中遇到的问题及其解决方案。</p>

        <h2>1. 功能需求</h2>
        <p>现有系统对所有用户（包括订阅者和作者）一视同仁，缺乏对内容创作者的专属展示空间。为了提升作者的荣誉感和可见度，需要创建一个公开的、对SEO友好的个人主页，用于展示其个人资料和已发布的文章列表。</p>
        
        <h2>2. 后端实现 (fd-member)</h2>
        <p>后端的改动核心是创建一个安全的、只暴露必要公开信息的 GraphQL 查询接口。</p>

        <h3>2.1. 新建 `PublicAuthorProfile` 类型和 `getAuthorProfile` 查询</h3>
        <p>为了实现功能，我们在 <code>fd-member/graphql/</code> 目录下创建了一个新文件 <code>author-profile-graphql.php</code>，其内容如下：</p>
        <span class="file-path">fd-member/graphql/author-profile-graphql.php</span>
        <pre><code>&lt;?php
/**
 * GraphQL schema for public author profiles.
 */

if (!defined('ABSPATH')) {
    exit;
}

add_action('graphql_register_types', function () {
    // 1. 定义 PublicAuthorProfile 类型
    register_graphql_object_type(
        'PublicAuthorProfile',
        [
            'description' => __('A publicly accessible author profile with limited information.', 'fd-member'),
            'fields' => [
                'databaseId' => [
                    'type' => 'Int',
                    'description' => __('The numeric ID of the author.', 'fd-member'),
                    'resolve' => fn($user) => (int) $user->ID,
                ],
                'name' => [
                    'type' => 'String',
                    'description' => __('The display name of the author.'),
                    'resolve' => fn($user) => $user->display_name,
                ],
                // ... 其他字段如 description, avatar, slug ...
                'posts' => [
                    'type' => ['list_of' => 'Post'],
                    'description' => __('A list of published posts by the author.', 'fd-member'),
                    'resolve' => function ($user, $args) {
                        $query_args = [
                            'author' => $user->ID,
                            'post_status' => 'publish',
                            'posts_per_page' => 10,
                            'fields' => 'ids',
                        ];
                        $post_query = new WP_Query($query_args);
                        $post_ids   = $post_query->posts;

                        if (empty($post_ids)) {
                            return [];
                        }

                        $posts   = [];
                        $context = \WPGraphQL::get_app_context();

                        foreach ($post_ids as $post_id) {
                            $post = \WPGraphQL\Data\DataSource::resolve_post_object($post_id, $context);
                            if ($post) {
                                $posts[] = $post;
                            }
                        }
                        return $posts;
                    },
                ],
            ],
        ]
    );

    // 2. 注册根查询 getAuthorProfile
    register_graphql_field(
        'RootQuery',
        'getAuthorProfile',
        [
            'type' => 'PublicAuthorProfile',
            'description' => __('Retrieves a public profile for a user...', 'fd-member'),
            'args' => [
                'slug' => [
                    'type' => ['non_null' => 'String'],
                ],
            ],
            'resolve' => function ($root, $args) {
                // ...
                $user = get_user_by('slug', $args['slug']);
                if (!$user) return null;

                // --- 关键安全检查 ---
                $allowed_roles = ['author', 'editor', 'administrator'];
                if (empty(array_intersect((array) $user->roles, $allowed_roles))) {
                    return null; // 保护订阅者等非公开角色的隐私
                }

                return $user;
            },
        ]
    );
});
</code></pre>
        <div class="alert alert-info">
            <strong>关键设计点:</strong>
            <ul>
                <li><strong>专用类型:</strong> 创建 <code>PublicAuthorProfile</code> 而不是直接暴露 <code>User</code> 类型，遵循最小权限原则。</li>
                <li><strong>安全检查:</strong> 在 resolver 中强制检查用户角色，只返回作者及以上角色的信息，防止普通用户隐私泄露。</li>
                <li><strong>高效解析:</strong> <code>posts</code> 字段的 resolver 先用 <code>WP_Query</code> 高效查询出文章 ID 列表 (<code>'fields' => 'ids'</code>)，然后通过 <code>\WPGraphQL\Data\DataSource::resolve_post_object</code> 将 ID 安全地转换为 GraphQL 对象，避免了直接返回 <code>WP_Post</code> 对象可能导致的 N+1 问题和数据结构不匹配错误。</li>
                <li><strong>新增 `databaseId`</strong>: 为 `PublicAuthorProfile` 添加 `databaseId` 字段，供前端后续查询该作者的文章列表。</li>
            </ul>
        </div>
        
        <h3>2.2. 加载新的 GraphQL 定义</h3>
        <p>在插件的 GraphQL 加载器中引入新文件，使其生效。</p>
        <span class="file-path">fd-member/graphql/member-graphql-loader.php</span>
        <pre><code>// ...
// 加载作者文章操作
require_once dirname(__FILE__) . '/author-post-mutations.php';

// 加载作者公开主页
require_once dirname(__FILE__) . '/author-profile-graphql.php';
// ...
</code></pre>

        <h2>3. 前端实现 (fd-frontend)</h2>
        <p>前端工作主要围绕创建新页面、改造现有 Hook 和查询，并复用通用组件来完成。</p>

        <h3>3.1. GraphQL 查询与 Hooks 改造</h3>
        <p>为了支持作者主页的文章分页加载，我们对现有的 <code>GET_POSTS_BY_AUTHOR</code> 查询和 <code>useAuthorPosts</code> Hook 进行了增强。</p>
        
        <span class="file-path">fd-frontend/src/lib/graphql/queries.ts</span>
        <pre><code>// ...
export const GET_POSTS_BY_AUTHOR = gql`
  query GetPostsByAuthor($authorId: Int!, $first: Int = 10, $after: String) {
    posts(first: $first, after: $after, where: { author: $authorId }) {
      pageInfo {
        endCursor
        hasNextPage
      }
      nodes {
        ...PostFields
        author {
          node {
            id
            name
            slug
            avatar { url }
          }
        }
      }
    }
  }
  ${POST_FRAGMENT}
`;
// ...
</code></pre>
        <div class="alert alert-info">
            <strong>关键改动:</strong>
            <ul>
                <li>增加了 <code>$after: String</code> 参数以接收分页游标。</li>
                <li>查询返回中增加了 <code>pageInfo</code> 对象，用于判断是否有下一页。</li>
                <li>在 <code>where</code> 参数中，使用 <code>author: $authorId</code> 替换了错误的 <code>authorId</code>，修复了GraphQL报错。</li>
                <li>为 <code>nodes</code> 补全了 <code>author</code> 字段，确保文章列表数据完整。</li>
            </ul>
        </div>

        <span class="file-path">fd-frontend/src/hooks/useAuthorPosts.ts</span>
        <pre><code>// ...
export const useAuthorPosts = (options: UseAuthorPostsOptions) => {
  const { authorId, first = 10, after } = options;

  // 如果authorId暂未可用，将在skip参数控制下延迟执行
  const { data, loading, error, fetchMore, refetch } = useQuery<AuthorPostsData>(
    GET_POSTS_BY_AUTHOR,
    {
      variables: { authorId, first, after },
      notifyOnNetworkStatusChange: true,
      skip: !authorId, // 当 authorId 不存在时不执行查询
    }
  );

  const loadMore = (afterCursor: string) => {
    return fetchMore({
      // ...
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        // 合并并去重
        const combined = [...prev.posts.nodes, ...fetchMoreResult.posts.nodes];
        const deduped = Array.from(new Map(combined.map((p) => [p.id, p])).values());
        return {
          posts: {
            ...fetchMoreResult.posts,
            nodes: deduped,
          },
        };
      },
    });
  };

  return {
    posts: data?.posts?.nodes || [],
    endCursor: data?.posts?.pageInfo?.endCursor || null,
    hasNextPage: data?.posts?.pageInfo?.hasNextPage || false,
    loading,
    error,
    loadMore,
    refetch,
  };
};
</code></pre>
        <div class="alert alert-info">
            <strong>关键改动:</strong>
            <ul>
                <li>Hook 现在返回 <code>endCursor</code> 和 <code>hasNextPage</code> 状态，供UI判断分页。</li>
                <li>移除了当 <code>authorId</code> 为空时抛出错误的逻辑，改用 Apollo Client 的 <code>skip: !authorId</code> 选项，使组件可以在 `authorId` 异步加载完成前安全地调用此 Hook。</li>
                <li>在 <code>loadMore</code> 的 <code>updateQuery</code> 逻辑中，增加了基于文章 <code>id</code> 的去重操作，作为后端分页可能出现数据重叠时的前端兜底方案。</li>
            </ul>
        </div>
        
        <h3>3.2. 作者主页页面组件</h3>
        <p>我们创建了新的动态路由页面 <code>app/author/[slug]/page.tsx</code>，并使其功能和 UI 与分类页等列表页保持高度一致。</p>
        <span class="file-path">fd-frontend/src/app/author/[slug]/page.tsx</span>
        <pre><code>'use client';

import React, { useState, useEffect } from 'react';
import { gql, useQuery } from '@apollo/client';
import MainLayout from '@/components/layouts/MainLayout';
import ArticleListView, { Article } from '@/components/ArticleListView';
import ViewModeSwitcher, { ViewMode, ColumnCount } from '@/components/ViewModeSwitcher';
import { useRoutePrefixes, useAuthorPosts } from '@/hooks';
// ...其他 imports

const GET_AUTHOR_PROFILE = gql`
  query GetAuthorProfile($slug: String!) {
    getAuthorProfile(slug: $slug) {
      name
      description
      slug
      databaseId
      avatar { url }
    }
  }
`;

export default function AuthorPage({ params }) {
  const { slug } = params;

  // 1. 先获取作者资料
  const { data: profileData, loading: profileLoading } = useQuery(GET_AUTHOR_PROFILE, { variables: { slug } });
  const profile = profileData?.getAuthorProfile;
  const authorId = profile?.databaseId;

  // 2. 根据作者 ID 获取文章列表（分页）
  const { posts, endCursor, hasNextPage, loading: postsLoading, loadMore } = useAuthorPosts({ 
      authorId: authorId || 0, 
      first: 10 
  });

  const { prefixes } = useRoutePrefixes();
  const [viewMode, setViewMode] = useState('list');
  // ... 其他状态和处理函数 ...

  // 3. 渲染UI
  return (
    &lt;MainLayout&gt;
      &lt;div className="container"&gt;
        &lt;AuthorProfileHeader profile={profile} /&gt;
        
        &lt;ViewModeSwitcher
            currentMode={viewMode}
            onChange={handleViewModeChange}
        /&gt;

        &lt;ArticleListView
          articles={posts}
          mode={viewMode}
          routePrefixes={prefixes}
          // ...其他 props
        /&gt;

        {hasNextPage && (
          &lt;button onClick={() => loadMore(endCursor)}&gt;
            {postsLoading ? '加载中...' : '加载更多'}
          &lt;/button&gt;
        )}
      &lt;/div&gt;
    &lt;/MainLayout&gt;
  );
}
</code></pre>
        <div class="alert alert-success">
            <strong>实现亮点:</strong>
            <ul>
                <li><strong>逻辑清晰:</strong> 页面首先获取作者的 `databaseId`，然后用它作为参数调用 `useAuthorPosts` Hook 来获取文章。</li>
                <li><strong>组件复用:</strong> 大量复用了 `ArticleListView`, `ViewModeSwitcher` 等核心UI组件，保证了全站体验的一致性，并减少了开发成本。</li>
                <li><strong>用户体验:</strong> 完整实现了加载状态、空状态、分页加载、视图切换等功能，体验完善。</li>
                <li><strong>链接正确性:</strong> 通过向 `ArticleListView` 传入 `routePrefixes`，确保了文章链接的生成逻辑与全站中间件规则保持一致。</li>
            </ul>
        </div>
        
        <h2>4. 期间遇到的问题与修复</h2>
        <p>开发过程中遇到了一些预期内外的错误，均已得到修复。</p>
        
        <h3>4.1. 构建失败: `SEARCH_USERS` 未导出</h3>
        <p>在一次代码修改中，意外删除了 <code>fd-frontend/src/lib/graphql/queries.ts</code> 文件中的 <code>SEARCH_USERS</code> 查询，导致私信组件 <code>ConversationList.tsx</code> 导入失败，引发了 `npm run build` 报错。</p>
        <div class="alert alert-danger">
            <strong>解决方案:</strong> 将被误删的 <code>SEARCH_USERS</code> 查询重新添加回 <code>queries.ts</code> 文件中，问题解决。这提醒我们在重构时需注意代码的相互依赖关系。
        </div>

        <h3>4.2. GraphQL 错误: `authorId` 字段未定义</h3>
        <p>作者主页的文章列表最初无法加载，GraphQL Network 面板报错 "Field 'authorId' is not defined by type 'RootQueryToPostConnectionWhereArgs'"。</p>
        <div class="alert alert-danger">
            <strong>原因与解决方案:</strong> 查阅 WPGraphQL 文档发现，按作者ID筛选文章时，正确的 `where` 参数应为 <code>author</code> 而非 <code>authorId</code>。修改 <code>GET_POSTS_BY_AUTHOR</code> 查询后问题解决。
        </div>

    </div>
</body>
</html> 