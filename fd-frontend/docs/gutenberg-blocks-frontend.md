# Gutenberg区块前端渲染快速参考

## Block Renderer用法

```tsx
// 在页面文件中使用BlockRenderer
import { BlockRenderer } from '@/components/blocks';

// 在页面组件内部
return (
  <div className="content-area">
    {page.blocks && page.blocks.length > 0 ? (
      <BlockRenderer blocks={page.blocks} />
    ) : (
      <div dangerouslySetInnerHTML={{ __html: page.content || '' }} />
    )}
  </div>
);
```

## 核心组件列表和开发状态

| 区块名称 | 实现状态 | 优先级 |
|---------|--------|--------|
| `core/paragraph` | ✅ 已实现 | P0 |
| `core/heading` | ✅ 已实现 | P0 |
| `core/image` | ✅ 已实现 | P0 |
| `core/list` | ✅ 已实现 | P0 |
| `core/quote` | ✅ 已实现 | P0 |
| `core/gallery` | ⏳ 待实现 | P1 |
| `core/table` | ⏳ 待实现 | P1 |
| `core/columns` | ⏳ 待实现 | P1 |
| `core/button` | ⏳ 待实现 | P1 |
| `core/embed` | ⏳ 待实现 | P2 |

## 常见问题解决

1. **区块显示为空白**：检查attributesJSON是否正确解析
   ```tsx
   // BlockRenderer.tsx中添加调试信息
   console.log("区块属性:", getBlockAttributes(block));
   ```

2. **嵌套区块不显示**：确保处理innerBlocks
   ```tsx
   // 递归渲染innerBlocks
   {block.innerBlocks && block.innerBlocks.length > 0 && (
     <BlockRenderer blocks={block.innerBlocks} />
   )}
   ```

3. **样式不正确**：检查Tailwind类名和内联样式
   ```tsx
   // 样式处理示例
   const className = [
     'base-class',
     align && `align-${align}`,
     otherProp && 'conditional-class'
   ].filter(Boolean).join(' ');
   ```

## 添加新区块组件的步骤

1. **创建组件文件**：
   ```bash
   touch src/components/blocks/NewBlock.tsx
   ```

2. **实现组件**：
   ```tsx
   // src/components/blocks/NewBlock.tsx
   import React from 'react';
   
   interface NewBlockProps {
     attributes: {
       // 定义属性类型
       property1: string;
       property2?: number;
     };
   }
   
   const NewBlock: React.FC<NewBlockProps> = ({ attributes }) => {
     const { property1, property2 } = attributes;
     
     return (
       <div className="new-block">
         {/* 渲染区块内容 */}
       </div>
     );
   };
   
   export default NewBlock;
   ```

3. **更新索引文件**：
   ```tsx
   // src/components/blocks/index.ts
   export { default as NewBlock } from './NewBlock';
   ```

4. **注册到BlockRenderer**：
   ```tsx
   // src/components/blocks/BlockRenderer.tsx
   import NewBlock from './NewBlock';
   
   // 在switch语句中添加
   case 'CoreNewBlock':
     return <NewBlock key={index} attributes={attributes} />;
   ```

## 调试技巧

1. **查看区块结构**：
   ```tsx
   useEffect(() => {
     console.log('页面区块结构:', page.blocks);
   }, [page.blocks]);
   ```

2. **调试特定区块**：
   ```tsx
   if (__typename === 'CoreProblemBlock') {
     console.log('问题区块:', block);
   }
   ```

3. **临时显示原始HTML**：
   ```tsx
   // 当区块组件出问题时添加此回退
   return (
     <>
       <div style={{color: 'red'}}>调试区块: {__typename}</div>
       <div dangerouslySetInnerHTML={{__html: saveContent}} />
     </>
   );
   ``` 