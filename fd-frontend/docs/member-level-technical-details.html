<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员等级系统技术实现细节</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            margin-top: 40px;
            margin-bottom: 20px;
            padding-left: 10px;
            border-left: 4px solid #e74c3c;
        }
        h3 {
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 6px;
            padding: 20px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        .code-block .comment {
            color: #68d391;
        }
        .code-block .keyword {
            color: #f687b3;
        }
        .code-block .string {
            color: #fbb6ce;
        }
        .file-header {
            background: #4a5568;
            color: white;
            padding: 8px 15px;
            border-radius: 4px 4px 0 0;
            font-weight: 600;
            font-size: 13px;
            margin-bottom: 0;
        }
        .api-endpoint {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .function-signature {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
        }
        .change-log {
            background: #fffaf0;
            border: 1px solid #feb2b2;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .migration-step {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #e2e8f0;
            padding: 12px;
            text-align: left;
        }
        th {
            background: #f7fafc;
            font-weight: 600;
        }
        .file-path {
            background: #e2e8f0;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 13px;
        }
        .emoji {
            font-size: 1.2em;
            margin-right: 5px;
        }
        ul, ol {
            padding-left: 25px;
        }
        li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><span class="emoji">⚙️</span>会员等级系统技术实现细节</h1>
        
        <h2><span class="emoji">🔧</span>核心函数实现</h2>

        <h3>1. 配置验证函数</h3>
        <div class="function-signature">
            <strong>函数签名：</strong> fd_member_validate_level_config($level_data, $exclude_id = null)
        </div>
        
        <div class="file-header">fd-member/includes/membership/member-levels.php</div>
        <div class="code-block">
<span class="comment">/**
 * 检查会员等级配置的合理性
 *
 * @param array $level_data 会员等级数据
 * @param int $exclude_id 排除的等级ID（用于更新时）
 * @return array 检查结果 ['valid' => bool, 'warnings' => array, 'suggestions' => array]
 */</span>
<span class="keyword">function</span> fd_member_validate_level_config($level_data, $exclude_id = null) {
    $result = array(
        <span class="string">'valid'</span> => true,
        <span class="string">'warnings'</span> => array(),
        <span class="string">'suggestions'</span> => array()
    );
    
    $priority = isset($level_data[<span class="string">'priority'</span>]) ? intval($level_data[<span class="string">'priority'</span>]) : 0;
    $price = isset($level_data[<span class="string">'price'</span>]) ? floatval($level_data[<span class="string">'price'</span>]) : 0;
    $duration = isset($level_data[<span class="string">'duration'</span>]) ? intval($level_data[<span class="string">'duration'</span>]) : 0;
    
    <span class="comment">// 获取其他等级进行比较</span>
    $other_levels = fd_member_get_member_levels();
    <span class="keyword">if</span> ($exclude_id) {
        $other_levels = array_filter($other_levels, <span class="keyword">function</span>($level) <span class="keyword">use</span> ($exclude_id) {
            <span class="keyword">return</span> isset($level[<span class="string">'id'</span>]) && $level[<span class="string">'id'</span>] != $exclude_id;
        });
    }
    
    <span class="comment">// 检查价格与优先级的关系</span>
    <span class="keyword">foreach</span> ($other_levels <span class="keyword">as</span> $other_level) {
        $other_priority = isset($other_level[<span class="string">'priority'</span>]) ? intval($other_level[<span class="string">'priority'</span>]) : 0;
        $other_price = isset($other_level[<span class="string">'price'</span>]) ? floatval($other_level[<span class="string">'price'</span>]) : 0;
        
        <span class="comment">// 如果优先级更高但价格更低，给出警告</span>
        <span class="keyword">if</span> ($priority > $other_priority && $price < $other_price && $price > 0 && $other_price > 0) {
            $result[<span class="string">'warnings'</span>][] = sprintf(
                <span class="string">'优先级 %d 高于现有等级"%s"(优先级 %d)，但价格 %.2f 元低于其价格 %.2f 元'</span>,
                $priority, $other_level[<span class="string">'name'</span>], $other_priority, $price, $other_price
            );
        }
    }
    
    <span class="keyword">return</span> $result;
}
        </div>

        <h3>2. 等级比较函数</h3>
        <div class="function-signature">
            <strong>函数签名：</strong> fd_member_compare_levels($level1, $level2)
        </div>
        
        <div class="code-block">
<span class="comment">/**
 * 比较两个会员等级的优先级
 *
 * @param array $level1 第一个会员等级
 * @param array $level2 第二个会员等级
 * @return int 比较结果：1表示level1更高，-1表示level2更高，0表示相等
 */</span>
<span class="keyword">function</span> fd_member_compare_levels($level1, $level2) {
    $priority1 = isset($level1[<span class="string">'priority'</span>]) ? intval($level1[<span class="string">'priority'</span>]) : 0;
    $priority2 = isset($level2[<span class="string">'priority'</span>]) ? intval($level2[<span class="string">'priority'</span>]) : 0;
    
    <span class="keyword">if</span> ($priority1 > $priority2) {
        <span class="keyword">return</span> 1;
    } <span class="keyword">elseif</span> ($priority1 < $priority2) {
        <span class="keyword">return</span> -1;
    } <span class="keyword">else</span> {
        <span class="keyword">return</span> 0;
    }
}
        </div>

        <h3>3. 升级检查函数</h3>
        <div class="function-signature">
            <strong>函数签名：</strong> fd_member_can_upgrade_to_level($user_id, $target_level_id)
        </div>
        
        <div class="code-block">
<span class="comment">/**
 * 检查用户是否可以升级到指定等级
 *
 * @param int $user_id 用户ID
 * @param int $target_level_id 目标等级ID
 * @return bool 是否可以升级
 */</span>
<span class="keyword">function</span> fd_member_can_upgrade_to_level($user_id, $target_level_id) {
    $current_level = fd_member_get_user_member_level($user_id);
    $target_level = fd_member_get_member_level($target_level_id);
    
    <span class="keyword">if</span> (!$target_level) {
        <span class="keyword">return</span> false;
    }
    
    <span class="comment">// 如果用户没有当前等级，可以升级到任何等级</span>
    <span class="keyword">if</span> (!$current_level) {
        <span class="keyword">return</span> true;
    }
    
    <span class="comment">// 比较优先级</span>
    <span class="keyword">return</span> fd_member_compare_levels($target_level, $current_level) > 0;
}
        </div>

        <h2><span class="emoji">🎨</span>前端实现改进</h2>

        <h3>1. 升级逻辑修复</h3>
        <div class="file-header">fd-frontend/src/app/membership/upgrade/page.tsx</div>
        <div class="code-block">
<span class="comment">// 修改前（错误的实现）</span>
{data.allMemberLevels.map((level: MemberLevel) => (
  &lt;MembershipCard 
    key={level.id}
    level={level}
    isCurrent={level.id === currentLevelId}
    canUpgrade={!currentLevelId || level.id > currentLevelId}
  /&gt;
))}

<span class="comment">// 修改后（正确的实现）</span>
{data.allMemberLevels.map((level: MemberLevel) => (
  &lt;MembershipCard 
    key={level.id}
    level={level}
    isCurrent={level.id === currentLevel?.id}
    canUpgrade={!currentLevel || (level.priority || 0) > (currentLevel.priority || 0)}
    currentLevel={currentLevel}
  /&gt;
))}
        </div>

        <h3>2. GraphQL查询增强</h3>
        <div class="file-header">fd-frontend/src/app/membership/upgrade/page.tsx</div>
        <div class="code-block">
<span class="keyword">const</span> GET_ALL_MEMBER_LEVELS = gql`
  <span class="keyword">query</span> GetAllMemberLevels {
    allMemberLevels {
      id
      name
      description
      priority
      price
      duration
      durationUnit
      tier          <span class="comment">// 新增字段</span>
    }
  }
`;
        </div>

        <h3>3. 类型定义更新</h3>
        <div class="file-header">fd-frontend/src/types/user-types.ts</div>
        <div class="code-block">
<span class="comment">/**
 * 会员等级信息
 */</span>
<span class="keyword">export interface</span> MemberLevel {
  id: number;
  name: string;
  description?: string;
  priority?: number;
  price?: number;
  duration?: number;
  durationUnit?: <span class="string">'days'</span> | <span class="string">'months'</span> | <span class="string">'years'</span>;
  tier?: string;        <span class="comment">// 新增字段</span>
}
        </div>

        <h2><span class="emoji">🔌</span>GraphQL API 增强</h2>

        <h3>1. 新增tier字段</h3>
        <div class="file-header">fd-member/includes/membership/member-graphql.php</div>
        <div class="code-block">
<span class="comment">// 在MemberLevel类型中添加tier字段</span>
<span class="string">'tier'</span> => [
    <span class="string">'type'</span> => <span class="string">'String'</span>,
    <span class="string">'description'</span> => <span class="string">'会员等级层次描述'</span>,
    <span class="string">'resolve'</span> => <span class="keyword">function</span>($level) {
        $priority = isset($level[<span class="string">'priority'</span>]) ? intval($level[<span class="string">'priority'</span>]) : 0;
        <span class="keyword">return</span> fd_member_get_level_tier($priority);
    }
]
        </div>

        <h3>2. 升级检查变更</h3>
        <div class="api-endpoint">
            <strong>GraphQL Mutation:</strong> checkMemberLevelUpgrade
        </div>

        <div class="code-block">
<span class="comment">// 检查用户是否可以升级到指定等级</span>
register_graphql_mutation(<span class="string">'checkMemberLevelUpgrade'</span>, [
    <span class="string">'inputFields'</span> => [
        <span class="string">'userId'</span> => [
            <span class="string">'type'</span> => [<span class="string">'non_null'</span> => <span class="string">'ID'</span>],
            <span class="string">'description'</span> => <span class="string">'用户ID'</span>
        ],
        <span class="string">'targetLevelId'</span> => [
            <span class="string">'type'</span> => [<span class="string">'non_null'</span> => <span class="string">'Int'</span>],
            <span class="string">'description'</span> => <span class="string">'目标会员等级ID'</span>
        ]
    ],
    <span class="string">'outputFields'</span> => [
        <span class="string">'canUpgrade'</span> => [
            <span class="string">'type'</span> => <span class="string">'Boolean'</span>,
            <span class="string">'description'</span> => <span class="string">'是否可以升级'</span>
        ],
        <span class="string">'currentLevel'</span> => [
            <span class="string">'type'</span> => <span class="string">'MemberLevel'</span>,
            <span class="string">'description'</span> => <span class="string">'当前会员等级'</span>
        ],
        <span class="string">'targetLevel'</span> => [
            <span class="string">'type'</span> => <span class="string">'MemberLevel'</span>,
            <span class="string">'description'</span> => <span class="string">'目标会员等级'</span>
        ],
        <span class="string">'message'</span> => [
            <span class="string">'type'</span> => <span class="string">'String'</span>,
            <span class="string">'description'</span> => <span class="string">'升级检查结果消息'</span>
        ]
    ]
]);
        </div>

        <h2><span class="emoji">🗄️</span>数据迁移系统</h2>

        <h3>1. 自动迁移脚本</h3>
        <div class="file-header">fd-member/includes/membership/member-levels-migration.php</div>
        <div class="migration-step">
            <strong>迁移功能：</strong>
            <ul>
                <li>自动为现有等级分配合理的priority值</li>
                <li>基于等级名称和价格智能推测priority</li>
                <li>确保priority值的唯一性</li>
                <li>提供手动迁移和自动迁移选项</li>
            </ul>
        </div>

        <div class="code-block">
<span class="comment">/**
 * 根据等级名称和价格推测合理的priority值
 */</span>
<span class="keyword">function</span> fd_member_suggest_priority_by_name_and_price($level) {
    $name = strtolower($level[<span class="string">'name'</span>]);
    $price = isset($level[<span class="string">'price'</span>]) ? floatval($level[<span class="string">'price'</span>]) : 0;

    <span class="comment">// 根据名称关键词判断</span>
    <span class="keyword">if</span> (strpos($name, <span class="string">'钻石'</span>) !== false || strpos($name, <span class="string">'diamond'</span>) !== false) {
        <span class="keyword">return</span> 100;
    }
    <span class="keyword">if</span> (strpos($name, <span class="string">'黄金'</span>) !== false || strpos($name, <span class="string">'gold'</span>) !== false) {
        <span class="keyword">return</span> 80;
    }
    <span class="keyword">if</span> (strpos($name, <span class="string">'白银'</span>) !== false || strpos($name, <span class="string">'silver'</span>) !== false) {
        <span class="keyword">return</span> 50;
    }

    <span class="comment">// 根据价格判断</span>
    <span class="keyword">if</span> ($price >= 1000) <span class="keyword">return</span> 90;
    <span class="keyword">if</span> ($price >= 500) <span class="keyword">return</span> 70;
    <span class="keyword">if</span> ($price >= 100) <span class="keyword">return</span> 40;

    <span class="keyword">return</span> 0; <span class="comment">// 默认为基础级</span>
}
        </div>

        <h2><span class="emoji">🎛️</span>后台管理界面增强</h2>

        <h3>1. 配置指南组件</h3>
        <div class="file-header">fd-member/admin/member-levels.php</div>
        <div class="code-block">
&lt;div class="fd-member-config-guide-container"&gt;
    &lt;div class="fd-config-guide-header" onclick="toggleConfigGuide()"&gt;
        &lt;h2&gt;
            &lt;span class="dashicons dashicons-info"&gt;&lt;/span&gt;
            会员等级配置指南
            &lt;span class="dashicons dashicons-arrow-down-alt2" id="guide-arrow"&gt;&lt;/span&gt;
        &lt;/h2&gt;
    &lt;/div&gt;
    &lt;div class="fd-config-guide-content" id="config-guide-content" style="display: none;"&gt;
        <span class="comment">&lt;!-- 配置指南内容 --&gt;</span>
    &lt;/div&gt;
&lt;/div&gt;
        </div>

        <h3>2. 配置状态显示</h3>
        <div class="code-block">
<span class="comment">// 在等级列表中显示配置状态</span>
&lt;td&gt;
    &lt;?php <span class="keyword">if</span> (!empty($validation[<span class="string">'warnings'</span>])): ?&gt;
        &lt;span class="fd-config-status fd-config-warning"&gt;
            &lt;span class="dashicons dashicons-warning"&gt;&lt;/span&gt; 需要注意
        &lt;/span&gt;
    &lt;?php <span class="keyword">elseif</span> (!empty($validation[<span class="string">'suggestions'</span>])): ?&gt;
        &lt;span class="fd-config-status fd-config-suggestion"&gt;
            &lt;span class="dashicons dashicons-info"&gt;&lt;/span&gt; 可优化
        &lt;/span&gt;
    &lt;?php <span class="keyword">else</span>: ?&gt;
        &lt;span class="fd-config-status fd-config-good"&gt;
            &lt;span class="dashicons dashicons-yes"&gt;&lt;/span&gt; 配置合理
        &lt;/span&gt;
    &lt;?php <span class="keyword">endif</span>; ?&gt;
&lt;/td&gt;
        </div>

        <h2><span class="emoji">📊</span>配置检查工具</h2>

        <h3>1. 独立检查页面</h3>
        <div class="file-header">fd-member/admin/level-config-checker.php</div>
        <div class="api-endpoint">
            <strong>访问路径：</strong> admin.php?page=fd-member-config-checker
        </div>

        <div class="code-block">
<span class="comment">/**
 * 会员等级配置检查页面
 */</span>
<span class="keyword">function</span> fd_member_level_config_checker_page() {
    $levels = fd_member_get_sorted_member_levels();
    $overall_issues = array();
    $level_validations = array();

    <span class="comment">// 检查每个等级的配置</span>
    <span class="keyword">foreach</span> ($levels <span class="keyword">as</span> $level) {
        $validation = fd_member_validate_level_config($level, $level[<span class="string">'id'</span>]);
        $level_validations[$level[<span class="string">'id'</span>]] = $validation;

        <span class="keyword">if</span> (!empty($validation[<span class="string">'warnings'</span>])) {
            $overall_issues = array_merge($overall_issues, $validation[<span class="string">'warnings'</span>]);
        }
    }

    <span class="comment">// 渲染检查结果页面</span>
}
        </div>

        <h2><span class="emoji">🔄</span>文件变更清单</h2>

        <h3>新增文件</h3>
        <table>
            <thead>
                <tr>
                    <th>文件路径</th>
                    <th>功能描述</th>
                    <th>主要内容</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><span class="file-path">fd-member/includes/membership/member-levels-migration.php</span></td>
                    <td>数据迁移脚本</td>
                    <td>自动为现有等级分配priority值</td>
                </tr>
                <tr>
                    <td><span class="file-path">fd-member/admin/level-config-checker.php</span></td>
                    <td>配置检查工具</td>
                    <td>独立的配置检查和诊断页面</td>
                </tr>
            </tbody>
        </table>

        <h3>修改文件</h3>
        <table>
            <thead>
                <tr>
                    <th>文件路径</th>
                    <th>修改类型</th>
                    <th>主要变更</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><span class="file-path">fd-member/includes/membership/member-levels.php</span></td>
                    <td>功能增强</td>
                    <td>添加验证函数、比较函数、辅助函数</td>
                </tr>
                <tr>
                    <td><span class="file-path">fd-member/admin/member-levels.php</span></td>
                    <td>界面增强</td>
                    <td>添加配置指南、状态显示、验证提醒</td>
                </tr>
                <tr>
                    <td><span class="file-path">fd-member/includes/membership/member-graphql.php</span></td>
                    <td>API增强</td>
                    <td>添加tier字段、升级检查变更</td>
                </tr>
                <tr>
                    <td><span class="file-path">fd-frontend/src/app/membership/upgrade/page.tsx</span></td>
                    <td>逻辑修复</td>
                    <td>修复升级判断逻辑，使用priority而非ID</td>
                </tr>
                <tr>
                    <td><span class="file-path">fd-frontend/src/components/membership/MembershipCard.tsx</span></td>
                    <td>显示增强</td>
                    <td>添加等级层次显示、优化升级提示</td>
                </tr>
                <tr>
                    <td><span class="file-path">fd-frontend/src/types/user-types.ts</span></td>
                    <td>类型更新</td>
                    <td>添加tier字段类型定义</td>
                </tr>
            </tbody>
        </table>

        <h2><span class="emoji">🧪</span>测试用例</h2>

        <h3>1. 升级逻辑测试</h3>
        <div class="change-log">
            <strong>测试场景：</strong>
            <ol>
                <li>创建多个不同priority的等级</li>
                <li>测试用户从低等级向高等级升级</li>
                <li>验证高等级用户不能"降级"</li>
                <li>确认priority为0的用户可以升级到任何等级</li>
            </ol>
        </div>

        <h3>2. 配置验证测试</h3>
        <div class="change-log">
            <strong>测试用例：</strong>
            <ol>
                <li>创建高priority低价格等级（应显示警告）</li>
                <li>创建重复priority等级（应被阻止）</li>
                <li>创建合理配置等级（应通过验证）</li>
                <li>测试批量配置检查功能</li>
            </ol>
        </div>

        <h2><span class="emoji">📈</span>性能优化</h2>

        <h3>1. 缓存策略</h3>
        <ul>
            <li><strong>等级列表缓存：</strong>使用WordPress transient缓存排序后的等级列表</li>
            <li><strong>验证结果缓存：</strong>缓存配置验证结果，避免重复计算</li>
            <li><strong>GraphQL缓存：</strong>利用Apollo Client缓存机制</li>
        </ul>

        <h3>2. 数据库优化</h3>
        <ul>
            <li><strong>索引优化：</strong>为priority字段添加索引</li>
            <li><strong>查询优化：</strong>减少不必要的数据库查询</li>
            <li><strong>批量操作：</strong>支持批量更新等级配置</li>
        </ul>

        <hr style="margin: 40px 0; border: none; border-top: 1px solid #e2e8f0;">

        <p style="text-align: center; color: #6c757d; font-size: 14px;">
            <strong>技术文档版本：</strong> v1.0 |
            <strong>最后更新：</strong> 2024年12月 |
            <strong>开发团队：</strong> FD项目组
        </p>
    </div>
</body>
</html>
