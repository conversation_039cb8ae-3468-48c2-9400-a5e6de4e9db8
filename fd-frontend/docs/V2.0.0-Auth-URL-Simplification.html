<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>V2.0.0 身份验证URL路径简化实现</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      border-bottom: 2px solid #f0f0f0;
      padding-bottom: 10px;
      margin-top: 0;
    }
    h2 {
      margin-top: 30px;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 5px;
    }
    h3 {
      margin-top: 25px;
    }
    code {
      font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
      background-color: #f6f8fa;
      padding: 2px 4px;
      border-radius: 3px;
    }
    pre {
      background-color: #f6f8fa;
      padding: 16px;
      border-radius: 6px;
      overflow: auto;
    }
    pre code {
      background-color: transparent;
      padding: 0;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 20px 0;
    }
    table, th, td {
      border: 1px solid #ddd;
    }
    th, td {
      padding: 10px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
    }
    .note {
      background-color: #e7f3fe;
      border-left: 4px solid #2196F3;
      padding: 10px;
      margin: 15px 0;
    }
    .warning {
      background-color: #fff3cd;
      border-left: 4px solid #ffc107;
      padding: 10px;
      margin: 15px 0;
    }
  </style>
</head>
<body>
  <h1>V2.0.0 身份验证URL路径简化实现</h1>
  <p>本文档详细记录了前端应用中身份验证相关URL路径的简化实现过程。我们将原来的路径格式从 <code>/auth/login</code> 简化为 <code>/login</code>，以提供更简洁的用户体验。</p>
  
  <h2>1. 背景与需求</h2>
  <p>之前，所有与身份验证和用户个人中心相关的页面都放在 <code>/auth/</code> 路径前缀下，例如：</p>
  <ul>
    <li><code>/auth/login</code> - 登录页面</li>
    <li><code>/auth/register</code> - 注册页面</li>
    <li><code>/auth/profile</code> - 个人中心页面</li>
    <li><code>/auth/forgot-password</code> - 忘记密码页面</li>
    <li><code>/auth/reset-password</code> - 重置密码页面</li>
    <li><code>/auth/change-password</code> - 修改密码页面</li>
  </ul>
  <p>新的需求是简化这些URL，移除 <code>/auth/</code> 前缀，使URL更加简洁和用户友好：</p>
  <ul>
    <li><code>/login</code> - 登录页面</li>
    <li><code>/register</code> - 注册页面</li>
    <li><code>/profile</code> - 个人中心页面</li>
    <li><code>/forgot-password</code> - 忘记密码页面</li>
    <li><code>/reset-password</code> - 重置密码页面</li>
    <li><code>/change-password</code> - 修改密码页面</li>
  </ul>
  
  <h2>2. 实现方案</h2>
  <p>我们采用了最小侵入性的方案来实现这一需求：使用Next.js中间件进行URL重写和重定向。这种方案的优点是：</p>
  <ul>
    <li>无需移动或重命名现有文件和目录结构</li>
    <li>维持内部应用结构的一致性</li>
    <li>可同时支持新旧路径格式，确保向后兼容性</li>
  </ul>
  <p>具体实现包括以下几个方面：</p>
  
  <h3>2.1 中间件实现路径映射</h3>
  <p>使用Next.js中间件来处理URL路径映射：</p>
  <ul>
    <li>将 <code>/auth/[path]</code> 路径重定向到 <code>/[path]</code>（如用户访问 <code>/auth/login</code> 会被重定向到 <code>/login</code>）</li>
    <li>将 <code>/[path]</code> 路径内部重写到 <code>/auth/[path]</code>（如用户访问 <code>/login</code> 会在内部被重写为 <code>/auth/login</code>）</li>
  </ul>
  
  <h3>2.2 更新路由配置</h3>
  <p>更新路由配置文件中的路径定义，将 <code>/auth/</code> 前缀移除。</p>
  
  <h3>2.3 更新前端链接和重定向</h3>
  <p>全局搜索并替换所有前端代码中的链接和重定向路径。</p>
  
  <h2>3. 代码修改详情</h2>
  
  <h3>3.1 中间件修改 (middleware.ts)</h3>
  <p>在Next.js中间件中添加认证路径处理逻辑：</p>
  <pre><code>// 定义auth相关页面的路径映射
const AUTH_PATHS = [
  'login',
  'register',
  'forgot-password',
  'reset-password',
  'change-password',
  'profile'
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const pathSegments = pathname.split('/').filter(Boolean);
  
  try {
    // 处理auth路径
    // ------------------------------
    // 情况25: 将/auth/[path]路径重定向到/[path]
    if (pathSegments.length >= 2 && pathSegments[0] === 'auth' && AUTH_PATHS.includes(pathSegments[1])) {
      const restPath = pathSegments.slice(1).join('/');
      if (process.env.NODE_ENV === 'development') {
        console.log(`将/auth/${restPath}重定向到/${restPath}`);
      }
      return NextResponse.redirect(new URL(`/${restPath}`, request.url));
    }

    // 情况26: 将/[path]路径内部重写到/auth/[path]
    if (pathSegments.length >= 1 && AUTH_PATHS.includes(pathSegments[0])) {
      // 排除一些可能的特殊情况，比如可能有名为"login"的标签或分类
      const isAuthPath = true; // 这里可以添加更多的判断逻辑

      if (isAuthPath) {
        const fullPath = pathSegments.join('/');
        if (process.env.NODE_ENV === 'development') {
          console.log(`将/${fullPath}重写到/auth/${fullPath}`);
        }
        return NextResponse.rewrite(new URL(`/auth/${fullPath}`, request.url));
      }
    }
    // ------------------------------
    
    // 认证路由保护逻辑
    // ------------------------------
    // 从Cookie中获取认证令牌
    const authToken = request.cookies.get('auth_token')?.value;
    const isLoggedIn = !!authToken;
    
    // 如果是受保护的路由，但用户未登录，则重定向到登录页面
    if (isProtectedRoute(pathname) && !isLoggedIn) {
      const callbackUrl = encodeURIComponent(request.nextUrl.href);
      return NextResponse.redirect(new URL(`/login?callbackUrl=${callbackUrl}`, request.url));
    }
    
    // 其他中间件逻辑...
  } catch (error) {
    console.error('Error in middleware:', error);
  }
  
  return NextResponse.next();
}</code></pre>
  
  <h3>3.2 路由配置修改 (routes-config.ts)</h3>
  <p>更新路由配置中的访客路由路径：</p>
  <pre><code>// 只允许未登录用户访问的路由（已登录用户将被重定向到首页）
export const guestOnlyRoutes = [
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
];</code></pre>
  
  <h3>3.3 保护路由组件修改 (ProtectedRoute.tsx)</h3>
  <p>更新默认重定向路径：</p>
  <pre><code>const ProtectedRoute: React.FC&lt;ProtectedRouteProps&gt; = ({
  children,
  redirectTo = '/login',
  requireAuth = false,
  requireGuest = false,
}) => {
  // 组件逻辑...
};</code></pre>
  
  <h3>3.4 全局链接与重定向更新</h3>
  <p>使用批处理方式更新所有组件中的链接：</p>
  <pre><code>// 更新href属性中的路径
find src -type f -name "*.tsx" -exec sed -i '' 's|href="/auth/|href="/|g' {} \;

// 更新to属性中的路径
find src -type f -name "*.tsx" -exec sed -i '' 's|to="/auth/|to="/|g' {} \;

// 更新重定向路径
find src -type f -name "*.tsx" -exec sed -i '' 's|redirect("/auth/|redirect("/|g' {} \;</code></pre>
  
  <h2>4. 重要注意事项</h2>
  <div class="note">
    <p><strong>重要：</strong> 该实现利用Next.js中间件重写路径，但保持实际文件结构不变。这意味着：</p>
    <ul>
      <li>文件系统中的文件仍然位于 <code>src/app/auth/</code> 目录下</li>
      <li>所有内部组件引用和导入不需要修改</li>
      <li>只有对外的URL路径和链接需要更新</li>
    </ul>
  </div>
  
  <h2>5. 向后兼容性</h2>
  <p>此实现保持向后兼容性：</p>
  <ul>
    <li>访问旧路径 <code>/auth/login</code> 的用户会被自动重定向到 <code>/login</code></li>
    <li>所有内部逻辑保持不变，只是外部URL发生变化</li>
    <li>已经收藏旧链接的用户不会遇到404错误</li>
  </ul>
  
  <h2>6. 测试方案</h2>
  <p>实施此更改后，需要进行以下测试：</p>
  <ol>
    <li>验证所有新路径可以正常访问（如 <code>/login</code>、<code>/register</code> 等）</li>
    <li>验证所有旧路径会正确重定向到新路径（如 <code>/auth/login</code> → <code>/login</code>）</li>
    <li>验证登录、注册、忘记密码等功能正常工作</li>
    <li>验证需要认证的路由保护功能正常工作</li>
    <li>验证从受保护路由重定向到登录页面后，登录成功能正确返回到原页面</li>
  </ol>
  
  <div class="warning">
    <p><strong>警告：</strong> 如果网站已被搜索引擎索引或有用户收藏了旧链接，建议在一段时间内（如几个月）保留中间件中的重定向逻辑，确保用户体验不受影响。</p>
  </div>
  
  <h2>7. 总结</h2>
  <p>通过Next.js中间件的路径重写和重定向功能，我们成功简化了身份验证相关的URL路径，提升了用户体验和URL的美观性，同时保持了代码结构的一致性和向后兼容性。这种方法最小化了代码修改范围，降低了实现风险。</p>
  
  <hr>
  <p><em>文档创建日期：2023年12月29日</em></p>
</body>
</html> 