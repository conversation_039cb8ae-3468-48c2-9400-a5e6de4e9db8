<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <title>V1.5.3 ‑ CPT 社交分享前端集成（完整实现与调试）</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", <PERSON><PERSON>, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif; line-height: 1.7; padding: 2rem; max-width: 800px; margin: auto; background: #f9f9f9; }
    h1,h2,h3 { margin-top: 2.5rem; color:#1a1a1a; border-bottom: 1px solid #e0e0e0; padding-bottom: 0.5rem; }
    h1 { font-size: 2.2em; }
    h2 { font-size: 1.8em; }
    h3 { font-size: 1.4em; border: none; }
    pre { background:#2d2d2d; color:#f8f8f2; padding:1.2rem; overflow-x:auto; border-radius: 6px; border: 1px solid #444; }
    code{font-family:"SFMono-Regular",<PERSON>solas,"Liberation Mono",Menlo,monospace;}
    .inline-code { color:#c7254e; background:#f9f2f4; padding:0.2em 0.4em; border-radius:4px; font-size: 0.9em; }
    ul, ol { margin-left:1.5rem; padding-left: 1rem; }
    li { margin-bottom: 0.5rem; }
    .note { background: #fffbe6; border-left: 4px solid #ffc107; padding: 1rem; margin: 1.5rem 0; border-radius: 4px; }
    .success { background: #e8f5e9; border-left: 4px solid #4caf50; padding: 1rem; margin: 1.5rem 0; border-radius: 4px; }
    .summary-table { border-collapse: collapse; width: 100%; margin-top: 1.5rem; }
    .summary-table th, .summary-table td { border: 1px solid #ddd; padding: 0.8rem; text-align: left; }
    .summary-table th { background-color: #f2f2f2; }
  </style>
</head>
<body>
  <h1>V1.5.3 ‑ CPT 社交分享前端集成（完整实现与调试）</h1>

  <h2>一、最终成果</h2>
  <div class="success">
    <strong>目标完成</strong>：自定义文章类型（CPT）页面已成功集成社交分享功能，实现了与标准文章完全一致的分享体验，包括微信分享卡片的正常显示（标题、摘要、缩略图）。
  </div>

  <h2>二、实现阶段回顾</h2>
  <ul>
    <li>✅ <strong>阶段 0-1</strong>: 确认页面组件可正常渲染，并通过 GraphQL 查询成功获取了 <code>shareImage</code> 和 <code>featuredImageUrl</code> 字段。</li>
    <li>✅ <strong>阶段 2</strong>: 在 CPT 页面中嵌入了 <code>&lt;ShareButtons&gt;</code> 组件，并传入了基础参数（如 postId, postTitle, postUrl）。</li>
    <li>✅ <strong>阶段 3</strong>: 完整实现了微信 JS-SDK 的集成和调试，解决了分享卡片无图等核心问题。</li>
  </ul>
  
  <h2>三、核心实现：微信JS-SDK集成</h2>
  <p>微信分享的核心逻辑封装在 <code>ShareButtons.tsx</code> 组件的自定义 Hook <code>useWeChatShare</code> 中。该 Hook 完成了以下自动化流程：</p>
  <ol>
    <li><strong>环境判断</strong>：通过 <code>/MicroMessenger/i.test(navigator.userAgent)</code> 检查当前是否位于微信内置浏览器。</li>
    <li><strong>获取签名</strong>：若在微信环境中，则执行 <code>GET_WECHAT_CONFIG</code> GraphQL 查询，向后端请求 JS-SDK 所需的签名配置。</li>
    <li><strong>动态加载SDK</strong>：获取到签名后，动态创建 <code>&lt;script&gt;</code> 标签加载微信官方的 JS-SDK 文件。</li>
    <li><strong>配置并鉴权</strong>：SDK 加载完毕后，调用 <code>window.wx.config()</code> 方法，传入后端返回的 appId, timestamp, nonceStr, signature 进行鉴权。</li>
    <li><strong>设置分享内容</strong>：在 <code>window.wx.ready()</code> 回调中，调用 <code>updateAppMessageShareData</code> (分享给朋友) 和 <code>updateTimelineShareData</code> (分享到朋友圈) 方法，将当前页面的标题、链接、摘要和分享图URL传递给微信。</li>
  </ol>
  <pre><code>// 位于: src/components/share/ShareButtons.tsx -> useWeChatShare Hook

useEffect(() => {
  // 仅在微信浏览器且获取到SDK配置后执行
  if (isWeChatBrowser() && configData?.wechatSdkConfig) {
    
    // ... 动态加载JS-SDK脚本 ...
    
    script.onload = () => {
      // @ts-ignore
      window.wx.config({
        debug: false, // 生产环境建议关闭
        appId: configData.wechatSdkConfig.appId,
        timestamp: configData.wechatSdkConfig.timestamp,
        nonceStr: configData.wechatSdkConfig.nonceStr,
        signature: configData.wechatSdkConfig.signature,
        jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData']
      });

      // @ts-ignore
      window.wx.ready(() => {
        // 分享给朋友
        // @ts-ignore
        window.wx.updateAppMessageShareData({
          title: postTitle,
          desc: settings?.wechatDesc || postExcerpt || '',
          link: postUrl,
          imgUrl: shareImage || settings?.defaultThumb || '', // 核心图片URL
        });
        
        // 分享到朋友圈
        // @ts-ignore
        window.wx.updateTimelineShareData({
          title: postTitle,
          link: postUrl,
          imgUrl: shareImage || settings?.defaultThumb || '', // 核心图片URL
        });
      });
    };
  }
}, [configData, postTitle, postUrl, postExcerpt, shareImage, settings]);</code></pre>

  <h2>四、问题排查与解决方案</h2>
  <p>在集成过程中，我们遇到了"CPT文章分享到微信时，卡片不显示缩略图"的核心问题。通过一系列调试，最终定位并解决了问题。</p>

  <h3>1. 问题一：错误的图片降级策略</h3>
  <ul>
    <li><strong>现象</strong>: 有特色图的 CPT 文章，分享后无缩略图。</li>
    <li><strong>根源分析</strong>: CPT 页面在调用分享组件时，增加了一行计划外的降级逻辑：<code>shareImage={post.shareImage || post.featuredImageUrl}</code>。当 <code>shareImage</code> 因网络等原因未及时返回时，代码会降级使用 <code>featuredImageUrl</code>，而后者通常指向未经压缩的原图。过大的图片（体积或尺寸）会导致微信服务器抓取超时而失败，最终不显示图片。这违背了后端"图片策略中心化"的核心设计。</li>
    <li><strong>解决方案</strong>: 移除前端的降级逻辑，确保所有分享统一且只使用后端处理好的 <code>shareImage</code> 字段。</li>
  </ul>
  <pre><code>// 路径：src/app/post-type/[type]/[uuid]/[slug]/page.tsx

// -- 修改前 (错误) --
&lt;ShareButtons
  shareImage={post.shareImage || post.featuredImageUrl}
/&gt;

// -- 修改后 (正确) --
&lt;ShareButtons
  shareImage={post.shareImage}
/&gt;
</code></pre>
  
  <h3>2. 问题二：中文Slug与二维码复杂度</h3>
  <ul>
    <li><strong>现象</strong>: 包含中文 slug 的文章，生成的二维码"看起来"更复杂，并且分享时不稳定。</li>
    <li><strong>根源分析</strong>:
      <ul>
        <li><strong>二维码复杂度</strong>: 二维码的复杂度由其承载的数据量决定。中文 slug 经过 URL encode 后，字符串长度会显著增加（如"机"->"%E6%9C%BA"），因此二维码图案更密集。这属于正常现象。</li>
        <li><strong>分享不稳定</strong>: 起初怀疑是 URL 编码问题导致前后端签名用的 URL 不一致。通过在调试面板增加对 <code>postUrl</code> 和 <code>encodeURIComponent(postUrl)</code> 的监控，我们确认了前后端用于签名的 URL 字符串是完全一致的。因此，问题不在于签名本身。</li>
      </ul>
    </li>
    <li><strong>结论</strong>: 中文 slug 自身并非导致分享失败的直接原因。只要 URL 被正确编码且前后端统一，签名就不会出错。不稳定的根源仍是指向的图片资源本身或微信的缓存机制。</li>
  </ul>
  
  <h3>3. 问题三：无特色图的文章不显示默认图</h3>
  <ul>
      <li><strong>现象</strong>: 没有设置特色图的 CPT 文章，分享后也不显示在后台设置的默认缩略图。</li>
      <li><strong>根源分析</strong>:
          <ul>
              <li><strong>代码逻辑检查</strong>: 后端 <code>shareImage</code> 解析器逻辑正确，在无特色图时会返回默认图。前端 <code>ShareButtons</code> 组件也会使用从 `useShare` hook 中获取的 `settings.defaultThumb` 作为最终兜底。代码层面无误。</li>
              <li><strong>环境与缓存</strong>: 主要问题在于测试环境和微信缓存。在非微信浏览器中测试（如PC扫码后在系统浏览器打开），JS-SDK 不会执行，微信会尝试抓取页面的 Open Graph (OG) 标签，而我们尚未设置 OG 标签。此外，微信会对同一链接的分享内容进行缓存，如果首次抓取失败，后续分享也会失败。</li>
          </ul>
      </li>
      <li><strong>解决方案</strong>: 确保在真实的微信客户端内进行测试。对于缓存问题，可通过在分享链接后附加随机查询参数（如 <code>?v=timestamp</code>）来强制微信重新抓取。</li>
  </ul>

  <h2>五、最终验证结果</h2>
  <ul>
    <li>✅ 自定义文章（无论是否有特色图、无论slug是中文还是英文）在微信内分享，均能正确显示标题、摘要和缩略图。</li>
    <li>✅ 分享至微博、QQ、X 等其他平台的链接参数正确。</li>
    <li>✅ 分享海报生成功能正常。</li>
  </ul>

  <section>
    <h2>3 · 阶段 3 — 微信分享完整实现</h2>
    <p>在本阶段，我们补全了微信 JS-SDK 的集成，使自定义内容类型与普通文章一样，可以在微信内正常分享并显示缩略图。</p>

    <h3>3.1 主要改动</h3>
    <ul>
      <li><strong>补充数据</strong>：<code>postExcerpt</code>、<code>shareImage</code> 作为新 props 传入 <code>&lt;ShareButtons&gt;</code>。</li>
      <li><strong>统一图片策略</strong>：移除前端对 <code>featuredImageUrl</code> 的多余降级，始终使用后端生成的 <code>shareImage</code> 字段。</li>
      <li><strong>useWeChatShare Hook</strong>：
        <ul>
          <li>仅在微信浏览器内执行 <code>GET_WECHAT_CONFIG</code> 查询。</li>
          <li>动态加载 <code>jweixin-1.6.0.js</code> 并在 <code>window.wx.ready()</code> 中调用 <code>updateAppMessageShareData</code> 与 <code>updateTimelineShareData</code>。</li>
          <li>调试输出分享参数，方便排查签名或图片抓取问题。</li>
        </ul>
      </li>
      <li><strong>调试面板</strong>：新增 <code>link(postUrl)</code>、<code>encoded link</code>、<code>SDK config loaded</code> 等字段，可实时对比前后端 URL 是否一致。</li>
    </ul>

    <h3>3.2 关键代码片段</h3>
    <pre><code class="language-tsx">// page.tsx — 传参
&lt;ShareButtons
  postId={(post.databaseId || 0).toString()}
  postTitle={post.title}
  postUrl={`${SITE_URL}${buildCustomPostUrl(...)}`}
  postExcerpt={cleanExcerpt}
  shareImage={post.shareImage}
/&gt;

// ShareButtons.tsx — 微信分享
const { isWeChatBrowser, hasConfig } = useWeChatShare(postUrl, postTitle, postExcerpt, shareImage);
...
window.wx.updateAppMessageShareData({
  title: postTitle,
  desc: settings?.wechatDesc || postExcerpt || '',
  link: postUrl,
  imgUrl: shareImage || settings?.defaultThumb || ''
});</code></pre>

    <h3>3.3 验证结果</h3>
    <ul>
      <li>在微信内打开任意自定义类型文章，<code>WeChat browser</code> 与 <code>SDK loaded</code> 均为 <code>true</code>。</li>
      <li>分享给好友 / 朋友圈的卡片正确显示标题、描述与缩略图（含默认图场景）。</li>
      <li>二维码（桌面端）或"右上角 …"提示（移动端）均可正常分享。</li>
    </ul>

    <h3>3.4 后续优化</h3>
    <ol>
      <li>为 SEO 与非微信浏览器补充 OpenGraph / Twitter Card 元标签。</li>
      <li>增加失败重试与错误提示，提升鲁棒性。</li>
    </ol>
  </section>

</body>
</html> 