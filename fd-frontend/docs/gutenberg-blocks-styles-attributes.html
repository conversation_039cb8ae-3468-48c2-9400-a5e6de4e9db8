<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gutenberg区块样式属性参考文档</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            border-bottom: 2px solid #0073aa;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        h2 {
            margin-top: 30px;
            border-bottom: 1px solid #e5e5e5;
            padding-bottom: 5px;
        }
        h3 {
            margin-top: 25px;
            color: #0073aa;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        code {
            background-color: #f1f1f1;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            font-size: 0.9em;
        }
        .color-sample {
            display: inline-block;
            width: 24px;
            height: 24px;
            border-radius: 3px;
            border: 1px solid #ccc;
            vertical-align: middle;
            margin-right: 8px;
        }
        .gradient-sample {
            display: inline-block;
            width: 100px;
            height: 24px;
            border-radius: 3px;
            vertical-align: middle;
            margin-right: 8px;
        }
        .note {
            background-color: #f8f8f8;
            border-left: 4px solid #0073aa;
            padding: 12px 16px;
            margin: 20px 0;
        }
        .attr-group {
            margin-bottom: 40px;
        }
    </style>
</head>
<body>
    <h1>Gutenberg区块样式属性参考文档</h1>
    <p>本文档详细介绍了Gutenberg编辑器中区块可用的各种样式属性及其预设值，包括颜色、排版、间距、边框等。</p>

    <div class="attr-group">
        <h2>1. 颜色属性 (Color)</h2>
        <p>颜色属性控制区块中文本、背景、边框等元素的颜色。</p>

        <h3>1.1 文本颜色 (Text Color)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>color.text</code></li>
            <li><strong>属性名</strong>: <code>textColor</code>（预设颜色）或 <code>style.color.text</code>（自定义颜色）</li>
            <li><strong>默认启用</strong>: 是</li>
        </ul>

        <h3>1.2 背景颜色 (Background Color)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>color.background</code></li>
            <li><strong>属性名</strong>: <code>backgroundColor</code>（预设颜色）或 <code>style.color.background</code>（自定义颜色）</li>
            <li><strong>默认启用</strong>: 是</li>
        </ul>

        <h3>1.3 渐变背景 (Gradient)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>color.gradients</code></li>
            <li><strong>属性名</strong>: <code>gradient</code>（预设渐变）或 <code>style.color.gradient</code>（自定义渐变）</li>
            <li><strong>默认启用</strong>: 否</li>
        </ul>

        <h3>1.4 链接颜色 (Link Color)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>color.link</code></li>
            <li><strong>属性名</strong>: <code>style.elements.link.color.text</code></li>
            <li><strong>默认启用</strong>: 否</li>
        </ul>

        <h3>1.5 标题颜色 (Heading Color)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>color.heading</code></li>
            <li><strong>属性名</strong>: <code>style.elements.heading.color.text</code> 和 <code>style.elements.heading.color.background</code></li>
            <li><strong>默认启用</strong>: 否</li>
        </ul>

        <h3>1.6 按钮颜色 (Button Color)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>color.button</code></li>
            <li><strong>属性名</strong>: <code>style.elements.button.color.text</code> 和 <code>style.elements.button.color.background</code></li>
            <li><strong>默认启用</strong>: 否</li>
        </ul>

        <h3>1.7 标准调色板预设值</h3>
        <p>Gutenberg中的标准颜色预设通常包括以下选项，但实际可用的颜色取决于主题设置：</p>

        <table>
            <tr>
                <th>名称</th>
                <th>Slug</th>
                <th>颜色值</th>
                <th>示例</th>
            </tr>
            <tr>
                <td>黑色</td>
                <td><code>black</code></td>
                <td>#000000</td>
                <td><div class="color-sample" style="background-color: #000000;"></div></td>
            </tr>
            <tr>
                <td>白色</td>
                <td><code>white</code></td>
                <td>#ffffff</td>
                <td><div class="color-sample" style="background-color: #ffffff;"></div></td>
            </tr>
            <tr>
                <td>深灰色</td>
                <td><code>very-dark-grey</code></td>
                <td>#313131</td>
                <td><div class="color-sample" style="background-color: #313131;"></div></td>
            </tr>
            <tr>
                <td>深紫色</td>
                <td><code>strong-magenta</code></td>
                <td>#a156b4</td>
                <td><div class="color-sample" style="background-color: #a156b4;"></div></td>
            </tr>
            <tr>
                <td>浅紫色</td>
                <td><code>light-magenta</code></td>
                <td>#d0a5db</td>
                <td><div class="color-sample" style="background-color: #d0a5db;"></div></td>
            </tr>
        </table>

        <h3>1.8 标准渐变预设值</h3>
        <table>
            <tr>
                <th>名称</th>
                <th>Slug</th>
                <th>渐变值</th>
                <th>示例</th>
            </tr>
            <tr>
                <td>红酒色渐变</td>
                <td><code>blush-bordeaux</code></td>
                <td>linear-gradient(135deg,rgb(254,205,165) 0%,rgb(254,45,45) 50%,rgb(107,0,62) 100%)</td>
                <td>
                    <div class="gradient-sample" style="background: linear-gradient(135deg,rgb(254,205,165) 0%,rgb(254,45,45) 50%,rgb(107,0,62) 100%);"></div>
                </td>
            </tr>
            <tr>
                <td>浅紫渐变</td>
                <td><code>blush-light-purple</code></td>
                <td>linear-gradient(135deg,rgb(255,206,236) 0%,rgb(152,150,240) 100%)</td>
                <td>
                    <div class="gradient-sample" style="background: linear-gradient(135deg,rgb(255,206,236) 0%,rgb(152,150,240) 100%);"></div>
                </td>
            </tr>
        </table>
    </div>

    <div class="attr-group">
        <h2>2. 排版属性 (Typography)</h2>
        <p>排版属性控制区块中文本的外观和布局。</p>

        <h3>2.1 字体大小 (Font Size)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>typography.fontSize</code></li>
            <li><strong>属性名</strong>: <code>fontSize</code>（预设大小）或 <code>style.typography.fontSize</code>（自定义大小）</li>
            <li><strong>默认启用</strong>: 否</li>
        </ul>

        <h3>2.2 行高 (Line Height)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>typography.lineHeight</code></li>
            <li><strong>属性名</strong>: <code>style.typography.lineHeight</code></li>
            <li><strong>默认启用</strong>: 否</li>
        </ul>

        <h3>2.3 文本对齐 (Text Alignment)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>typography.textAlign</code></li>
            <li><strong>属性名</strong>: <code>align</code>（在区块属性中）或 <code>style.typography.textAlign</code></li>
            <li><strong>默认启用</strong>: 否</li>
            <li><strong>可选值</strong>: <code>left</code>, <code>center</code>, <code>right</code></li>
        </ul>

        <h3>2.4 字体系列 (Font Family)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>typography.__experimentalFontFamily</code></li>
            <li><strong>属性名</strong>: <code>fontFamily</code>（预设）或 <code>style.typography.fontFamily</code>（自定义）</li>
            <li><strong>默认启用</strong>: 否</li>
        </ul>

        <h3>2.5 字体样式 (Font Style)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>typography.__experimentalFontStyle</code></li>
            <li><strong>属性名</strong>: <code>style.typography.fontStyle</code></li>
            <li><strong>默认启用</strong>: 否</li>
            <li><strong>可选值</strong>: <code>normal</code>, <code>italic</code></li>
        </ul>

        <h3>2.6 字体粗细 (Font Weight)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>typography.__experimentalFontWeight</code></li>
            <li><strong>属性名</strong>: <code>style.typography.fontWeight</code></li>
            <li><strong>默认启用</strong>: 否</li>
            <li><strong>可选值</strong>: <code>normal</code>, <code>bold</code>, <code>100</code> 到 <code>900</code></li>
        </ul>

        <h3>2.7 字母间距 (Letter Spacing)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>typography.__experimentalLetterSpacing</code></li>
            <li><strong>属性名</strong>: <code>style.typography.letterSpacing</code></li>
            <li><strong>默认启用</strong>: 否</li>
        </ul>

        <h3>2.8 文本装饰 (Text Decoration)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>typography.__experimentalTextDecoration</code></li>
            <li><strong>属性名</strong>: <code>style.typography.textDecoration</code></li>
            <li><strong>默认启用</strong>: 否</li>
            <li><strong>可选值</strong>: <code>none</code>, <code>underline</code>, <code>line-through</code></li>
        </ul>

        <h3>2.9 文本转换 (Text Transform)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>typography.__experimentalTextTransform</code></li>
            <li><strong>属性名</strong>: <code>style.typography.textTransform</code></li>
            <li><strong>默认启用</strong>: 否</li>
            <li><strong>可选值</strong>: <code>none</code>, <code>uppercase</code>, <code>lowercase</code>, <code>capitalize</code></li>
        </ul>

        <h3>2.10 字体大小预设值</h3>
        <table>
            <tr>
                <th>名称</th>
                <th>Slug</th>
                <th>大小</th>
            </tr>
            <tr>
                <td>小</td>
                <td><code>small</code></td>
                <td>13px</td>
            </tr>
            <tr>
                <td>中</td>
                <td><code>medium</code></td>
                <td>20px</td>
            </tr>
            <tr>
                <td>大</td>
                <td><code>large</code></td>
                <td>36px</td>
            </tr>
            <tr>
                <td>特大</td>
                <td><code>x-large</code></td>
                <td>42px</td>
            </tr>
        </table>

        <h3>2.11 字体系列预设值</h3>
        <table>
            <tr>
                <th>名称</th>
                <th>Slug</th>
                <th>字体系列</th>
            </tr>
            <tr>
                <td>系统字体</td>
                <td><code>system-font</code></td>
                <td>-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif</td>
            </tr>
            <tr>
                <td>Helvetica或Arial</td>
                <td><code>helvetica-arial</code></td>
                <td>Helvetica Neue, Helvetica, Arial, sans-serif</td>
            </tr>
        </table>
    </div>

    <div class="attr-group">
        <h2>3. 尺寸与间距属性 (Dimensions & Spacing)</h2>
        <p>尺寸和间距属性控制区块的大小、边距和内边距。</p>

        <h3>3.1 外边距 (Margin)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>spacing.margin</code></li>
            <li><strong>属性名</strong>: <code>style.spacing.margin</code></li>
            <li><strong>默认启用</strong>: 否</li>
            <li><strong>可设置方向</strong>: top, right, bottom, left</li>
        </ul>

        <h3>3.2 内边距 (Padding)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>spacing.padding</code></li>
            <li><strong>属性名</strong>: <code>style.spacing.padding</code></li>
            <li><strong>默认启用</strong>: 否</li>
            <li><strong>可设置方向</strong>: top, right, bottom, left</li>
        </ul>

        <h3>3.3 区块间距 (Block Gap)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>spacing.blockGap</code></li>
            <li><strong>属性名</strong>: <code>style.spacing.blockGap</code></li>
            <li><strong>默认启用</strong>: 否</li>
            <li><strong>支持轴向</strong>: horizontal, vertical</li>
        </ul>

        <h3>3.4 间距预设值</h3>
        <table>
            <tr>
                <th>名称</th>
                <th>Slug</th>
                <th>大小</th>
            </tr>
            <tr>
                <td>小</td>
                <td><code>40</code></td>
                <td>1rem</td>
            </tr>
            <tr>
                <td>中</td>
                <td><code>50</code></td>
                <td>1.5rem</td>
            </tr>
            <tr>
                <td>大</td>
                <td><code>60</code></td>
                <td>2rem</td>
            </tr>
        </table>

        <h3>3.5 单位支持</h3>
        <p>间距属性支持的单位:</p>
        <ul>
            <li><code>px</code> - 像素</li>
            <li><code>em</code> - 相对于元素字体大小的单位</li>
            <li><code>rem</code> - 相对于根元素字体大小的单位</li>
            <li><code>vh</code> - 视口高度的百分比</li>
            <li><code>vw</code> - 视口宽度的百分比</li>
            <li><code>%</code> - 百分比</li>
        </ul>
    </div>

    <div class="attr-group">
        <h2>4. 边框属性 (Border)</h2>
        <p>边框属性控制区块边框的外观。</p>

        <h3>4.1 边框颜色 (Border Color)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>__experimentalBorder.color</code></li>
            <li><strong>属性名</strong>: <code>borderColor</code>（预设颜色）或 <code>style.border.color</code>（自定义颜色）</li>
            <li><strong>默认启用</strong>: 否</li>
            <li><strong>CSS类</strong>: <code>.has-{slug}-border-color</code></li>
        </ul>

        <h3>4.2 边框宽度 (Border Width)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>__experimentalBorder.width</code></li>
            <li><strong>属性名</strong>: <code>style.border.width</code></li>
            <li><strong>默认启用</strong>: 否</li>
            <li><strong>单位</strong>: px, em, rem等</li>
        </ul>

        <h3>4.3 边框样式 (Border Style)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>__experimentalBorder.style</code></li>
            <li><strong>属性名</strong>: <code>style.border.style</code></li>
            <li><strong>默认启用</strong>: 否</li>
            <li><strong>可选值</strong>: <code>none</code>, <code>solid</code>, <code>dashed</code>, <code>dotted</code>, <code>double</code></li>
        </ul>

        <h3>4.4 边框圆角 (Border Radius)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>__experimentalBorder.radius</code></li>
            <li><strong>属性名</strong>: <code>style.border.radius</code></li>
            <li><strong>默认启用</strong>: 否</li>
            <li><strong>单位</strong>: px, em, rem, %等</li>
            <li><strong>可设置角落</strong>: <code>topLeft</code>, <code>topRight</code>, <code>bottomLeft</code>, <code>bottomRight</code></li>
        </ul>

        <h3>4.5 边框方向</h3>
        <p>边框可以针对特定边进行设置：</p>
        <ul>
            <li><strong>上边框</strong>: <code>style.border.top</code></li>
            <li><strong>右边框</strong>: <code>style.border.right</code></li>
            <li><strong>下边框</strong>: <code>style.border.bottom</code></li>
            <li><strong>左边框</strong>: <code>style.border.left</code></li>
        </ul>

        <h3>4.6 边框圆角最大最小值</h3>
        <ul>
            <li><strong>最小值</strong>: 0</li>
            <li><strong>最大值 (px)</strong>: 100px</li>
            <li><strong>最大值 (em)</strong>: 20em</li>
            <li><strong>最大值 (rem)</strong>: 20rem</li>
        </ul>
    </div>

    <div class="attr-group">
        <h2>5. 背景属性 (Background)</h2>
        <p>背景属性控制区块的背景图像和其他背景设置。</p>

        <h3>5.1 背景图像 (Background Image)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>background.backgroundImage</code></li>
            <li><strong>属性名</strong>: <code>style.background.backgroundImage</code></li>
            <li><strong>默认启用</strong>: 否</li>
        </ul>

        <h3>5.2 背景尺寸 (Background Size)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>background.backgroundSize</code></li>
            <li><strong>属性名</strong>: <code>style.background.backgroundSize</code></li>
            <li><strong>默认启用</strong>: 否</li>
            <li><strong>可选值</strong>: <code>cover</code>, <code>contain</code>, <code>auto</code></li>
        </ul>

        <h3>5.3 背景位置 (Background Position)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>background.backgroundPosition</code></li>
            <li><strong>属性名</strong>: <code>style.background.backgroundPosition</code></li>
            <li><strong>默认启用</strong>: 否</li>
            <li><strong>可选值</strong>: <code>center center</code>, <code>top left</code>, <code>top center</code>, <code>top right</code>, <code>center left</code>, <code>center right</code>, <code>bottom left</code>, <code>bottom center</code>, <code>bottom right</code></li>
        </ul>

        <h3>5.4 背景重复 (Background Repeat)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>background.backgroundRepeat</code></li>
            <li><strong>属性名</strong>: <code>style.background.backgroundRepeat</code></li>
            <li><strong>默认启用</strong>: 否</li>
            <li><strong>可选值</strong>: <code>no-repeat</code>, <code>repeat</code>, <code>repeat-x</code>, <code>repeat-y</code></li>
        </ul>

        <h3>5.5 背景附着 (Background Attachment)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>background.backgroundAttachment</code></li>
            <li><strong>属性名</strong>: <code>style.background.backgroundAttachment</code></li>
            <li><strong>默认启用</strong>: 否</li>
            <li><strong>可选值</strong>: <code>scroll</code>, <code>fixed</code>, <code>local</code></li>
        </ul>
    </div>

    <div class="attr-group">
        <h2>6. 尺寸比例 (Dimensions)</h2>
        <p>控制区块的尺寸比例设置。</p>

        <h3>6.1 宽高比 (Aspect Ratio)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>dimensions.aspectRatio</code></li>
            <li><strong>属性名</strong>: <code>style.dimensions.aspectRatio</code></li>
            <li><strong>默认启用</strong>: 否</li>
        </ul>

        <h3>6.2 最小高度 (Min Height)</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>dimensions.minHeight</code></li>
            <li><strong>属性名</strong>: <code>style.dimensions.minHeight</code></li>
            <li><strong>默认启用</strong>: 否</li>
        </ul>

        <h3>6.3 宽高比预设值</h3>
        <table>
            <tr>
                <th>名称</th>
                <th>Slug</th>
                <th>比例</th>
            </tr>
            <tr>
                <td>正方形 - 1:1</td>
                <td><code>square</code></td>
                <td>1</td>
            </tr>
            <tr>
                <td>标准 - 4:3</td>
                <td><code>4-3</code></td>
                <td>4/3</td>
            </tr>
            <tr>
                <td>竖向 - 3:4</td>
                <td><code>3-4</code></td>
                <td>3/4</td>
            </tr>
            <tr>
                <td>经典 - 3:2</td>
                <td><code>3-2</code></td>
                <td>3/2</td>
            </tr>
            <tr>
                <td>经典竖向 - 2:3</td>
                <td><code>2-3</code></td>
                <td>2/3</td>
            </tr>
            <tr>
                <td>宽屏 - 16:9</td>
                <td><code>16-9</code></td>
                <td>16/9</td>
            </tr>
            <tr>
                <td>高屏 - 9:16</td>
                <td><code>9-16</code></td>
                <td>9/16</td>
            </tr>
        </table>
    </div>

    <div class="attr-group">
        <h2>7. 阴影 (Shadow)</h2>
        <p>阴影属性控制区块的阴影效果。</p>

        <h3>7.1 阴影属性</h3>
        <ul>
            <li><strong>支持方式</strong>: <code>shadow</code></li>
            <li><strong>属性名</strong>: <code>style.shadow</code></li>
            <li><strong>默认启用</strong>: 否</li>
        </ul>

        <h3>7.2 阴影预设值</h3>
        <table>
            <tr>
                <th>名称</th>
                <th>Slug</th>
                <th>阴影值</th>
            </tr>
            <tr>
                <td>自然</td>
                <td><code>natural</code></td>
                <td>6px 6px 9px rgba(0, 0, 0, 0.2)</td>
            </tr>
            <tr>
                <td>深度</td>
                <td><code>deep</code></td>
                <td>12px 12px 50px rgba(0, 0, 0, 0.4)</td>
            </tr>
            <tr>
                <td>锐利</td>
                <td><code>sharp</code></td>
                <td>6px 6px 0px rgba(0, 0, 0, 0.2)</td>
            </tr>
            <tr>
                <td>轮廓</td>
                <td><code>outlined</code></td>
                <td>6px 6px 0px -3px rgb(255, 255, 255), 6px 6px rgb(0, 0, 0)</td>
            </tr>
            <tr>
                <td>清晰</td>
                <td><code>crisp</code></td>
                <td>6px 6px 0px rgb(0, 0, 0)</td>
            </tr>
        </table>
    </div>

    <div class="note">
        <p><strong>注意</strong>：</p>
        <ul>
            <li>并非所有区块都支持所有样式属性。每个区块可以选择支持哪些属性。</li>
            <li>预设值可以通过主题的<code>theme.json</code>文件自定义或扩展。</li>
            <li>实验性属性（带有<code>__experimental</code>前缀）可能会在未来版本中变更或稳定。</li>
            <li>CSS自定义属性的命名模式为<code>--wp--preset--{preset-category}--{preset-slug}</code>，例如<code>--wp--preset--color--black</code>。</li>
            <li>生成的CSS类的命名模式为<code>.has-{preset-slug}-{preset-category}</code>，例如<code>.has-black-color</code>。</li>
        </ul>
    </div>

    <div class="attr-group">
        <h2>8. 区块支持配置示例</h2>
        <p>以下是一个完整的区块支持配置示例：</p>

        <pre><code>{
    // 颜色支持
    color: {
        text: true,             // 文本颜色
        background: true,       // 背景颜色
        gradients: true,        // 渐变背景
        link: true,             // 链接颜色
        heading: true,          // 标题颜色
        button: true            // 按钮颜色
    },
    
    // 排版支持
    typography: {
        fontSize: true,         // 字体大小
        lineHeight: true,       // 行高
        textAlign: true,        // 文本对齐
        __experimentalFontFamily: true,       // 字体系列
        __experimentalFontWeight: true,       // 字体粗细
        __experimentalFontStyle: true,        // 字体样式
        __experimentalTextTransform: true,    // 文本转换
        __experimentalTextDecoration: true,   // 文本装饰
        __experimentalLetterSpacing: true     // 字母间距
    },
    
    // 间距支持
    spacing: {
        margin: true,           // 外边距
        padding: true,          // 内边距
        blockGap: true          // 区块间距
    },
    
    // 边框支持
    __experimentalBorder: {
        radius: true,           // 圆角
        color: true,            // 边框颜色
        width: true,            // 边框宽度
        style: true             // 边框样式
    },
    
    // 背景支持
    background: {
        backgroundImage: true,  // 背景图片
        backgroundSize: true    // 背景尺寸
    },
    
    // 尺寸支持
    dimensions: {
        minHeight: true         // 最小高度
    }
}</code></pre>
    </div>

    <footer>
        <p>基于Gutenberg版本信息生成，可能会随WordPress版本更新而变化。</p>
    </footer>
</body>
</html> 