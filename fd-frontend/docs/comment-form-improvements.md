# 评论表单改进 - 智能通知与错误处理

## 概述

本次改进解决了评论表单中的两个关键问题：
1. **智能审核通知** - 根据WordPress后台设置显示正确的成功消息
2. **详细错误提示** - 显示GraphQL错误的具体信息，包括评论间隔和频次限制

## 修复内容

### 1. 智能审核通知

**问题**：无论WordPress后台是否开启评论审核，都显示"等待审核"消息

**解决方案**：
- 使用 `useDiscussionSettings` Hook获取WordPress讨论设置
- 根据 `defaultCommentStatus` 判断是否需要审核
- 显示相应的成功消息

```typescript
// 获取讨论设置
const { settings: discussion } = useDiscussionSettings();
const requiresApproval = discussion?.defaultCommentStatus !== 'open';

// 根据设置显示不同消息
{success && (
  <div className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-3 rounded mb-4">
    {requiresApproval ? '评论提交成功，等待审核' : '评论发布成功！'}
  </div>
)}
```

### 2. 详细错误处理

**问题**：GraphQL错误信息不够具体，用户无法了解具体的失败原因

**解决方案**：
- 解析GraphQL错误对象
- 识别常见的WordPress评论错误类型
- 显示用户友好的错误消息

```typescript
} catch (err: any) {
  console.error('[CommentForm] Error:', err);
  
  // 处理GraphQL错误
  let errorMessage = '评论提交失败，请稍后重试';
  
  if (err.graphQLErrors && err.graphQLErrors.length > 0) {
    const graphQLError = err.graphQLErrors[0];
    if (graphQLError.message) {
      // 处理常见的WordPress评论错误
      if (graphQLError.message.includes('duplicate comment')) {
        errorMessage = '请勿重复提交相同的评论';
      } else if (graphQLError.message.includes('too quickly') || graphQLError.message.includes('slow down')) {
        errorMessage = '评论提交过于频繁，请稍后再试';
      } else if (graphQLError.message.includes('flood')) {
        errorMessage = '评论间隔时间太短，请稍后再试';
      } else if (graphQLError.message.includes('spam')) {
        errorMessage = '评论被识别为垃圾信息，请修改后重试';
      } else {
        errorMessage = graphQLError.message;
      }
    }
  } else if (err.networkError) {
    errorMessage = '网络连接失败，请检查网络后重试';
  } else if (err.message) {
    errorMessage = err.message;
  }
  
  setError(errorMessage);
}
```

## 错误类型映射

| GraphQL错误关键词 | 用户友好消息 |
|------------------|-------------|
| `duplicate comment` | 请勿重复提交相同的评论 |
| `too quickly` / `slow down` | 评论提交过于频繁，请稍后再试 |
| `flood` | 评论间隔时间太短，请稍后再试 |
| `spam` | 评论被识别为垃圾信息，请修改后重试 |
| 网络错误 | 网络连接失败，请检查网络后重试 |

## 技术实现

### 依赖的Hook

1. **useDiscussionSettings** - 获取WordPress讨论设置
   ```typescript
   const { settings: discussion } = useDiscussionSettings();
   ```

2. **useCreateComment** - 创建评论，包含错误处理
   ```typescript
   const { createComment } = useCreateComment();
   ```

### 关键逻辑

1. **审核状态判断**
   ```typescript
   const requiresApproval = discussion?.defaultCommentStatus !== 'open';
   ```

2. **错误类型识别**
   ```typescript
   if (err.graphQLErrors && err.graphQLErrors.length > 0) {
     const graphQLError = err.graphQLErrors[0];
     // 根据错误消息内容判断错误类型
   }
   ```

## 用户体验改进

### 修复前
- ❌ 总是显示"等待审核"，即使不需要审核
- ❌ 错误信息模糊："评论提交失败，请稍后重试"
- ❌ 用户不知道具体的失败原因

### 修复后
- ✅ 根据WordPress设置显示正确的成功消息
- ✅ 具体的错误提示，帮助用户理解问题
- ✅ 针对不同错误类型的指导性建议

## 测试场景

1. **审核设置测试**
   - WordPress后台开启评论审核 → 显示"等待审核"
   - WordPress后台关闭评论审核 → 显示"发布成功"

2. **错误处理测试**
   - 快速连续提交评论 → 显示频率限制提示
   - 提交重复评论 → 显示重复评论提示
   - 网络断开时提交 → 显示网络错误提示

## 总结

这次改进显著提升了评论表单的用户体验：
- 🎯 **智能化**：根据后台设置自动调整提示信息
- 🔍 **透明化**：清晰的错误信息帮助用户理解问题
- 🛡️ **健壮性**：完善的错误处理机制
- 📱 **一致性**：与WordPress原生行为保持一致
