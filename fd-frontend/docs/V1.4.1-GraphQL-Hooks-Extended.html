<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Future Decade V1.5 - GraphQL Hooks扩展功能</title>
  <style>
    :root {
      --primary-color: #0066cc;
      --secondary-color: #666;
      --border-color: #e0e0e0;
      --code-bg: #f5f5f5;
      --code-color: #333;
      --link-color: #0066cc;
      --heading-color: #333;
      --text-color: #444;
      --background-color: #fff;
      --section-bg: #f9f9f9;
      --border-radius: 4px;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: var(--text-color);
      background-color: var(--background-color);
      margin: 0;
      padding: 0;
    }

    .container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem 1rem;
    }

    header {
      text-align: center;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid var(--border-color);
    }

    h1 {
      color: var(--primary-color);
      font-size: 2rem;
      margin: 0.5rem 0;
    }

    h2 {
      color: var(--heading-color);
      font-size: 1.6rem;
      margin: 2rem 0 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid var(--border-color);
    }

    h3 {
      color: var(--heading-color);
      font-size: 1.3rem;
      margin: 1.5rem 0 1rem;
    }

    h4 {
      color: var(--heading-color);
      font-size: 1.1rem;
      margin: 1.2rem 0 0.8rem;
    }

    p {
      margin: 0 0 1rem;
    }

    a {
      color: var(--link-color);
      text-decoration: none;
    }

    a:hover {
      text-decoration: underline;
    }

    .version {
      color: var(--secondary-color);
      font-size: 1rem;
    }

    .section {
      margin-bottom: 2rem;
      padding: 1.5rem;
      background-color: var(--section-bg);
      border-radius: var(--border-radius);
    }

    .subsection {
      margin-bottom: 1.5rem;
    }

    pre, code {
      font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
      font-size: 0.9rem;
    }

    pre {
      background-color: var(--code-bg);
      color: var(--code-color);
      padding: 1rem;
      border-radius: var(--border-radius);
      overflow-x: auto;
      margin: 1rem 0;
      line-height: 1.5;
    }

    code {
      background-color: var(--code-bg);
      color: var(--code-color);
      padding: 0.2rem 0.4rem;
      border-radius: var(--border-radius);
    }

    pre code {
      padding: 0;
      background-color: transparent;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin: 1rem 0;
    }

    table, th, td {
      border: 1px solid var(--border-color);
    }

    th, td {
      padding: 0.5rem;
      text-align: left;
    }

    th {
      background-color: var(--section-bg);
    }

    .toc {
      background-color: var(--section-bg);
      padding: 1rem;
      border-radius: var(--border-radius);
      margin-bottom: 2rem;
    }

    .toc ul {
      padding-left: 1.5rem;
      margin: 0;
    }

    .toc li {
      margin-bottom: 0.5rem;
    }

    .note {
      border-left: 4px solid var(--primary-color);
      padding: 0.5rem 1rem;
      background-color: rgba(0, 102, 204, 0.05);
      margin: 1rem 0;
    }

    .code-title {
      font-weight: bold;
      margin-bottom: 0.5rem;
    }

    .function-name {
      color: var(--primary-color);
      font-weight: bold;
    }

    @media print {
      .toc {
        break-before: always;
      }
      
      .section {
        break-inside: avoid;
      }
      
      pre {
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>Future Decade</h1>
      <p class="version">版本 1.5 - GraphQL Hooks扩展功能文档</p>
    </header>

    <section class="toc">
      <h2>目录</h2>
      <ul>
        <li><a href="#introduction">1. 介绍</a></li>
        <li><a href="#user-hooks">2. 用户相关Hooks</a>
          <ul>
            <li><a href="#use-user">2.1 useUser</a></li>
            <li><a href="#use-users">2.2 useUsers</a></li>
          </ul>
        </li>
        <li><a href="#settings-hooks">3. 设置相关Hooks</a>
          <ul>
            <li><a href="#use-all-settings">3.1 useAllSettings</a></li>
            <li><a href="#use-general-settings">3.2 useGeneralSettings</a></li>
            <li><a href="#use-reading-settings">3.3 useReadingSettings</a></li>
            <li><a href="#use-discussion-settings">3.4 useDiscussionSettings</a></li>
            <li><a href="#use-writing-settings">3.5 useWritingSettings</a></li>
          </ul>
        </li>
        <li><a href="#comment-hooks">4. 评论相关Hooks</a>
          <ul>
            <li><a href="#use-comments">4.1 useComments</a></li>
            <li><a href="#use-comment">4.2 useComment</a></li>
            <li><a href="#use-create-comment">4.3 useCreateComment</a></li>
            <li><a href="#use-update-comment">4.4 useUpdateComment</a></li>
            <li><a href="#use-delete-comment">4.5 useDeleteComment</a></li>
          </ul>
        </li>
        <li><a href="#search-hooks">5. 搜索相关Hooks</a>
          <ul>
            <li><a href="#use-search-posts">5.1 useSearchPosts</a></li>
            <li><a href="#use-search-content">5.2 useSearchContent</a></li>
          </ul>
        </li>
        <li><a href="#graphql-search">6. GraphQL搜索实现</a></li>
        <li><a href="#examples">7. 使用示例</a></li>
      </ul>
    </section>

    <section class="section" id="introduction">
      <h2>1. 介绍</h2>
      <p>本文档是V1.4 GraphQL Hooks API的扩展，介绍了V1.5版本新增的功能，包括用户、设置、评论和搜索等相关的hooks。这些hooks使您能够更全面地与WordPress GraphQL API进行交互。</p>
      
      <div class="note">
        <p>V1.5版本对GraphQL hooks进行了扩展，添加了更多功能性hooks，使前端应用能够更好地满足产品化、模块化和自定义的需求。</p>
      </div>
    </section>

    <section class="section" id="user-hooks">
      <h2>2. 用户相关Hooks</h2>
      
      <section class="subsection" id="use-user">
        <h3>2.1 useUser</h3>
        <p>获取单个用户信息的hook。</p>
        <table>
          <tr>
            <th width="30%">参数</th>
            <th>说明</th>
          </tr>
          <tr>
            <td><code>id: string</code></td>
            <td>用户ID</td>
          </tr>
        </table>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useUser = (id: string) => {
  const { data, loading, error, refetch } = useQuery&lt;UserData&gt;(
    GET_USER,
    {
      variables: { id },
      skip: !id
    }
  );

  return {
    user: data?.user,
    loading,
    error,
    refetch,
  };
};</code></pre>
      </section>

      <section class="subsection" id="use-users">
        <h3>2.2 useUsers</h3>
        <p>获取用户列表的hook。</p>
        <table>
          <tr>
            <th width="30%">参数</th>
            <th>说明</th>
          </tr>
          <tr>
            <td><code>first: number</code></td>
            <td>获取的用户数量，默认为10</td>
          </tr>
        </table>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useUsers = (first: number = 10) => {
  const { data, loading, error, refetch } = useQuery&lt;UsersData&gt;(
    GET_USERS,
    {
      variables: { first }
    }
  );

  return {
    users: data?.users?.nodes || [],
    loading,
    error,
    refetch,
  };
};</code></pre>
      </section>
    </section>

    <section class="section" id="settings-hooks">
      <h2>3. 设置相关Hooks</h2>
      
      <section class="subsection" id="use-all-settings">
        <h3>3.1 useAllSettings</h3>
        <p>获取所有WordPress设置的hook。</p>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useAllSettings = () => {
  const { data, loading, error, refetch } = useQuery&lt;AllSettingsData&gt;(GET_ALL_SETTINGS);

  return {
    settings: data?.allSettings,
    loading,
    error,
    refetch,
  };
};</code></pre>
      </section>

      <section class="subsection" id="use-general-settings">
        <h3>3.2 useGeneralSettings</h3>
        <p>获取WordPress常规设置的hook。</p>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useGeneralSettings = () => {
  const { data, loading, error, refetch } = useQuery&lt;GeneralSettingsData&gt;(GET_GENERAL_SETTINGS);

  return {
    settings: data?.generalSettings,
    loading,
    error,
    refetch,
  };
};</code></pre>
      </section>

      <section class="subsection" id="use-reading-settings">
        <h3>3.3 useReadingSettings</h3>
        <p>获取WordPress阅读设置的hook。</p>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useReadingSettings = () => {
  const { data, loading, error, refetch } = useQuery&lt;ReadingSettingsData&gt;(GET_READING_SETTINGS);

  return {
    settings: data?.readingSettings,
    loading,
    error,
    refetch,
  };
};</code></pre>
      </section>

      <section class="subsection" id="use-discussion-settings">
        <h3>3.4 useDiscussionSettings</h3>
        <p>获取WordPress讨论设置的hook。</p>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useDiscussionSettings = () => {
  const { data, loading, error, refetch } = useQuery&lt;DiscussionSettingsData&gt;(GET_DISCUSSION_SETTINGS);

  return {
    settings: data?.discussionSettings,
    loading,
    error,
    refetch,
  };
};</code></pre>
      </section>

      <section class="subsection" id="use-writing-settings">
        <h3>3.5 useWritingSettings</h3>
        <p>获取WordPress写作设置的hook。</p>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useWritingSettings = () => {
  const { data, loading, error, refetch } = useQuery&lt;WritingSettingsData&gt;(GET_WRITING_SETTINGS);

  return {
    settings: data?.writingSettings,
    loading,
    error,
    refetch,
  };
};</code></pre>
      </section>
    </section>

    <section class="section" id="comment-hooks">
      <h2>4. 评论相关Hooks</h2>
      
      <section class="subsection" id="use-comments">
        <h3>4.1 useComments</h3>
        <p>获取文章评论列表的hook。</p>
        <table>
          <tr>
            <th width="30%">参数</th>
            <th>说明</th>
          </tr>
          <tr>
            <td><code>postId: string</code></td>
            <td>文章ID</td>
          </tr>
          <tr>
            <td><code>first: number</code></td>
            <td>获取的评论数量，默认为20</td>
          </tr>
        </table>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useComments = (postId: string, first: number = 20) => {
  const { data, loading, error, refetch } = useQuery&lt;CommentsData&gt;(
    GET_POST_COMMENTS,
    {
      variables: { postId, first },
      skip: !postId
    }
  );

  return {
    comments: data?.comments?.nodes || [],
    loading,
    error,
    refetch,
  };
};</code></pre>
      </section>

      <section class="subsection" id="use-comment">
        <h3>4.2 useComment</h3>
        <p>获取单个评论详情的hook。</p>
        <table>
          <tr>
            <th width="30%">参数</th>
            <th>说明</th>
          </tr>
          <tr>
            <td><code>id: string</code></td>
            <td>评论ID</td>
          </tr>
        </table>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useComment = (id: string) => {
  const { data, loading, error, refetch } = useQuery&lt;CommentData&gt;(
    GET_COMMENT,
    {
      variables: { id },
      skip: !id
    }
  );

  return {
    comment: data?.comment,
    loading,
    error,
    refetch,
  };
};</code></pre>
      </section>

      <section class="subsection" id="use-create-comment">
        <h3>4.3 useCreateComment</h3>
        <p>创建评论的hook，使用GraphQL mutation。</p>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useCreateComment = () => {
  const [createComment, { data, loading, error }] = useMutation&lt;CreateCommentData&gt;(
    CREATE_COMMENT
  );

  const handleCreateComment = async (input: CreateCommentInput) => {
    try {
      const response = await createComment({
        variables: { input }
      });
      return response?.data?.createComment;
    } catch (err) {
      console.error('创建评论失败:', err);
      throw err;
    }
  };

  return {
    createComment: handleCreateComment,
    newComment: data?.createComment?.comment,
    loading,
    error,
  };
};</code></pre>
      </section>

      <section class="subsection" id="use-update-comment">
        <h3>4.4 useUpdateComment</h3>
        <p>更新评论的hook，使用GraphQL mutation。</p>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useUpdateComment = () => {
  const [updateComment, { data, loading, error }] = useMutation&lt;UpdateCommentData&gt;(
    UPDATE_COMMENT
  );

  const handleUpdateComment = async (input: UpdateCommentInput) => {
    try {
      const response = await updateComment({
        variables: { input }
      });
      return response?.data?.updateComment;
    } catch (err) {
      console.error('更新评论失败:', err);
      throw err;
    }
  };

  return {
    updateComment: handleUpdateComment,
    updatedComment: data?.updateComment?.comment,
    loading,
    error,
  };
};</code></pre>
      </section>

      <section class="subsection" id="use-delete-comment">
        <h3>4.5 useDeleteComment</h3>
        <p>删除评论的hook，使用GraphQL mutation。</p>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useDeleteComment = () => {
  const [deleteComment, { data, loading, error }] = useMutation&lt;DeleteCommentData&gt;(
    DELETE_COMMENT
  );

  const handleDeleteComment = async (id: string) => {
    try {
      const response = await deleteComment({
        variables: { id }
      });
      return response?.data?.deleteComment;
    } catch (err) {
      console.error('删除评论失败:', err);
      throw err;
    }
  };

  return {
    deleteComment: handleDeleteComment,
    deletedComment: data?.deleteComment?.comment,
    loading,
    error,
  };
};</code></pre>
      </section>
    </section>

    <section class="section" id="search-hooks">
      <h2>5. 搜索相关Hooks</h2>
      
      <section class="subsection" id="use-search-posts">
        <h3>5.1 useSearchPosts</h3>
        <p>搜索文章的hook。</p>
        <table>
          <tr>
            <th width="30%">参数</th>
            <th>说明</th>
          </tr>
          <tr>
            <td><code>search: string</code></td>
            <td>搜索关键词</td>
          </tr>
          <tr>
            <td><code>first: number</code></td>
            <td>获取的数量，默认为10</td>
          </tr>
        </table>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useSearchPosts = (search: string, first: number = 10) => {
  const { data, loading, error, refetch } = useQuery&lt;PostsSearchData&gt;(
    SEARCH_POSTS,
    {
      variables: { search, first },
      skip: !search
    }
  );

  return {
    posts: data?.posts?.nodes || [],
    loading,
    error,
    refetch,
  };
};</code></pre>
      </section>

      <section class="subsection" id="use-search-content">
        <h3>5.2 useSearchContent</h3>
        <p>高级搜索hook，可搜索多种内容类型，返回标准化的结果。</p>
        <table>
          <tr>
            <th width="30%">参数</th>
            <th>说明</th>
          </tr>
          <tr>
            <td><code>search: string</code></td>
            <td>搜索关键词</td>
          </tr>
          <tr>
            <td><code>types: string[]</code></td>
            <td>要搜索的内容类型数组，默认为 ['post']</td>
          </tr>
          <tr>
            <td><code>first: number</code></td>
            <td>获取的数量，默认为10</td>
          </tr>
        </table>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useSearchContent = (search: string, types: string[] = ['post'], first: number = 10) => {
  const { data, loading, error, refetch } = useQuery&lt;ContentSearchData&gt;(
    SEARCH_CONTENT,
    {
      variables: { search, types, first },
      skip: !search || types.length === 0
    }
  );

  // 处理搜索结果，将不同内容类型的结果标准化
  const results: ContentSearchResult[] = data?.contentNodes?.nodes.map(node => {
    return {
      id: node.id,
      title: node.title,
      slug: node.slug,
      date: node.date,
      content: (node as Post).content,
      excerpt: (node as Post).excerpt,
      contentType: (node as any).__typename || 'Unknown',
      featuredImage: node.featuredImage,
      uri: (node as CustomPost).uri,
    };
  }) || [];

  // 计算每种类型的结果数量
  const typeCounts: ContentTypeCount[] = [];
  if (results.length > 0) {
    const counts: Record<string, number> = {};
    results.forEach(result => {
      counts[result.contentType] = (counts[result.contentType] || 0) + 1;
    });
    
    Object.entries(counts).forEach(([type, count]) => {
      typeCounts.push({ type, count });
    });
  }

  return {
    results,
    typeCounts,
    loading,
    error,
    refetch,
  };
};</code></pre>
      </section>
    </section>

    <section class="section" id="graphql-search">
      <h2>6. GraphQL搜索实现</h2>
      <p>与REST API不同，GraphQL不需要专门的搜索端点。在GraphQL中，搜索是通过对现有类型增加搜索参数来实现的。</p>
      
      <div class="note">
        <p>WordPress GraphQL 支持两种主要的搜索方式：</p>
      </div>
      
      <h3>6.1 基于特定类型的搜索</h3>
      <p>针对特定内容类型（如posts, pages等）的搜索，使用该类型的<code>where</code>参数中的<code>search</code>字段：</p>
      
      <pre><code>query SearchPosts($search: String!) {
  posts(where: { search: $search }) {
    nodes {
      id
      title
      excerpt
      date
    }
  }
}</code></pre>

      <h3>6.2 跨类型的统一搜索</h3>
      <p>GraphQL提供了<code>contentNodes</code>查询，可以搜索多种内容类型：</p>
      
      <pre><code>query SearchContent($search: String!, $types: [ContentTypeEnum]) {
  contentNodes(where: { search: $search, contentTypes: $types }) {
    nodes {
      __typename
      id
      ... on NodeWithTitle {
        title
      }
      ... on ContentNode {
        slug
        date
      }
      ... on NodeWithContentEditor {
        content
      }
    }
  }
}</code></pre>

      <h3>6.3 搜索特性</h3>
      <ul>
        <li><strong>灵活性</strong>：可以通过接口和片段组合，获取不同内容类型的特定字段</li>
        <li><strong>效率</strong>：一次查询获取多种内容类型</li>
        <li><strong>类型安全</strong>：使用<code>__typename</code>字段区分不同类型</li>
        <li><strong>分面搜索</strong>：可以计算不同内容类型的结果数量</li>
      </ul>
      
      <h3>6.4 搜索实现优化</h3>
      <p>GraphQL搜索实现了以下优化：</p>
      <ul>
        <li>使用接口和片段处理不同内容类型的共性和差异</li>
        <li>仅加载必要字段，减少数据传输</li>
        <li>在客户端将结果归一化，便于UI渲染</li>
        <li>支持分类统计，便于实现分面搜索</li>
      </ul>
    </section>

    <section class="section" id="examples">
      <h2>7. 使用示例</h2>
      
      <h3>7.1 评论系统</h3>
      <pre><code>import { useComments, useCreateComment } from '@/api/useGraphQL';

const CommentSection = ({ postId }) => {
  const { comments, loading, error, refetch } = useComments(postId);
  const { createComment, loading: submitting } = useCreateComment();
  const [content, setContent] = useState('');
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (content.trim()) {
      try {
        await createComment({
          commentOn: parseInt(postId),
          content
        });
        setContent('');
        refetch(); // 刷新评论列表
      } catch (err) {
        alert('提交评论失败');
      }
    }
  };
  
  if (loading) return &lt;div&gt;加载评论中...&lt;/div&gt;;
  if (error) return &lt;div&gt;加载评论失败: {error.message}&lt;/div&gt;;
  
  return (
    &lt;div className="comments-section"&gt;
      &lt;h3&gt;评论 ({comments.length})&lt;/h3&gt;
      
      &lt;form onSubmit={handleSubmit}&gt;
        &lt;textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          placeholder="发表评论..."
          rows={4}
        /&gt;
        &lt;button type="submit" disabled={submitting}&gt;
          {submitting ? '提交中...' : '提交评论'}
        &lt;/button&gt;
      &lt;/form&gt;
      
      &lt;div className="comments-list"&gt;
        {comments.map(comment => (
          &lt;div key={comment.id} className="comment"&gt;
            &lt;div className="comment-author"&gt;
              {comment.author?.node.name || '匿名用户'}
              &lt;span className="comment-date"&gt;
                {new Date(comment.date).toLocaleDateString()}
              &lt;/span&gt;
            &lt;/div&gt;
            &lt;div className="comment-content"
              dangerouslySetInnerHTML={{ __html: comment.content }}
            /&gt;
          &lt;/div&gt;
        ))}
      &lt;/div&gt;
    &lt;/div&gt;
  );
};</code></pre>

      <h3>7.2 高级搜索</h3>
      <pre><code>import { useSearchContent } from '@/api/useGraphQL';

const SearchPage = () => {
  const [query, setQuery] = useState('');
  const [types, setTypes] = useState(['post', 'page', 'product']);
  const { results, typeCounts, loading } = useSearchContent(query, types);
  
  return (
    &lt;div&gt;
      &lt;div className="search-form"&gt;
        &lt;input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="搜索关键词..."
        /&gt;
        
        &lt;div className="content-types"&gt;
          {['post', 'page', 'product'].map(type => (
            &lt;label key={type}&gt;
              &lt;input
                type="checkbox"
                checked={types.includes(type)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setTypes([...types, type]);
                  } else {
                    setTypes(types.filter(t => t !== type));
                  }
                }}
              /&gt;
              {type}
            &lt;/label&gt;
          ))}
        &lt;/div&gt;
      &lt;/div&gt;
      
      {loading ? (
        &lt;div&gt;搜索中...&lt;/div&gt;
      ) : (
        &lt;&gt;
          &lt;div className="search-stats"&gt;
            共找到 {results.length} 个结果
            {typeCounts.map(({type, count}) => (
              &lt;span key={type}&gt;{type}: {count}&lt;/span&gt;
            ))}
          &lt;/div&gt;
          
          &lt;div className="search-results"&gt;
            {results.map(item => (
              &lt;div key={item.id} className="search-result-item"&gt;
                &lt;span className="content-type"&gt;{item.contentType}&lt;/span&gt;
                &lt;h3&gt;
                  &lt;a href={item.uri || `/${item.contentType}/${item.slug}`}&gt;
                    {item.title}
                  &lt;/a&gt;
                &lt;/h3&gt;
                {item.excerpt && (
                  &lt;div className="excerpt" 
                    dangerouslySetInnerHTML={{ __html: item.excerpt }}
                  /&gt;
                )}
                &lt;div className="meta"&gt;
                  {new Date(item.date).toLocaleDateString()}
                &lt;/div&gt;
              &lt;/div&gt;
            ))}
          &lt;/div&gt;
        &lt;/&gt;
      )}
    &lt;/div&gt;
  );
};</code></pre>

      <h3>7.3 站点设置</h3>
      <pre><code>import { useGeneralSettings } from '@/api/useGraphQL';

const SiteHeader = () => {
  const { settings, loading } = useGeneralSettings();
  
  if (loading) return null;
  
  return (
    &lt;header&gt;
      &lt;div className="site-branding"&gt;
        &lt;h1 className="site-title"&gt;
          &lt;a href="/"&gt;{settings?.title || 'My Website'}&lt;/a&gt;
        &lt;/h1&gt;
        &lt;p className="site-description"&gt;
          {settings?.description}
        &lt;/p&gt;
      &lt;/div&gt;
    &lt;/header&gt;
  );
};</code></pre>
    </section>
  </div>
</body>
</html> 