<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>V1.5.2 菜单性能优化重构 - Future Decade Frontend 文档</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3, h4 {
      color: #2c3e50;
      margin-top: 1.5em;
      margin-bottom: 0.5em;
    }
    h1 { 
      font-size: 2.2em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    h2 {
      font-size: 1.8em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    h3 { font-size: 1.5em; }
    h4 { font-size: 1.3em; }
    code {
      font-family: <PERSON><PERSON>, Monaco, Con<PERSON><PERSON>, "Courier New", monospace;
      padding: 0.2em 0.4em;
      background-color: #f3f4f5;
      border-radius: 3px;
      font-size: 0.9em;
    }
    pre {
      background-color: #f6f8fa;
      border-radius: 3px;
      padding: 16px;
      overflow: auto;
    }
    pre code {
      background-color: transparent;
      padding: 0;
    }
    a {
      color: #0366d6;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 20px 0;
    }
    th, td {
      border: 1px solid #dfe2e5;
      padding: 8px 12px;
      text-align: left;
    }
    th {
      background-color: #f6f8fa;
      font-weight: 600;
    }
    .note {
      background-color: #f8f9fa;
      border-left: 4px solid #007bff;
      padding: 15px;
      margin: 20px 0;
    }
    .warning {
      background-color: #fff3cd;
      border-left: 4px solid #ffc107;
      padding: 15px;
      margin: 20px 0;
    }
    .important {
      background-color: #f8d7da;
      border-left: 4px solid #dc3545;
      padding: 15px;
      margin: 20px 0;
    }
    .success {
      background-color: #d4edda;
      border-left: 4px solid #28a745;
      padding: 15px;
      margin: 20px 0;
    }
    .performance-table {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
    }
    .performance-table table {
      color: white;
    }
    .performance-table th {
      background-color: rgba(255,255,255,0.1);
      color: white;
    }
    .performance-table td {
      border-color: rgba(255,255,255,0.2);
    }
    .code-section {
      background: #1e1e1e;
      color: #d4d4d4;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
    }
    .code-section pre {
      background: transparent;
      color: #d4d4d4;
      margin: 0;
    }
    .architecture-diagram {
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
      text-align: center;
    }
  </style>
</head>
<body>
  <h1>V1.5.2 菜单性能优化重构</h1>
  <p>版本: 1.5.2<br>更新日期: 2024年7月17日<br>作者: Augment Agent</p>

  <div class="important">
    <strong>重构目标:</strong> 将菜单加载性能提升至与Category页面相同水平，实现零客户端延迟的菜单显示。
  </div>

  <h2>1. 问题分析</h2>
  
  <h3>1.1 原有架构的性能瓶颈</h3>
  <p>在重构前，菜单系统存在以下性能问题：</p>
  
  <ul>
    <li><strong>客户端依赖:</strong> 菜单完全依赖客户端GraphQL查询</li>
    <li><strong>每页重新查询:</strong> 每次页面加载都需要等待网络请求</li>
    <li><strong>缺乏缓存策略:</strong> 没有服务端缓存，仅依赖Apollo Client缓存</li>
    <li><strong>加载延迟明显:</strong> 用户可以明显感知到菜单的加载过程</li>
  </ul>

  <div class="performance-table">
    <h4>性能对比分析</h4>
    <table>
      <thead>
        <tr>
          <th>对比项</th>
          <th>Category页面</th>
          <th>菜单组件(重构前)</th>
          <th>差距</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>数据获取方式</td>
          <td>服务端预获取</td>
          <td>客户端GraphQL查询</td>
          <td>❌ 明显差距</td>
        </tr>
        <tr>
          <td>缓存策略</td>
          <td>ISR (revalidate: 600)</td>
          <td>Apollo Client缓存</td>
          <td>❌ 缓存效果差</td>
        </tr>
        <tr>
          <td>首屏显示</td>
          <td>立即显示</td>
          <td>等待查询完成</td>
          <td>❌ 用户体验差</td>
        </tr>
        <tr>
          <td>页面切换</td>
          <td>缓存命中</td>
          <td>重新查询</td>
          <td>❌ 性能浪费</td>
        </tr>
      </tbody>
    </table>
  </div>

  <h3>1.2 用户体验影响</h3>
  <p>原有实现导致的用户体验问题：</p>
  <ul>
    <li>菜单加载时显示骨架屏，用户感知明显</li>
    <li>页面切换时菜单重新加载，体验不连贯</li>
    <li>网络较慢时菜单加载时间过长</li>
    <li>与其他页面(如Category)的加载速度形成明显对比</li>
  </ul>

  <h2>2. 重构方案设计</h2>

  <h3>2.1 核心设计思路</h3>
  <p>基于Category页面的成功模式，设计菜单性能优化方案：</p>
  
  <div class="architecture-diagram">
    <h4>新架构流程图</h4>
    <pre>
RootLayout (app/layout.tsx)
├── fetchMenuData() ────────── 服务端预获取菜单数据
├── MenuProvider ─────────────── 全局菜单Context
└── 所有页面
    └── MainLayout ──────────── 从Context获取菜单，立即显示
        ├── Header ──────────── NavigationMenu + MobileMenu
        └── Footer ──────────── FooterMenu
    </pre>
  </div>

  <h3>2.2 技术实现策略</h3>
  <ul>
    <li><strong>服务端预获取:</strong> 在根布局中预获取菜单数据</li>
    <li><strong>Context全局共享:</strong> 通过React Context提供菜单数据</li>
    <li><strong>ISR缓存策略:</strong> 与Category页面相同的缓存配置</li>
    <li><strong>智能降级机制:</strong> Context + GraphQL双重保障</li>
  </ul>

  <h2>3. 核心实现</h2>

  <h3>3.1 菜单数据获取服务</h3>
  <p>创建专门的菜单数据获取服务，实现服务端预获取：</p>

  <div class="code-section">
    <h4>lib/menu-data.ts</h4>
    <pre><code>// 服务端获取菜单数据
export async function fetchMenuData(): Promise&lt;{
  topMenu: MenuItem[];
  footerMenu: MenuItem[];
} | null&gt; {
  const GRAPHQL_ENDPOINT = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql';
  
  const query = `
    query GetMenus {
      menus {
        nodes {
          id
          name
          menuItems {
            nodes {
              id
              title
              label
              url
              target
              parentId
              cssClasses
            }
          }
        }
      }
    }
  `;

  try {
    const res = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query }),
      next: {
        revalidate: 600, // 10分钟缓存，与category页面相同
        tags: ['menus'] // 缓存标签，用于按需重新验证
      },
    });

    const json = await res.json();
    
    if (json.errors) {
      console.error('GraphQL errors:', json.errors);
      return null;
    }

    const menus = json.data?.menus?.nodes || [];
    
    // 找到顶部菜单和底部菜单
    const topMenuData = menus.find((menu) =&gt; menu.name === '顶部菜单');
    const footerMenuData = menus.find((menu) =&gt; menu.name === '底部菜单');
    
    // 转换为层次结构
    const topMenu = topMenuData ? flatListToHierarchical(topMenuData.menuItems?.nodes || []) : [];
    const footerMenu = footerMenuData ? flatListToHierarchical(footerMenuData.menuItems?.nodes || []) : [];

    return {
      topMenu,
      footerMenu
    };
  } catch (error) {
    console.error('Error fetching menu data:', error);
    return null;
  }
}</code></pre>
  </div>

  <div class="note">
    <strong>关键特性:</strong>
    <ul>
      <li><code>revalidate: 600</code> - 与Category页面相同的10分钟ISR缓存</li>
      <li><code>tags: ['menus']</code> - 支持按需重新验证</li>
      <li><code>flatListToHierarchical</code> - 将扁平菜单转换为层次结构</li>
      <li>错误处理和日志记录</li>
    </ul>
  </div>
</body>
</html>
