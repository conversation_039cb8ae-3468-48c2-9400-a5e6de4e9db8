# 评论层级显示修复

## 问题描述

1. **普通文章页**：评论提交成功后没有层级显示，回复评论都显示为根评论
2. **自定义类型文章页**：评论回复之后没有显示出来

## 问题分析

### 根本原因

1. **ID格式不匹配**：
   - CommentItem传递`comment.databaseId`作为parentId
   - CommentList使用`comment.parentId`来组织层级
   - WordPress GraphQL返回的parentId可能是"comment:123"格式

2. **键值映射错误**：
   - CommentList使用`comment.databaseId.toString()`作为键
   - 但parentId可能是不同格式的字符串

3. **参数传递缺失**：
   - CommentItem缺少`isCustomType`参数传递

## 修复方案

### 1. 改进CommentList组件

**文件**：`fd-frontend/src/components/comments/CommentList.tsx`

**修改内容**：
- 添加`isCustomType`参数
- 改进parentId解析逻辑，支持"comment:123"格式
- 改进回复查找逻辑，使用多种可能的键
- 添加调试日志

```typescript
// 改进的parentId解析
let parentKey = comment.parentId || '';

// 如果parentId包含冒号，提取后面的数字部分
if (parentKey.includes(':')) {
  parentKey = parentKey.split(':')[1];
}

// 改进的回复查找
const possibleKeys = [
  comment.databaseId?.toString(),
  comment.id,
  comment.id.includes(':') ? comment.id.split(':')[1] : comment.id
].filter(Boolean);

let replies: Comment[] = [];
for (const key of possibleKeys) {
  if (childComments[key]) {
    replies = childComments[key];
    break;
  }
}
```

### 2. 更新CommentItem组件

**文件**：`fd-frontend/src/components/comments/CommentItem.tsx`

**修改内容**：
- 添加`isCustomType`参数
- 传递`isCustomType`给CommentForm和嵌套的CommentItem

```typescript
interface CommentItemProps {
  comment: Comment;
  postId: number;
  onCommentAdded: () => void;
  replies?: Comment[];
  isCustomType?: boolean; // 新增
}

// 传递给CommentForm
<CommentForm
  postId={postId}
  parentId={comment.databaseId}
  onCommentAdded={() => {
    setIsReplying(false);
    onCommentAdded();
  }}
  isCustomType={isCustomType} // 新增
/>

// 传递给嵌套CommentItem
<CommentItem
  key={reply.id}
  comment={reply}
  postId={postId}
  onCommentAdded={onCommentAdded}
  isCustomType={isCustomType} // 新增
/>
```

### 3. 更新CommentSection组件

**文件**：`fd-frontend/src/components/comments/CommentSection.tsx`

**修改内容**：
- 传递`isCustomType`参数给CommentList

```typescript
<CommentList 
  comments={comments} 
  postId={postId} 
  onCommentAdded={handleCommentAdded}
  isCustomType={isCustomType} // 新增
/>
```

## 调试功能

添加了详细的调试日志来帮助排查问题：

```typescript
console.log('[CommentList] Organizing comments:', comments.map(c => ({
  id: c.id,
  databaseId: c.databaseId,
  parentId: c.parentId,
  content: c.content.substring(0, 50) + '...'
})));

console.log('[CommentList] Child comment:', {
  id: comment.id,
  parentId: comment.parentId,
  parentKey,
  content: comment.content.substring(0, 30) + '...'
});

console.log('[CommentList] Organized result:', {
  parentCount: parentComments.length,
  childGroups: Object.keys(childComments).map(key => ({
    parentKey: key,
    childCount: childComments[key].length
  }))
});
```

## 修复的文件列表

1. `fd-frontend/src/components/comments/CommentList.tsx`
2. `fd-frontend/src/components/comments/CommentItem.tsx`
3. `fd-frontend/src/components/comments/CommentSection.tsx`

## 测试验证

### 测试场景

1. **普通文章页测试**：
   - 发布根评论 → 应该显示为根评论
   - 回复评论 → 应该显示为子评论，有缩进

2. **自定义类型文章页测试**：
   - 发布根评论 → 应该显示为根评论
   - 回复评论 → 应该显示为子评论，有缩进

3. **多层级测试**：
   - 回复的回复 → 应该正确显示层级关系

### 预期效果

- ✅ 所有评论都能正确显示层级关系
- ✅ 回复评论显示在父评论下方，有适当缩进
- ✅ 乐观更新后层级关系立即可见
- ✅ 刷新页面后层级关系保持正确

## 技术要点

1. **ID格式处理**：支持WordPress GraphQL返回的不同ID格式
2. **多键查找**：使用多种可能的键来查找回复关系
3. **参数传递**：确保`isCustomType`参数在整个组件树中正确传递
4. **调试支持**：添加详细日志帮助问题排查

这次修复解决了评论层级显示的核心问题，确保了普通文章和自定义类型文章的评论都能正确显示层级关系。
