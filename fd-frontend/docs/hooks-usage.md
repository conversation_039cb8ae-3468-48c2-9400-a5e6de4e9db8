# GraphQL Hooks 使用指南

本文档介绍如何在Future Decade前端中使用GraphQL数据获取hooks。

## 目录

- [基本用法](#基本用法)
- [可用的hooks](#可用的hooks)
  - [文章相关](#文章相关)
  - [分类相关](#分类相关)
  - [自定义内容类型](#自定义内容类型)
  - [分类法相关](#分类法相关)
  - [菜单相关](#菜单相关)
- [高级用法](#高级用法)
- [错误处理](#错误处理)
- [组件示例](#组件示例)

## 基本用法

所有的hooks都已经被封装并从统一的API导出，您可以这样导入它们：

```tsx
// 导入单个hook
import { usePosts } from '@/api/useGraphQL';

// 导入多个hooks
import { usePost, useCategories, useMenu } from '@/api/useGraphQL';
```

然后在组件中使用：

```tsx
const PostsList = () => {
  const { posts, loading, error } = usePosts({ first: 10 });

  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error.message}</div>;

  return (
    <ul>
      {posts.map(post => (
        <li key={post.id}>{post.title}</li>
      ))}
    </ul>
  );
};
```

## 可用的hooks

### 文章相关

#### `usePosts`

获取文章列表。

```tsx
const { posts, loading, error, loadMore, refetch } = usePosts({
  first: 10,           // 获取数量，默认10
  after: 'cursor',     // 分页游标
  categoryId: 'cat1',  // 按分类筛选
  tagId: 'tag1'        // 按标签筛选
});
```

#### `usePost`

获取单篇文章详情。

```tsx
// 通过slug获取
const { post, loading, error, refetch } = usePost({ slug: 'hello-world' });

// 通过ID获取
const { post, loading, error, refetch } = usePost({ id: '123' });
```

### 分类相关

#### `useCategories`

获取所有分类列表。

```tsx
const { categories, loading, error, refetch } = useCategories();
```

#### `useCategory`

获取单个分类详情。

```tsx
const { category, loading, error, refetch } = useCategory('news');
```

### 自定义内容类型

#### `useCustomPosts`

获取自定义内容类型列表。

```tsx
const { posts, loading, error, loadMore, refetch } = useCustomPosts({
  type: 'product',  // 内容类型，必填
  first: 10,        // 获取数量，默认10
  after: 'cursor'   // 分页游标
});
```

#### `useCustomPost`

获取单个自定义内容。

```tsx
// 通过slug获取
const { post, loading, error, refetch } = useCustomPost({
  type: 'product',
  slug: 'product-1'
});

// 通过ID获取
const { post, loading, error, refetch } = useCustomPost({
  type: 'product',
  id: '123'
});
```

### 分类法相关

#### `useTaxonomies`

获取所有分类法列表。

```tsx
const { taxonomies, loading, error, refetch } = useTaxonomies();
```

#### `useTaxonomy`

获取单个分类法详情。

```tsx
const { taxonomy, loading, error, refetch } = useTaxonomy('product_category');
```

### 菜单相关

#### `useMenu`

获取指定名称的菜单。

```tsx
// 获取顶部菜单
const { menuItems, loading, error, refetch } = useMenu('顶部菜单');

// 获取底部菜单
const { menuItems, loading, error, refetch } = useMenu('底部菜单');
```

## 高级用法

### 使用Apollo Client的缓存

所有的hooks都利用Apollo Client的缓存机制。默认情况下，数据会被缓存，并在后续请求中重用。

### 强制刷新数据

使用`refetch`函数可以强制重新获取数据：

```tsx
const { posts, refetch } = usePosts();

// 刷新数据
const refreshData = () => {
  refetch();
};
```

### 加载更多数据（分页）

对于支持分页的hooks，可以使用`loadMore`函数：

```tsx
const { posts, loading, loadMore } = usePosts({ first: 10 });

// 加载更多
const handleLoadMore = () => {
  if (posts.length > 0) {
    const lastPost = posts[posts.length - 1];
    loadMore(lastPost.cursor);
  }
};
```

## 错误处理

所有hooks都提供统一的错误处理方式：

```tsx
const { data, loading, error } = usePost({ slug: 'hello-world' });

if (error) {
  // 处理不同类型的错误
  if (error.networkError) {
    // 网络错误
    console.error('网络错误:', error.networkError);
  } else if (error.graphQLErrors) {
    // GraphQL错误
    error.graphQLErrors.forEach(err => {
      console.error('GraphQL错误:', err.message);
    });
  } else {
    // 其他错误
    console.error('未知错误:', error.message);
  }
}
```

## 组件示例

### 文章列表组件

```tsx
import { usePosts } from '@/api/useGraphQL';

const PostsList = () => {
  const { posts, loading, error, loadMore } = usePosts({ first: 5 });

  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error.message}</div>;

  return (
    <div>
      <h2>最新文章</h2>
      <ul>
        {posts.map(post => (
          <li key={post.id}>
            <h3>{post.title}</h3>
            <div dangerouslySetInnerHTML={{ __html: post.excerpt }} />
          </li>
        ))}
      </ul>
      <button onClick={() => loadMore()}>加载更多</button>
    </div>
  );
};
```

### 菜单组件示例

```tsx
import { useMenu } from '@/api/useGraphQL';
import { useState, useEffect } from 'react';
import Link from 'next/link';

const NavigationMenu = () => {
  const { menuItems, loading } = useMenu('顶部菜单');
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  if (!mounted || loading) return <div>加载菜单...</div>;
  
  return (
    <nav className="flex space-x-4">
      {menuItems.map(item => (
        !item.parentId && (
          <Link 
            key={item.id}
            href={item.url}
            target={item.target || '_self'}
            className="text-gray-600 hover:text-blue-600"
          >
            {item.label || item.title}
          </Link>
        )
      ))}
    </nav>
  );
};

export default NavigationMenu;
```

### 文章详情页组件

```tsx
import { usePost } from '@/api/useGraphQL';

const PostDetail = ({ slug }) => {
  const { post, loading, error } = usePost({ slug });

  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error.message}</div>;
  if (!post) return <div>文章不存在</div>;

  return (
    <article>
      <h1>{post.title}</h1>
      <div className="metadata">
        <time>{new Date(post.date).toLocaleDateString()}</time>
        {post.author && <span>作者: {post.author.node.name}</span>}
      </div>
      <div className="content" dangerouslySetInnerHTML={{ __html: post.content }} />
    </article>
  );
};
``` 