<!DOCTYPE html>
<html lang="zh-C<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文章投稿与内容管理功能实现文档</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif; line-height: 1.6; color: #333; max-width: 960px; margin: 20px auto; padding: 0 20px; }
        h1, h2, h3 { color: #2c3e50; border-bottom: 2px solid #eaecef; padding-bottom: 0.3em; }
        h1 { font-size: 2.2em; }
        h2 { font-size: 1.8em; }
        h3 { font-size: 1.4em; }
        code { background-color: #f6f8fa; padding: 0.2em 0.4em; margin: 0; font-size: 85%; border-radius: 3px; font-family: "SFMono-Regular", <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace; }
        pre { background-color: #f6f8fa; padding: 16px; overflow: auto; border-radius: 6px; }
        pre code { padding: 0; margin: 0; font-size: 100%; }
        .container { background-color: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .note { background-color: #fffbe6; border-left: 4px solid #ffcb00; padding: 12px 16px; margin: 20px 0; border-radius: 4px; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #dfe2e5; padding: 8px 12px; text-align: left; }
        th { background-color: #f6f8fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>文章投稿与内容管理功能实现文档</h1>
        <p><strong>版本:</strong> 1.2.0</p>
        <p><strong>日期:</strong> 2025-06-22</p>

        <h2>1. 功能概述</h2>
        <p>
            本项目实现了一套<strong>基于用户角色的内容贡献与管理系统</strong>。该系统为不同角色的用户提供了量身定制的操作体验：
        </p>
        <ul>
            <li><strong>订阅者 (Subscriber):</strong> 普通登录用户拥有"投稿"权限。他们可以通过前端页面提交文章，提交后文章状态为"待审(pending)"，需要管理员审核后才能发布。此流程旨在鼓励用户贡献内容。</li>
            <li><strong>作者及以上角色 (Author, Editor, etc.):</strong> 拥有更高权限的用户，则拥有一个完整的前端"内容管理"界面。他们可以直接创建、发布、修改和删除自己的文章，无需经过审核流程。</li>
        </ul>
        <p>
            系统通过一个基于 <a href="https://tiptap.dev/">TipTap</a> 的富文本编辑器提供统一的创作体验，并集成了微信文章一键采集和图片自动本地化等高级功能，旨在提升效率和内容质量。
        </p>
        <div class="note">
            <strong>核心升级:</strong> 从单一的投稿系统，演进为能智能识别用户角色、提供差异化功能和工作流的专业内容管理平台。
        </div>

        <h2>2. 后端实现 (fd-member 插件)</h2>
        <p>后端通过在 `fd-member` 插件中扩展 GraphQL Schema 和新增多个 Mutation 来支持这套双轨系统。</p>
        
        <h3>2.1. 文件结构</h3>
        <p>核心逻辑分布在以下文件中：</p>
        <ul>
            <li><code>/includes/submission/submission-graphql.php</code>: 定义 GraphQL 接口和处理文章创建的逻辑。</li>
        </ul>
        <p>这些文件在主插件文件 <code>index.php</code> 和 GraphQL 加载器 <code>member-graphql-loader.php</code> 中被引入。</p>

        <h3>2.2. GraphQL Schema 增强：用户角色</h3>
        <p>为了让前端能够识别当前用户并展示对应的UI，我们在 GraphQL Schema 中为 <code>User</code> 和 <code>Viewer</code> 类型新增了一个字段。</p>
        <table>
            <thead>
                <tr><th>字段名</th><th>类型</th><th>描述</th></tr>
            </thead>
            <tbody>
                <tr>
                    <td><code>role</code></td>
                    <td><code>String</code></td>
                    <td>用户的主角色，例如 "subscriber" 或 "author"。这使得前端可以进行简单直接的权限判断。</td>
                </tr>
            </tbody>
        </table>
        <p>该实现在 <code>/graphql/user-role-graphql.php</code> 文件中定义。</p>

        <h3>2.3. 订阅者专用接口: `createPostSubmission`</h3>
        <p>我们保留了原有的 <code>createPostSubmission</code> 突变，现明确其为订阅者专用接口。</p>
        
        <h4>输入字段 (InputFields)</h4>
        <table>
            <thead>
                <tr>
                    <th>字段名</th>
                    <th>类型</th>
                    <th>描述</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><code>title</code></td>
                    <td><code>String!</code></td>
                    <td>文章的标题，不能为空。</td>
                </tr>
                <tr>
                    <td><code>contentHtml</code></td>
                    <td><code>String!</code></td>
                    <td>文章内容，以 HTML 格式提供。</td>
                </tr>
                <tr>
                    <td><code>contentJson</code></td>
                    <td><code>String!</code></td>
                    <td>文章内容的 JSON 结构化表示，由 TipTap 编辑器生成。</td>
                </tr>
            </tbody>
        </table>

        <h4>输出字段 (OutputFields)</h4>
        <table>
            <thead>
                <tr>
                    <th>字段名</th>
                    <th>类型</th>
                    <th>描述</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><code>success</code></td>
                    <td><code>Boolean!</code></td>
                    <td>操作是否成功。</td>
                </tr>
                <tr>
                    <td><code>postId</code></td>
                    <td><code>ID</code></td>
                    <td>新创建的文章的数据库 ID。</td>
                </tr>
                <tr>
                    <td><code>postStatus</code></td>
                    <td><code>String</code></td>
                    <td>新文章的状态，默认为 "pending"。</td>
                </tr>
            </tbody>
        </table>

        <h3>2.4. 作者及以上角色专属接口</h3>
        <p>为了赋予作者完整的文章管理能力，我们新增了三个专属的 GraphQL Mutation，定义在 <code>/graphql/author-post-mutations.php</code> 文件中。这些接口的核心是内置了基于 WordPress 用户能力 (Capabilities) 的严格权限检查。</p>

        <h4>2.4.1. `createMyPost`</h4>
        <p>允许作者直接创建文章（可选择发布或存为草稿）。</p>
        <table>
            <thead><tr><th>字段</th><th>类型</th><th>描述</th></tr></thead>
            <tbody>
                <tr><td><strong>输入:</strong> title</td><td><code>String!</code></td><td>文章标题</td></tr>
                <tr><td><strong>输入:</strong> content</td><td><code>String</code></td><td>文章HTML内容</td></tr>
                <tr><td><strong>输入:</strong> status</td><td><code>String</code></td><td>目标状态，可选 'publish' 或 'draft'</td></tr>
                <tr><td><strong>输出:</strong> post</td><td><code>Post</code></td><td>新创建的文章对象</td></tr>
            </tbody>
        </table>
        <p><strong>权限检查:</strong> <code>current_user_can('publish_posts')</code></p>

        <h4>2.4.2. `updateMyPost`</h4>
        <p>允许作者更新自己已有的文章。</p>
        <table>
            <thead><tr><th>字段</th><th>类型</th><th>描述</th></tr></thead>
            <tbody>
                <tr><td><strong>输入:</strong> id</td><td><code>ID!</code></td><td>文章的全局ID</td></tr>
                <tr><td><strong>输入:</strong> title, content, status</td><td><code>String</code></td><td>要更新的字段</td></tr>
                <tr><td><strong>输出:</strong> post</td><td><code>Post</code></td><td>更新后的文章对象</td></tr>
            </tbody>
        </table>
        <p><strong>权限检查:</strong> <code>current_user_can('edit_post', $post_id)</code></p>

        <h4>2.4.3. `deleteMyPost`</h4>
        <p>允许作者删除自己的文章。</p>
        <table>
            <thead><tr><th>字段</th><th>类型</th><th>描述</th></tr></thead>
            <tbody>
                <tr><td><strong>输入:</strong> id</td><td><code>ID!</code></td><td>文章的全局ID</td></tr>
                <tr><td><strong>输出:</strong> deletedId</td><td><code>ID</code></td><td>被删除文章的ID</td></tr>
            </tbody>
        </table>
        <p><strong>权限检查:</strong> <code>current_user_can('delete_post', $post_id)</code></p>

        <h2>3. 前端实现 (fd-frontend)</h2>
        <p>前端实现的核心是"角色感知"：首先获取用户角色，然后动态渲染UI并调用合适的API。</p>

        <h3>3.1. 全局状态管理：获取用户角色</h3>
        <p>通过修改 <code>/lib/graphql/fragments.ts</code> 中的 <code>USER_DETAIL_FRAGMENT</code>，我们在获取当前用户信息时一并请求了新增的 <code>role</code> 字段。<code>/contexts/AuthContext.tsx</code> 现在会在全局状态中存储并提供当前用户的角色信息，使整个应用都能方便地访问。</p>
        <pre><code class="language-typescript">
// AuthContext.tsx
const auth = useContext(AuthContext);
const userRole = auth?.user?.role;
const isAuthorOrHigher = userRole && ['author', 'editor', 'administrator'].includes(userRole);
        </code></pre>

        <h3>3.2. 新路由</h3>
        <p>投稿页面被设置为顶级路由：</p>
        <ul>
            <li><code>/submit</code>: 由 <code>src/app/submit/page.tsx</code> 文件提供支持。</li>
        </ul>
        
        <h3>3.3. 编辑器组件: `TiptapEditor.tsx`</h3>
        <p>一个封装了 TipTap 功能的可重用编辑器组件被创建在 <code>@/components/editor/TiptapEditor.tsx</code>。</p>
        <ul>
            <li><strong>核心依赖</strong>: <code>@tiptap/react</code>, <code>@tiptap/starter-kit</code>。</li>
            <li><strong>工具栏 (`MenuBar`)</strong>: 提供常用的文本格式化选项，如粗体、斜体、标题、列表等，并集成了 <code>react-icons</code> 来显示图标。</li>
            <li><strong>数据通信</strong>: 组件通过 <code>onUpdate</code> prop 与父组件通信。每当编辑器内容发生变化时，它会同时返回 HTML 和 JSON 两种格式的数据。</li>
        </ul>
        <pre><code class="language-typescript">
// TiptapEditor.tsx
interface TiptapEditorProps {
  onUpdate: (payload: { html: string; json: string }) => void;
}
// ...
const editor = useEditor({
  // ...
  onUpdate: ({ editor }) => {
    const html = editor.getHTML();
    const json = JSON.stringify(editor.getJSON());
    onUpdate({ html, json });
  },
});
        </code></pre>
        
        <h3>3.4. 投稿/编辑页面: `submit/page.tsx`</h3>
        <p>此页面经过了重大改造，现在是一个能适应不同角色的智能页面。</p>
        <ul>
            <li><strong>GraphQL 定义</strong>: 页面现在同时定义了订阅者用的 <code>CREATE_POST_SUBMISSION</code> 和作者用的 <code>createMyPost</code> / <code>updateMyPost</code> 突变。</li>
            <li><strong>角色判断</strong>: 组件在渲染时会从 <code>AuthContext</code> 获取用户角色，并设置 <code>isAuthorOrHigher</code> 布尔状态。</li>
            <li><strong>UI动态变化</strong>:
                <ul>
                    <li><strong>页面标题:</strong> 根据是否为作者，显示"撰写新文章"或"发布新文章"。</li>
                    <li><strong>操作按钮:</strong> 订阅者看到的是单个"提交审核"按钮；而作者会看到"立即发布"和"保存草稿"两个按钮。</li>
                </ul>
            </li>
            <li><strong>条件化提交 (`handleSubmit`)</strong>: 提交函数现在会检查 <code>isAuthorOrHigher</code> 的值。如果是作者，则调用 `createMyPost` 或 `updateMyPost`；否则，调用 `createPostSubmission`。</li>
        </ul>
        <pre><code class="language-typescript">
// submit/page.tsx
const CREATE_POST_SUBMISSION = gql`
  mutation CreatePostSubmission(
    $title: String!
    $contentHtml: String!
    $contentJson: String!
  ) { /* ... */ }
`;

export default function SubmitPage() {
  const [title, setTitle] = useState('');
  const [editorContent, setEditorContent] = useState('');
  const [editorJson, setEditorJson] = useState('');

  const [createPost, { loading, error, data }] = useMutation(CREATE_POST_SUBMISSION);

  const handleSubmit = async () => {
    await createPost({
      variables: {
        title,
        contentHtml: editorContent,
        contentJson: editorJson,
      },
    });
    // ...
  };
  // ...
}
        </code></pre>

        <h2>4. 微信公众号文章一键采集功能</h2>
        <p>
            此功能保持不变，继续为所有用户提供高效的内容采集能力。
        </p>
        <div class="note">
            <strong>注意:</strong> 采集功能现已支持<strong>自动本地化</strong>微信文章中的所有 <code>&lt;img data-src&gt;</code> 图片：Node.js API 会先调用 WordPress 插件提供的 <code>/sideload-image</code> 端点，将图片下载到媒体库（或对象存储），并把返回的本地 URL 写回正文，最终在编辑器中可直接预览。如网络较慢，首次采集图片可能需要数秒，请耐心等待 Toast 提示。
        </div>

        <h3>5.1. Node.js API 实现 (fd-frontend)</h3>
        <p>
            与文章提交不同，采集功能完全由前端应用的Node.js环境处理，不涉及PHP后端。这利用了Next.js的API Routes特性。
        </p>
        <h4>5.1.1. 新增API路由</h4>
        <ul>
            <li><strong>路径:</strong> <code>src/app/api/weixin-fetch/route.ts</code></li>
            <li><strong>方法:</strong> <code>POST</code></li>
            <li><strong>功能:</strong> 接收前端发送的包含文章URL的请求。</li>
        </ul>

        <h4>5.1.2. 核心依赖</h4>
        <ul>
            <li><strong>axios:</strong> 用于向微信服务器发送HTTP GET请求，获取文章页面的完整HTML。请求时必须设置<code>User-Agent</code>请求头来模拟浏览器访问。</li>
            <li><strong>cheerio:</strong> 一个在服务器端使用的、类似jQuery的库。用它来解析axios获取到的HTML字符串，提取所需内容，比正则表达式更稳定可靠。</li>
        </ul>

        <h4>5.1.3. 实现逻辑</h4>
        <ol>
            <li>API路由接收到POST请求，并从中解析出<code>url</code>参数。</li>
            <li>使用<code>axios</code>伪装成浏览器，请求该URL。</li>
            <li>请求成功后，将返回的HTML内容加载到<code>cheerio</code>中。</li>
            <li>使用选择器从HTML中精准提取数据：
                <ul>
                    <li>标题: 从<code>&lt;meta property="og:title"&gt;</code>标签的<code>content</code>属性中获取。</li>
                    <li>正文: 从<code>&lt;div id="js_content"&gt;</code>元素中获取其内部的HTML。</li>
                </ul>
            </li>
            <li>将提取出的标题和正文HTML打包成一个JSON对象，返回给前端。</li>
        </ol>

        <h3>5.2. 前端集成 (fd-frontend)</h3>
        <h4>5.2.1. 投稿页面修改 (<code>submit/page.tsx</code>)</h4>
        <ul>
            <li><strong>UI添加:</strong> 在页面顶部新增了一个采集区域，包含URL输入框和"一键采集"按钮。</li>
            <li><strong>状态管理:</strong> 使用<code>useState</code>增加了<code>weixinUrl</code>（存储链接）和<code>isFetching</code>（标记采集状态）两个状态。</li>
            <li><strong>API调用:</strong> 创建了<code>handleFetchArticle</code>异步函数。该函数在点击按钮时触发，负责调用<code>/api/weixin-fetch</code>接口，并处理返回的数据或错误。</li>
            <li><strong>数据填充:</strong> 接口成功返回数据后，调用<code>setTitle</code>和<code>setEditorContent</code>方法，将采集到的内容更新到页面的状态中，从而填充表单。</li>
            <li><strong>用户反馈:</strong> 集成了<code>Toast</code>通知，在采集开始、成功、失败时给予用户清晰的反馈。在采集中，按钮会显示加载状态并被禁用。</li>
        </ul>

        <h4>5.2.2. 编辑器组件增强 (<code>TiptapEditor.tsx</code>)</h4>
        <p>为了能接收采集到的内容，编辑器组件被改造成了一个"受控组件"。</p>
        <ul>
            <li><strong>新增<code>content</code> Prop:</strong> 组件的Props接口中增加了一个可选的<code>content: string</code>属性，用于接收外部传入的HTML内容。</li>
            <li><strong>使用<code>useEffect</code>监听:</strong> 组件内部使用React的<code>useEffect</code>钩子来监听<code>content</code> prop的变化。</li>
            <li><strong>动态更新编辑器:</strong> 当<code>content</code> prop发生改变时，<code>useEffect</code>会执行，并调用编辑器实例的<code>editor.commands.setContent()</code>方法，将新的HTML内容渲染到编辑器中。</li>
        </ul>

        <h4>5.2.3. 图片本地化完整实现</h4>
        <p>为解决微信图片的防盗链限制，实现了<strong>一键本地化</strong>功能，核心流程如下：</p>
        <ol>
            <li><strong>扫描 HTML：</strong> 在 <code>src/app/api/weixin-fetch/route.ts</code> 中使用 <code>cheerio</code> 遍历 <code>#js_content</code> 里的所有 <code>&lt;img&gt;</code>，读取其 <code>data-src</code> 属性。</li>
            <li><strong>调用 WordPress 端点：</strong> 对每一张图片，Node.js 使用 <code>fetch</code> 向 <code>${WP_BASE_URL}/wp-json/fd-grabweixin/v1/sideload-image</code> 发送 <code>POST</code> 请求，<code>body</code> 为 <code>{ imageUrl }</code>。</li>
            <li><strong>认证方式：</strong> 请求头携带 <code>Authorization: Basic &lt;base64(user:appPassword)&gt;</code>。用户名与 24 位 Application Password 均存放在 Docker 环境变量 <code>WP_APP_USER</code>、<code>WP_APP_PASS</code> 中。</li>
            <li><strong>WordPress 插件实现：</strong> <code>fd-grabweixin</code> 插件在 <code>admin/admin.php</code> 中注册 <code>sideload-image</code> REST 路由，权限回调检查 <code>current_user_can('upload_files')</code>。</li>
            <li><strong>下载并返回：</strong> 插件内部使用 <code>download_url → media_handle_sideload</code> 下载图片；成功后返回 <code>{ success, url, id }</code>。</li>
            <li><strong>替换链接：</strong> Node 收到成功响应后，将原 <code>data-src</code> 替换为 <code>src={url}</code>，并移除 <code>data-src</code>。</li>
            <li><strong>编辑器渲染：</strong> 在前端，加入了 TipTap <code>@tiptap/extension-image</code> 扩展，并在提交时通过 <code>key</code> 强制刷新编辑器，保证本地化后的图片立即可见。</li>
        </ol>
        <p><strong>环境变量示例 (.env)</strong></p>
        <pre><code>WP_BASE_URL=https://admin.futuredecade.com
WP_APP_USER=conglin
WP_APP_PASS=mN2gON8Sbbn6RUVhlk4Y5rda</code></pre>
        <p>在 Docker 环境中，通过 <code>env_file: .env</code> 注入上述变量，容器即可安全地访问 WordPress 媒体库。</p>

        <h3>5.3. 更新后的用户工作流</h3>
        <ol>
            <li>用户导航到<code>/submit</code>页面。</li>
            <li>用户在新增的输入框中粘贴微信文章URL，并点击"一键采集"。</li>
            <li>采集按钮变为禁用状态并显示加载中。</li>
            <li>前端页面调用内部的Node.js API进行采集。</li>
            <li>API抓取、解析并返回文章数据。</li>
            <li>前端收到数据后，自动填充文章标题和编辑器内容。</li>
            <li>用户可以在已填充内容的基础上进行修改，然后点击"提交审核"。</li>
        </ol>

        <h2>6. 投稿与内容管理 (仪表盘)</h2>
        <p>
            为了让用户能够管理自己的内容，我们对仪表盘（<code>/dashboard</code>）进行了增强，使其也能感知用户角色，提供差异化的功能。
        </p>

        <h3>6.1. 订阅者投稿管理</h3>
        <h4>6.1.1. `userSubmissions` 查询</h4>
        <p>
            该查询用于获取用户自己的投稿列表。我们通过为订阅者动态添加 `edit_posts` 的用户能力（Capability），解决了他们无法查看 `pending` 状态文章的问题。
        </p>

        <h4>6.1.2. `withdrawPostSubmission` 接口 (Mutation)</h4>
        <p>该接口允许订阅者将"审核中"的投稿撤回为"草稿"状态，以便修改。</p>
        
        <h4>6.1.3. 仪表盘UI (`UserActivityTabs.tsx`)</h4>
        <p>在"我的投稿"标签页中：</p>
        <ul>
            <li>如果文章状态是 <code>pending</code>，显示"撤回"按钮。</li>
            <li>如果文章状态是 <code>draft</code>，显示"修改"按钮，链接到编辑页面。</li>
        </ul>

        <h3>6.2. 作者内容管理</h3>
        <p>对于作者及以上角色，仪表盘提供了更强大的管理能力。</p>
        <ul>
            <li><strong>标签文本:</strong> "我的投稿"标签会动态显示为"我的作品"，更符合作者身份。</li>
            <li><strong>文章列表:</strong> 作者可以看到自己所有状态的文章，包括"已发布 (publish)"。</li>
            <li><strong>操作按钮:</strong>
                <ul>
                    <li>每篇文章旁都有"修改"按钮，可以随时编辑。</li>
                    <li>新增了"删除"按钮，绑定了 <code>deleteMyPost</code> 接口，允许作者直接删除自己的文章。</li>
                </ul>
            </li>
        </ul>
        
        <h2>7. 完整工作流</h2>
        <h3>7.1. 订阅者工作流</h3>
        <ol>
            <li>用户在 <code>/submit</code> 页面撰写文章，点击"提交审核"。</li>
            <li>用户访问 <code>/dashboard</code> 的"我的投稿"页，可看到"审核中"的稿件和"撤回"按钮。</li>
            <li>用户可选择撤回稿件进行修改，修改后重新提交审核。</li>
            <li>管理员在后台审核通过后，文章被发布。</li>
        </ol>

        <h3>7.2. 作者工作流</h3>
        <ol>
            <li>用户（作者角色）访问 <code>/submit</code> 页面，页面显示为"发布新文章"。</li>
            <li>撰写完毕后，可选择"立即发布"或"保存草稿"。</li>
            <li>用户访问 <code>/dashboard</code> 的"我的作品"页，可看到自己所有的文章（包括已发布和草稿）。</li>
            <li>对任何一篇文章，都可以随时进行"修改"或"删除"操作。整个流程无需管理员介入。</li>
        </ol>
    </div>
</body>
</html> 