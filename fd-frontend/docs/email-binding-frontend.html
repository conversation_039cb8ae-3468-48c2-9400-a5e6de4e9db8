<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱绑定功能前端实现文档</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #1a75cf;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            font-size: 14px;
        }
        .note {
            background-color: #fffacd;
            border-left: 4px solid #ffcc00;
            padding: 10px 15px;
            margin: 20px 0;
        }
        .warning {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 10px 15px;
            margin: 20px 0;
        }
        .solution {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 10px 15px;
            margin: 20px 0;
        }
        img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 4px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>邮箱绑定功能 - 前端实现文档</h1>
    
    <p>本文档详细描述了React前端应用中邮箱绑定功能的实现，包括组件结构、状态管理、GraphQL通信等。</p>
    
    <h2>1. 功能概述</h2>
    
    <p>邮箱绑定功能允许用户在个人中心的「账户安全」部分绑定或更改邮箱地址。实现流程如下：</p>
    
    <ol>
        <li>用户输入新邮箱地址，点击发送验证码</li>
        <li>用户收到验证码后输入，系统验证并生成临时令牌</li>
        <li>用户点击绑定按钮，使用令牌完成邮箱绑定</li>
    </ol>
    
    <h2>2. 组件结构</h2>
    
    <p>邮箱绑定功能的前端实现主要包含以下组件：</p>
    
    <ol>
        <li><code>ProfileInfo.tsx</code> - 个人中心页面，包含邮箱绑定入口</li>
        <li><code>BindEmailSection.tsx</code> - 邮箱绑定组件，处理验证码发送、验证和绑定逻辑</li>
        <li><code>BindEmailPage.tsx</code> - 邮箱绑定页面，包含BindEmailSection组件</li>
    </ol>
    
    <h3>2.1 页面路由关系</h3>
    
    <ul>
        <li><code>/auth/profile</code> - 个人中心页面，包含账户安全部分</li>
        <li><code>/auth/profile/bind-email</code> - 邮箱绑定页面</li>
    </ul>
    
    <h2>3. GraphQL变更定义</h2>
    
    <p>在<code>src/lib/graphql/mutations.ts</code>中定义了三个用于邮箱绑定的GraphQL变更：</p>
    
    <pre><code>// 发送邮箱绑定验证码
export const SEND_EMAIL_BINDING_CODE = gql`
  mutation SendEmailBindingCode($email: String!) {
    sendEmailBindingCode(input: {
      email: $email
    }) {
      success
      message
    }
  }
`;

// 验证邮箱绑定验证码并获取令牌
export const VERIFY_EMAIL_BINDING_CODE_AND_GET_TOKEN = gql`
  mutation VerifyEmailBindingCodeAndGetToken($email: String!, $code: String!) {
    verifyEmailBindingCodeAndGetToken(input: {
      email: $email,
      code: $code
    }) {
      success
      message
      token
    }
  }
`;

// 绑定邮箱
export const BIND_EMAIL = gql`
  mutation BindEmail($email: String!, $token: String!) {
    bindEmail(input: {
      email: $email,
      token: $token
    }) {
      success
      message
      user {
        ...UserDetailFields
      }
    }
  }
  ${USER_DETAIL_FRAGMENT}
`;</code></pre>

    <h2>4. 类型定义</h2>
    
    <p>在<code>src/types/user-types.ts</code>中定义了邮箱绑定相关的类型：</p>
    
    <pre><code>/**
 * 绑定邮箱输入数据
 */
export interface BindEmailInput {
  email: string;
  token: string;
}</code></pre>

    <h2>5. 认证上下文</h2>
    
    <p>在<code>src/contexts/AuthContext.tsx</code>中添加邮箱绑定相关的方法和状态：</p>
    
    <h3>5.1 AuthContext接口扩展</h3>
    
    <pre><code>interface AuthContextType {
  // ... 其他接口定义
  
  // 邮箱绑定相关方法
  sendEmailBindingCode: (email: string) => Promise<{ success: boolean; message: string }>;
  verifyEmailBindingCodeAndGetToken: (email: string, code: string) => Promise<{ success: boolean; message: string; token?: string }>;
  bindEmail: (input: BindEmailInput) => Promise<{ success: boolean; message: string; user?: User }>;
}</code></pre>

    <h3>5.2 GraphQL变更钩子</h3>
    
    <pre><code>// 邮箱绑定相关变更钩子
const [sendEmailBindingCodeMutation] = useMutation(SEND_EMAIL_BINDING_CODE);
const [verifyEmailBindingCodeAndGetTokenMutation] = useMutation(VERIFY_EMAIL_BINDING_CODE_AND_GET_TOKEN);
const [bindEmailMutation] = useMutation(BIND_EMAIL);</code></pre>

    <h3>5.3 邮箱绑定方法实现</h3>
    
    <pre><code>/**
 * 发送邮箱绑定验证码
 * @param email 邮箱地址
 */
const sendEmailBindingCode = async (email: string): Promise<{ success: boolean; message: string }> => {
  setError(null);
  
  try {
    console.log('[调试-邮箱绑定] 发送邮箱绑定验证码:', email);
    
    const { data } = await sendEmailBindingCodeMutation({
      variables: { email },
      fetchPolicy: 'no-cache'
    });
    
    console.log('[调试-邮箱绑定] 发送结果:', data);
    
    if (data?.sendEmailBindingCode) {
      return {
        success: data.sendEmailBindingCode.success,
        message: data.sendEmailBindingCode.message
      };
    } else {
      throw new Error('发送验证码失败');
    }
  } catch (err) {
    console.error('[调试-邮箱绑定] 发送验证码错误:', err);
    
    let errorMessage = '发送验证码失败，请重试';
    if (err instanceof Error) {
      errorMessage = err.message;
    }
    
    setError(errorMessage);
    return {
      success: false,
      message: errorMessage
    };
  }
};

/**
 * 验证邮箱绑定验证码并获取令牌
 * @param email 邮箱地址
 * @param code 验证码
 */
const verifyEmailBindingCodeAndGetToken = async (email: string, code: string): Promise<{ success: boolean; message: string; token?: string }> => {
  setError(null);
  
  try {
    console.log('[调试-邮箱绑定] 验证邮箱验证码:', { email, code });
    
    const { data } = await verifyEmailBindingCodeAndGetTokenMutation({
      variables: { email, code },
      fetchPolicy: 'no-cache'
    });
    
    console.log('[调试-邮箱绑定] 验证结果:', data);
    
    if (data?.verifyEmailBindingCodeAndGetToken) {
      return {
        success: data.verifyEmailBindingCodeAndGetToken.success,
        message: data.verifyEmailBindingCodeAndGetToken.message,
        token: data.verifyEmailBindingCodeAndGetToken.token
      };
    } else {
      throw new Error('验证码验证失败');
    }
  } catch (err) {
    console.error('[调试-邮箱绑定] 验证码验证错误:', err);
    
    let errorMessage = '验证码验证失败，请重试';
    if (err instanceof Error) {
      errorMessage = err.message;
    }
    
    setError(errorMessage);
    return {
      success: false,
      message: errorMessage
    };
  }
};

/**
 * 绑定邮箱
 * @param input 包含邮箱和验证令牌的输入数据
 */
const bindEmail = async (input: BindEmailInput): Promise<{ success: boolean; message: string; user?: User }> => {
  setError(null);
  setIsLoading(true);
  
  try {
    console.log('[调试-邮箱绑定] 绑定邮箱:', { 
      email: input.email, 
      token: input.token ? `${input.token.substring(0, 10)}...` : '空' 
    });
    
    const { data } = await bindEmailMutation({
      variables: {
        email: input.email,
        token: input.token
      }
    });
    
    console.log('[调试-邮箱绑定] 绑定结果:', data);
    
    if (data?.bindEmail) {
      if (data.bindEmail.success && data.bindEmail.user) {
        // 更新用户数据
        setUser(data.bindEmail.user);
        setUserData(data.bindEmail.user);
        
        return {
          success: true,
          message: data.bindEmail.message || '邮箱绑定成功',
          user: data.bindEmail.user
        };
      } else {
        return {
          success: false,
          message: data.bindEmail.message || '邮箱绑定失败'
        };
      }
    } else {
      throw new Error('邮箱绑定失败');
    }
  } catch (err) {
    console.error('[调试-邮箱绑定] 绑定错误:', err);
    
    let errorMessage = '邮箱绑定失败，请重试';
    if (err instanceof Error) {
      errorMessage = err.message;
    }
    
    setError(errorMessage);
    return {
      success: false,
      message: errorMessage
    };
  } finally {
    setIsLoading(false);
  }
};</code></pre>

    <h2>6. BindEmailSection组件实现</h2>
    
    <p>在<code>src/components/profile/BindEmailSection.tsx</code>中实现邮箱绑定的UI和业务逻辑：</p>
    
    <pre><code>"use client";

import React, { useState } from 'react';
import { useAuth } from '../../hooks/useAuth';
import Button from '../ui/Button';
import FormField from '../ui/FormField';
import FormError from '../ui/FormError';
import FormSuccess from '../ui/FormSuccess';

/**
 * 邮箱绑定组件
 * 提供基于token的两步邮箱绑定流程
 */
const BindEmailSection: React.FC = () => {
  const { 
    user, 
    sendEmailBindingCode, 
    verifyEmailBindingCodeAndGetToken, 
    bindEmail
  } = useAuth();
  
  // 步骤状态
  const [step, setStep] = useState<'initial' | 'verify' | 'bind'>('initial');
  
  // 表单状态
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [token, setToken] = useState('');
  
  // UI状态
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  
  // 处理发送验证码
  const handleSendCode = async () => {
    if (!email) {
      setError('请输入邮箱地址');
      return;
    }
    
    // 简单的邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('请输入有效的邮箱地址');
      return;
    }
    
    setError('');
    setIsLoading(true);
    
    try {
      const result = await sendEmailBindingCode(email);
      
      if (result.success) {
        setSuccess('验证码已发送，请查收您的邮箱');
        setStep('verify');
        
        // 开始倒计时
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        setError(result.message || '发送验证码失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '发送验证码失败');
    } finally {
      setIsLoading(false);
    }
  };
  
  // 处理验证码验证并获取token
  const handleVerifyCode = async () => {
    if (!email || !code) {
      setError('请输入邮箱地址和验证码');
      return;
    }
    
    console.log('[调试-绑定邮箱] 验证验证码:', { email, code });
    
    setError('');
    setSuccess('');
    setIsLoading(true);
    
    try {
      console.log('[调试-绑定邮箱] 调用 verifyEmailBindingCodeAndGetToken API...');
      const result = await verifyEmailBindingCodeAndGetToken(email, code);
      
      console.log('[调试-绑定邮箱] 验证结果:', {
        success: result.success,
        message: result.message,
        token: result.token ? `${result.token.substring(0, 10)}...${result.token.substring(result.token.length - 10)}` : '未获取到'
      });
      
      if (result.success && result.token) {
        setToken(result.token);
        setSuccess('验证码验证成功，请点击绑定按钮完成绑定');
        setStep('bind');
      } else {
        setError(result.message || '验证码验证失败');
      }
    } catch (err) {
      console.error('[调试-绑定邮箱] 验证码验证异常:', err);
      setError(err instanceof Error ? err.message : '验证码验证失败');
    } finally {
      setIsLoading(false);
    }
  };
  
  // 处理绑定邮箱
  const handleBindEmail = async () => {
    if (!email || !token) {
      setError('缺少必要参数');
      return;
    }
    
    console.log('[调试-绑定邮箱] 开始绑定邮箱:', { 
      email, 
      token: token ? `${token.substring(0, 10)}...` : '空'
    });
    
    setError('');
    setSuccess('');
    setIsLoading(true);
    
    try {
      console.log('[调试-绑定邮箱] 调用 bindEmail API...');
      const result = await bindEmail({
        email,
        token
      });
      
      console.log('[调试-绑定邮箱] bindEmail API 返回结果:', result);
      
      if (result.success) {
        console.log('[调试-绑定邮箱] 邮箱绑定成功!');
        setSuccess('邮箱绑定成功！');
        
        // 重置表单和状态
        setTimeout(() => {
          setEmail('');
          setCode('');
          setToken('');
          setStep('initial');
          setSuccess('');
        }, 3000);
      } else {
        console.error('[调试-绑定邮箱] 绑定失败:', result.message);
        setError(result.message || '绑定邮箱失败');
      }
    } catch (err) {
      console.error('[调试-绑定邮箱] 捕获到异常:', err);
      setError(err instanceof Error ? err.message : '绑定邮箱失败');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">邮箱绑定</h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
          {user?.email 
            ? '更换您的邮箱地址'
            : '绑定邮箱可用于登录和找回密码'
          }
        </p>
      </div>
      
      <div className="px-4 py-5 sm:p-6">
        {error && <FormError message={error} className="mb-4" />}
        {success && <FormSuccess message={success} className="mb-4" />}
        
        <div className="space-y-4">
          {/* 邮箱地址输入 */}
          <div>
            <FormField
              label="邮箱地址"
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="请输入邮箱地址"
              disabled={isLoading || step === 'bind'}
              required
            />
          </div>
          
          {/* 步骤1：发送验证码 */}
          {step === 'initial' && (
            <div>
              <Button
                type="button"
                variant="primary"
                onClick={handleSendCode}
                isLoading={isLoading}
                disabled={isLoading || !email}
                className="w-full"
              >
                发送验证码
              </Button>
            </div>
          )}
          
          {/* 步骤2：验证验证码 */}
          {step === 'verify' && (
            <>
              <div>
                <FormField
                  label="验证码"
                  type="text"
                  id="code"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  placeholder="请输入验证码"
                  disabled={isLoading}
                  required
                />
              </div>
              
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="primary"
                  onClick={handleVerifyCode}
                  isLoading={isLoading}
                  disabled={isLoading || !code}
                  className="flex-1"
                >
                  验证验证码
                </Button>
                
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleSendCode}
                  disabled={isLoading || countdown > 0}
                  className="whitespace-nowrap"
                >
                  {countdown > 0 ? `${countdown}秒后重新发送` : '重新发送'}
                </Button>
              </div>
            </>
          )}
          
          {/* 步骤3：绑定邮箱 */}
          {step === 'bind' && (
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                验证码已验证成功，点击下方按钮完成邮箱绑定：
              </p>
              
              <Button
                type="button"
                variant="primary"
                onClick={handleBindEmail}
                isLoading={isLoading}
                disabled={isLoading}
                className="w-full"
              >
                绑定邮箱
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BindEmailSection;</code></pre>

    <h2>7. BindEmailPage页面实现</h2>
    
    <p>在<code>src/app/auth/profile/bind-email/page.tsx</code>中创建邮箱绑定页面：</p>
    
    <pre><code>"use client";

import React from 'react';
import { useAuth } from '../../../../hooks/useAuth';
import { redirect } from 'next/navigation';
import PageTitle from '../../../../components/ui/PageTitle';
import BindEmailSection from '../../../../components/profile/BindEmailSection';

export default function BindEmailPage() {
  const { isAuthenticated, isLoading } = useAuth();
  
  // 如果用户加载完成但未登录，重定向到登录页面
  if (!isLoading && !isAuthenticated) {
    redirect('/auth/login');
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <PageTitle title="绑定邮箱" description="绑定邮箱方便账号安全验证和密码找回" />
      
      <div className="mt-6">
        <BindEmailSection />
      </div>
      
      <div className="mt-4 text-center">
        <a href="/auth/profile" className="text-primary-600 hover:underline">
          返回个人资料
        </a>
      </div>
    </div>
  );
}</code></pre>

    <h2>8. 个人资料页面中的入口</h2>
    
    <p>在<code>src/components/profile/ProfileInfo.tsx</code>中的账户安全部分添加邮箱绑定入口：</p>
    
    <pre><code>&lt;div&gt;
  &lt;div className="flex justify-between items-center"&gt;
    &lt;div&gt;
      &lt;h4 className="text-sm font-medium text-gray-900 dark:text-white"&gt;邮箱&lt;/h4&gt;
      &lt;p className="text-sm text-gray-500 dark:text-gray-400"&gt;
        {user?.email ? `已绑定: ${user.email}` : '未绑定邮箱'}
      &lt;/p&gt;
    &lt;/div&gt;
    &lt;Link href="/auth/profile/bind-email"&gt;
      &lt;Button variant="outline" size="sm"&gt;
        {user?.email ? '更换邮箱' : '绑定邮箱'}
      &lt;/Button&gt;
    &lt;/Link&gt;
  &lt;/div&gt;
&lt;/div&gt;</code></pre>

    <h2>9. 状态管理与用户体验</h2>
    
    <h3>9.1 表单状态</h3>
    
    <p>BindEmailSection组件使用以下本地状态管理表单：</p>
    
    <ul>
        <li><code>step</code>: 当前步骤 (initial | verify | bind)</li>
        <li><code>email</code>: 用户输入的邮箱地址</li>
        <li><code>code</code>: 用户输入的验证码</li>
        <li><code>token</code>: 验证成功后获取的令牌</li>
        <li><code>error</code>: 错误信息</li>
        <li><code>success</code>: 成功信息</li>
        <li><code>isLoading</code>: 加载状态</li>
        <li><code>countdown</code>: 验证码重发倒计时</li>
    </ul>
    
    <h3>9.2 用户体验优化</h3>
    
    <ol>
        <li><strong>分步骤流程</strong>: 将邮箱绑定拆分为三个步骤，使用户能够清晰地了解当前进度</li>
        <li><strong>表单验证</strong>: 在客户端对邮箱格式进行初步验证</li>
        <li><strong>倒计时功能</strong>: 验证码发送后，提供60秒倒计时，防止用户频繁点击发送按钮</li>
        <li><strong>状态反馈</strong>: 提供错误和成功提示，让用户了解操作结果</li>
        <li><strong>按钮禁用状态</strong>: 在操作进行中或条件不满足时禁用按钮</li>
    </ol>
    
    <h2>10. 与手机绑定对比</h2>
    
    <p>邮箱绑定的前端实现与手机绑定类似，主要区别在于：</p>
    
    <table>
        <tr>
            <th>功能点</th>
            <th>邮箱绑定</th>
            <th>手机绑定</th>
        </tr>
        <tr>
            <td>输入验证</td>
            <td>邮箱格式验证</td>
            <td>手机号格式验证</td>
        </tr>
        <tr>
            <td>国际化支持</td>
            <td>不需要国际区号</td>
            <td>支持国际区号输入</td>
        </tr>
        <tr>
            <td>解绑功能</td>
            <td>不支持解绑，只能更换</td>
            <td>支持解绑操作</td>
        </tr>
    </table>
    
    <h2>11. 测试要点</h2>
    
    <p>邮箱绑定功能的前端测试应关注以下几点：</p>
    
    <ol>
        <li>邮箱格式验证是否正确</li>
        <li>验证码发送、验证流程是否正常</li>
        <li>错误处理和用户提示是否合理</li>
        <li>绑定成功后，用户界面是否正确更新</li>
        <li>当遇到后端错误时，前端是否能够友好地提示用户</li>
        <li>与GraphQL请求相关的错误处理是否完善</li>
    </ol>
    
    <div class="warning">
        <h3>可能的问题</h3>
        <p>在实现过程中，可能会遇到"令牌已被使用"的错误，这通常是由于：</p>
        <ol>
            <li>后端GraphQL响应中的类型转换错误导致前端重复提交请求</li>
            <li>用户在短时间内多次点击绑定按钮</li>
        </ol>
        <p>为避免此类问题，可以考虑在前端实现防重复提交机制，或在显示错误消息时进行特定处理。</p>
    </div>
    
    <h2>12. 总结</h2>
    
    <p>邮箱绑定功能的前端实现采用了React Hooks和GraphQL，通过分步骤的表单引导用户完成邮箱验证和绑定过程。实现过程注重用户体验，包括加载状态、错误处理、表单验证等方面。</p>
    
    <p>该功能与手机绑定类似，但考虑到邮箱作为用户基本信息的特殊性，不提供解绑功能，仅支持绑定或更换。</p>
    
    <p>前端与后端通过GraphQL通信，使用基于令牌的安全验证机制确保操作的安全性。</p>
</body>
</html> 