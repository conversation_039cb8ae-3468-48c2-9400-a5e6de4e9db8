<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Future Decade Frontend v1.6.1 文档</title>
    <style>
        body {
            font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 1.5em;
        }
        h1 {
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        h2 {
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        code {
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            font-size: 0.9em;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        pre code {
            background-color: transparent;
            padding: 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .version-badge {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8em;
            margin-left: 10px;
        }
        .notice {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 10px 15px;
            margin: 20px 0;
        }
        .important {
            background-color: #ffedef;
            border-left: 4px solid #f44336;
            padding: 10px 15px;
            margin: 20px 0;
        }
        .method {
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border: 1px solid #eee;
        }
        .method h4 {
            margin-top: 0;
        }
        .method-signature {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            margin-bottom: 10px;
        }
        .parameters {
            margin-left: 20px;
        }
        .returns {
            margin-top: 15px;
        }
        .code-filename {
            color: #666;
            margin-bottom: 5px;
            font-size: 0.85em;
        }
    </style>
</head>
<body>
    <h1>Future Decade Frontend <span class="version-badge">v1.6.1</span></h1>
    <p>本文档详细介绍Future Decade Frontend v1.6.1版本的文章详情页实现，基于Next.js框架构建。本版本优化了路由结构，采用UUID作为文章标识，提升了内容访问稳定性和SEO表现。</p>
    
    <div class="notice">
        <p><strong>注意：</strong> 本应用需要Future Decade Theme (v1.2.6+)和WPGraphQL插件支持，建议使用Node.js 16+环境。</p>
    </div>
    
    <h2>目录</h2>
    <ul>
        <li><a href="#introduction">介绍</a></li>
        <li><a href="#article-pages">文章详情页系统</a>
            <ul>
                <li><a href="#url-structure">URL结构设计</a></li>
                <li><a href="#routing-system">路由实现</a></li>
                <li><a href="#middleware">路由中间件</a></li>
            </ul>
        </li>
        <li><a href="#article-components">文章页面组件</a></li>
        <li><a href="#uuid-integration">UUID集成方案</a></li>
        <li><a href="#graphql-queries">GraphQL查询</a></li>
        <li><a href="#seo-optimization">SEO优化</a></li>
        <li><a href="#redirects">重定向和向后兼容</a></li>
        <li><a href="#performance">性能优化</a></li>
        <li><a href="#troubleshooting">故障排除</a></li>
        <li><a href="#version-history">版本历史</a></li>
    </ul>
    
    <h2 id="introduction">介绍</h2>
    <p>Future Decade Frontend v1.6.1在v1.6.0的基础上，全面更新了文章详情页的实现方式，采用基于UUID的路由系统，以解决传统WordPress Slug路由的稳定性问题，并引入了一系列优化来提升用户体验和搜索引擎友好性。</p>
    
    <p>主要特性：</p>
    <ul>
        <li>基于UUID的稳定路由系统</li>
        <li>改进的SEO元数据生成</li>
        <li>支持旧链接重定向</li>
        <li>文章页面组件重构</li>
        <li>服务端渲染与客户端交互优化</li>
        <li>改进的GraphQL查询结构</li>
        <li>性能优化和监控</li>
    </ul>
    
    <h2 id="article-pages">文章详情页系统</h2>
    <p>v1.6.1版本对文章详情页系统进行了全面更新，主要围绕UUID路由和页面组件结构展开。</p>
    
    <h3 id="url-structure">URL结构设计</h3>
    <p>新的URL结构采用UUID+Slug模式，既保证了链接稳定性，又维持了可读性和SEO友好：</p>
    
    <table>
        <tr>
            <th>类型</th>
            <th>URL结构</th>
            <th>示例</th>
        </tr>
        <tr>
            <td>文章详情页</td>
            <td><code>/articles/[uuid]/[slug]</code></td>
            <td><code>/articles/a1b2c3d4e5/why-nextjs-is-great</code></td>
        </tr>
        <tr>
            <td>页面详情页</td>
            <td><code>/pages/[uuid]/[slug]</code></td>
            <td><code>/pages/f5e4d3c2b1/about-us</code></td>
        </tr>
        <tr>
            <td>自定义类型详情页</td>
            <td><code>/[type]/[uuid]/[slug]</code></td>
            <td><code>/products/z9y8x7w6v5/awesome-product</code></td>
        </tr>
    </table>
    
    <div class="important">
        <p><strong>重要：</strong> UUID是主要标识符，Slug仅用于可读性和SEO。即使Slug变更，只要UUID不变，链接仍然有效。</p>
    </div>
    
    <h3 id="routing-system">路由实现</h3>
    <p>文章详情页路由基于Next.js动态路由功能实现：</p>
    
    <div class="method">
        <h4>路由文件结构</h4>
        <pre><code>app/
├── articles/
│   └── [uuid]/
│       └── [slug]/
│           └── page.tsx
├── pages/
│   └── [uuid]/
│       └── [slug]/
│           └── page.tsx
└── [type]/
    └── [uuid]/
        └── [slug]/
            └── page.tsx</code></pre>
    </div>
    
    <div class="method">
        <h4>文章详情页实现</h4>
        <div class="code-filename">app/articles/[uuid]/[slug]/page.tsx</div>
        <pre><code>import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { ArticleContent } from '@/components/articles/ArticleContent';
import { getPostByUuid } from '@/lib/api/posts';
import { generateArticleSEO } from '@/lib/seo';

// 定义生成元数据的函数
export async function generateMetadata({ 
  params 
}: { 
  params: { uuid: string; slug: string } 
}): Promise<Metadata> {
  const { uuid } = params;
  
  // 通过UUID获取文章数据
  const post = await getPostByUuid(uuid);
  
  // 文章不存在，返回404
  if (!post) {
    return {
      title: '页面未找到',
    };
  }
  
  // 生成SEO元数据
  return generateArticleSEO(post);
}

// 主页面组件
export default async function ArticlePage({ 
  params 
}: { 
  params: { uuid: string; slug: string } 
}) {
  const { uuid, slug } = params;
  
  // 通过UUID获取文章数据
  const post = await getPostByUuid(uuid);
  
  // 文章不存在，返回404
  if (!post) {
    notFound();
  }
  
  // 如果slug不匹配当前文章的slug，将重定向到正确的URL
  // 这部分实际由中间件处理，此处为安全检查
  if (post.slug !== slug) {
    // 中间件会处理重定向，这里只是额外检查
    console.warn(`Slug mismatch: ${slug} vs ${post.slug}`);
  }
  
  return (
    <div className="article-container">
      <ArticleContent post={post} />
    </div>
  );
}

// 动态生成静态页面路径
export async function generateStaticParams() {
  // 此函数可以预生成热门文章的静态路径
  // 在v1.6.1中，我们选择不预生成路径，而是依赖ISR缓存策略
  return [];
}</code></pre>
    </div>
    
    <h3 id="middleware">路由中间件</h3>
    <p>v1.6.1引入了专用中间件来处理路由重写、重定向和参数验证：</p>
    
    <div class="method">
        <h4>路由中间件实现</h4>
        <div class="code-filename">middleware.ts</div>
        <pre><code>import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// UUID验证正则表达式
const UUID_REGEX = /^[a-zA-Z0-9]{10}$/;

// 支持的内容类型
const CONTENT_TYPES = ['articles', 'pages', 'products', 'news'];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // 处理旧版URL格式重定向
  // 例如: /post/sample-post -> /articles/{uuid}/sample-post
  if (pathname.startsWith('/post/')) {
    const slug = pathname.replace('/post/', '');
    
    try {
      // 调用API获取UUID
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/wp-json/fd/v1/legacy/slug-lookup?slug=${slug}&type=post`);
      
      if (response.ok) {
        const data = await response.json();
        if (data.uuid && data.slug) {
          return NextResponse.redirect(
            new URL(`/articles/${data.uuid}/${data.slug}`, request.url)
          );
        }
      }
    } catch (error) {
      console.error('Error in legacy URL redirect:', error);
    }
  }
  
  // 检查标准内容URL格式
  // /[type]/[uuid]/[slug]
  const segments = pathname.split('/').filter(Boolean);
  
  if (segments.length >= 2) {
    const type = segments[0];
    const potentialUuid = segments[1];
    
    // 如果是有效的内容类型路径
    if (CONTENT_TYPES.includes(type)) {
      // 检查UUID格式
      if (potentialUuid && !UUID_REGEX.test(potentialUuid)) {
        // 无效的UUID格式，可能是旧URL，尝试重定向
        try {
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_API_URL}/wp-json/fd/v1/legacy/path-lookup?path=${pathname}`
          );
          
          if (response.ok) {
            const data = await response.json();
            if (data.redirect_url) {
              return NextResponse.redirect(
                new URL(data.redirect_url, request.url)
              );
            }
          }
        } catch (error) {
          console.error('Error in path lookup:', error);
        }
        
        // 如果无法找到重定向路径，继续处理
        // 页面组件将负责返回404
      }
      
      // UUID格式有效，但缺少slug (例如: /articles/a1b2c3d4e5)
      if (segments.length === 2 && UUID_REGEX.test(potentialUuid)) {
        try {
          // 查询正确的slug
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_API_URL}/wp-json/fd/v1/uuid/lookup/${potentialUuid}`
          );
          
          if (response.ok) {
            const data = await response.json();
            if (data.object && data.object.slug) {
              // 重定向到包含slug的完整URL
              return NextResponse.redirect(
                new URL(`/${type}/${potentialUuid}/${data.object.slug}`, request.url)
              );
            }
          }
        } catch (error) {
          console.error('Error in UUID lookup:', error);
        }
      }
      
      // UUID和slug都存在，但slug可能不匹配当前内容
      if (segments.length === 3 && UUID_REGEX.test(potentialUuid)) {
        const providedSlug = segments[2];
        
        try {
          // 验证slug是否与UUID匹配
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_API_URL}/wp-json/fd/v1/uuid/lookup/${potentialUuid}`
          );
          
          if (response.ok) {
            const data = await response.json();
            
            if (data.object && data.object.slug && data.object.slug !== providedSlug) {
              // slug不匹配，重定向到正确的URL
              return NextResponse.redirect(
                new URL(`/${type}/${potentialUuid}/${data.object.slug}`, request.url),
                { status: 301 } // 永久重定向
              );
            }
          }
        } catch (error) {
          console.error('Error in slug validation:', error);
        }
      }
    }
  }
  
  // 继续处理请求
  return NextResponse.next();
}

// 配置中间件应用的路径
export const config = {
  matcher: [
    // 匹配所有内容类型路径
    '/articles/:path*',
    '/pages/:path*',
    '/products/:path*',
    '/news/:path*',
    // 匹配旧格式的URL
    '/post/:path*',
    '/page/:path*',
  ],
};</code></pre>
    </div>
    
    <h2 id="article-components">文章页面组件</h2>
    <p>v1.6.1版本重构了文章页面组件，采用模块化设计，提高了复用性和可维护性：</p>
    
    <div class="method">
        <h4>文章内容组件</h4>
        <div class="code-filename">components/articles/ArticleContent.tsx</div>
        <pre><code>import { ArticleHeader } from './ArticleHeader';
import { ArticleBody } from './ArticleBody';
import { ArticleMeta } from './ArticleMeta';
import { ArticleRelated } from './ArticleRelated';
import { ArticleComments } from './ArticleComments';
import { ShareButtons } from '../common/ShareButtons';
import { Post } from '@/types';

interface ArticleContentProps {
  post: Post;
}

export function ArticleContent({ post }: ArticleContentProps) {
  return (
    <article className="article">
      <ArticleHeader 
        title={post.title} 
        categories={post.categories} 
        date={post.date} 
        featuredImage={post.featuredImage}
        author={post.author}
      />
      
      <div className="article-layout">
        <div className="article-main">
          <ArticleBody 
            content={post.content} 
            blocks={post.blocks}
          />
          
          <ArticleMeta 
            tags={post.tags} 
            readingTime={post.readingTime}
          />
          
          <ShareButtons 
            title={post.title} 
            url={post.url} 
            uuid={post.uuid}
          />
          
          <ArticleComments 
            postId={post.databaseId} 
            uuid={post.uuid}
          />
        </div>
        
        <aside className="article-sidebar">
          <ArticleRelated 
            postId={post.databaseId} 
            categories={post.categories}
            uuid={post.uuid}
          />
        </aside>
      </div>
    </article>
  );
}</code></pre>
    </div>
    
    <h2 id="uuid-integration">UUID集成方案</h2>
    <p>v1.6.1版本与Future Decade Theme v1.2.6+紧密集成，充分利用UUID系统：</p>
    
    <div class="method">
        <h4>UUID获取和缓存</h4>
        <div class="code-filename">lib/api/posts.ts</div>
        <pre><code>import { Post } from '@/types';
import { client } from './apollo-client';
import { gql } from '@apollo/client';
import { cache } from '@/lib/cache';

// 通过UUID获取文章
export async function getPostByUuid(uuid: string): Promise<Post | null> {
  // 尝试从缓存获取
  const cacheKey = `post:uuid:${uuid}`;
  const cachedPost = await cache.get(cacheKey);
  
  if (cachedPost) {
    return cachedPost as Post;
  }
  
  try {
    // 从GraphQL API获取
    const { data } = await client.query({
      query: gql`
        query GetPostByUuid($uuid: String!) {
          contentNodeByUuid(uuid: $uuid) {
            ... on Post {
              id
              databaseId
              uuid
              slug
              title
              date
              modified
              content
              excerpt
              featuredImage {
                node {
                  sourceUrl
                  altText
                  mediaDetails {
                    width
                    height
                  }
                }
              }
              author {
                node {
                  name
                  slug
                  avatar {
                    url
                  }
                }
              }
              categories {
                nodes {
                  name
                  slug
                  uuid
                }
              }
              tags {
                nodes {
                  name
                  slug
                  uuid
                }
              }
              seo {
                title
                metaDesc
                opengraphTitle
                opengraphDescription
                opengraphImage {
                  sourceUrl
                }
              }
            }
          }
        }
      `,
      variables: { uuid },
    });
    
    // 文章不存在
    if (!data.contentNodeByUuid) {
      return null;
    }
    
    // 转换为应用内部Post类型
    const post = transformPostData(data.contentNodeByUuid);
    
    // 存入缓存
    await cache.set(cacheKey, post, 3600); // 缓存1小时
    
    return post;
  } catch (error) {
    console.error('Error fetching post by UUID:', error);
    return null;
  }
}

// 转换API数据到内部数据结构
function transformPostData(apiPost: any): Post {
  return {
    id: apiPost.id,
    databaseId: apiPost.databaseId,
    uuid: apiPost.uuid,
    slug: apiPost.slug,
    title: apiPost.title,
    content: apiPost.content,
    excerpt: apiPost.excerpt,
    date: apiPost.date,
    modified: apiPost.modified,
    featuredImage: apiPost.featuredImage?.node || null,
    author: apiPost.author?.node || null,
    categories: apiPost.categories?.nodes || [],
    tags: apiPost.tags?.nodes || [],
    seo: apiPost.seo || {},
    url: `/articles/${apiPost.uuid}/${apiPost.slug}`,
    readingTime: calculateReadingTime(apiPost.content),
  };
}

// 计算阅读时间
function calculateReadingTime(content: string): number {
  const wordCount = content.replace(/<[^>]*>/g, '').split(/\s+/).length;
  return Math.ceil(wordCount / 200); // 假设平均阅读速度是每分钟200字
}</code></pre>
    </div>
    
    <h2 id="graphql-queries">GraphQL查询</h2>
    <p>v1.6.1采用优化的GraphQL查询来获取文章数据，充分利用WPGraphQL和Future Decade Theme的UUID功能：</p>
    
    <div class="method">
        <h4>主要GraphQL查询</h4>
        <table>
            <tr>
                <th>查询名称</th>
                <th>用途</th>
            </tr>
            <tr>
                <td><code>GetPostByUuid</code></td>
                <td>通过UUID获取文章完整数据</td>
            </tr>
            <tr>
                <td><code>GetRecentPosts</code></td>
                <td>获取最近文章列表（带UUID）</td>
            </tr>
            <tr>
                <td><code>GetRelatedPosts</code></td>
                <td>获取相关文章（基于分类和标签）</td>
            </tr>
            <tr>
                <td><code>GetPostComments</code></td>
                <td>获取文章评论</td>
            </tr>
        </table>
    </div>
    
    <h2 id="seo-optimization">SEO优化</h2>
    <p>v1.6.1版本增强了SEO性能，特别是针对基于UUID的URL结构：</p>
    
    <div class="method">
        <h4>SEO元数据生成</h4>
        <div class="code-filename">lib/seo.ts</div>
        <pre><code>import { Metadata } from 'next';
import { Post } from '@/types';
import { site } from '@/config/site';

export function generateArticleSEO(post: Post): Metadata {
  // 基本元数据
  const metadata: Metadata = {
    title: post.seo?.title || post.title,
    description: post.seo?.metaDesc || post.excerpt?.replace(/<[^>]*>/g, '').slice(0, 160),
    alternates: {
      canonical: `${site.url}/articles/${post.uuid}/${post.slug}`,
    },
  };
  
  // 开放图标签（社交媒体分享）
  const openGraph = {
    type: 'article',
    url: `${site.url}/articles/${post.uuid}/${post.slug}`,
    title: post.seo?.opengraphTitle || post.title,
    description: post.seo?.opengraphDescription || metadata.description,
    siteName: site.name,
    locale: 'zh_CN',
    publishedTime: post.date,
    modifiedTime: post.modified,
    authors: [post.author?.name].filter(Boolean),
  };
  
  // 添加特色图像
  if (post.featuredImage?.sourceUrl) {
    openGraph.images = [
      {
        url: post.featuredImage.sourceUrl,
        alt: post.featuredImage.altText || post.title,
        width: post.featuredImage.mediaDetails?.width || 1200,
        height: post.featuredImage.mediaDetails?.height || 630,
      },
    ];
  } else if (post.seo?.opengraphImage?.sourceUrl) {
    openGraph.images = [
      {
        url: post.seo.opengraphImage.sourceUrl,
        alt: post.title,
      },
    ];
  }
  
  // 添加文章结构化数据
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: post.title,
    description: metadata.description,
    datePublished: post.date,
    dateModified: post.modified,
    url: `${site.url}/articles/${post.uuid}/${post.slug}`,
    author: {
      '@type': 'Person',
      name: post.author?.name,
    },
    publisher: {
      '@type': 'Organization',
      name: site.name,
      logo: {
        '@type': 'ImageObject',
        url: `${site.url}/logo.png`,
      },
    },
  };
  
  // 添加特色图像到结构化数据
  if (post.featuredImage?.sourceUrl) {
    structuredData.image = post.featuredImage.sourceUrl;
  }
  
  // 应用结构化数据
  metadata.other = {
    'script:ld+json': JSON.stringify(structuredData),
  };
  
  return {
    ...metadata,
    openGraph,
  };
}</code></pre>
    </div>
    
    <p>SEO优化要点：</p>
    <ul>
        <li>为所有基于UUID的URL设置规范链接</li>
        <li>完整的结构化数据标记</li>
        <li>优化的开放图标签</li>
        <li>支持自定义SEO元数据和默认回退</li>
        <li>基于内容分类的元数据增强</li>
    </ul>
    
    <h2 id="redirects">重定向和向后兼容</h2>
    <p>v1.6.1提供了完整的向后兼容解决方案，确保旧链接仍然有效：</p>
    
    <table>
        <tr>
            <th>旧URL格式</th>
            <th>新URL格式</th>
            <th>重定向方式</th>
        </tr>
        <tr>
            <td><code>/post/sample-post</code></td>
            <td><code>/articles/a1b2c3d4e5/sample-post</code></td>
            <td>中间件 + API查询</td>
        </tr>
        <tr>
            <td><code>/articles/sample-post</code></td>
            <td><code>/articles/a1b2c3d4e5/sample-post</code></td>
            <td>中间件 + API查询</td>
        </tr>
        <tr>
            <td><code>/articles/a1b2c3d4e5</code></td>
            <td><code>/articles/a1b2c3d4e5/sample-post</code></td>
            <td>中间件自动补全</td>
        </tr>
        <tr>
            <td><code>/articles/a1b2c3d4e5/old-slug</code></td>
            <td><code>/articles/a1b2c3d4e5/new-slug</code></td>
            <td>中间件自动更正</td>
        </tr>
    </table>
    
    <h2 id="performance">性能优化</h2>
    <p>v1.6.1对文章详情页性能进行了全面优化：</p>
    
    <ul>
        <li><strong>ISR缓存策略</strong> - 使用增量静态再生成，平衡静态生成和动态内容</li>
        <li><strong>组件级缓存</strong> - 对不频繁变化的组件应用React Server Components缓存</li>
        <li><strong>图像优化</strong> - 使用Next.js图像组件进行自动优化和延迟加载</li>
        <li><strong>API响应缓存</strong> - 使用Redis缓存GraphQL响应</li>
        <li><strong>批量数据获取</strong> - 合并API请求，减少网络往返</li>
        <li><strong>代码拆分</strong> - 基于路由和组件的自动代码拆分</li>
    </ul>
    
    <div class="method">
        <h4>组件级缓存示例</h4>
        <div class="code-filename">components/articles/ArticleRelated.tsx</div>
        <pre><code>import { cache } from 'react';
import { getRelatedPosts } from '@/lib/api/posts';
import { Post } from '@/types';
import { ArticleCard } from './ArticleCard';

interface ArticleRelatedProps {
  postId: number;
  categories: { id: string; slug: string }[];
  uuid: string;
}

// 使用React缓存获取相关文章
const getRelatedPostsCached = cache(async (postId: number, categoryIds: string[]) => {
  return getRelatedPosts(postId, categoryIds, 3);
});

export async function ArticleRelated({ postId, categories, uuid }: ArticleRelatedProps) {
  // 提取分类ID
  const categoryIds = categories.map(cat => cat.id);
  
  // 获取相关文章
  const relatedPosts = await getRelatedPostsCached(postId, categoryIds);
  
  // 如果没有相关文章，不渲染组件
  if (!relatedPosts || relatedPosts.length === 0) {
    return null;
  }
  
  return (
    <section className="article-related">
      <h3 className="related-title">相关文章</h3>
      <div className="related-posts">
        {relatedPosts.map((post: Post) => (
          <ArticleCard 
            key={post.uuid} 
            post={post} 
            compact={true} 
          />
        ))}
      </div>
    </section>
  );
}</code></pre>
    </div>
    
    <h2 id="troubleshooting">故障排除</h2>
    <p>使用v1.6.1可能遇到的常见问题及解决方法：</p>
    
    <table>
        <tr>
            <th>问题</th>
            <th>可能原因</th>
            <th>解决方法</th>
        </tr>
        <tr>
            <td>404未找到错误</td>
            <td>UUID不存在或无效</td>
            <td>检查UUID格式和API响应</td>
        </tr>
        <tr>
            <td>重定向循环</td>
            <td>中间件配置错误</td>
            <td>检查middleware.ts中的重定向逻辑</td>
        </tr>
        <tr>
            <td>旧链接不重定向</td>
            <td>缺少API端点或配置</td>
            <td>确保主题API正确配置</td>
        </tr>
        <tr>
            <td>SEO元数据不显示</td>
            <td>generateMetadata未正确实现</td>
            <td>检查元数据生成逻辑</td>
        </tr>
        <tr>
            <td>GraphQL查询失败</td>
            <td>缺少UUID字段或API</td>
            <td>确保使用主题v1.2.6+</td>
        </tr>
    </table>
    
    <h2 id="version-history">版本历史</h2>
    <h3>v1.6.1（当前版本）</h3>
    <ul>
        <li>引入基于UUID的文章路由系统</li>
        <li>添加路由中间件处理重定向和URL规范化</li>
        <li>重构文章页面组件</li>
        <li>增强SEO元数据生成</li>
        <li>优化性能和缓存策略</li>
        <li>添加向后兼容性支持</li>
    </ul>
    
    <h3>v1.6.0</h3>
    <ul>
        <li>路由前缀设置功能</li>
        <li>自定义钩子增强</li>
        <li>基于slug的文章路由</li>
        <li>基础组件库更新</li>
    </ul>
    
    <h3>v1.5.5及更早版本</h3>
    <ul>
        <li>传统WordPress slug路由</li>
        <li>基础Next.js功能</li>
        <li>无UUID支持</li>
    </ul>

</body>
</html> 