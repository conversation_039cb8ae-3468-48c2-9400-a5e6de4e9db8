<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V1.5.3 VI设置集成 - Future Decade前端文档</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #1a73e8;
            color: white;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 5px;
        }
        h1 {
            margin: 0;
            font-size: 2.2em;
        }
        h2 {
            color: #1a73e8;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        h3 {
            color: #3c4043;
            margin-top: 30px;
        }
        code {
            background-color: #f6f8fa;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: <PERSON><PERSON><PERSON>, Monaco, 'Andale Mono', monospace;
            font-size: 0.9em;
        }
        pre {
            background-color: #f6f8fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            line-height: 1.4;
        }
        pre code {
            background-color: transparent;
            padding: 0;
        }
        .version-info {
            font-size: 0.9em;
            margin-top: 10px;
            color: #fff;
        }
        .note {
            background-color: #e8f0fe;
            padding: 15px;
            border-left: 4px solid #1a73e8;
            margin: 20px 0;
        }
        .warning {
            background-color: #fef7e0;
            padding: 15px;
            border-left: 4px solid #fbbc04;
            margin: 20px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f6f8fa;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px;
        }
        .section-nav {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .section-nav ul {
            list-style-type: none;
            padding-left: 0;
        }
        .section-nav li {
            margin-bottom: 8px;
        }
        .section-nav a {
            color: #1a73e8;
            text-decoration: none;
        }
        .section-nav a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <header>
        <h1>VI设置集成指南</h1>
        <div class="version-info">Version 1.5.3 | Future Decade 前端开发文档</div>
    </header>

    <div class="section-nav">
        <h3>目录</h3>
        <ul>
            <li><a href="#introduction">1. 简介</a></li>
            <li><a href="#schema">2. VI设置GraphQL Schema</a></li>
            <li><a href="#context">3. 创建VIThemeContext</a></li>
            <li><a href="#tailwind">4. 与Tailwind集成</a></li>
            <li><a href="#components">5. 创建使用VI设置的组件</a></li>
            <li><a href="#test-page">6. VI设置测试页面</a></li>
            <li><a href="#usage">7. 在应用中使用VI设置</a></li>
            <li><a href="#deployment">8. 部署注意事项</a></li>
        </ul>
    </div>

    <section id="introduction">
        <h2>1. 简介</h2>
        <p>本文档描述了如何将WordPress后端的VI（Visual Identity）设置集成到Next.js前端应用中。通过这种集成，允许内容管理员在WordPress后台更改品牌视觉标识设置，并实时反映到前端应用，无需重新部署前端代码。</p>
        
        <div class="note">
            <p><strong>什么是VI设置？</strong> VI设置（Visual Identity设置）是指与品牌视觉标识相关的配置，包括品牌色彩、Logo、排版、UI元素样式等。通过统一管理这些设置，可以确保整个应用的设计风格一致性。</p>
        </div>
        
        <h3>主要功能和优势</h3>
        <ul>
            <li>集中管理品牌视觉标识，确保一致性</li>
            <li>无需修改代码即可更新视觉风格</li>
            <li>品牌标识变更不需要重新部署前端应用</li>
            <li>适应多品牌或白标应用场景</li>
            <li>与Tailwind CSS无缝集成</li>
        </ul>
    </section>

    <section id="schema">
        <h2>2. VI设置GraphQL Schema</h2>
        <p>WordPress后端通过GraphQL API提供VI设置数据，查询结构如下：</p>
        
        <pre><code>query GetVISettings {
  viSettings {
    # 基础设置
    logoUrl
    logoDarkUrl
    faviconUrl
    
    # 颜色设置
    primaryColor
    secondaryColor
    backgroundColor
    darkModeEnabled
    darkBackgroundColor
    amberColor
    roseColor
    successColor
    errorColor
    
    # 排版设置
    headingFont
    bodyFont
    baseFontSize
    lineHeight
    spacingUnit
    
    # UI设计令牌
    radiusSmall
    radiusMedium
    radiusLarge
    shadowSmall
    shadowMedium
    shadowLarge
  }
}</code></pre>

        <p>此查询返回完整的VI设置，可以在前端的任何组件中使用。默认情况下，如果某些设置值在后端未设置，将返回预定义的默认值。</p>
    </section>

    <section id="context">
        <h2>3. 创建VIThemeContext</h2>
        <p>为了在整个应用中共享VI设置，创建一个React Context。这个Context不仅提供设置值，还提供辅助函数来方便使用这些设置：</p>
        
        <pre><code>// src/contexts/VIThemeContext.tsx
'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode, useMemo } from 'react';
import { useQuery } from '@apollo/client';
import { GET_VI_SETTINGS } from '@/lib/graphql/queries';

// VI主题设置接口
interface VISettings {
  // 基础设置
  logoUrl?: string;
  logoDarkUrl?: string;
  faviconUrl?: string;
  
  // 颜色设置
  primaryColor?: string;
  secondaryColor?: string;
  backgroundColor?: string;
  darkModeEnabled?: boolean;
  darkBackgroundColor?: string;
  amberColor?: string;
  roseColor?: string;
  successColor?: string;
  errorColor?: string;
  
  // 排版设置
  headingFont?: string;
  bodyFont?: string;
  baseFontSize?: string;
  lineHeight?: string;
  spacingUnit?: string;
  
  // UI设计令牌
  radiusSmall?: string;
  radiusMedium?: string;
  radiusLarge?: string;
  shadowSmall?: string;
  shadowMedium?: string;
  shadowLarge?: string;
}

// 默认VI设置
const defaultSettings: VISettings = {
  // 具体默认值...
};

// VI主题上下文接口
interface VIThemeContextType {
  settings: VISettings;
  loading: boolean;
  error: any;
  cssVariables: string;
  getLogoUrl: (isDark?: boolean) => string;
  getRadius: (size?: 'small' | 'medium' | 'large') => string;
  getShadow: (size?: 'small' | 'medium' | 'large') => string;
  getColor: (colorName: string) => string;
  getFontFamily: (type?: 'heading' | 'body') => string;
}

// 创建上下文
const VIThemeContext = createContext<VIThemeContextType | undefined>(undefined);

// Context Provider组件
export const VIThemeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // 实现代码...
};

// 使用钩子
export const useVITheme = (): VIThemeContextType => {
  const context = useContext(VIThemeContext);
  if (context === undefined) {
    throw new Error('useVITheme必须在VIThemeProvider内部使用');
  }
  return context;
};</code></pre>

        <p>Context Provider中的主要功能：</p>
        <ul>
            <li>使用Apollo Client查询GraphQL API获取VI设置</li>
            <li>生成全局CSS变量应用于应用</li>
            <li>提供辅助函数以更方便地访问设置</li>
            <li>处理加载状态和错误处理</li>
        </ul>
        
        <div class="note">
            <p>为了最大限度地减少重新渲染，使用了useMemo钩子来缓存计算值，提高性能。</p>
        </div>
    </section>

    <section id="tailwind">
        <h2>4. 与Tailwind集成</h2>
        <p>修改Tailwind配置，使其能够使用从VI设置中获取的CSS变量：</p>
        
        <pre><code>// tailwind.config.js
/** @type {import('tailwindcss').Config} */
module.exports = {
  // ...
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: 'var(--primary-color)',
          50: '#eef2ff',
          // ...
          500: 'var(--primary-color, #6366f1)', // 使用CSS变量，带默认值
        },
        secondary: {
          DEFAULT: 'var(--secondary-color)',
          500: 'var(--secondary-color, #4285f4)',
        },
        // 其他颜色...
      },
      fontFamily: {
        sans: ['var(--body-font)', 'Inter', 'var(--font-inter)', 'sans-serif'],
        heading: ['var(--heading-font)', 'Inter', 'var(--font-inter)', 'sans-serif'],
      },
      borderRadius: {
        'sm': 'var(--radius-small, 0.125rem)',
        'md': 'var(--radius-medium, 0.375rem)',
        'lg': 'var(--radius-large, 0.5rem)',
      },
      boxShadow: {
        'sm': 'var(--shadow-small, 0 1px 2px 0 rgba(0, 0, 0, 0.05))',
        'md': 'var(--shadow-medium)',
        'lg': 'var(--shadow-large)',
      },
    },
  },
  // ...
};</code></pre>

        <p>这种方法允许在Tailwind类中直接使用VI设置。例如，<code>bg-primary</code>将使用主品牌色，<code>font-heading</code>将使用标题字体。</p>
    </section>

    <section id="components">
        <h2>5. 创建使用VI设置的组件</h2>
        <p>以按钮组件为例，展示如何创建使用VI设置的组件：</p>
        
        <pre><code>// src/components/ui/Button.tsx
'use client';

import React, { ButtonHTMLAttributes, ReactNode } from 'react';
import { useVITheme } from '@/contexts/VIThemeContext';
import { twMerge } from 'tailwind-merge';

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'success' | 'error';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  icon,
  iconPosition = 'left',
  className,
  ...props
}) => {
  const { getRadius } = useVITheme();
  
  // 样式定义...
  
  // 使用VI设置的圆角
  const borderRadiusStyle = `rounded-[${getRadius('medium')}]`;
  
  // 合并所有样式
  const buttonStyles = twMerge(
    // 各种样式...
    borderRadiusStyle,
    className
  );
  
  return (
    <button className={buttonStyles} {...props}>
      {/* 按钮内容... */}
    </button>
  );
};</code></pre>

        <p>类似地，可以创建卡片(Card)、文本输入(Input)、导航栏(Navbar)等组件，它们都使用来自VIThemeContext的设置。</p>
    </section>

    <section id="test-page">
        <h2>6. VI设置测试页面</h2>
        <p>创建一个测试页面，展示从后端获取的所有VI设置及其视觉效果：</p>
        
        <pre><code>// src/app/vi-test/page.tsx
'use client';

import { useQuery } from '@apollo/client';
import { GET_VI_SETTINGS } from '@/lib/graphql/queries';
import { useState } from 'react';
import Link from 'next/link';

export default function VITestPage() {
  const { loading, error, data } = useQuery(GET_VI_SETTINGS);
  const [activeTab, setActiveTab] = useState('basic');

  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error.message}</div>;

  const viSettings = data?.viSettings || {};

  // 标签页定义
  const tabs = [
    { id: 'basic', label: '基础设置' },
    { id: 'colors', label: '颜色设置' },
    { id: 'typography', label: '排版设置' },
    { id: 'tokens', label: '设计令牌' },
  ];

  return (
    <div className="container mx-auto p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">VI设置测试页面</h1>
        <Link href="/" className="text-blue-500 hover:underline">返回首页</Link>
      </div>
      
      {/* 标签导航 */}
      <div className="flex border-b mb-6">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`py-2 px-4 font-medium ${activeTab === tab.id
              ? 'border-b-2 border-blue-500 text-blue-500'
              : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* 各标签页内容... */}
      
      {/* 原始数据查看 */}
      <div className="mt-8">
        <h2 className="text-xl font-bold mb-2">原始数据</h2>
        <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-96">
          {JSON.stringify(viSettings, null, 2)}
        </pre>
      </div>
    </div>
  );
}</code></pre>

        <p>这个测试页面包含四个标签页，分别展示：</p>
        <ul>
            <li><strong>基础设置</strong>: Logo和Favicon</li>
            <li><strong>颜色设置</strong>: 品牌色彩方案</li>
            <li><strong>排版设置</strong>: 字体和文字规范</li>
            <li><strong>设计令牌</strong>: UI元素的圆角和阴影</li>
        </ul>
        
        <p>通过这个页面，设计和开发团队可以直观地查看当前VI设置的效果。</p>
    </section>

    <section id="usage">
        <h2>7. 在应用中使用VI设置</h2>
        <p>在实际应用中使用VI设置的三种主要方式：</p>
        
        <h3>7.1 直接在JSX中使用CSS变量</h3>
        <pre><code>// 在样式对象中使用CSS变量
&lt;div style={{ 
  backgroundColor: 'var(--primary-color)',
  borderRadius: 'var(--radius-medium)'
}}>
  内容
&lt;/div></code></pre>

        <h3>7.2 使用Tailwind类</h3>
        <pre><code>// 使用配置了VI设置变量的Tailwind类
&lt;div className="bg-primary rounded-md shadow-md">
  内容
&lt;/div></code></pre>

        <h3>7.3 使用Context钩子函数</h3>
        <pre><code>// 使用useVITheme钩子获取辅助函数
const { getColor, getRadius } = useVITheme();

// 在样式对象中使用
&lt;div style={{ 
  backgroundColor: getColor('primary'),
  borderRadius: getRadius('medium')
}}>
  内容
&lt;/div></code></pre>

        <div class="note">
            <p>推荐的方法是结合使用Tailwind类和Context钩子函数，这样可以获得最大的灵活性和代码可读性。</p>
        </div>
    </section>

    <section id="deployment">
        <h2>8. 部署注意事项</h2>
        <p>在部署使用VI设置的应用时，需要注意以下几点：</p>
        
        <h3>8.1 依赖安装</h3>
        <p>确保安装了所有必要的依赖：</p>
        <pre><code>npm install tailwind-merge</code></pre>
        
        <p>或在Dockerfile中：</p>
        <pre><code>RUN npm install tailwind-merge</code></pre>
        
        <h3>8.2 环境变量</h3>
        <p>确保配置了正确的GraphQL API端点：</p>
        <pre><code>NEXT_PUBLIC_WORDPRESS_API_URL=https://your-wordpress-site.com/graphql</code></pre>
        
        <h3>8.3 构建优化</h3>
        <p>由于VI设置是在运行时动态获取的，确保在构建时不对其进行过度优化：</p>
        <ul>
            <li>避免对CSS变量进行提前优化或清除</li>
            <li>确保GraphQL查询不被意外移除</li>
        </ul>
        
        <h3>8.4 CDN和缓存策略</h3>
        <p>如果使用CDN，适当配置缓存策略：</p>
        <ul>
            <li>静态资源可以长期缓存</li>
            <li>VI设置相关的数据应使用更短的缓存时间</li>
            <li>考虑实现缓存失效机制，在VI设置更新后清除缓存</li>
        </ul>
    </section>

    <div class="note" style="margin-top: 40px;">
        <h3>总结</h3>
        <p>通过本文介绍的方法，前端应用可以完全集成WordPress后端的VI设置，实现无需代码更改就能更新整个应用视觉风格的能力。这种方法不仅提高了维护效率，还为不同品牌需求提供了灵活的支持。</p>
    </div>

    <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #666; font-size: 0.9em;">
        <p>Future Decade Frontend Team &copy; 2023</p>
        <p>文档版本：V1.5.3 | 最后更新：2023-12-20</p>
    </footer>
</body>
</html> 