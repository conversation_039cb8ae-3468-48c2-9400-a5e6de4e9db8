<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ACF文章模板功能文档 - V1.8.7</title>
  <style>
    body {
      font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3, h4 {
      color: #1a1a1a;
      margin-top: 24px;
      margin-bottom: 16px;
    }
    h1 {
      font-size: 2em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    h2 {
      font-size: 1.5em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    h3 {
      font-size: 1.25em;
    }
    h4 {
      font-size: 1em;
    }
    code {
      font-family: "SFMono-Regular", <PERSON><PERSON><PERSON>, "Liberation Mono", Menlo, monospace;
      background-color: rgba(27, 31, 35, 0.05);
      padding: 0.2em 0.4em;
      border-radius: 3px;
      font-size: 0.85em;
    }
    pre {
      background-color: #f6f8fa;
      border-radius: 3px;
      padding: 16px;
      overflow: auto;
      line-height: 1.45;
    }
    pre code {
      background-color: transparent;
      padding: 0;
    }
    ul, ol {
      padding-left: 2em;
    }
    li + li {
      margin-top: 0.25em;
    }
    a {
      color: #0366d6;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 20px 0;
    }
    table thead {
      background-color: #f6f8fa;
    }
    table th, table td {
      border: 1px solid #dfe2e5;
      padding: 8px 12px;
      text-align: left;
    }
    .note {
      background-color: #f8f9fa;
      border-left: 4px solid #007bff;
      padding: 12px 16px;
      margin: 16px 0;
    }
    .warning {
      background-color: #fff3cd;
      border-left: 4px solid #ffc107;
      padding: 12px 16px;
      margin: 16px 0;
    }
  </style>
</head>
<body>
  <h1>ACF文章模板功能文档</h1>
  <p>版本：V1.8.7</p>
  <p>本文档描述了使用Advanced Custom Fields (ACF)定义的文章模板功能，以及在前端应用中实现不同模板的方法。</p>

  <h2>目录</h2>
  <ol>
    <li><a href="#overview">功能概述</a></li>
    <li><a href="#acf-setup">ACF字段设置</a></li>
    <li><a href="#graphql-queries">GraphQL查询</a></li>
    <li><a href="#template-components">前端模板组件</a></li>
    <li><a href="#article-page">文章页面实现</a></li>
    <li><a href="#usage-example">使用示例</a></li>
  </ol>

  <h2 id="overview">1. 功能概述</h2>
  <p>文章模板功能允许编辑人员为文章选择不同的显示模板，从而实现多种文章展示方式。当前支持以下模板类型：</p>
  <ul>
    <li><strong>标准模板</strong>：传统文章布局，包含标题、作者、日期、内容和特色图片</li>
    <li><strong>封面模板</strong>：带有全屏封面图的布局，标题和作者信息覆盖在封面图上</li>
    <li><strong>大标题模板</strong>：突出显示大号标题，可以包含额外的图片展示</li>
    <li><strong>视频模板</strong>：支持视频内容的模板，优先显示视频而非特色图片</li>
  </ul>

  <h2 id="acf-setup">2. ACF字段设置</h2>
  <p>文章模板功能基于WordPress的Advanced Custom Fields插件和WPGraphQL for ACF插件实现。需要在WordPress后台创建名为"postTemplate"的ACF字段组，包含以下字段：</p>

  <pre><code>{
  "key": "group_12345",
  "title": "文章模板",
  "fields": [
    {
      "key": "field_template_type",
      "label": "模板类型",
      "name": "templateType",
      "type": "select",
      "choices": {
        "standard": "标准模板",
        "cover": "封面模板",
        "hero_title": "大标题模板",
        "video": "视频模板"
      },
      "default_value": "standard"
    },
    {
      "key": "field_subtitle",
      "label": "副标题",
      "name": "subtitle",
      "type": "text"
    },
    {
      "key": "field_article_author",
      "label": "文章作者",
      "name": "articleAuthor",
      "type": "text"
    },
    {
      "key": "field_article_source",
      "label": "来源",
      "name": "articleSource",
      "type": "text"
    },
    {
      "key": "field_copyright_type",
      "label": "版权类型",
      "name": "copyrightType",
      "type": "select",
      "choices": {
        "original": "原创",
        "reprint": "转载",
        "translation": "翻译"
      }
    },
    {
      "key": "field_video_url",
      "label": "视频链接",
      "name": "videoUrl",
      "type": "url",
      "conditional_logic": [
        {
          "field": "field_template_type",
          "operator": "==",
          "value": "video"
        }
      ]
    },
    {
      "key": "field_additional_images",
      "label": "额外图片",
      "name": "additionalImages",
      "type": "gallery",
      "conditional_logic": [
        {
          "field": "field_template_type",
          "operator": "==",
          "value": "hero_title"
        }
      ]
    }
  ],
  "location": [
    [
      {
        "param": "post_type",
        "operator": "==",
        "value": "post"
      }
    ]
  ]
}</code></pre>

  <h2 id="graphql-queries">3. GraphQL查询</h2>
  
  <h3>3.1 PostTemplate片段</h3>
  <p>在<code>src/lib/graphql/fragments.ts</code>中定义PostTemplate片段：</p>
  <pre><code>// 文章模板片段
export const POST_TEMPLATE_FRAGMENT = gql`
  fragment PostTemplateFields on PostTemplate {
    templateType
    subtitle
    articleAuthor
    articleSource
    copyrightType
    videoUrl
    additionalImages {
      nodes {
        sourceUrl
        altText
      }
    }
  }
`;</code></pre>

  <h3>3.2 文章查询</h3>
  <p>在<code>src/lib/api.ts</code>中使用该片段查询文章：</p>
  <pre><code>// 根据短UUID获取文章的GraphQL查询
const POST_BY_UUID_QUERY = gql`
  query GetPostByUuid($uuid: String!) {
    postByUuid(uuid: $uuid) {
      id
      databaseId
      title
      slug
      date
      excerpt
      content
      shortUuid
      featuredImage {
        node {
          sourceUrl
          altText
        }
      }
      author {
        node {
          name
          avatar {
            url
          }
        }
      }
      categories {
        nodes {
          id
          name
          slug
        }
      }
      tags {
        nodes {
          id
          name
          slug
        }
      }
      postTemplate {
        ...PostTemplateFields
      }
    }
  }
  ${POST_TEMPLATE_FRAGMENT}
`;</code></pre>

  <h2 id="template-components">4. 前端模板组件</h2>
  <p>模板组件位于<code>src/components/templates/</code>目录下，使用模块化的方式组织：</p>

  <h3>4.1 组件结构</h3>
  <pre><code>src/components/templates/
├── StandardTemplate.tsx  // 标准模板
├── CoverTemplate.tsx     // 封面模板
├── HeroTitleTemplate.tsx // 大标题模板
├── VideoTemplate.tsx     // 视频模板
└── index.ts              // 统一导出</code></pre>

  <h3>4.2 PostType接口</h3>
  <p>在<code>StandardTemplate.tsx</code>中定义了共享的PostType接口：</p>
  <pre><code>export interface PostType {
  id: string;
  title: string;
  date: string;
  content: string;
  shortUuid?: string;
  author?: PostAuthor;
  featuredImage?: FeaturedImage;
  categories?: {
    nodes: PostCategory[];
  };
  tags?: {
    nodes: PostTag[];
  };
  postTemplate?: PostTemplateType;
}</code></pre>

  <h3>4.3 模板统一导出</h3>
  <p>在<code>index.ts</code>中统一导出所有模板组件：</p>
  <pre><code>export { default as StandardTemplate } from './StandardTemplate';
export { default as CoverTemplate } from './CoverTemplate';
export { default as HeroTitleTemplate } from './HeroTitleTemplate';
export { default as VideoTemplate } from './VideoTemplate';
export type { PostType } from './StandardTemplate';</code></pre>

  <div class="note">
    <p><strong>注意</strong>：重新导出类型时，需要使用<code>export type</code>语法，特别是当项目启用了TypeScript的<code>isolatedModules</code>设置。</p>
  </div>

  <h2 id="article-page">5. 文章页面实现</h2>
  <p>在<code>src/app/post/[uuid]/[slug]/page.tsx</code>中，根据文章的<code>postTemplate.templateType</code>字段选择合适的模板组件：</p>

  <pre><code>// 导入文章模板组件
import { 
  StandardTemplate, 
  CoverTemplate, 
  HeroTitleTemplate, 
  VideoTemplate 
} from '@/components/templates';

export default async function ArticlePage({ params }: { 
  params: { uuid: string; slug: string } 
}) {
  // ...获取文章数据的代码...

  // 根据postTemplate.templateType选择不同的模板渲染
  const templateType = post.postTemplate?.templateType?.[0] || 'standard';
  
  // 选择要使用的模板组件
  let TemplateComponent;
  switch (templateType) {
    case 'cover':
      TemplateComponent = CoverTemplate;
      break;
    case 'hero_title':
      TemplateComponent = HeroTitleTemplate;
      break;
    case 'video':
      TemplateComponent = VideoTemplate;
      break;
    case 'standard':
    default:
      TemplateComponent = StandardTemplate;
      break;
  }
  
  return (
    <div className="post-container">
      <TemplateComponent post={post} />
      
      {/* 评论区 */}
      <div className="max-w-4xl mx-auto px-4">
        <CommentSection 
          postId={post.databaseId || 0} 
          commentStatus={post.commentStatus} 
        />
      </div>
    </div>
  );
}</code></pre>

  <h2 id="usage-example">6. 使用示例</h2>
  
  <h3>6.1 创建带ACF字段的文章</h3>
  <ol>
    <li>在WordPress后台新建或编辑文章</li>
    <li>在文章编辑页面下方找到"文章模板"字段组</li>
    <li>选择模板类型（例如"封面模板"）</li>
    <li>填写相关字段（副标题、作者、来源等）</li>
    <li>发布文章</li>
  </ol>

  <h3>6.2 前端渲染</h3>
  <p>当访问文章页面时，系统会：</p>
  <ol>
    <li>从GraphQL获取文章数据，包括ACF字段</li>
    <li>根据<code>templateType</code>选择合适的模板组件</li>
    <li>渲染文章内容，应用模板特定的样式和布局</li>
  </ol>

  <h3>6.3 模板特性</h3>
  <table>
    <thead>
      <tr>
        <th>模板类型</th>
        <th>特性</th>
        <th>推荐使用场景</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>标准模板</td>
        <td>传统布局，顺序显示标题、作者、内容</td>
        <td>一般文章、新闻</td>
      </tr>
      <tr>
        <td>封面模板</td>
        <td>全屏封面图，标题覆盖在封面上</td>
        <td>视觉报道、图文故事</td>
      </tr>
      <tr>
        <td>大标题模板</td>
        <td>突出的大号标题，支持额外图片</td>
        <td>重要公告、特稿</td>
      </tr>
      <tr>
        <td>视频模板</td>
        <td>视频内容优先显示</td>
        <td>视频报道、视频教程</td>
      </tr>
    </tbody>
  </table>

  <div class="warning">
    <p><strong>注意</strong>：使用视频模板时，请确保提供有效的视频URL，并考虑设置特色图片作为视频封面。</p>
  </div>

  <h2>总结</h2>
  <p>ACF文章模板功能通过将WordPress的内容管理功能与前端灵活的组件系统相结合，实现了多样化的文章展示效果。编辑人员可以根据内容特点选择最合适的模板，提高用户体验和内容呈现效果。</p>
  <p>该功能遵循项目的架构指南，采用模块化设计，便于维护和扩展。未来可以根据需求添加更多模板类型，满足不同的内容展示需求。</p>

  <hr>
  <footer>
    <p>Future Decade Frontend - V1.8.7 文档</p>
    <p>文档生成日期：<script>document.write(new Date().toLocaleDateString('zh-CN'))</script></p>
  </footer>
</body>
</html> 