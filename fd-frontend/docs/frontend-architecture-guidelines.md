# Future Decade Frontend 应用架构与开发规范

## 1. 应用架构概述

Future Decade Frontend (fd-frontend) 是基于Next.js构建的Headless WordPress前端应用，采用GraphQL作为数据获取层，实现了与WordPress后端的解耦。应用采用现代化的前端技术栈，实现了响应式设计，支持多主题切换，并遵循组件化开发模式。

### 1.1 核心功能模块

- **布局系统**：响应式布局组件，支持多设备适配
- **导航系统**：支持多级菜单，包含移动端汉堡菜单
- **内容渲染**：文章、页面、自定义内容类型的渲染
- **主题系统**：支持亮色/暗色模式切换与VI系统集成
- **API集成**：使用GraphQL查询WordPress数据

### 1.2 技术栈

| 技术/框架 | 版本 | 用途 |
|----------|------|------|
| Next.js | 14.x | 应用框架与服务端渲染 |
| React | 18.x | 前端视图库 |
| TypeScript | 5.x | 类型系统 |
| Apollo Client | 3.x | GraphQL客户端 |
| Tailwind CSS | 3.x | 样式系统 |

## 2. 目录结构

```
fd-frontend/
├── src/                  # 源代码目录
│   ├── app/              # Next.js App Router页面
│   │   ├── layouts/      # 布局相关组件
│   │   ├── ui/           # 通用UI组件
│   │   └── [feature]/    # 功能模块组件
│   ├── hooks/            # 自定义React Hooks
│   ├── lib/              # 工具函数和库
│   │   ├── graphql/      # GraphQL相关
│   │   │   ├── fragments.ts  # GraphQL片段
│   │   │   └── queries.ts    # GraphQL查询
│   │   └── utils/        # 通用工具函数
│   ├── contexts/         # React上下文
│   └── types/            # TypeScript类型定义
├── public/               # 静态资源
├── docs/                 # 项目文档
└── [配置文件]            # 各种配置文件
```

## 3. 开发规范

### 3.1 GraphQL数据获取规范

#### 3.1.1 片段定义

- 所有可重用的GraphQL片段必须定义在 `src/lib/graphql/fragments.ts` 中
- 片段命名格式：`[实体名]_FRAGMENT` 或 `[实体名]_[类型]_FRAGMENT`
- 确保片段包含必要的基础字段，避免过度获取数据

```typescript
// 文章基础片段示例
export const POST_FRAGMENT = gql`
  fragment PostFields on Post {
    id
    title
    date
    slug
    excerpt
    featuredImage {
      node {
        sourceUrl
        altText
      }
    }
    categories {
      nodes {
        id
        name
        slug
      }
    }
  }
`;
```

#### 3.1.2 查询定义

- 所有GraphQL查询必须定义在 `src/lib/graphql/queries.ts` 中
- 查询命名规则：`GET_[实体]_[BY_条件]`
- 尽可能使用片段复用字段定义
- 查询参数应有清晰的类型定义和默认值

```typescript
// 查询示例
export const GET_POSTS_BY_CATEGORY = gql`
  query GetPostsByCategory($categoryId: Int!, $first: Int = 10) {
    posts(where: { categoryId: $categoryId }, first: $first) {
      nodes {
        ...PostFields
      }
    }
  }
  ${POST_FRAGMENT}
`;
```

#### 3.1.3 自定义Hook

- 数据获取逻辑封装在自定义Hook中，放置在 `src/hooks/` 目录
- Hook命名规则：`use[实体][动作]`
- Hook应返回统一的状态结构：`{ data, loading, error, refetch }`
- 实现适当的缓存和数据处理逻辑

```typescript
// Hook示例
export const usePostsByCategory = (categoryId?: number) => {
  const { data, loading, error, refetch } = useQuery(
    GET_POSTS_BY_CATEGORY,
    {
      variables: { categoryId },
      skip: !categoryId,
      // 其他配置...
    }
  );

  // 数据处理...
  
  return {
    posts: data?.posts?.nodes || [],
    loading,
    error,
    refetch
  };
};
```

### 3.2 组件设计规范

#### 3.2.1 组件分类与位置

- **布局组件**：放置在 `src/components/layouts/`
- **UI组件**：通用界面元素，放置在 `src/components/ui/`
- **功能组件**：特定功能模块，放置在 `src/components/[feature]/`
- **页面组件**：放置在 `src/app/` 目录下对应路由

#### 3.2.2 组件命名与结构

- 文件采用大驼峰命名法：`PostCard.tsx`、`CategoryList.tsx`
- 组件名与文件名一致
- 每个组件文件只导出一个主要组件
- 组件内部辅助组件使用 `const SubComponent = () => {...}` 形式定义

```typescript
// PostCard.tsx
import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

// 组件接口定义
interface PostCardProps {
  title: string;
  excerpt?: string;
  slug: string;
  // 其他属性...
}

// 可选的辅助组件
const PostCardImage = ({ src, alt }: { src: string; alt?: string }) => (
  // 实现...
);

// 主组件
const PostCard: React.FC<PostCardProps> = ({ title, excerpt, slug }) => {
  return (
    // 组件实现...
  );
};

export default PostCard;
```

#### 3.2.3 组件设计原则

- 遵循单一职责原则，每个组件只专注于一个功能
- 使用TypeScript接口明确定义组件属性
- 使用适当的React Hooks管理状态和副作用
- 大型组件拆分为多个小型组件
- 使用适当的memo优化，避免不必要的重渲染

### 3.3 响应式设计规范

#### 3.3.1 断点系统

使用Tailwind CSS的标准断点系统：

| 断点名称 | 最小宽度 | 适用设备 |
|---------|---------|---------|
| (无前缀) | 0px | 所有移动设备 |
| sm | 640px | 小型平板/大型手机 |
| md | 768px | 平板设备 |
| lg | 1024px | 笔记本电脑 |
| xl | 1280px | 桌面显示器 |
| 2xl | 1536px | 大型显示器 |

#### 3.3.2 响应式实现原则

- **移动优先设计**：先实现移动版布局，再使用媒体查询扩展到大屏幕
- **灵活布局**：使用Flex布局和Grid布局实现响应式设计
- **明确断点使用**：主要使用 `md` 和 `lg` 作为主要断点，避免过多断点变化
- **组件适配**：组件需要兼容不同屏幕尺寸，必要时为不同设备提供不同渲染结构

#### 3.3.3 响应式Tailwind类使用

- 按从小到大的断点顺序排列类：`class="p-3 md:p-4 lg:p-6"`
- 使用合适的展示/隐藏机制：`class="hidden md:block"` 或 `class="block md:hidden"`
- 为交互元素提供足够的点击区域，特别是在移动设备上

```tsx
// 响应式组件示例
<div className="flex flex-col md:flex-row gap-4 md:gap-6">
  <div className="w-full md:w-1/3">
    {/* 侧边栏内容 */}
  </div>
  <div className="w-full md:w-2/3">
    {/* 主要内容 */}
  </div>
</div>
```

### 3.4 样式系统规范

#### 3.4.1 Tailwind使用规范

- 直接在JSX中使用Tailwind类，避免自定义CSS
- 对于重复使用的样式组合，可以使用Tailwind的`@apply`指令创建自定义类
- 遵循项目调色板，不直接使用颜色代码
- 合理组织类名，相关的类放在一起

#### 3.4.2 主题色彩系统

| 用途 | 浅色模式 | 深色模式 |
|------|---------|----------|
| 主题色 | `text-primary`, `bg-primary` | `dark:text-primary-400`, `dark:bg-primary-600` |
| 文本颜色 | `text-gray-900`, `text-gray-600` | `dark:text-white`, `dark:text-gray-300` |
| 背景色 | `bg-white`, `bg-gray-100` | `dark:bg-gray-900`, `dark:bg-gray-800` |
| 边框色 | `border-gray-200` | `dark:border-gray-700` |

#### 3.4.3 间距与排版规则

- 页面外边距：`px-3 md:px-4 lg:px-6`
- 区块间距：`my-4 md:my-6 lg:my-8`
- 元素间距：`space-y-2 md:space-y-3` 或 `gap-4 md:gap-6`
- 字体大小：`text-sm md:text-base` (正文)、`text-xl md:text-2xl` (标题)

### 3.5 路由与页面结构规范

#### 3.5.1 Next.js App Router使用规则

- 页面组件放置在 `src/app/` 目录下对应的路由文件夹中
- 使用 `page.tsx` 定义路由组件
- 使用 `layout.tsx` 定义布局组件
- 使用 `loading.tsx` 定义加载状态组件
- 使用 `error.tsx` 定义错误处理组件

#### 3.5.2 路径命名规范

| 内容类型 | 路径格式 | 示例 |
|---------|---------|------|
| 首页 | `/` | `/` |
| 文章详情 | `/[slug]` | `/hello-world` |
| 分类页面 | `/category/[slug]` | `/category/news` |
| 标签页面 | `/tag/[slug]` | `/tag/technology` |
| 自定义页面 | `/page/[slug]` | `/page/about-us` |
| 存档页面 | `/archive/[year]/[month]` | `/archive/2023/01` |

#### 3.5.3 路由数据获取策略

- **静态页面(SSG)**：对于变化不频繁的内容，使用`generateStaticParams`预生成页面
- **ISR(增量静态再生)**：对于经常变化的内容，设置合适的重新验证间隔
- **SSR(服务器端渲染)**：对于需要实时数据的页面，使用服务器组件获取数据

```typescript
// 文章详情页示例
export async function generateStaticParams() {
  // 获取所有文章slug...
  return posts.map((post) => ({
    slug: post.slug,
  }));
}

export const revalidate = 3600; // 1小时重新验证
```

### 3.6 状态管理规范

#### 3.6.1 局部状态

- 使用 `useState` 和 `useReducer` 管理组件局部状态
- 确保状态逻辑分离，避免复杂组件中的状态混乱

#### 3.6.2 全局状态

- 使用React Context API管理全局状态
- 常见全局状态包括：
  - 主题设置（亮色/暗色模式）
  - 用户认证状态
  - 全局UI状态（侧边栏开关等）
- Context Provider放置在 `src/contexts/` 目录

```typescript
// 主题上下文示例
import React, { createContext, useContext, useState, useEffect } from 'react';

type ThemeMode = 'light' | 'dark';

interface ThemeContextType {
  themeMode: ThemeMode;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // 实现...
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
```

### 3.7 错误处理与加载状态规范

#### 3.7.1 加载状态

- 所有数据获取操作应显示适当的加载状态
- 使用骨架屏代替简单的加载指示器，提高用户体验
- 页面级加载使用App Router的loading.tsx机制

```tsx
// 组件内加载状态示例
if (loading) {
  return <PostCardSkeleton />;
}
```

#### 3.7.2 错误处理

- 使用try/catch捕获异步操作错误
- 页面级错误处理使用App Router的error.tsx机制
- 提供用户友好的错误消息和重试选项

```tsx
// 错误处理示例
if (error) {
  return (
    <ErrorDisplay 
      message="无法加载文章内容" 
      detail={error.message}
      onRetry={() => refetch()} 
    />
  );
}
```

### 3.8 测试规范

#### 3.8.1 单元测试

- 使用Jest和React Testing Library进行组件测试
- 测试文件与组件同目录，命名为`[组件名].test.tsx`
- 关注组件行为测试，而非实现细节

#### 3.8.2 集成测试

- 测试关键用户流程
- 模拟API响应，确保UI与数据交互正确

#### 3.8.3 端到端测试

- 使用Cypress或Playwright进行端到端测试
- 测试关键用户旅程和功能流程

## 4. 开发流程与最佳实践

### 4.1 开发工作流

1. **功能规划**：明确需求和设计规范
2. **组件设计**：设计组件结构和接口
3. **数据层实现**：实现GraphQL查询和数据获取Hook
4. **UI实现**：实现组件UI和交互
5. **测试与优化**：单元测试、集成测试和性能优化
6. **代码审查**：提交PR并进行代码审查
7. **部署**：合并到主分支，触发CI/CD流程

### 4.2 性能优化最佳实践

- **组件优化**：
  - 使用React.memo()包装纯组件
  - 使用useMemo和useCallback缓存计算结果和回调函数
  - 避免不必要的重渲染

- **图片优化**：
  - 使用Next.js的Image组件优化图片加载
  - 为不同设备提供适当尺寸的图片
  - 使用WebP格式和适当的图片压缩

- **代码分割**：
  - 使用dynamic import按需加载组件
  - 适当设置预加载策略

- **缓存策略**：
  - 为GraphQL查询设置合适的缓存策略
  - 使用SWR或React Query进行客户端缓存

### 4.3 无障碍性最佳实践

- 使用语义化HTML标签
- 确保适当的颜色对比度
- 为图片提供alt文本
- 确保键盘可访问性
- 使用ARIA属性增强可访问性

### 4.4 安全最佳实践

- 避免在客户端暴露敏感信息
- 实施适当的内容安全策略(CSP)
- 防止XSS攻击，谨慎使用dangerouslySetInnerHTML
- 确保所有用户输入得到适当验证和清洁

## 5. 文档与注释规范

### 5.1 代码注释

- 为复杂逻辑添加解释性注释
- 使用JSDoc风格为函数和组件添加文档注释
- 不要添加对显而易见代码的冗余注释

```typescript
/**
 * 文章卡片组件，显示文章的摘要信息
 * @param {PostCardProps} props - 组件属性
 * @returns {JSX.Element} 文章卡片组件
 */
const PostCard: React.FC<PostCardProps> = ({ title, excerpt, slug }) => {
  // ...
};
```

### 5.2 项目文档

- 重要功能模块应在`docs/`目录中有对应的文档
- 文档应包含：
  - 功能概述
  - API参考
  - 使用示例
  - 注意事项

## 6. 总结

本规范文档提供了Future Decade Frontend开发的指导原则和标准。遵循这些规范不仅能确保代码质量和一致性，还能提高开发效率和团队协作。

开发人员应该：

1. 熟悉并遵循GraphQL数据获取规范
2. 按照组件设计规范组织代码
3. 实现响应式设计，确保多设备兼容性
4. 遵循路由和状态管理规范
5. 重视性能优化和无障碍性
6. 编写适当的测试和文档

这些规范不是一成不变的，随着项目的发展和技术的进步，我们会不断更新和完善这些规范。 