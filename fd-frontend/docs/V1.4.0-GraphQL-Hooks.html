<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Future Decade V1.4 - GraphQL Hooks实现</title>
  <style>
    :root {
      --primary-color: #0066cc;
      --secondary-color: #666;
      --border-color: #e0e0e0;
      --code-bg: #f5f5f5;
      --code-color: #333;
      --link-color: #0066cc;
      --heading-color: #333;
      --text-color: #444;
      --background-color: #fff;
      --section-bg: #f9f9f9;
      --border-radius: 4px;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: var(--text-color);
      background-color: var(--background-color);
      margin: 0;
      padding: 0;
    }

    .container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem 1rem;
    }

    header {
      text-align: center;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid var(--border-color);
    }

    h1 {
      color: var(--primary-color);
      font-size: 2rem;
      margin: 0.5rem 0;
    }

    h2 {
      color: var(--heading-color);
      font-size: 1.6rem;
      margin: 2rem 0 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid var(--border-color);
    }

    h3 {
      color: var(--heading-color);
      font-size: 1.3rem;
      margin: 1.5rem 0 1rem;
    }

    h4 {
      color: var(--heading-color);
      font-size: 1.1rem;
      margin: 1.2rem 0 0.8rem;
    }

    p {
      margin: 0 0 1rem;
    }

    a {
      color: var(--link-color);
      text-decoration: none;
    }

    a:hover {
      text-decoration: underline;
    }

    .version {
      color: var(--secondary-color);
      font-size: 1rem;
    }

    .section {
      margin-bottom: 2rem;
      padding: 1.5rem;
      background-color: var(--section-bg);
      border-radius: var(--border-radius);
    }

    .subsection {
      margin-bottom: 1.5rem;
    }

    pre, code {
      font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
      font-size: 0.9rem;
    }

    pre {
      background-color: var(--code-bg);
      color: var(--code-color);
      padding: 1rem;
      border-radius: var(--border-radius);
      overflow-x: auto;
      margin: 1rem 0;
      line-height: 1.5;
    }

    code {
      background-color: var(--code-bg);
      color: var(--code-color);
      padding: 0.2rem 0.4rem;
      border-radius: var(--border-radius);
    }

    pre code {
      padding: 0;
      background-color: transparent;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin: 1rem 0;
    }

    table, th, td {
      border: 1px solid var(--border-color);
    }

    th, td {
      padding: 0.5rem;
      text-align: left;
    }

    th {
      background-color: var(--section-bg);
    }

    .toc {
      background-color: var(--section-bg);
      padding: 1rem;
      border-radius: var(--border-radius);
      margin-bottom: 2rem;
    }

    .toc ul {
      padding-left: 1.5rem;
      margin: 0;
    }

    .toc li {
      margin-bottom: 0.5rem;
    }

    .note {
      border-left: 4px solid var(--primary-color);
      padding: 0.5rem 1rem;
      background-color: rgba(0, 102, 204, 0.05);
      margin: 1rem 0;
    }

    .code-title {
      font-weight: bold;
      margin-bottom: 0.5rem;
    }

    .function-name {
      color: var(--primary-color);
      font-weight: bold;
    }

    @media print {
      .toc {
        break-before: always;
      }
      
      .section {
        break-inside: avoid;
      }
      
      pre {
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>Future Decade</h1>
      <p class="version">版本 1.4 - GraphQL Hooks API实现文档</p>
    </header>

    <section class="toc">
      <h2>目录</h2>
      <ul>
        <li><a href="#introduction">1. 介绍</a></li>
        <li><a href="#architecture">2. 架构设计</a></li>
        <li><a href="#hooks">3. GraphQL Hooks</a>
          <ul>
            <li><a href="#posts-hooks">3.1 文章相关</a></li>
            <li><a href="#category-hooks">3.2 分类相关</a></li>
            <li><a href="#custom-post-hooks">3.3 自定义内容类型</a></li>
            <li><a href="#taxonomy-hooks">3.4 分类法相关</a></li>
            <li><a href="#menu-hooks">3.5 菜单相关</a></li>
          </ul>
        </li>
        <li><a href="#usage">4. 使用方法</a>
          <ul>
            <li><a href="#basic-usage">4.1 基本用法</a></li>
            <li><a href="#advanced-usage">4.2 高级用法</a></li>
          </ul>
        </li>
        <li><a href="#examples">5. 组件示例</a></li>
        <li><a href="#error-handling">6. 错误处理</a></li>
      </ul>
    </section>

    <section class="section" id="introduction">
      <h2>1. 介绍</h2>
      <p>本文档介绍了Future Decade前端V1.4版本中实现的GraphQL Hooks API。这些hooks提供了一种简洁、统一的方式来获取WordPress数据，基于Apollo Client构建，优化了缓存和数据获取体验。</p>
      
      <div class="note">
        <p>GraphQL Hooks是对Apollo Client的进一步封装，提供了更具语义化的API，减少了样板代码，提高了开发效率。</p>
      </div>
    </section>

    <section class="section" id="architecture">
      <h2>2. 架构设计</h2>
      
      <h3>2.1 模块结构</h3>
      <p>GraphQL Hooks架构采用模块化设计：</p>
      <pre><code>src/
├── api/
│   └── useGraphQL.ts      # 统一导出所有hooks的入口文件
├── hooks/
│   ├── index.ts           # hooks索引
│   ├── usePost.ts         # 文章详情hooks
│   ├── usePosts.ts        # 文章列表hooks
│   ├── useCategory.ts     # 分类hooks
│   ├── useCustomPost.ts   # 自定义内容类型hooks
│   ├── useTaxonomy.ts     # 分类法hooks
│   └── useMenu.ts         # 菜单hooks
├── lib/
│   ├── apollo-client.ts   # Apollo Client配置
│   └── graphql/
│       ├── fragments.ts   # GraphQL片段定义
│       └── queries.ts     # GraphQL查询定义
└── types/
    └── post.ts            # 数据类型定义</code></pre>

      <h3>2.2 数据流</h3>
      <p>数据流遵循以下路径：</p>
      <ol>
        <li>组件调用hooks（如 <code>usePosts</code>）</li>
        <li>hooks使用Apollo Client的 <code>useQuery</code> 执行GraphQL查询</li>
        <li>查询结果存储在Apollo Client的缓存中</li>
        <li>后续对相同数据的请求优先从缓存中获取</li>
        <li>数据变更时，使用 <code>refetch</code> 刷新数据</li>
      </ol>
    </section>

    <section class="section" id="hooks">
      <h2>3. GraphQL Hooks</h2>
      
      <section class="subsection" id="posts-hooks">
        <h3>3.1 文章相关</h3>
        
        <h4>usePosts</h4>
        <p>获取文章列表的hook。</p>
        <table>
          <tr>
            <th width="30%">参数</th>
            <th>说明</th>
          </tr>
          <tr>
            <td><code>first?: number</code></td>
            <td>获取的文章数量，默认为10</td>
          </tr>
          <tr>
            <td><code>after?: string</code></td>
            <td>分页游标</td>
          </tr>
          <tr>
            <td><code>categoryId?: string</code></td>
            <td>按分类筛选文章</td>
          </tr>
          <tr>
            <td><code>tagId?: string</code></td>
            <td>按标签筛选文章</td>
          </tr>
        </table>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const usePosts = (options: UsePostsOptions = {}) => {
  const { first = 10, after, categoryId, tagId } = options;

  // 根据参数选择不同的查询
  const queryInfo = (() => {
    if (categoryId) {
      return {
        query: GET_POSTS_BY_CATEGORY,
        variables: { categoryId, first, after }
      };
    } else if (tagId) {
      return {
        query: GET_POSTS_BY_TAG,
        variables: { tagId, first, after }
      };
    } else {
      return {
        query: GET_LATEST_POSTS,
        variables: { first, after }
      };
    }
  })();

  const { data, loading, error, fetchMore, refetch } = useQuery&lt;PostsData&gt;(
    queryInfo.query,
    {
      variables: queryInfo.variables,
      notifyOnNetworkStatusChange: true,
    }
  );

  const loadMore = (afterCursor: string) => {
    return fetchMore({
      variables: {
        ...queryInfo.variables,
        after: afterCursor,
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;
        return {
          posts: {
            ...fetchMoreResult.posts,
            nodes: [...prev.posts.nodes, ...fetchMoreResult.posts.nodes],
          },
        };
      },
    });
  };

  return {
    posts: data?.posts?.nodes || [],
    loading,
    error,
    loadMore,
    refetch,
  };
};</code></pre>

        <h4>usePost</h4>
        <p>获取单篇文章详情的hook。</p>
        <table>
          <tr>
            <th width="30%">参数</th>
            <th>说明</th>
          </tr>
          <tr>
            <td><code>id?: string</code></td>
            <td>文章ID</td>
          </tr>
          <tr>
            <td><code>slug?: string</code></td>
            <td>文章别名</td>
          </tr>
        </table>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const usePost = (options: UsePostOptions) => {
  const { id, slug } = options;

  if (!id && !slug) {
    throw new Error('usePost hook需要提供id或slug参数');
  }

  // 根据参数选择查询方式
  const queryInfo = id 
    ? { query: GET_POST_BY_ID, variables: { id } }
    : { query: GET_POST_BY_SLUG, variables: { slug } };

  const { data, loading, error, refetch } = useQuery&lt;PostData&gt;(
    queryInfo.query, 
    { 
      variables: queryInfo.variables,
      skip: !id && !slug
    }
  );

  return {
    post: data?.post,
    loading,
    error,
    refetch,
  };
};</code></pre>
      </section>

      <section class="subsection" id="category-hooks">
        <h3>3.2 分类相关</h3>
        
        <h4>useCategories</h4>
        <p>获取所有分类的hook。</p>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useCategories = () => {
  const { data, loading, error, refetch } = useQuery&lt;CategoriesData&gt;(GET_CATEGORIES);

  return {
    categories: data?.categories?.nodes || [],
    loading,
    error,
    refetch,
  };
};</code></pre>

        <h4>useCategory</h4>
        <p>获取单个分类详情的hook。</p>
        <table>
          <tr>
            <th width="30%">参数</th>
            <th>说明</th>
          </tr>
          <tr>
            <td><code>slug: string</code></td>
            <td>分类别名</td>
          </tr>
        </table>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useCategory = (slug: string) => {
  const { data, loading, error, refetch } = useQuery&lt;CategoryData&gt;(
    GET_CATEGORY_BY_SLUG,
    { 
      variables: { slug },
      skip: !slug
    }
  );

  return {
    category: data?.category,
    loading,
    error,
    refetch,
  };
};</code></pre>
      </section>

      <section class="subsection" id="custom-post-hooks">
        <h3>3.3 自定义内容类型</h3>
        
        <h4>useCustomPosts</h4>
        <p>获取自定义内容类型列表的hook。</p>
        <table>
          <tr>
            <th width="30%">参数</th>
            <th>说明</th>
          </tr>
          <tr>
            <td><code>type: string</code></td>
            <td>内容类型标识符，必填</td>
          </tr>
          <tr>
            <td><code>first?: number</code></td>
            <td>获取的数量，默认为10</td>
          </tr>
          <tr>
            <td><code>after?: string</code></td>
            <td>分页游标</td>
          </tr>
        </table>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useCustomPosts = (options: UseCustomPostsOptions) => {
  const { type, first = 10, after } = options;

  const { data, loading, error, fetchMore, refetch } = useQuery&lt;CustomPostsData&gt;(
    GET_CUSTOM_POSTS,
    {
      variables: { type, first, after },
      skip: !type,
      notifyOnNetworkStatusChange: true,
    }
  );

  const loadMore = (afterCursor: string) => {
    return fetchMore({
      variables: {
        type,
        first,
        after: afterCursor,
      },
      updateQuery: (prev: any, { fetchMoreResult }: any) => {
        if (!fetchMoreResult) return prev;
        return {
          contentNodes: {
            ...fetchMoreResult.contentNodes,
            nodes: [...prev.contentNodes.nodes, ...fetchMoreResult.contentNodes.nodes],
          },
        };
      },
    });
  };

  return {
    posts: data?.contentNodes?.nodes || [],
    loading,
    error,
    loadMore,
    refetch,
  };
};</code></pre>

        <h4>useCustomPost</h4>
        <p>获取单个自定义内容详情的hook。</p>
        <table>
          <tr>
            <th width="30%">参数</th>
            <th>说明</th>
          </tr>
          <tr>
            <td><code>type: string</code></td>
            <td>内容类型标识符，必填</td>
          </tr>
          <tr>
            <td><code>id?: string</code></td>
            <td>内容ID</td>
          </tr>
          <tr>
            <td><code>slug?: string</code></td>
            <td>内容别名</td>
          </tr>
        </table>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useCustomPost = (options: UseCustomPostOptions) => {
  const { type, id, slug } = options;

  if (!type || (!id && !slug)) {
    throw new Error('useCustomPost hook需要提供type和id或slug参数');
  }

  // 根据参数选择查询方式
  const queryInfo = id
    ? { query: GET_CUSTOM_POST_BY_ID, variables: { type, id } }
    : { query: GET_CUSTOM_POST_BY_SLUG, variables: { type, slug } };

  const { data, loading, error, refetch } = useQuery&lt;CustomPostData&gt;(
    queryInfo.query,
    {
      variables: queryInfo.variables,
      skip: !type || (!id && !slug),
    }
  );

  return {
    post: data?.contentNode,
    loading,
    error,
    refetch,
  };
};</code></pre>
      </section>

      <section class="subsection" id="taxonomy-hooks">
        <h3>3.4 分类法相关</h3>
        
        <h4>useTaxonomies</h4>
        <p>获取所有分类法的hook。</p>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useTaxonomies = () => {
  const { data, loading, error, refetch } = useQuery&lt;TaxonomiesData&gt;(GET_TAXONOMIES);

  return {
    taxonomies: data?.taxonomies?.nodes || [],
    loading,
    error,
    refetch,
  };
};</code></pre>

        <h4>useTaxonomy</h4>
        <p>获取单个分类法详情的hook。</p>
        <table>
          <tr>
            <th width="30%">参数</th>
            <th>说明</th>
          </tr>
          <tr>
            <td><code>name: string</code></td>
            <td>分类法名称</td>
          </tr>
        </table>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useTaxonomy = (name: string) => {
  const { data, loading, error, refetch } = useQuery&lt;TaxonomyData&gt;(
    GET_TAXONOMY,
    {
      variables: { name },
      skip: !name,
    }
  );

  return {
    taxonomy: data?.taxonomy,
    loading,
    error,
    refetch,
  };
};</code></pre>
      </section>

      <section class="subsection" id="menu-hooks">
        <h3>3.5 菜单相关</h3>
        
        <h4>useMenu</h4>
        <p>获取指定位置菜单的hook。</p>
        <table>
          <tr>
            <th width="30%">参数</th>
            <th>说明</th>
          </tr>
          <tr>
            <td><code>location: string</code></td>
            <td>菜单位置标识符</td>
          </tr>
        </table>
        
        <div class="code-title">实现代码：</div>
        <pre><code>export const useMenu = (location: string) => {
  const { data, loading, error, refetch } = useQuery&lt;MenuData&gt;(
    GET_MENU_BY_LOCATION,
    {
      variables: { location },
      skip: !location,
    }
  );

  return {
    menu: data?.menu,
    menuItems: data?.menu?.menuItems?.nodes || [],
    loading,
    error,
    refetch,
  };
};</code></pre>
      </section>
    </section>

    <section class="section" id="usage">
      <h2>4. 使用方法</h2>
      
      <section class="subsection" id="basic-usage">
        <h3>4.1 基本用法</h3>
        <p>所有的hooks都已经被封装并从统一的API导出，可以这样导入它们：</p>
        
        <pre><code>// 导入单个hook
import { usePosts } from '@/api/useGraphQL';

// 导入多个hooks
import { usePost, useCategories, useMenu } from '@/api/useGraphQL';</code></pre>

        <p>然后在组件中使用：</p>
        
        <pre><code>const PostsList = () => {
  const { posts, loading, error } = usePosts({ first: 10 });

  if (loading) return &lt;div&gt;加载中...&lt;/div&gt;;
  if (error) return &lt;div&gt;错误: {error.message}&lt;/div&gt;;

  return (
    &lt;ul&gt;
      {posts.map(post => (
        &lt;li key={post.id}&gt;{post.title}&lt;/li&gt;
      ))}
    &lt;/ul&gt;
  );
};</code></pre>
      </section>

      <section class="subsection" id="advanced-usage">
        <h3>4.2 高级用法</h3>
        
        <h4>缓存管理</h4>
        <p>所有的hooks都利用Apollo Client的缓存机制。默认情况下，数据会被缓存，并在后续请求中重用。</p>
        
        <h4>强制刷新数据</h4>
        <p>使用<code>refetch</code>函数可以强制重新获取数据：</p>
        
        <pre><code>const { posts, refetch } = usePosts();

// 刷新数据
const refreshData = () => {
  refetch();
};</code></pre>

        <h4>加载更多数据（分页）</h4>
        <p>对于支持分页的hooks，可以使用<code>loadMore</code>函数：</p>
        
        <pre><code>const { posts, loading, loadMore } = usePosts({ first: 10 });

// 加载更多
const handleLoadMore = () => {
  if (posts.length > 0) {
    const lastPost = posts[posts.length - 1];
    loadMore(lastPost.cursor);
  }
};</code></pre>
      </section>
    </section>

    <section class="section" id="examples">
      <h2>5. 组件示例</h2>
      
      <h3>文章列表组件</h3>
      <pre><code>import { usePosts } from '@/api/useGraphQL';

const PostsList = () => {
  const { posts, loading, error, loadMore } = usePosts({ first: 5 });

  if (loading) return &lt;div&gt;加载中...&lt;/div&gt;;
  if (error) return &lt;div&gt;错误: {error.message}&lt;/div&gt;;

  return (
    &lt;div&gt;
      &lt;h2&gt;最新文章&lt;/h2&gt;
      &lt;ul&gt;
        {posts.map(post => (
          &lt;li key={post.id}&gt;
            &lt;h3&gt;{post.title}&lt;/h3&gt;
            &lt;div dangerouslySetInnerHTML={{ __html: post.excerpt }} /&gt;
          &lt;/li&gt;
        ))}
      &lt;/ul&gt;
      &lt;button onClick={() => loadMore()}&gt;加载更多&lt;/button&gt;
    &lt;/div&gt;
  );
};</code></pre>

      <h3>文章详情页组件</h3>
      <pre><code>import { usePost } from '@/api/useGraphQL';

const PostDetail = ({ slug }) => {
  const { post, loading, error } = usePost({ slug });

  if (loading) return &lt;div&gt;加载中...&lt;/div&gt;;
  if (error) return &lt;div&gt;错误: {error.message}&lt;/div&gt;;
  if (!post) return &lt;div&gt;文章不存在&lt;/div&gt;;

  return (
    &lt;article&gt;
      &lt;h1&gt;{post.title}&lt;/h1&gt;
      &lt;div className="metadata"&gt;
        &lt;time&gt;{new Date(post.date).toLocaleDateString()}&lt;/time&gt;
        {post.author && &lt;span&gt;作者: {post.author.node.name}&lt;/span&gt;}
      &lt;/div&gt;
      &lt;div className="content" dangerouslySetInnerHTML={{ __html: post.content }} /&gt;
    &lt;/article&gt;
  );
};</code></pre>

      <h3>导航菜单组件</h3>
      <pre><code>import { useMenu } from '@/api/useGraphQL';

const Navigation = () => {
  const { menuItems, loading, error } = useMenu('PRIMARY');

  if (loading) return &lt;div&gt;加载中...&lt;/div&gt;;
  if (error) return &lt;div&gt;错误: {error.message}&lt;/div&gt;;

  return (
    &lt;nav&gt;
      &lt;ul&gt;
        {menuItems.map(item => (
          &lt;li key={item.id}&gt;
            &lt;a href={item.url} target={item.target || '_self'}&gt;
              {item.label}
            &lt;/a&gt;
          &lt;/li&gt;
        ))}
      &lt;/ul&gt;
    &lt;/nav&gt;
  );
};</code></pre>
    </section>

    <section class="section" id="error-handling">
      <h2>6. 错误处理</h2>
      <p>所有hooks都提供统一的错误处理方式：</p>
      
      <pre><code>const { data, loading, error } = usePost({ slug: 'hello-world' });

if (error) {
  // 处理不同类型的错误
  if (error.networkError) {
    // 网络错误
    console.error('网络错误:', error.networkError);
  } else if (error.graphQLErrors) {
    // GraphQL错误
    error.graphQLErrors.forEach(err => {
      console.error('GraphQL错误:', err.message);
    });
  } else {
    // 其他错误
    console.error('未知错误:', error.message);
  }
}</code></pre>
      
      <div class="note">
        <p>建议在实际应用中使用统一的错误处理组件来处理各种错误情况，提供用户友好的错误信息。</p>
      </div>
    </section>
  </div>
</body>
</html> 