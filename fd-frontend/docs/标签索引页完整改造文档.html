<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>标签索引页完整改造文档</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif; line-height: 1.7; margin: 0; padding: 2rem; background: #fafafa; }
    h1, h2, h3, h4 { color: #333; }
    code, pre { font-family: SFMono-Regular, <PERSON>solas, "Liberation Mono", Menlo, monospace; background: #f4f4f4; padding: 0.2em 0.4em; border-radius: 4px; }
    pre { padding: 1rem; overflow-x: auto; }
    .note { background: #fff8dc; padding: 0.6rem 1rem; border-left: 4px solid #f0ad4e; margin: 1rem 0; }
    .success { background: #dff0d8; padding: 0.6rem 1rem; border-left: 4px solid #3c763d; margin: 1rem 0; }
    .danger { background: #f2dede; padding: 0.6rem 1rem; border-left: 4px solid #a94442; margin: 1rem 0; }
    .info { background: #d9edf7; padding: 0.6rem 1rem; border-left: 4px solid #31708f; margin: 1rem 0; }
    table { width: 100%; border-collapse: collapse; margin: 1rem 0; }
    th, td { border: 1px solid #ddd; padding: 0.5rem; text-align: left; }
    th { background-color: #f5f5f5; }
    .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; margin: 1rem 0; }
    .feature-card { background: white; padding: 1rem; border-radius: 8px; border-left: 4px solid #007acc; }
  </style>
</head>
<body>

<h1>标签索引页完整改造文档</h1>
<p>本文档详细记录了标签索引页 (<code>/innovation</code>) 的完整改造过程，包括后端GraphQL API、前端实现、SEO集成、Banner图片功能以及WebSocket实时推送机制的端到端实现。</p>

<div class="success">
<strong>🎉 改造完成状态</strong><br>
✅ 后端GraphQL API完整实现<br>
✅ 前端页面与组件完整实现<br>
✅ SEO设置完全集成<br>
✅ Banner图片功能完全实现<br>
✅ WebSocket实时推送机制完整实现<br>
✅ 与分类索引页功能完全对等
</div>

<h2>目录</h2>
<ol>
  <li><a href="#overview">项目概览与架构</a></li>
  <li><a href="#backend-api">后端GraphQL API实现</a></li>
  <li><a href="#frontend-impl">前端页面实现</a></li>
  <li><a href="#seo-integration">SEO设置集成</a></li>
  <li><a href="#banner-feature">Banner图片功能</a></li>
  <li><a href="#websocket-push">WebSocket实时推送</a></li>
  <li><a href="#testing-validation">测试与验证</a></li>
  <li><a href="#deployment-notes">部署注意事项</a></li>
</ol>

<h2 id="overview">1. 项目概览与架构</h2>

<h3>1.1 功能对比表</h3>
<table>
  <thead>
    <tr>
      <th>功能模块</th>
      <th>分类索引页</th>
      <th>标签索引页</th>
      <th>实现状态</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>GraphQL API</td>
      <td>categoryIndexPageData</td>
      <td>tagIndexPageData</td>
      <td>✅ 完全对等</td>
    </tr>
    <tr>
      <td>SEO设置</td>
      <td>AI生成标题/描述/JSON-LD</td>
      <td>AI生成标题/描述/JSON-LD</td>
      <td>✅ 完全对等</td>
    </tr>
    <tr>
      <td>Banner图片</td>
      <td>分类Banner支持</td>
      <td>标签Banner支持</td>
      <td>✅ 完全对等</td>
    </tr>
    <tr>
      <td>统计信息</td>
      <td>分类数/文章数/平均值</td>
      <td>标签数/文章数/平均值</td>
      <td>✅ 完全对等</td>
    </tr>
    <tr>
      <td>WebSocket推送</td>
      <td>分类更新推送</td>
      <td>标签更新推送</td>
      <td>✅ 完全对等</td>
    </tr>
    <tr>
      <td>缓存机制</td>
      <td>ISR + 按需重新验证</td>
      <td>ISR + 按需重新验证</td>
      <td>✅ 完全对等</td>
    </tr>
  </tbody>
</table>

<h3>1.2 技术架构</h3>
<div class="feature-grid">
  <div class="feature-card">
    <h4>🔧 后端技术栈</h4>
    <ul>
      <li>WordPress + fd-ai-router插件</li>
      <li>GraphQL API (WPGraphQL)</li>
      <li>自定义字段管理</li>
      <li>WebSocket推送 (fd-pusher)</li>
    </ul>
  </div>
  <div class="feature-card">
    <h4>🎨 前端技术栈</h4>
    <ul>
      <li>Next.js 14 + App Router</li>
      <li>TypeScript + Tailwind CSS</li>
      <li>ISR缓存 + 按需重新验证</li>
      <li>WebSocket实时更新</li>
    </ul>
  </div>
</div>

<h2 id="backend-api">2. 后端GraphQL API实现</h2>

<h3>2.1 GraphQL查询结构</h3>
<pre><code class="language-graphql">query GetTagIndexPageData {
  tagIndexPageData {
    seoSettings {
      aiSeoTitle
      aiSeoDescription
      aiSeoJsonLd
      enabled
      lastUpdated
    }
    tags {
      id
      databaseId
      name
      slug
      description
      count
      bannerImageUrl
      bannerImage {
        sourceUrl
        altText
        mediaDetails {
          width
          height
        }
      }
    }
    statistics {
      totalTags
      totalPosts
      averagePostsPerTag
    }
  }
}</code></pre>

<h3>2.2 后端实现文件</h3>
<div class="info">
<strong>📁 核心文件位置</strong><br>
<code>fd-ai-router/GraphQL/GraphQLController.php</code> - 主要GraphQL控制器<br>
<code>fd-theme/inc/graphql.php</code> - 标签Banner字段注册<br>
<code>fd-theme/inc/tag-fields.php</code> - 标签自定义字段管理
</div>

<h3>2.3 关键实现代码</h3>
<pre><code class="language-php">// fd-ai-router/GraphQL/GraphQLController.php
case 'tagIndexPageData':
    // 获取SEO设置
    $seo_settings = $this->get_page_seo_settings('tag-index');
    
    // 获取所有标签
    $wp_tags = get_terms([
        'taxonomy' => 'post_tag',
        'hide_empty' => false,
        'orderby' => 'name',
        'order' => 'ASC'
    ]);
    
    $tags = [];
    $total_posts = 0;
    
    foreach ($wp_tags as $wp_tag) {
        // 使用WPGraphQL的DataSource来获取正确的Tag对象
        $tag = \WPGraphQL\Data\DataSource::resolve_term_object($wp_tag->term_id, $context);
        if ($tag) {
            $tags[] = $tag;
        }
        $total_posts += $wp_tag->count;
    }
    
    // 计算统计信息
    $total_tags = count($tags);
    $average_posts_per_tag = $total_tags > 0 ? round($total_posts / $total_tags, 1) : 0;
    
    return [
        'seoSettings' => $seo_settings,
        'tags' => $tags,
        'statistics' => [
            'totalTags' => $total_tags,
            'totalPosts' => $total_posts,
            'averagePostsPerTag' => $average_posts_per_tag
        ]
    ];</code></pre>

<h2 id="frontend-impl">3. 前端页面实现</h2>

<h3>3.1 页面结构</h3>
<div class="info">
<strong>📁 前端文件结构</strong><br>
<code>src/app/tag-index/page.tsx</code> - 服务器组件（数据获取 + SEO）<br>
<code>src/components/pages/TagIndexClientPage.tsx</code> - 客户端组件（交互逻辑）
</div>

<h3>3.2 服务器组件实现</h3>
<pre><code class="language-typescript">// src/app/tag-index/page.tsx
import React from 'react';
import { notFound } from 'next/navigation';
import Script from 'next/script';
import { getRoutePrefixes } from '@/lib/route-prefixes';
import TagIndexClientPage from '@/components/pages/TagIndexClientPage';

// 启用ISR缓存，1小时重新验证
export const revalidate = 3600;

/**
 * 获取标签索引页面数据
 */
async function fetchTagIndexData(): Promise<TagIndexData> {
  const query = `
    query GetTagIndexPageData {
      tagIndexPageData {
        seoSettings { aiSeoTitle aiSeoDescription aiSeoJsonLd enabled lastUpdated }
        tags {
          id databaseId name slug description count
          bannerImageUrl
          bannerImage { sourceUrl altText mediaDetails { width height } }
        }
        statistics { totalTags totalPosts averagePostsPerTag }
      }
    }
  `;

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query }),
      next: {
        revalidate: 3600, // ISR缓存
        tags: ['tag-index-page'] // 缓存标签，用于按需重新验证
      }
    }
  );

  const data = await response.json();
  if (data.errors) {
    console.error('GraphQL errors:', data.errors);
    throw new Error('Failed to fetch tag index data');
  }

  return data.data.tagIndexPageData;
}</code></pre>

<h3>3.3 客户端组件核心功能</h3>
<pre><code class="language-typescript">// src/components/pages/TagIndexClientPage.tsx - 核心功能摘要
const TagIndexClientPage: React.FC<TagIndexClientPageProps> = ({
  initialData,
  routePrefixes
}) => {
  // 状态管理
  const [filteredTags, setFilteredTags] = useState<Tag[]>(initialData.tags);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'count' | 'recent'>('name');
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'stats'>('grid');

  // 搜索和排序逻辑
  const handleSearch = useCallback((term: string) => {
    const filtered = initialData.tags.filter(tag =>
      tag.name.toLowerCase().includes(term.toLowerCase())
    );
    setFilteredTags(sortTags(filtered, sortBy));
  }, [initialData.tags, sortBy]);

  // Banner图片渲染
  const renderTagCard = (tag: Tag) => (
    <Link href={getTagUrl(tag.slug)} className="group">
      <div className="bg-white rounded-xl shadow-sm border hover:shadow-lg transition-all duration-300 overflow-hidden h-full">
        {/* 标签Banner图片 */}
        {tag.bannerImageUrl && (
          <div className="relative h-48 overflow-hidden">
            <Image
              src={tag.bannerImageUrl}
              alt={tag.name}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
            />
          </div>
        )}
        {/* 标签内容 */}
        <div className="p-6">
          <div className="flex items-start justify-between mb-3">
            <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
              {tag.name}
            </h3>
            <span className="bg-blue-100 text-blue-800 text-sm font-medium px-2.5 py-1 rounded-full">
              {tag.count}
            </span>
          </div>
          {tag.description && (
            <p className="text-gray-600 text-sm mb-4 line-clamp-2">
              {tag.description}
            </p>
          )}
          <div className="flex items-center justify-between">
            <span className="text-blue-600 text-sm font-medium group-hover:text-blue-700">
              查看文章 →
            </span>
          </div>
        </div>
      </div>
    </Link>
  );
};</code></pre>

<h2 id="seo-integration">4. SEO设置集成</h2>

<h3>4.1 SEO设置管理</h3>
<div class="success">
<strong>✅ SEO功能完全集成</strong><br>
标签索引页的SEO设置已完全集成到fd-ai-router插件的设置页面中，与分类索引页采用相同的管理方式。
</div>

<h3>4.2 SEO设置位置</h3>
<p><strong>WordPress后台路径：</strong> <code>设置 → FD AI Router → 页面SEO设置 → 标签索引页</code></p>

<h3>4.3 SEO字段说明</h3>
<table>
  <thead>
    <tr>
      <th>字段名称</th>
      <th>用途</th>
      <th>默认值</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>AI SEO标题</td>
      <td>页面标题，用于&lt;title&gt;和og:title</td>
      <td>标签索引 - 探索所有文章标签</td>
    </tr>
    <tr>
      <td>AI SEO描述</td>
      <td>页面描述，用于meta description和og:description</td>
      <td>浏览Future Decade的所有标签，快速找到您关注的主题和关键词</td>
    </tr>
    <tr>
      <td>AI SEO JSON-LD</td>
      <td>结构化数据，增强搜索引擎理解</td>
      <td>自动生成CollectionPage结构</td>
    </tr>
    <tr>
      <td>启用状态</td>
      <td>控制是否使用AI生成的SEO内容</td>
      <td>启用</td>
    </tr>
  </tbody>
</table>

<h3>4.4 动态SEO生成</h3>
<pre><code class="language-typescript">// 动态生成SEO元数据
export async function generateMetadata() {
  const data = await fetchTagIndexData();
  const seo = data.seoSettings;

  const metaTitle = seo?.aiSeoTitle || '标签索引 - 探索所有文章标签';
  const metaDescription = seo?.aiSeoDescription ||
    `浏览Future Decade的所有${data.statistics.totalTags}个文章标签，涵盖${data.statistics.totalPosts}篇优质内容。`;

  return {
    title: `${metaTitle} - Future Decade`,
    description: metaDescription,
    keywords: ['文章标签', '内容标签', '主题标签', 'Future Decade'].join(', '),
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      url: 'https://www.futuredecade.com/tag-index',
      images: [`/api/og-image/tag-index?tags=${data.statistics.totalTags}&posts=${data.statistics.totalPosts}`]
    }
  };
}</code></pre>

<h2 id="banner-feature">5. Banner图片功能</h2>

<h3>5.1 Banner功能概述</h3>
<div class="success">
<strong>🎨 Banner图片功能完全实现</strong><br>
标签Banner图片功能与分类Banner功能完全对等，支持在WordPress后台为每个标签设置Banner图片，前端自动显示。
</div>

<h3>5.2 后端Banner字段注册</h3>
<pre><code class="language-php">// fd-theme/inc/graphql.php - 标签Banner字段GraphQL注册
register_graphql_field('Tag', 'bannerImageUrl', [
    'type' => 'String',
    'description' => '标签Banner图片URL',
    'resolve' => function($tag) {
        $banner_id = get_term_meta($tag->databaseId, 'tag_banner', true);
        if ($banner_id) {
            return wp_get_attachment_image_url($banner_id, 'full');
        }
        return null;
    }
]);

register_graphql_field('Tag', 'bannerImage', [
    'type' => 'MediaItem',
    'description' => '标签Banner图片对象',
    'resolve' => function($tag) {
        $banner_id = get_term_meta($tag->databaseId, 'tag_banner', true);
        if ($banner_id) {
            return get_post($banner_id);
        }
        return null;
    }
]);</code></pre>

<h3>5.3 WordPress后台Banner管理</h3>
<pre><code class="language-php">// fd-theme/inc/tag-fields.php - 标签编辑页面Banner字段
function add_tag_banner_field($tag) {
    $banner_id = get_term_meta($tag->term_id, 'tag_banner', true);
    $banner_url = $banner_id ? wp_get_attachment_image_url($banner_id, 'medium') : '';
    ?>
    <tr class="form-field">
        <th scope="row"><label for="tag_banner">标签Banner图片</label></th>
        <td>
            <div id="tag_banner_container">
                <?php if ($banner_url): ?>
                    <img src="<?php echo esc_url($banner_url); ?>" style="max-width: 300px; height: auto;" />
                <?php endif; ?>
            </div>
            <input type="hidden" id="tag_banner" name="tag_banner" value="<?php echo esc_attr($banner_id); ?>" />
            <button type="button" class="button" id="upload_tag_banner">
                <?php echo $banner_id ? '更换Banner图片' : '上传Banner图片'; ?>
            </button>
            <?php if ($banner_id): ?>
                <button type="button" class="button" id="remove_tag_banner">移除Banner图片</button>
            <?php endif; ?>
            <p class="description">为此标签设置Banner图片，将在标签索引页和标签页面中显示。</p>
        </td>
    </tr>
    <?php
}</code></pre>

<h3>5.4 前端Banner显示逻辑</h3>
<pre><code class="language-typescript">// 前端Banner图片渲染逻辑
const renderTagCard = (tag: Tag) => (
  <Link href={getTagUrl(tag.slug)} className="group">
    <div className="bg-white rounded-xl shadow-sm border hover:shadow-lg transition-all duration-300 overflow-hidden h-full">
      {/* 条件渲染Banner图片 */}
      {tag.bannerImageUrl && (
        <div className="relative h-48 overflow-hidden">
          <Image
            src={tag.bannerImageUrl}
            alt={tag.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          {/* Banner遮罩层 */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
        </div>
      )}

      {/* 标签内容区域 */}
      <div className="p-6">
        <div className="flex items-start justify-between mb-3">
          <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
            {tag.name}
          </h3>
          <span className="bg-blue-100 text-blue-800 text-sm font-medium px-2.5 py-1 rounded-full">
            {tag.count}
          </span>
        </div>

        {/* 标签描述 */}
        {tag.description && (
          <p className="text-gray-600 text-sm mb-4 line-clamp-2">
            {tag.description}
          </p>
        )}

        <div className="flex items-center justify-between">
          <span className="text-blue-600 text-sm font-medium group-hover:text-blue-700">
            查看文章 →
          </span>
        </div>
      </div>
    </div>
  </Link>
);</code></pre>

<h3>5.5 Banner图片优化</h3>
<div class="info">
<strong>🎯 性能优化措施</strong><br>
• 使用Next.js Image组件自动优化<br>
• 响应式图片尺寸适配<br>
• 懒加载和渐进式加载<br>
• WebP格式自动转换<br>
• CDN缓存优化
</div>

<h2 id="websocket-push">6. WebSocket实时推送</h2>

<h3>6.1 推送架构概览</h3>
<pre><code class="language-mermaid">sequenceDiagram
    participant Admin as WordPress Admin
    participant Pusher as fd-pusher
    participant Frontend as Next.js Frontend
    participant WS as fd-websocket
    participant Browser as Browser

    Admin->>Pusher: 更新标签/标签banner
    Pusher->>Frontend: POST /api/revalidate (清除tag-index-page缓存)
    Pusher->>WS: POST /push-event (tag:updated事件)

    WS->>Browser: WebSocket emit tag:updated
    Browser->>Browser: WebSocketEventHub检查当前路径

    alt 在标签索引页
        Browser->>Browser: router.refresh()
        Browser->>Frontend: 请求新的页面数据
        Note over Frontend: 缓存已清除，返回最新数据
        Frontend-->>Browser: 返回更新后的标签列表
    end</code></pre>

<h3>6.2 后端推送实现</h3>
<pre><code class="language-php">// fd-pusher/fd-pusher.php - 标签更新推送
function fd_pusher_notify_on_tag_update( $term_id, $tt_id, $taxonomy ) {
    // 只处理标签分类
    if ( $taxonomy !== 'post_tag' ) return;

    // 检查推送密钥
    if ( ! defined( 'FD_WEBSOCKET_PUSH_SECRET' ) ) {
        error_log( 'FD Pusher: 未定义 FD_WEBSOCKET_PUSH_SECRET' );
        return;
    }

    // 准备事件数据
    $term = get_term( $term_id, $taxonomy );
    $event_data = [
        'event' => 'tag:updated',
        'data'  => [
            'termId'   => $term_id,
            'taxonomy' => $taxonomy,
            'slug'     => $term ? $term->slug : '',
            'name'     => $term ? $term->name : '',
        ],
    ];

    // 发送WebSocket推送
    wp_remote_post( 'http://websocket:8080/push-event', [
        'method'   => 'POST',
        'headers'  => [
            'Content-Type'  => 'application/json; charset=utf-8',
            'x-push-secret' => FD_WEBSOCKET_PUSH_SECRET,
        ],
        'body'     => wp_json_encode( $event_data ),
        'blocking' => false,
    ] );

    // 清除前端缓存
    $revalidate_secret = defined( 'REVALIDATE_SECRET' ) ? REVALIDATE_SECRET : '';
    if ( ! empty( $revalidate_secret ) ) {
        wp_remote_post( 'http://frontend:3000/api/revalidate', [
            'method'   => 'POST',
            'headers'  => ['x-revalidate-secret' => $revalidate_secret],
            'body'     => 'tag-index-page', // 标签索引页的缓存标签
            'blocking' => false,
        ] );
    }
}

// 注册钩子
add_action( 'created_post_tag', 'fd_pusher_notify_on_tag_update', 10, 3 );
add_action( 'edited_post_tag', 'fd_pusher_notify_on_tag_update', 10, 3 );
add_action( 'delete_post_tag', 'fd_pusher_notify_on_tag_update', 10, 3 );
add_action( 'updated_term_meta', 'fd_pusher_notify_on_tag_meta_update', 10, 4 );</code></pre>

<h3>6.3 前端WebSocket事件处理</h3>
<pre><code class="language-typescript">// src/contexts/WebSocketEventHub.tsx - 标签更新事件处理
interface TagUpdatedPayload {
  termId?: number;
  taxonomy?: string;
  slug?: string;
  name?: string;
  [key: string]: any;
}

const WebSocketEventHub: React.FC = () => {
  const { socket } = useWebSocket();
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    if (!socket) return;

    // 标签更新事件处理器
    const tagHandler = (payload: TagUpdatedPayload) => {
      console.log('WebSocket event received:', 'tag:updated', payload);

      try {
        // 如果当前在标签索引页，则刷新页面
        if (pathname === '/innovation' || pathname === '/tag-index') {
          console.log(`Tag updated, refreshing tag index page. Payload:`, payload);
          router.refresh();
          return;
        }

        // 如果当前在特定标签页，且该标签被更新，则刷新页面
        if (payload.slug && pathname.includes(`/tag/${payload.slug}`)) {
          console.log(`Current tag page "${payload.slug}" was updated. Refreshing.`);
          router.refresh();
          return;
        }
      } catch (e) {
        console.error('WebSocketEventHub tag handler error', e);
      }
    };

    socket.on('tag:updated', tagHandler);
    return () => socket.off('tag:updated', tagHandler);
  }, [socket, pathname, router]);

  return null;
};</code></pre>

<h3>6.4 推送触发场景</h3>
<table>
  <thead>
    <tr>
      <th>操作类型</th>
      <th>触发钩子</th>
      <th>推送事件</th>
      <th>前端响应</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>创建新标签</td>
      <td>created_post_tag</td>
      <td>tag:updated</td>
      <td>标签索引页刷新</td>
    </tr>
    <tr>
      <td>编辑标签信息</td>
      <td>edited_post_tag</td>
      <td>tag:updated</td>
      <td>标签索引页 + 标签页刷新</td>
    </tr>
    <tr>
      <td>删除标签</td>
      <td>delete_post_tag</td>
      <td>tag:updated</td>
      <td>标签索引页刷新</td>
    </tr>
    <tr>
      <td>更新Banner图片</td>
      <td>updated_term_meta</td>
      <td>tag:updated</td>
      <td>标签索引页 + 标签页刷新</td>
    </tr>
  </tbody>
</table>

<div class="danger">
<strong>⚠️ 认证限制</strong><br>
当前WebSocket推送需要用户登录后才能接收。未登录用户需要等待ISR缓存过期（最多1小时）才能看到更新。<br>
<strong>解决方案：</strong> 可考虑为公开内容创建不需要认证的WebSocket频道。
</div>

<h2 id="testing-validation">7. 测试与验证</h2>

<h3>7.1 GraphQL API测试</h3>
<pre><code class="language-bash"># 测试标签索引页GraphQL API
curl -X POST https://admin.futuredecade.com/graphql \
  -H "Content-Type: application/json" \
  -d '{
    "query": "query GetTagIndexPageData {
      tagIndexPageData {
        tags { id name slug bannerImageUrl }
        statistics { totalTags totalPosts averagePostsPerTag }
      }
    }"
  }'

# 预期返回示例
{
  "data": {
    "tagIndexPageData": {
      "tags": [
        {
          "id": "dGVybTo3Mw==",
          "name": "Z世代面面观",
          "slug": "view-of-generation-z",
          "bannerImageUrl": "https://img.futuredecade.com/s3/fd_series_banner-Z-generation.jpeg"
        },
        {
          "id": "dGVybTo4Mg==",
          "name": "快时尚",
          "slug": "fast-fashion",
          "bannerImageUrl": "https://img.futuredecade.com/s3/fd_series_banner-fast-fashion.jpg"
        }
      ],
      "statistics": {
        "totalTags": 10,
        "totalPosts": 33,
        "averagePostsPerTag": 3.3
      }
    }
  }
}</code></pre>

<h3>7.2 前端页面测试</h3>
<div class="success">
<strong>✅ 页面功能验证</strong><br>
• 访问 <code>https://www.futuredecade.com/innovation</code><br>
• 确认Banner图片正确显示<br>
• 验证搜索和排序功能<br>
• 检查SEO元数据完整性<br>
• 测试响应式布局
</div>

<h3>7.3 WebSocket推送测试</h3>
<pre><code class="language-bash"># 测试步骤
1. 登录前端系统，访问标签索引页
2. 在WordPress后台编辑任意标签
3. 观察前端页面是否自动刷新
4. 检查浏览器控制台的WebSocket日志

# 预期控制台输出
WebSocket event received: tag:updated {termId: 73, taxonomy: "post_tag", slug: "view-of-generation-z", name: "Z世代面面观"}
Tag updated, refreshing tag index page. Payload: {...}
</code></pre>

<h3>7.4 缓存机制测试</h3>
<pre><code class="language-bash"># 测试缓存失效API
curl -X POST https://www.futuredecade.com/api/revalidate \
  -H "x-revalidate-secret: YOUR_REVALIDATE_SECRET" \
  -d "tag-index-page"

# 预期返回
{"revalidated": true, "tag": "tag-index-page"}
</code></pre>

<h2 id="deployment-notes">8. 部署注意事项</h2>

<h3>8.1 环境变量配置</h3>
<table>
  <thead>
    <tr>
      <th>变量名</th>
      <th>位置</th>
      <th>说明</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>FD_WEBSOCKET_PUSH_SECRET</td>
      <td>wp-config.php</td>
      <td>WebSocket推送密钥</td>
    </tr>
    <tr>
      <td>REVALIDATE_SECRET</td>
      <td>wp-config.php + .env</td>
      <td>缓存失效API密钥</td>
    </tr>
    <tr>
      <td>NEXT_PUBLIC_WORDPRESS_API_URL</td>
      <td>前端.env</td>
      <td>GraphQL API端点</td>
    </tr>
    <tr>
      <td>NEXT_PUBLIC_WEBSOCKET_URL</td>
      <td>前端.env</td>
      <td>WebSocket服务地址</td>
    </tr>
  </tbody>
</table>

<h3>8.2 性能优化建议</h3>
<div class="info">
<strong>🚀 性能优化清单</strong><br>
• ISR缓存时间设置为1小时，平衡性能与实时性<br>
• Banner图片使用CDN加速<br>
• GraphQL查询优化，只获取必要字段<br>
• 前端组件懒加载和代码分割<br>
• WebSocket连接复用和错误重连
</div>

<h3>8.3 监控和日志</h3>
<pre><code class="language-php">// 添加到 wp-config.php 用于调试
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);

// 监控WebSocket推送日志
tail -f /var/log/wordpress/debug.log | grep "FD Pusher"

// 监控前端构建和缓存
# Next.js 构建日志会显示ISR页面信息
</code></pre>

<div class="success">
<strong>🎉 改造完成总结</strong><br>
标签索引页已完全改造完成，实现了与分类索引页完全对等的功能：<br>
✅ 后端GraphQL API完整实现<br>
✅ 前端页面与交互功能完整<br>
✅ SEO设置完全集成<br>
✅ Banner图片功能完全实现<br>
✅ WebSocket实时推送完整实现<br>
✅ 缓存机制和性能优化<br>
✅ 测试验证和部署文档
</div>

<hr />
<p>© 2025 Future Decade Project — 本文档生成于 <script>document.write(new Date().toLocaleString())</script></p>

</body>
</html>

