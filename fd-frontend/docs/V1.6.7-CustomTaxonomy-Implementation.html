<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Future Decade - 自定义分类法实现文档 V1.6.7</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3, h4 {
            color: #1a73e8;
            margin-top: 24px;
        }
        h1 {
            font-size: 28px;
            border-bottom: 2px solid #eaecef;
            padding-bottom: 12px;
        }
        h2 {
            font-size: 24px;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 8px;
        }
        code {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            background-color: #f6f8fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 0.9em;
        }
        pre {
            background-color: #f6f8fa;
            border-radius: 6px;
            padding: 16px;
            overflow: auto;
            line-height: 1.45;
        }
        pre code {
            background-color: transparent;
            padding: 0;
        }
        .code-block {
            position: relative;
            margin: 16px 0;
        }
        .code-header {
            background-color: #e1e4e8;
            color: #24292e;
            border-radius: 6px 6px 0 0;
            padding: 8px 16px;
            font-weight: 600;
            font-size: 14px;
        }
        .code-block pre {
            margin-top: 0;
            border-top-left-radius: 0;
            border-top-right-radius: 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
        }
        th, td {
            border: 1px solid #dfe2e5;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f6f8fa;
            font-weight: 600;
        }
        tr:nth-child(even) {
            background-color: #f6f8fa;
        }
        .note {
            background-color: #e1f5fe;
            border-left: 4px solid #03a9f4;
            padding: 12px 16px;
            margin: 16px 0;
            border-radius: 0 4px 4px 0;
        }
        .warning {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 12px 16px;
            margin: 16px 0;
            border-radius: 0 4px 4px 0;
        }
        .success {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 12px 16px;
            margin: 16px 0;
            border-radius: 0 4px 4px 0;
        }
    </style>
</head>
<body>
    <h1>自定义分类法实现 - V1.6.7</h1>
    <p>
        <strong>版本:</strong> 1.6.7<br>
        <strong>更新日期:</strong> <span id="current-date"></span><br>
    </p>

    <div class="note">
        <p><strong>摘要</strong>：本文档详细说明了自定义分类法（Custom Taxonomy）页面的实现，包括分类法索引页 <code>/taxonomy/[taxonomy]</code> 和分类法条目详情页 <code>/taxonomy/[taxonomy]/[slug]</code> 的设计与实现。</p>
    </div>

    <h2>1. 背景与目标</h2>
    <p>WordPress支持创建自定义分类法，除了默认的分类（Category）和标签（Tag）外，还可以创建如"公司"、"产品类型"等自定义分类法。实现自定义分类法页面有以下目标：</p>
    <ul>
        <li>提供统一路由访问各种自定义分类法</li>
        <li>显示分类法条目列表</li>
        <li>显示特定分类法条目的详情和相关文章</li>
        <li>保持与分类和标签页面一致的用户体验</li>
    </ul>

    <h2>2. 实现方案</h2>
    <p>实现分为以下几个部分：</p>
    <ol>
        <li>创建分类法索引页 <code>/taxonomy/[taxonomy]</code></li>
        <li>创建分类法条目详情页 <code>/taxonomy/[taxonomy]/[slug]</code></li>
        <li>创建数据获取Hook：<code>useTaxonomyTerms</code> 和 <code>useTaxonomyTerm</code></li>
        <li>处理GraphQL查询和类型安全问题</li>
    </ol>

    <h2>3. 详细实现</h2>

    <h3>3.1 分类法条目列表Hook</h3>
    <p>创建 <code>useTaxonomyTerms</code> Hook用于获取分类法条目列表：</p>
    <div class="code-block">
        <div class="code-header">fd-frontend/src/hooks/useTaxonomyTerms.ts</div>
<pre><code>import { useQuery } from '@apollo/client';
import { GET_TAXONOMY_TERMS } from '../lib/graphql/queries';

interface TaxonomyTerm {
  __typename: string;
  id: string;
  name: string;
  slug: string;
  uri: string;
  count?: number;
  description?: string;
}

interface TaxonomyTermsData {
  terms: {
    nodes: TaxonomyTerm[];
  };
}

/**
 * 获取分类法条目的Hook
 * @param taxonomy 分类法名称
 * @returns 分类法条目列表数据、加载状态和错误信息
 */
export const useTaxonomyTerms = (taxonomy: string) => {
  // 为自定义分类法添加下划线前缀，并转为大写
  const formatTaxonomy = (tax: string) => {
    // 系统内置分类法不需要下划线前缀
    if (tax.toLowerCase() === 'category' || tax.toLowerCase() === 'post_tag' || tax.toLowerCase() === 'post_format') {
      return tax.toUpperCase();
    }
    // 自定义分类法需要添加下划线前缀
    return `_${tax.toUpperCase()}`;
  };

  const { data, loading, error, refetch } = useQuery<TaxonomyTermsData>(
    GET_TAXONOMY_TERMS,
    {
      variables: { taxonomy: formatTaxonomy(taxonomy) },
      skip: !taxonomy,
    }
  );

  return {
    terms: data?.terms?.nodes || [],
    loading,
    error,
    refetch,
  };
};</code></pre>
    </div>

    <h3>3.2 分类法条目详情Hook</h3>
    <p>创建 <code>useTaxonomyTerm</code> Hook用于获取分类法条目详情和相关文章：</p>
    <div class="code-block">
        <div class="code-header">fd-frontend/src/hooks/useTaxonomyTerm.ts</div>
<pre><code>import { useQuery } from '@apollo/client';
import { GET_TAXONOMY_TERM, GET_POSTS_BY_TAX_QUERY_SLUG } from '../lib/graphql/queries';

interface TaxonomyTermDetail {
  __typename: string;
  id: string;
  name: string;
  slug: string;
  description?: string;
  uri: string;
  count?: number;
  children?: {
    nodes: {
      id: string;
      name: string;
      slug: string;
      uri: string;
    }[];
  };
}

interface Post {
  id: string;
  title: string;
  date: string;
  slug: string;
  shortUuid: string;
  excerpt?: string;
  featuredImage?: {
    node: {
      sourceUrl: string;
      altText?: string;
    };
  };
  categories?: {
    nodes: {
      id: string;
      name: string;
      slug: string;
    }[];
  };
}

interface TaxonomyTermData {
  terms: {
    nodes: TaxonomyTermDetail[];
  };
}

interface PostsData {
  posts: {
    nodes: Post[];
  };
}

/**
 * 获取分类法条目详情的Hook
 * @param taxonomy 分类法名称
 * @param slug 条目别名
 * @returns 分类法条目详情数据、相关文章、加载状态和错误信息
 */
export const useTaxonomyTerm = (taxonomy: string, slug: string) => {
  // 为自定义分类法添加下划线前缀，并转为大写
  const formatTaxonomy = (tax: string) => {
    // 系统内置分类法不需要下划线前缀
    if (tax.toLowerCase() === 'category' || tax.toLowerCase() === 'post_tag' || tax.toLowerCase() === 'post_format') {
      return tax.toUpperCase();
    }
    // 自定义分类法需要添加下划线前缀
    return `_${tax.toUpperCase()}`;
  };

  // 获取分类法条目详情
  const { 
    data: termData, 
    loading: termLoading, 
    error: termError 
  } = useQuery<TaxonomyTermData>(
    GET_TAXONOMY_TERM,
    {
      variables: { 
        taxonomy: formatTaxonomy(taxonomy),
        slug: [slug]
      },
      skip: !taxonomy || !slug,
    }
  );

  // 获取该分类法条目下的文章
  const { 
    data: postsData, 
    loading: postsLoading, 
    error: postsError,
    fetchMore 
  } = useQuery<PostsData>(
    GET_POSTS_BY_TAX_QUERY_SLUG,
    {
      variables: { 
        taxonomy: formatTaxonomy(taxonomy),
        slugs: [slug],
        first: 10
      },
      skip: !taxonomy || !slug,
    }
  );

  // 加载更多文章
  const loadMorePosts = () => {
    if (fetchMore && postsData?.posts?.nodes && postsData.posts.nodes.length > 0) {
      fetchMore({
        variables: {
          first: 10,
          after: postsData.posts.nodes[postsData.posts.nodes.length - 1].id
        },
        updateQuery: (prev: PostsData, { fetchMoreResult }: { fetchMoreResult?: PostsData }) => {
          if (!fetchMoreResult) return prev;
          return {
            posts: {
              ...fetchMoreResult.posts,
              nodes: [
                ...prev.posts.nodes,
                ...fetchMoreResult.posts.nodes
              ]
            }
          };
        }
      });
    }
  };

  // 获取第一个匹配的条目（如果存在）
  const term = termData?.terms?.nodes && termData.terms.nodes.length > 0 ? termData.terms.nodes[0] : undefined;

  return {
    term,
    posts: postsData?.posts?.nodes || [],
    loading: termLoading || postsLoading,
    error: termError || postsError,
    loadMorePosts
  };
};</code></pre>
    </div>

    <h3>3.3 更新GraphQL查询</h3>
    <p>确保GraphQL查询正确处理分类法类型：</p>
    <div class="code-block">
        <div class="code-header">fd-frontend/src/lib/graphql/queries.ts（部分更新）</div>
<pre><code>// 获取自定义分类法术语详情
export const GET_TAXONOMY_TERM = gql`
  query GetTaxonomyTerm($taxonomy: TaxonomyEnum!, $slug: [String]!) {
    terms(where: { taxonomies: [$taxonomy], slug: $slug }) {
      nodes {
        __typename
        id
        name
        slug
        description
        uri
        ... on Category {
          count
          children {
            nodes {
              id
              name
              slug
              uri
            }
          }
        }
        ... on Tag {
          count
        }
      }
    }
  }
`;</code></pre>
    </div>

    <h3>3.4 分类法索引页组件</h3>
    <p>创建分类法索引页组件，显示特定分类法的所有条目：</p>
    <div class="code-block">
        <div class="code-header">fd-frontend/src/app/taxonomy/[taxonomy]/page.tsx</div>
<pre><code>'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useTaxonomy } from '@/hooks';
import { useTaxonomyTerms } from '@/hooks/useTaxonomyTerms';
import { useRoutePrefixes } from '@/hooks';

/**
 * 自定义分类法页面
 * 展示特定分类法下的所有条目
 */
export default function TaxonomyPage({ params }: { params: { taxonomy: string } }) {
  const { taxonomy } = params;
  const { prefixes } = useRoutePrefixes();
  const [searchTerm, setSearchTerm] = useState('');
  
  // 获取分类法详情
  const { 
    taxonomy: taxonomyInfo, 
    loading: taxonomyLoading, 
    error: taxonomyError 
  } = useTaxonomy(taxonomy);
  
  // 获取分类法条目
  const { 
    terms, 
    loading: termsLoading, 
    error: termsError 
  } = useTaxonomyTerms(taxonomy);
  
  // 过滤条目
  const filteredTerms = terms.filter(
    (term: any) => term.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  // 加载中状态
  if (taxonomyLoading || termsLoading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <h1 className="text-2xl font-bold mb-6">分类法详情</h1>
        <div className="flex justify-center">
          <div className="animate-pulse text-lg">加载中...</div>
        </div>
      </div>
    );
  }
  
  // 错误状态
  if (taxonomyError || termsError) {
    return (
      <div className="container mx-auto py-8 px-4">
        <h1 className="text-2xl font-bold mb-6">分类法详情</h1>
        <div className="bg-red-50 text-red-500 p-4 rounded-lg">
          <h2 className="text-lg font-medium mb-2">加载出错</h2>
          <p>{taxonomyError?.message || termsError?.message}</p>
        </div>
      </div>
    );
  }
  
  // 获取分类法条目链接
  const getTermUrl = (termSlug: string) => {
    return `/taxonomy/${taxonomy}/${termSlug}`;
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">{taxonomyInfo?.label || taxonomy}</h1>
        {taxonomyInfo?.description && (
          <div 
            className="mt-2 text-gray-600"
            dangerouslySetInnerHTML={{ __html: taxonomyInfo.description }}
          />
        )}
        <div className="mt-2 text-sm text-gray-500">
          共 {terms.length} 个条目
        </div>
      </div>
      
      {/* 搜索框 */}
      <div className="mb-6">
        <div className="relative">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="搜索条目..."
            className="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          {searchTerm && (
            <button 
              onClick={() => setSearchTerm('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          )}
        </div>
      </div>
      
      {/* 条目列表 */}
      {filteredTerms.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">该分类法下暂无条目或未找到匹配的条目</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTerms.map((term: any) => (
            <div 
              key={term.id}
              className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
            >
              <Link href={getTermUrl(term.slug)}>
                <h2 className="text-xl font-bold mb-2 hover:text-blue-600 transition-colors">
                  {term.name}
                </h2>
              </Link>
              {term.__typename === 'Category' && (
                <>
                  {term.description && (
                    <div 
                      className="text-gray-600 mb-4"
                      dangerouslySetInnerHTML={{ __html: term.description }}
                    />
                  )}
                  <div className="text-sm text-gray-500">
                    {term.count} 篇文章
                  </div>
                </>
              )}
              {term.__typename === 'Tag' && (
                <div className="text-sm text-gray-500">
                  {term.count} 篇文章
                </div>
              )}
              <div className="mt-4">
                <Link 
                  href={getTermUrl(term.slug)}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  浏览条目 →
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* 返回链接 */}
      <div className="mt-8">
        <Link 
          href="/"
          className="text-blue-600 hover:text-blue-800 flex items-center"
        >
          ← 返回首页
        </Link>
      </div>
    </div>
  );
}</code></pre>
    </div>

    <h3>3.5 分类法条目详情页组件</h3>
    <p>创建分类法条目详情页组件，显示特定条目详情和相关文章：</p>
    <div class="code-block">
        <div class="code-header">fd-frontend/src/app/taxonomy/[taxonomy]/[slug]/page.tsx</div>
<pre><code>'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useTaxonomy } from '@/hooks';
import { useTaxonomyTerm } from '@/hooks/useTaxonomyTerm';
import { useRoutePrefixes } from '@/hooks';
import { buildPostUrl } from '@/utils/url-builder';

/**
 * 分类法条目详情页面
 * 展示特定分类法下特定条目的详情和相关文章
 */
export default function TaxonomyTermPage({ params }: { params: { taxonomy: string; slug: string } }) {
  const { taxonomy, slug } = params;
  const { prefixes } = useRoutePrefixes();
  const [searchTerm, setSearchTerm] = useState('');
  
  // 获取分类法详情
  const { 
    taxonomy: taxonomyInfo
  } = useTaxonomy(taxonomy);
  
  // 获取分类法条目详情和相关文章
  const { 
    term,
    posts,
    loading,
    error,
    loadMorePosts
  } = useTaxonomyTerm(taxonomy, slug);
  
  // 过滤文章
  const filteredPosts = posts.filter(
    (post: any) => post.title.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  // 加载中状态
  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <h1 className="text-2xl font-bold mb-6">条目详情</h1>
        <div className="flex justify-center">
          <div className="animate-pulse text-lg">加载中...</div>
        </div>
      </div>
    );
  }
  
  // 错误状态
  if (error) {
    return (
      <div className="container mx-auto py-8 px-4">
        <h1 className="text-2xl font-bold mb-6">条目详情</h1>
        <div className="bg-red-50 text-red-500 p-4 rounded-lg">
          <h2 className="text-lg font-medium mb-2">加载出错</h2>
          <p>{error.message}</p>
        </div>
      </div>
    );
  }
  
  // 未找到条目
  if (!term) {
    return (
      <div className="container mx-auto py-8 px-4">
        <h1 className="text-2xl font-bold mb-6">条目详情</h1>
        <div className="bg-yellow-50 text-yellow-700 p-4 rounded-lg">
          <h2 className="text-lg font-medium mb-2">未找到条目</h2>
          <p>找不到指定的分类法条目 "{slug}"</p>
        </div>
        <div className="mt-6">
          <Link href={`/taxonomy/${taxonomy}`} className="text-blue-600 hover:text-blue-800">
            返回 {taxonomyInfo?.label || taxonomy} 索引
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      {/* 条目标题和描述 */}
      <div className="mb-8">
        <div className="flex items-center gap-2 text-sm text-gray-500 mb-2">
          <Link href={`/taxonomy/${taxonomy}`} className="hover:text-blue-600">
            {taxonomyInfo?.label || taxonomy}
          </Link>
          <span>›</span>
          <span>{term.name}</span>
        </div>
        
        <h1 className="text-3xl font-bold">{term.name}</h1>
        
        {term.description && (
          <div 
            className="mt-3 text-gray-600"
            dangerouslySetInnerHTML={{ __html: term.description }}
          />
        )}
        
        <div className="mt-3 text-sm text-gray-500">
          {term.count !== undefined && (
            <span>共 {term.count} 篇文章</span>
          )}
        </div>
      </div>

      {/* 搜索框 */}
      <div className="mb-6">
        <div className="relative">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="搜索文章..."
            className="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          {searchTerm && (
            <button 
              onClick={() => setSearchTerm('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          )}
        </div>
      </div>
      
      {/* 文章列表 */}
      {filteredPosts.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">该条目下暂无文章或未找到匹配的文章</p>
        </div>
      ) : (
        <div className="space-y-6">
          {filteredPosts.map((post: any) => (
            <div 
              key={post.id}
              className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
            >
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                {post.featuredImage?.node?.sourceUrl && (
                  <div className="md:col-span-1">
                    <Link href={buildPostUrl(post.shortUuid, post.slug, prefixes)}>
                      <div className="relative h-48 w-full rounded-md overflow-hidden">
                        <Image
                          src={post.featuredImage.node.sourceUrl}
                          alt={post.featuredImage.node.altText || post.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                    </Link>
                  </div>
                )}
                
                <div className={post.featuredImage?.node?.sourceUrl ? "md:col-span-3" : "md:col-span-4"}>
                  <Link href={buildPostUrl(post.shortUuid, post.slug, prefixes)}>
                    <h2 className="text-xl font-bold mb-2 hover:text-blue-600 transition-colors">
                      {post.title}
                    </h2>
                  </Link>
                  
                  <div className="text-sm text-gray-500 mb-3">
                    {new Date(post.date).toLocaleDateString('zh-CN')}
                    {post.categories?.nodes?.length > 0 && (
                      <>
                        <span className="mx-2">•</span>
                        <span>分类: </span>
                        {post.categories.nodes.map((cat: any, index: number) => (
                          <span key={cat.id}>
                            <Link 
                              href={`/category/${cat.slug}`}
                              className="hover:text-blue-600"
                            >
                              {cat.name}
                            </Link>
                            {index < post.categories.nodes.length - 1 && ", "}
                          </span>
                        ))}
                      </>
                    )}
                  </div>
                  
                  {post.excerpt && (
                    <div 
                      className="text-gray-600 mb-4"
                      dangerouslySetInnerHTML={{ __html: post.excerpt }}
                    />
                  )}
                  
                  <div className="flex justify-between items-center">
                    <Link 
                      href={buildPostUrl(post.shortUuid, post.slug, prefixes)}
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      阅读全文 →
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* 加载更多按钮 */}
      {posts.length > 0 && posts.length >= 10 && (
        <div className="mt-8 text-center">
          <button
            onClick={loadMorePosts}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            加载更多
          </button>
        </div>
      )}
      
      {/* 返回链接 */}
      <div className="mt-8">
        <Link 
          href={`/taxonomy/${taxonomy}`}
          className="text-blue-600 hover:text-blue-800 flex items-center"
        >
          ← 返回{taxonomyInfo?.label || taxonomy}索引
        </Link>
      </div>
    </div>
  );
}</code></pre>
    </div>

    <h3>3.6 导出API Hooks</h3>
    <p>在API导出文件中导出新的Hooks：</p>
    <div class="code-block">
        <div class="code-header">fd-frontend/src/api/useGraphQL.ts（部分更新）</div>
<pre><code>// 导出分类法相关Hook
export { useTaxonomyTerms } from '../hooks/useTaxonomyTerms';
export { useTaxonomyTerm } from '../hooks/useTaxonomyTerm';</code></pre>
    </div>

    <h2>4. 关键实现技术点</h2>

    <h3>4.1 GraphQL查询处理</h3>
    <div class="note">
        <p><strong>注意</strong>：WordPress GraphQL API中自定义分类法的枚举值需要添加下划线前缀。例如：分类法"company"的枚举值应为"_COMPANY"，而不是"COMPANY"。</p>
    </div>

    <p>为了处理这个问题，我们在Hook中添加了一个<code>formatTaxonomy</code>辅助函数：</p>
    <div class="code-block">
        <div class="code-header">分类法枚举值处理</div>
<pre><code>// 为自定义分类法添加下划线前缀，并转为大写
const formatTaxonomy = (tax: string) => {
  // 系统内置分类法不需要下划线前缀
  if (tax.toLowerCase() === 'category' || tax.toLowerCase() === 'post_tag' || tax.toLowerCase() === 'post_format') {
    return tax.toUpperCase();
  }
  // 自定义分类法需要添加下划线前缀
  return `_${tax.toUpperCase()}`;
};</code></pre>
    </div>

    <h3>4.2 文章链接处理</h3>
    <p>分类法条目详情页中的文章链接使用与Category和Tag页面相同的URL构建方式，确保一致性：</p>
    <div class="code-block">
        <div class="code-header">使用统一的URL构建函数</div>
<pre><code>import { buildPostUrl } from '@/utils/url-builder';

// 在文章链接中使用
<Link href={buildPostUrl(post.shortUuid, post.slug, prefixes)}>
  <h2 className="text-xl font-bold mb-2 hover:text-blue-600 transition-colors">
    {post.title}
  </h2>
</Link></code></pre>
    </div>

    <h3>4.3 类型安全处理</h3>
    <p>在加载更多文章的函数中，我们添加了额外的类型安全检查：</p>
    <div class="code-block">
        <div class="code-header">类型安全处理示例</div>
<pre><code>const loadMorePosts = () => {
  if (fetchMore && postsData?.posts?.nodes && postsData.posts.nodes.length > 0) {
    fetchMore({
      variables: {
        first: 10,
        after: postsData.posts.nodes[postsData.posts.nodes.length - 1].id
      },
      updateQuery: (prev: PostsData, { fetchMoreResult }: { fetchMoreResult?: PostsData }) => {
        if (!fetchMoreResult) return prev;
        return {
          posts: {
            ...fetchMoreResult.posts,
            nodes: [
              ...prev.posts.nodes,
              ...fetchMoreResult.posts.nodes
            ]
          }
        };
      }
    });
  }
};</code></pre>
    </div>

    <h2>5. 页面结构与用户体验</h2>

    <h3>5.1 页面组织</h3>
    <p>分类法页面采用了以下结构：</p>
    <ul>
        <li><strong>分类法索引页</strong>（<code>/taxonomy/[taxonomy]</code>）：显示特定分类法的所有条目</li>
        <li><strong>分类法条目详情页</strong>（<code>/taxonomy/[taxonomy]/[slug]</code>）：显示特定条目详情和相关文章</li>
    </ul>

    <h3>5.2 响应式设计</h3>
    <p>页面设计遵循移动优先原则，使用Tailwind CSS实现响应式布局：</p>
    <ul>
        <li><strong>移动设备</strong>：单列布局</li>
        <li><strong>平板设备</strong>：两列网格（md断点）</li>
        <li><strong>桌面设备</strong>：三列网格（lg断点）</li>
    </ul>

    <h3>5.3 加载状态和错误处理</h3>
    <p>页面实现了完整的加载状态和错误处理：</p>
    <ul>
        <li><strong>加载状态</strong>：显示骨架屏或加载指示器</li>
        <li><strong>错误状态</strong>：显示友好的错误消息</li>
        <li><strong>未找到状态</strong>：显示"未找到"消息和返回链接</li>
    </ul>

    <h3>5.4 搜索过滤功能</h3>
    <p>在分类法条目列表和相关文章列表中，都实现了客户端搜索过滤功能，无需额外的API请求。</p>

    <h2>6. 使用示例</h2>

    <div class="code-block">
        <div class="code-header">访问公司分类法页面</div>
<pre><code>// 查看"公司"分类法的所有条目
https://example.com/taxonomy/company

// 查看"公司"分类法中"苹果"条目的详情和相关文章
https://example.com/taxonomy/company/apple</code></pre>
    </div>

    <h2>7. 注意事项</h2>
    <div class="warning">
        <p><strong>重要</strong>：使用分类法页面时，需要确保以下几点：</p>
        <ol>
            <li>分类法名称必须与WordPress后台定义的分类法名称匹配（区分大小写）</li>
            <li>确保GraphQL API已启用并可访问相应的分类法数据</li>
            <li>自定义分类法需要在WordPress中正确配置并显示在GraphQL架构中</li>
        </ol>
    </div>

    <script>
        // 获取当前日期并显示在文档中
        document.getElementById('current-date').textContent = new Date().toISOString().split('T')[0];
    </script>
</body>
</html> 