<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>"收藏"功能实现文档</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; line-height: 1.6; color: #333; max-width: 1200px; margin: 20px auto; padding: 0 20px; }
        h1, h2, h3 { color: #222; }
        h1 { border-bottom: 2px solid #eee; padding-bottom: 10px; }
        h2 { margin-top: 40px; border-bottom: 1px solid #eee; padding-bottom: 8px;}
        pre { background-color: #f6f8fa; padding: 16px; border-radius: 6px; overflow-x: auto; white-space: pre-wrap; word-wrap: break-word; }
        code { font-family: "SFMono-Regular", <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, <PERSON><PERSON>, monospace; font-size: 14px; }
        .summary { padding: 15px; margin: 20px 0; border-radius: 6px; background-color: #eef8ff; border-left: 4px solid #0969da; }
        .file-path { font-size: 0.9em; font-family: monospace; color: #57606a; margin-bottom: 10px; }
    </style>
</head>
<body>
    <h1>"收藏"功能实现文档</h1>
    <div class="summary">
        <h3>整体架构</h3>
        <p>"收藏"(Bookmark)功能作为"点赞"(Like)和"推荐"(Recommend)功能的完整克隆而实现。其后端逻辑通过在<code>fd-member</code>插件中添加新数据表、核心逻辑文件、统计缓存文件、GraphQL接口和后台管理页面来构建。前端则通过在<code>fd-frontend</code>中添加新的React组件、自定义Hook、GraphQL变更以及更新文章模板来完成。此实现确保了与现有功能在逻辑、命名和缓存机制上的完全对等。</p>
    </div>

    <h1>后端实现 (fd-member 插件)</h1>

    <h2>1. 插件集成: index.php</h2>
    <div class="file-path">fd-member/index.php</div>
    <pre><code class="language-php">
// ... (在 fd_member_init 函数中)
// 加载收藏相关功能
if (get_option('fd_member_enable_bookmarks', true)) {
    require_once FD_MEMBER_DIR . 'includes/bookmarks/bookmark-core.php';
    require_once FD_MEMBER_DIR . 'includes/bookmarks/bookmark-stats.php';
}

// ... (在 admin_init 中)
// 加载收藏管理界面
if (get_option('fd_member_enable_bookmarks', true)) {
    require_once FD_MEMBER_DIR . 'admin/bookmarks-manager.php';
}

// ... (在 graphql_init 中)
// 加载收藏系统GraphQL支持
if (get_option('fd_member_enable_bookmarks', true)) {
    require_once FD_MEMBER_DIR . 'includes/bookmarks/bookmark-graphql.php';
}

// ... (在插件激活钩子 fd_member_activate 中)
// 创建收藏表
if (get_option('fd_member_enable_bookmarks', true)) {
    if (file_exists(FD_MEMBER_DIR . 'includes/bookmarks/bookmark-core.php')) {
        require_once FD_MEMBER_DIR . 'includes/bookmarks/bookmark-core.php';
        fd_member_bookmarks_create_table();
    }
}
</code></pre>

    <h2>2. 核心逻辑与后台列表集成: bookmark-core.php</h2>
    <div class="file-path">fd-member/includes/bookmarks/bookmark-core.php</div>
    <pre><code class="language-php">
&lt;?php
// ... (文件头部)

/**
 * 创建收藏表 (wp_bookmarks)
 */
function fd_member_bookmarks_create_table() {
    // ... (与点赞/推荐类似的表结构)
    /*
    CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        post_id bigint(20) NOT NULL,
        status tinyint(1) NOT NULL DEFAULT 1,
        created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY user_post (user_id, post_id),
        KEY post_id (post_id),
        KEY user_id (user_id)
    )
    */
}

/**
 * 添加/更新用户收藏状态
 */
function fd_member_set_bookmark_status($user_id, $post_id, $status = 1) {
    // ... (核心逻辑：验证参数、检查记录、更新或插入数据)
    // 触发 'fd_member_bookmark_added' 或 'fd_member_bookmark_updated' 动作
}

// ... (fd_member_bookmark_post, fd_member_unbookmark_post, fd_member_user_has_bookmarked 等辅助函数)

/**
 * 为支持的文章类型列表添加"收藏数"列
 */
function fd_member_bookmarks_add_posts_columns($columns) {
    $columns['bookmarks_count'] = '收藏数';
    return $columns;
}

/**
 * 显示"收藏数"列的内容
 */
function fd_member_bookmarks_posts_custom_column($column, $post_id) {
    if ($column == 'bookmarks_count') {
        echo esc_html(fd_member_get_post_bookmarks_count($post_id));
    }
}

/**
 * 使"收藏数"列可排序
 */
function fd_member_bookmarks_sortable_columns($columns) {
    $columns['bookmarks_count'] = 'bookmarks_count';
    return $columns;
}

/**
 * 处理"收藏数"列的排序逻辑 (基于 post_meta)
 */
function fd_member_bookmarks_orderby($query) {
    if (!is_admin() || !$query->is_main_query()) {
        return;
    }

    if ($query->get('orderby') === 'bookmarks_count') {
        $query->set('meta_key', '_fd_bookmarks_count');
        $query->set('orderby', 'meta_value_num');
    }
}

/**
 * 将后台列表功能挂载到所有公开的文章类型
 */
function fd_member_bookmarks_integrate_with_post_types() {
    $post_types = get_post_types(['public' => true], 'names');
    foreach ($post_types as $post_type) {
        add_filter("manage_{$post_type}_posts_columns", 'fd_member_bookmarks_add_posts_columns');
        add_action("manage_{$post_type}_posts_custom_column", 'fd_member_bookmarks_posts_custom_column', 10, 2);
        add_filter("manage_edit-{$post_type}_sortable_columns", 'fd_member_bookmarks_sortable_columns');
    }
}

add_action('admin_init', 'fd_member_bookmarks_integrate_with_post_types');
add_action('pre_get_posts', 'fd_member_bookmarks_orderby');
</code></pre>

    <h2>3. 统计与缓存: bookmark-stats.php</h2>
    <div class="file-path">fd-member/includes/bookmarks/bookmark-stats.php</div>
    <pre><code class="language-php">
&lt;?php
// ...

/**
 * 获取文章收藏数（优先使用缓存）
 */
function fd_member_get_post_bookmarks_count($post_id, $refresh = false) {
    // ... 逻辑：先尝试从 post_meta '_fd_bookmarks_count' 获取缓存
    // 如果缓存不存在，则调用 fd_member_update_post_bookmarks_count_cache
}

/**
 * 更新文章收藏数缓存
 */
function fd_member_update_post_bookmarks_count_cache($post_id) {
    // ... 逻辑：调用 fd_member_calculate_bookmarks_count 计算总数，并用 update_post_meta 更新
}

/**
 * 从数据库计算文章的实际收藏数
 */
function fd_member_calculate_bookmarks_count($post_id) {
    // ... 逻辑：SELECT COUNT(*) FROM wp_bookmarks WHERE post_id = %d AND status = 1
}

/**
 * 当收藏状态变更时，更新缓存
 */
function fd_member_update_cache_on_bookmark_change($user_id, $post_id, $status) {
    fd_member_update_post_bookmarks_count_cache($post_id);
}
add_action('fd_member_bookmark_added', 'fd_member_update_cache_on_bookmark_change', 10, 3);
add_action('fd_member_bookmark_updated', 'fd_member_update_cache_on_bookmark_change', 10, 3);
</code></pre>

    <h2>4. GraphQL 接口: bookmark-graphql.php</h2>
    <div class="file-path">fd-member/includes/bookmarks/bookmark-graphql.php</div>
    <pre><code class="language-php">
&lt;?php
// ...

function fd_member_bookmarks_register_graphql_fields() {
    // 注册 BookmarkStatus 类型
    register_graphql_object_type('BookmarkStatus', [
        'fields' => [
            'postId' => [ 'type' => 'Int' ],
            'bookmarksCount' => [ 'type' => 'Int' ],
            'userHasBookmarked' => [ 'type' => 'Boolean' ],
        ]
    ]);

    // 注册 bookmarkPost 变更
    register_graphql_mutation('bookmarkPost', [
        // ... (输入/输出字段)
        'mutateAndGetPayload' => function($input) {
            // ... (权限检查，调用 fd_member_bookmark_post)
            // 返回 'success', 'message', 'post_id'
        }
    ]);
    
    // 注册 unbookmarkPost 变更
    register_graphql_mutation('unbookmarkPost', [
        // ... (与 bookmarkPost 类似)
    ]);

    // 获取所有公开的 post_types
    $post_types = get_post_types(['show_in_graphql' => true], 'objects');

    // 循环为每个类型及 ContentNode 接口添加字段
    // register_graphql_field(..., 'userHasBookmarked', ...)
    // register_graphql_field(..., 'bookmarksCount', ...)
}
add_action('graphql_register_types', 'fd_member_bookmarks_register_graphql_fields');
</code></pre>
    
    <h1>前端实现 (fd-frontend App)</h1>

    <h2>1. GraphQL 变更与片段</h2>
    <div class="file-path">fd-frontend/src/lib/graphql/mutations.ts</div>
    <pre><code class="language-typescript">
// ...
// 收藏文章
export const BOOKMARK_POST = gql`
  mutation BookmarkPost($postId: Int!) {
    bookmarkPost(input: { postId: $postId }) {
      success
      bookmarkStatus {
        bookmarksCount
        userHasBookmarked
      }
    }
  }
`;

// 取消收藏文章
export const UNBOOKMARK_POST = gql`
  mutation UnbookmarkPost($postId: Int!) {
    unbookmarkPost(input: { postId: $postId }) {
      success
      bookmarkStatus {
        bookmarksCount
        userHasBookmarked
      }
    }
  }
`;
</code></pre>
    <div class="file-path">fd-frontend/src/lib/graphql/fragments.ts</div>
    <pre><code class="language-typescript">
// ...
export const CUSTOM_POST_FRAGMENT = gql`
  fragment CustomPostFields on ContentNode {
    // ...
    recommendsCount
    userHasRecommended
    bookmarksCount
    userHasBookmarked
    // ...
  }
`;
</code></pre>

    <h2>2. 核心 Hook: useBookmarkAction.ts</h2>
    <div class="file-path">fd-frontend/src/hooks/useBookmarkAction.ts</div>
    <pre><code class="language-typescript">
import { useMutation, gql, ApolloCache } from '@apollo/client';
// ... (其他 import)

// ... (Payload 类型接口定义)

export const useBookmarkAction = ({ /* ... options ... */ }) => {
  // ... (获取 authContext)

  // ... (getTypeName, cacheId 等辅助函数)

  const [bookmarkPost, { loading: bookmarkLoading }] = useMutation(BOOKMARK_POST, {
    update: (cache: ApolloCache&lt;BookmarkPostPayload&gt;, { data }) => {
      if (data?.bookmarkPost?.success) {
        const { bookmarksCount, userHasBookmarked } = data.bookmarkPost.bookmarkStatus;
        cache.writeFragment({
          id: cacheId,
          fragment: gql`
            fragment UpdateBookmarkStatus on ${getTypeName()} {
              bookmarksCount
              userHasBookmarked
            }
          `,
          data: { bookmarksCount, userHasBookmarked },
        });
      }
    },
    optimisticResponse: { /* ... */ }
  });

  const [unbookmarkPost, { loading: unbookmarkLoading }] = useMutation(UNBOOKMARK_POST, {
     // ... (与 bookmarkPost 类似的 update 和 optimisticResponse)
  });

  const handleBookmark = () => { /* ... 调用 bookmarkPost ... */ };
  const handleUnbookmark = () => { /* ... 调用 unbookmarkPost ... */ };
  
  return { handleBookmark, handleUnbookmark, loading: bookmarkLoading || unbookmarkLoading };
};
</code></pre>

    <h2>3. UI 组件: BookmarkButton.tsx</h2>
    <div class="file-path">fd-frontend/src/components/post/BookmarkButton.tsx</div>
    <pre><code class="language-typescript">
'use client';

import React, { useContext } from 'react';
import { useQuery, gql } from '@apollo/client';
import { useBookmarkAction } from '@/hooks/useBookmarkAction';
import { AuthContext } from '@/contexts/AuthContext';
import { Bookmark } from 'lucide-react'; // 使用书签图标

// ... (接口和动态查询构建函数)

export const BookmarkButton: React.FC&lt;BookmarkButtonProps&gt; = ({ /* ... props ... */ }) => {
  // ... (根据 contentType 选择或构建 query)
  
  const { data } = useQuery(query, { /* ... */ });
  
  const result = isCustomPost ? data?.contentNode : data?.post;
  const isBookmarked = result?.userHasBookmarked ?? initialIsBookmarked;
  const bookmarksCount = result?.bookmarksCount ?? initialBookmarksCount;

  const { handleBookmark, handleUnbookmark, loading } = useBookmarkAction({ /* ... */ });
  
  const handleClick = () => { /* ... */ };

  return (
    &lt;button /* ... */ &gt;
      &lt;Bookmark
        className={`w-5 h-5 mr-1 ${isBookmarked ? 'text-blue-500' : 'text-gray-500'}`}
        fill={isBookmarked ? 'currentColor' : 'none'}
      /&gt;
      &lt;span&gt;{bookmarksCount}&lt;/span&gt;
    &lt;/button&gt;
  );
};
</code></pre>

    <h2>4. 页面集成</h2>
    <p><code>BookmarkButton</code> 组件被添加到所有文章模板中 (<code>StandardTemplate.tsx</code>, <code>VideoTemplate.tsx</code>, etc.) 以及自定义文章类型模板 (<code>/app/post-type/[type]/[uuid]/[slug]/page.tsx</code>)。</p>
    <div class="file-path">fd-frontend/src/components/templates/StandardTemplate.tsx</div>
    <pre><code class="language-typescript">
// ...
export interface PostType {
  // ...
  recommendsCount?: number;
  userHasRecommended?: boolean;
  bookmarksCount?: number;
  userHasBookmarked?: boolean;
  // ...
}

// ... (在组件渲染中)
        &lt;div className="flex items-center space-x-6 mt-6 pt-4 border-t"&gt;
          &lt;LikeButton /* ... */ /&gt;
          &lt;RecommendButton /* ... */ /&gt;
          &lt;BookmarkButton
            id={post.id}
            postId={post.databaseId}
            initialBookmarksCount={post.bookmarksCount || 0}
            initialIsBookmarked={post.userHasBookmarked || false}
          /&gt;
        &lt;/div&gt;
// ...
</code></pre>
</body>
</html> 