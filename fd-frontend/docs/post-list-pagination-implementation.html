<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>文章列表页面分页加载实现文档</title>
  <style>
    body {
      font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.7;
      color: #333;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
      margin-top: 2em;
    }
    h2 {
      margin-top: 1.5em;
      padding-bottom: 0.3em;
      border-bottom: 1px solid #eaecef;
    }
    h3 {
      margin-top: 1.5em;
    }
    code {
      font-family: 'SFMono-Regular', Consolas, Monaco, 'Andale Mono', monospace;
      background-color: #f5f7f9;
      padding: 0.2em 0.4em;
      border-radius: 3px;
      font-size: 0.9em;
    }
    pre {
      background-color: #f5f7f9;
      padding: 16px;
      border-radius: 4px;
      overflow: auto;
      line-height: 1.45;
    }
    pre code {
      background-color: transparent;
      padding: 0;
      font-size: 0.9em;
    }
    .version {
      background-color: #e7f5ff;
      padding: 10px 15px;
      border-radius: 4px;
      display: inline-block;
      margin-bottom: 20px;
    }
    .note {
      background-color: #fff8e6;
      border-left: 4px solid #ffd174;
      padding: 15px;
      margin: 15px 0;
    }
    .code-block {
      margin: 20px 0;
    }
    .text-center {
      text-align: center;
    }
    .meta-info {
      color: #666;
      font-size: 0.9em;
      margin-bottom: 2em;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 20px 0;
    }
    table, th, td {
      border: 1px solid #ddd;
    }
    th, td {
      padding: 12px;
      text-align: left;
    }
    th {
      background-color: #f5f7f9;
    }
  </style>
</head>
<body>
  <h1>文章列表页面分页加载统一实现文档</h1>
  <div class="version">V2.3.0</div>
  <div class="meta-info">
    <p>创建日期: 2023-12-15</p>
    <p>最后更新: 2024-06-30</p>
    <p>适用版本: ≥ 2.1.0</p>
  </div>

  <h2>1. 功能概述</h2>
  <p>本文档详细描述了项目中所有文章列表页面的统一实现方案，包括分类页、标签页、作者页及自定义分类法术语页。方案核心为**无限滚动分页加载**、**骨架屏加载状态**以及**健壮的分页数据去重机制**，旨在确保数据的一致性和用户体验的统一。</p>

  <div class="note">
    <p><strong>关键点：</strong>全站所有文章列表（分类、标签、作者、自定义分类法）均实现了无限滚动加载，提供了流畅、一致的用户体验，并通过双层防御策略，从根本上解决了游标分页中常见的重复数据问题。</p>
  </div>

  <h2>2. 统一实现方案</h2>
  <p>所有文章列表页均遵循同一套架构，包含以下核心模块：</p>
  <ul>
    <li><strong>视图组件 <code>ArticleListView</code></strong>：负责根据不同视图模式（列表、网格等）渲染文章卡片。</li>
    <li><strong>无限滚动组件 <code>InfiniteScroll</code></strong>：封装了 <code>IntersectionObserver</code> 逻辑，用于自动触发加载更多。</li>
    <li><strong>骨架屏组件 <code>ArticleListSkeleton</code></strong>：提供多种模式下的加载状态填充，提升体验。</li>
    <li><strong>数据获取Hooks</strong>：针对不同列表类型封装了独立的、可复用的数据获取逻辑。</li>
  </ul>

  <h2>3. 核心组件与Hooks</h2>

  <h3>3.1 InfiniteScroll 组件</h3>
  <p>通过 IntersectionObserver API 监测页面滚动，当用户滚动到列表底部时自动调用 <code>onLoadMore</code> 函数，触发数据加载。</p>

  <h3>3.2 ArticleListSkeleton 组件</h3>
  <p>支持多种视图模式（<code>list</code>, <code>grid</code>, <code>compact</code>, <code>magazine</code>），在加载新数据时提供与最终布局一致的骨架屏，避免页面抖动。</p>

  <h3>3.3 数据获取 Hooks</h3>
  <p>为了逻辑复用和功能内聚，我们为不同类型的文章列表封装了专属的 Hook：</p>
  <table>
    <thead>
      <tr>
        <th>Hook 名称</th>
        <th>负责页面</th>
        <th>GraphQL 查询</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><code>usePosts</code></td>
        <td>分类页 (Category), 标签页 (Tag)</td>
        <td><code>GET_POSTS_BY_CATEGORY</code>, <code>GET_POSTS_BY_TAG</code></td>
      </tr>
      <tr>
        <td><code>useTaxonomyTerm</code></td>
        <td>自定义分类法术语页 (Taxonomy Term)</td>
        <td><code>GET_POSTS_BY_TAX_QUERY_SLUG</code></td>
      </tr>
      <tr>
        <td><code>useAuthorPosts</code></td>
        <td>作者页 (Author)</td>
        <td><code>GET_POSTS_BY_AUTHOR</code></td>
      </tr>
      <tr>
        <td><code>useCustomPosts</code></td>
        <td>自定义文章类型页 (Custom Post Type)</td>
        <td>动态生成的GraphQL查询</td>
      </tr>
    </tbody>
  </table>
  <p>这些 Hook 都遵循统一的设计模式，返回分页信息 (<code>pageInfo</code>)、文章数据 (<code>posts</code>)、加载状态 (<code>loading</code>) 和加载更多函数 (<code>loadMore</code>)。</p>

  <h2>4. 分页重复问题及解决方案：双层防御策略</h2>
  <p>为从根本上解决分页加载可能出现的数据重复问题，我们采取了覆盖缓存层和逻辑层的双层防御策略。</p>
  
  <h3>4.1 第一层防御：Apollo缓存层</h3>
  <p>在 Apollo Client 的 <code>InMemoryCache</code> 配置中，我们为 <code>posts</code> 字段定义了精准的 <code>merge</code> 合并策略。</p>
  <div class="code-block">
    <pre><code>// apollo-client.ts
const cache = new InMemoryCache({
  typePolicies: {
    Query: {
      fields: {
        posts: {
          keyArgs: ['where', ['categoryId', 'tagId', 'search', 'author']], // 精准区分不同列表
          merge(existing = { nodes: [] }, incoming, { args }) {
            if (!args?.after) { // 非分页请求，直接返回新数据
              return incoming;
            }
            // 分页加载时，合并且去重
            const existingIds = new Set(existing.nodes.map((node) => node.id));
            const uniqueNewNodes = incoming.nodes.filter((node) => !existingIds.has(node.id));
            return {
              ...incoming,
              nodes: [...existing.nodes, ...uniqueNewNodes],
            };
          },
        },
      },
    },
  },
});</code></pre>
  </div>

  <h3>4.2 第二层防御：Hook层</h3>
  <p>在所有数据获取 Hook (<code>usePosts</code>, <code>useTaxonomyTerm</code>, <code>useAuthorPosts</code>) 内部，我们都实现了两道额外的保障措施：</p>
  <ol>
    <li><b><code>updateQuery</code> 去重</b>：在调用 <code>fetchMore</code> 时，其 <code>updateQuery</code> 回调函数会再次检查并过滤掉新数据中的重复项。</li>
    <li><b><code>useMemo</code> 二次去重</b>：在 Hook最终返回数据前，使用 <code>useMemo</code> 和 <code>Map</code> 对来自 Apollo 缓存的所有文章进行最后一次去重，确保万无一失。</li>
  </ol>

  <div class="code-block">
    <pre><code>// 以 usePosts.ts 为例
export const usePosts = (options: UsePostsOptions = {}) => {
  // ... 查询逻辑 ...

  // updateQuery 去重
  const loadMore = (afterCursor: string) => {
    return fetchMore({
      updateQuery: (prev, { fetchMoreResult }) => {
        // ... 此处包含去重逻辑 ...
      },
    });
  };

  // useMemo 二次去重
  const uniquePosts = useMemo(() => {
    if (!data?.posts?.nodes) return [];
    const postsMap = new Map();
    data.posts.nodes.forEach(post => {
      if (!postsMap.has(post.id)) postsMap.set(post.id, post);
    });
    return Array.from(postsMap.values());
  }, [data?.posts?.nodes]);

  return {
    posts: uniquePosts, // 始终返回去重后的文章
    // ... 其他返回值
  };
};</code></pre>
  </div>

  <h2>5. 分页数量的全局配置</h2>

  <h3>5.1 全局配置架构</h3>
  <p>为了提供一致的用户体验并赋予运营团队灵活控制的能力，我们实现了一个完整的、端到端的分页数量全局配置系统。此系统包括三个关键部分：</p>

  <ol>
    <li><strong>WordPress后台配置界面</strong>：允许非技术人员轻松调整每页加载的文章数量。</li>
    <li><strong>GraphQL API</strong>：将配置值从WordPress暴露给前端应用。</li>
    <li><strong>前端Context系统</strong>：在React应用中共享和使用这个全局设置。</li>
  </ol>

  <h3>5.2 后台配置实现</h3>
  <p>在WordPress主题的<code>functions.php</code>中，我们添加了一个新的设置字段到"模块设置"选项卡中：</p>

  <div class="code-block">
    <pre><code>// 添加分页设置部分
add_settings_section(
    'fd_pagination_settings_section',
    '列表分页设置',
    'fd_pagination_settings_section_callback',
    'fd-module-settings'
);

// 添加前端列表分页数量设置字段
add_settings_field(
    'fd_posts_per_page',
    '列表分页数量',
    'fd_posts_per_page_callback',
    'fd-module-settings',
    'fd_pagination_settings_section'
);

// 前端列表分页数量回调函数
function fd_posts_per_page_callback() {
    $options = get_option('fd_module_settings');
    $count = isset($options['fd_posts_per_page']) ? intval($options['fd_posts_per_page']) : 12;
    ?>
    <input type="number" name="fd_module_settings[fd_posts_per_page]" id="fd_posts_per_page" 
           value="<?php echo esc_attr($count); ?>" min="1" max="50" step="1" />
    <p class="description">设置前端所有列表页（分类、标签等）每页加载的文章数量（1-50），默认为12。</p>
    <?php
}</code></pre>
  </div>

  <p>同时，我们在GraphQL API中注册了一个新的查询字段，使前端可以获取这个设置：</p>

  <div class="code-block">
    <pre><code>// 添加获取全局分页设置
register_graphql_field('RootQuery', 'postsPerPageSetting', [
    'type' => 'Int',
    'description' => '控制前端列表每页文章数量的全局设置.',
    'resolve' => function() {
        // 从fd_module_settings中获取，如果不存在则提供默认值
        $module_options = get_option('fd_module_settings');
        if (isset($module_options['fd_posts_per_page'])) {
            return (int) $module_options['fd_posts_per_page'];
        }
        return (int) get_option('fd_posts_per_page', 12);
    }
]);</code></pre>
  </div>

  <h3>5.3 前端Context系统</h3>
  <p>在前端，我们创建了一个完整的Context系统来获取和分发这个设置：</p>

  <ol>
    <li><strong>Context定义</strong>：创建一个专门的Context来存储全局设置</li>
    <li><strong>Provider组件</strong>：在应用启动时获取设置并提供给整个应用</li>
    <li><strong>自定义Hook</strong>：提供简便的方式让组件获取设置值</li>
  </ol>

  <div class="code-block">
    <pre><code>// SettingsContext.tsx
'use client';

import { createContext, useContext } from 'react';

export interface SettingsContextType {
  postsPerPage: number;
}

export const SettingsContext = createContext<SettingsContextType>({
  postsPerPage: 12, // 默认值
});

export const useSettings = () => useContext(SettingsContext);</code></pre>
  </div>

  <div class="code-block">
    <pre><code>// SettingsProvider.tsx
'use client';

import React, { ReactNode } from 'react';
import { useQuery } from '@apollo/client';
import { SettingsContext, SettingsContextType } from '@/contexts/SettingsContext';
import { GET_POSTS_PER_PAGE_SETTING } from '@/lib/graphql/queries';

export const SettingsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { data, loading, error } = useQuery(GET_POSTS_PER_PAGE_SETTING);

  if (error) {
    console.error('Failed to fetch posts per page setting:', error);
  }

  const settingsValue: SettingsContextType = {
    postsPerPage: data?.postsPerPageSetting || 12,
  };

  return (
    <SettingsContext.Provider value={settingsValue}>
      {children}
    </SettingsContext.Provider>
  );
};</code></pre>
  </div>

  <h3>5.4 在数据获取Hook中使用全局设置</h3>
  <p>我们改造了所有列表数据获取Hook，使其默认使用全局设置的分页数量：</p>

  <div class="code-block">
    <pre><code>// 例如，在usePosts.ts中：
export const usePosts = (options: UsePostsOptions = {}) => {
  const { postsPerPage } = useSettings();
  const { first: firstOption, after, categoryId, tagId } = options;
  const first = firstOption ?? postsPerPage;

  // 使用first作为分页数量参数
  // ...其余代码
};</code></pre>
  </div>

  <p>这种方式确保了：</p>
  <ul>
    <li>每个Hook都默认使用全局设置的分页数量</li>
    <li>如果需要，页面组件仍可以通过options参数覆盖默认设置</li>
    <li>修改后台设置后，全站所有列表页自动应用新的分页数量，无需代码修改</li>
  </ul>

  <h2>6. 各页面实现概览</h2>

  <h3>6.1 分类页与标签页</h3>
  <p>路径: <code>/category/[slug]</code>, <code>/tag/[slug]</code></p>
  <p>使用 <code>usePosts</code> Hook，通过传入 <code>categoryId</code> 或 <code>tagId</code> 来获取相应的文章列表。页面结构和逻辑与本文档所述的统一方案完全一致。</p>

  <h3>6.2 自定义分类法术语页</h3>
  <p>路径: <code>/taxonomy/[taxonomy]/[slug]</code></p>
  <p>该页面用于显示任意自定义分类法（如"项目阶段"、"技术栈"等）下的文章。它使用 <code>useTaxonomyTerm</code> Hook，该 Hook 内部调用 <code>GET_POSTS_BY_TAX_QUERY_SLUG</code> GraphQL 查询，并实现了与其他列表页完全一致的分页和去重逻辑。</p>

  <h3>6.3 作者页</h3>
  <p>路径: <code>/author/[slug]</code></p>
  <p>作者页展示了特定作者发布的所有文章。它通过 `useAuthorPosts` Hook 获取数据，该 Hook 基于作者的 `databaseId` 进行查询。此页面也已完全采用统一的无限滚动和骨架屏方案。</p>

  <h3>6.4 自定义文章类型页</h3>
  <p>路径: <code>/post-type/[type]</code></p>
  <p>自定义文章类型页面通过 `useCustomPosts` Hook 获取数据，使用从动态构建的 GraphQL 查询。该页面同样遵循一致的无限滚动、骨架屏和分页方案。</p>

  <h2>7. 总结与最佳实践</h2>
  <div class="note">
    <ol>
      <li><strong>统一用户体验：</strong>在功能相似的页面中保持一致的交互模式是良好用户体验的基础。</li>
      <li><strong>多层次去重保障：</strong>在缓存层、Hook层（updateQuery和useMemo）分别实现去重处理，形成防御深度，确保数据准确无误。</li>
      <li><strong>合理的加载状态：</strong>通过区分初次加载 (<code>loading && posts.length === 0</code>) 和增量加载的状态，避免了全屏加载动画的频繁闪烁。</li>
      <li><strong>组件与逻辑复用：</strong>通过提取公共组件（<code>InfiniteScroll</code>, <code>ArticleListSkeleton</code>）和可复用Hooks，极大地提高了代码复用率和项目可维护性。</li>
      <li><strong>全局配置灵活性：</strong>通过后台设置、GraphQL API和Context系统的结合，实现了既简单又灵活的全局配置方案，减少了硬编码，提高了运营灵活性。</li>
    </ol>
  </div>

  <hr>
  <p class="text-center">© 2024 FD团队</p>
</body>
</html> 