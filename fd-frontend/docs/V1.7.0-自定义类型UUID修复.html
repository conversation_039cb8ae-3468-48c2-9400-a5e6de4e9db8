<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>V1.7.0 自定义类型UUID查询修复</title>
  <style>
    body {
      font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 960px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      border-bottom: 2px solid #4285f4;
      padding-bottom: 10px;
      margin-bottom: 20px;
    }
    h2 {
      margin-top: 30px;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 8px;
    }
    h3 {
      margin-top: 25px;
    }
    code {
      font-family: Consolas, Monaco, 'Andale Mono', monospace;
      background-color: #f6f8fa;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 0.9em;
    }
    pre {
      background-color: #f6f8fa;
      border-radius: 6px;
      padding: 16px;
      overflow: auto;
    }
    pre code {
      background-color: transparent;
      padding: 0;
    }
    .highlight {
      background-color: #fffbdd;
      border-left: 3px solid #f9c513;
      padding: 1em;
      margin: 1em 0;
    }
    .change-add {
      background-color: #e6ffed;
      border-left: 3px solid #34d058;
      padding: 0.2em 0.5em;
    }
    .change-remove {
      background-color: #ffeef0;
      border-left: 3px solid #d73a49;
      padding: 0.2em 0.5em;
      text-decoration: line-through;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 20px 0;
    }
    th, td {
      border: 1px solid #dfe2e5;
      padding: 8px 12px;
      text-align: left;
    }
    th {
      background-color: #f6f8fa;
    }
    img {
      max-width: 100%;
      height: auto;
    }
    .note {
      background-color: #e8f4fd;
      border-left: 3px solid #4285f4;
      padding: 1em;
      margin: 1em 0;
    }
  </style>
</head>
<body>
  <h1>V1.7.0 自定义类型UUID查询修复</h1>
  
  <div class="highlight">
    <p><strong>文档概要：</strong>本文档记录了自定义类型文章详情页404问题的分析和修复过程。问题源于GraphQL查询方式不正确，解决方案是将查询从metaQuery改为使用contentNodeByUuid字段。</p>
  </div>

  <h2>1. 问题描述</h2>
  <p>在系统更新到V1.7.0后，发现自定义类型的文章详情页（<code>/post-type/[type]/[uuid]/[slug]</code>）无法正常访问，返回404错误。而普通文章详情页（<code>/post/[uuid]/[slug]</code>）可以正常访问。</p>
  
  <h3>1.1 复现步骤</h3>
  <ol>
    <li>访问自定义类型文章列表页，如<code>/post-type/note</code></li>
    <li>点击任意文章详情链接</li>
    <li>页面显示404错误</li>
  </ol>

  <h2>2. 问题分析</h2>
  
  <h3>2.1 初步诊断</h3>
  <p>由于普通文章详情可以正常访问，而自定义类型文章详情返回404，初步判断可能是自定义类型的API查询或路由处理有问题。检查相关组件和API函数：</p>
  <ul>
    <li>自定义类型详情页组件：<code>fd-frontend/src/app/post-type/[type]/[uuid]/[slug]/page.tsx</code></li>
    <li>自定义类型数据获取函数：<code>getCustomPostByUuid</code>（<code>fd-frontend/src/lib/api.ts</code>）</li>
  </ul>

  <h3>2.2 API查询测试</h3>
  <p>为了验证API是否正常，我们在路由测试中心（<code>/route-test</code>）添加了自定义类型UUID测试功能。初始测试显示以下错误：</p>
  <pre><code>Field "metaQuery" is not defined by type "RootQueryToContentNodeConnectionWhereArgs". Did you mean "dateQuery" or "taxQuery"?</code></pre>
  
  <p>这表明<code>getCustomPostByUuid</code>函数中使用的GraphQL查询语法有误。经查看GraphQL测试页面（<code>/graphql-test</code>）中的成功示例，发现正确的查询方式是使用<code>contentNodeByUuid</code>字段，而不是尝试通过<code>metaQuery</code>过滤<code>contentNodes</code>。</p>

  <h3>2.3 解决方向</h3>
  <p>通过测试不同的查询方式，确定需要将<code>getCustomPostByUuid</code>函数修改为使用<code>contentNodeByUuid</code>查询字段，这是后端专门为通过UUID查询任何内容类型提供的API。</p>

  <h2>3. 解决方案</h2>
  
  <h3>3.1 修改API查询函数</h3>
  <p>修改<code>fd-frontend/src/lib/api.ts</code>中的<code>getCustomPostByUuid</code>函数：</p>
  
  <h4>修改前：</h4>
  <pre><code>// 获取单个自定义类型内容（通过UUID）
export async function getCustomPostByUuid(type: string, uuid: string) {
  // 验证UUID格式
  if (!isValidUuid(uuid)) {
    console.warn(`警告: UUID格式不正确 "${uuid}"，应为YYMMDD-123456格式`);
  }
  
  try {
    const contentType = type.toUpperCase();
    
    // 直接通过自定义查询获取内容
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: `
            query GetCustomPostByUuid($type: ContentTypeEnum!, $metaValue: String!) {
              contentNodes(
                first: 1,
                where: {
                  contentTypes: [$type],
                  metaQuery: {
                    metaArray: [
                      { key: "_fd_short_uuid", value: $metaValue, compareOperator: EQUAL_TO }
                    ]
                  }
                }
              ) {
                nodes {
                  __typename
                  id
                  databaseId
                  slug
                  uri
                  date
                  ... on NodeWithTitle {
                    title
                  }
                  ... on NodeWithExcerpt {
                    excerpt
                  }
                  ... on NodeWithContentEditor {
                    content
                  }
                  ... on _note {
                    title
                    content
                    excerpt
                  }
                  ... on NodeWithFeaturedImage {
                    featuredImage {
                      node {
                        sourceUrl
                        altText
                      }
                    }
                  }
                  ... on NodeWithAuthor {
                    author {
                      node {
                        id
                        name
                        slug
                      }
                    }
                  }
                }
              }
            }
          `,
          variables: {
            type: contentType,
            metaValue: uuid
          }
        }),
        next: { revalidate: 3600 } // 1小时缓存
      }
    );
    
    const data = await response.json();
    const post = data?.data?.contentNodes?.nodes?.[0];
    
    return post || null;
  } catch (error) {
    console.error(`Error fetching ${type} by UUID ${uuid}:`, error);
    return null;
  }
}</code></pre>

  <h4>修改后：</h4>
  <pre><code>// 获取单个自定义类型内容（通过UUID）
export async function getCustomPostByUuid(type: string, uuid: string) {
  // 验证UUID格式
  if (!isValidUuid(uuid)) {
    console.warn(`警告: UUID格式不正确 "${uuid}"，应为YYMMDD-123456格式`);
  }
  
  try {
    // 使用contentNodeByUuid查询获取内容
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: `
            query GetContentNodeByUuid($uuid: String!) {
              contentNodeByUuid(uuid: $uuid) {
                __typename
                id
                databaseId
                slug
                uri
                date
                ... on NodeWithTitle {
                  title
                }
                ... on NodeWithExcerpt {
                  excerpt
                }
                ... on NodeWithContentEditor {
                  content
                }
                ... on _note {
                  title
                  content
                  excerpt
                }
                ... on NodeWithFeaturedImage {
                  featuredImage {
                    node {
                      sourceUrl
                      altText
                    }
                  }
                }
                ... on NodeWithAuthor {
                  author {
                    node {
                      id
                      name
                      slug
                    }
                  }
                }
              }
            }
          `,
          variables: {
            uuid: uuid
          }
        }),
        next: { revalidate: 3600 } // 1小时缓存
      }
    );
    
    const data = await response.json();
    const post = data?.data?.contentNodeByUuid;
    
    // 检查是否获取到了内容以及类型是否匹配
    if (post) {
      // 如果需要验证内容类型，可以在这里添加
      console.log(`成功通过UUID ${uuid} 找到内容，类型: ${post.__typename}`);
      return post;
    }
    
    console.warn(`通过UUID ${uuid} 未找到内容`);
    return null;
  } catch (error) {
    console.error(`Error fetching content by UUID ${uuid}:`, error);
    return null;
  }
}</code></pre>

  <h3>3.2 主要修改内容</h3>
  <ul>
    <li>将查询名称从<code>GetCustomPostByUuid</code>改为<code>GetContentNodeByUuid</code></li>
    <li>使用<code>contentNodeByUuid</code>字段替代原来的<code>contentNodes</code>查询</li>
    <li>移除了<code>type</code>参数和<code>metaQuery</code>查询条件</li>
    <li>修改结果获取逻辑，从<code>data?.data?.contentNodes?.nodes?.[0]</code>改为<code>data?.data?.contentNodeByUuid</code></li>
    <li>添加了更详细的日志记录，便于调试</li>
  </ul>

  <h2>4. 测试验证</h2>
  
  <h3>4.1 路由测试中心</h3>
  <p>在路由测试中心（<code>/route-test</code>）添加了"自定义类型UUID测试"功能，用于验证新的查询方式是否正常工作。测试结果如下：</p>
  
  <pre><code>自定义类型查询结果
状态: 成功
类型名称: _note
内容ID: 22695
标题: OpenAI向所有付费用户推出新语音助手Her
Slug: openai向所有付费用户推出新语音助手her
URI: /note/openai向所有付费用户推出新语音助手her/</code></pre>

  <h3>4.2 详情页访问测试</h3>
  <p>修改后，自定义类型文章详情页可以正常访问。以下URL能够正确显示内容：</p>
  <pre><code>/post-type/note/230628-123456/openai向所有付费用户推出新语音助手her</code></pre>
  
  <div class="note">
    <p><strong>注意：</strong>修复后的代码保留了<code>type</code>参数，这使得API与现有调用保持兼容，虽然在当前实现中这个参数并未使用。未来可能会添加类型验证逻辑，确保返回的内容类型与请求的类型匹配。</p>
  </div>

  <h2>5. 经验总结</h2>
  
  <h3>5.1 问题根源</h3>
  <p>这个问题的根源在于错误地使用GraphQL API：</p>
  <ul>
    <li>WordPress GraphQL API中不支持<code>metaQuery</code>参数，这是错误的查询方式</li>
    <li>后端已经提供了专门的<code>contentNodeByUuid</code>字段用于通过UUID查询任何内容类型</li>
  </ul>
  
  <h3>5.2 调试经验</h3>
  <ul>
    <li>使用GraphQL测试页面（<code>/graphql-test</code>）是验证API查询正确性的有效方法</li>
    <li>添加路由测试工具有助于快速隔离和解决API相关问题</li>
    <li>从类似的、已经正常工作的功能（如普通文章UUID查询）中获取灵感，可以加速问题解决</li>
  </ul>
  
  <h3>5.3 GraphQL查询最佳实践</h3>
  <ul>
    <li>优先使用专门的查询字段而不是通用字段加过滤条件</li>
    <li>注意参数类型声明（如<code>String!</code>和<code>ID!</code>的区别）</li>
    <li>查询失败时查看GraphQL错误信息，通常会提供有用的线索</li>
  </ul>

  <h2>6. 相关文件</h2>
  <table>
    <tr>
      <th>文件</th>
      <th>说明</th>
    </tr>
    <tr>
      <td><code>fd-frontend/src/lib/api.ts</code></td>
      <td>包含<code>getCustomPostByUuid</code>函数，这是本次修复的主要目标</td>
    </tr>
    <tr>
      <td><code>fd-frontend/src/app/post-type/[type]/[uuid]/[slug]/page.tsx</code></td>
      <td>自定义类型文章详情页组件，使用<code>getCustomPostByUuid</code>函数获取数据</td>
    </tr>
    <tr>
      <td><code>fd-frontend/src/app/route-test/page.tsx</code></td>
      <td>路由测试页面，添加了自定义类型UUID测试功能</td>
    </tr>
    <tr>
      <td><code>fd-frontend/src/app/graphql-test/page.tsx</code></td>
      <td>GraphQL测试页面，包含各种查询示例，帮助我们找到正确的查询方式</td>
    </tr>
    <tr>
      <td><code>fd-theme/inc/post-uuid.php</code></td>
      <td>后端主题中注册<code>contentNodeByUuid</code>查询字段的代码</td>
    </tr>
  </table>

  <hr>
  <p><small>文档创建日期：2023年12月20日</small></p>
</body>
</html> 