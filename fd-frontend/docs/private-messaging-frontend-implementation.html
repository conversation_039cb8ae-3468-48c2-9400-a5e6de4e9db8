<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>私信功能 · 前端实现文档 (v2.1)</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 0 auto;
            padding: 24px;
            background-color: #f9f9f9;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        h1 {
            font-size: 2.4em;
            border-bottom: none;
            text-align: center;
            margin-top: 0;
        }
        pre {
            background-color: #2d2d2d;
            color: #f8f8f2;
            padding: 14px;
            border-radius: 6px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        code {
            font-family: "Fira Code", "Consolas", "Monaco", monospace;
        }
        .note {
            background-color: #e7f3fe;
            border-left: 6px solid #2196F3;
            padding: 12px;
            margin: 20px 0;
        }
        .warn {
            background-color: #fffde7;
            border-left: 6px solid #FFB300;
            padding: 12px;
            margin: 20px 0;
        }
        .path {
            background-color: #eee;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <h1>私信 (Private Messaging) · 前端实现文档</h1>
    <p class="note">本文档对应 <strong>v2.1</strong> 版本，实现了：<br/>① 全中文界面<br/>② 搜索用户发起新会话<br/>③ 会话持久存在的「+ 新建会话」按钮<br/>④ 会话内支持删除对话<br/>⑤ 采用 <code>ConversationParticipant</code> 规避权限问题</p>

    <h2>1. 目录结构</h2>
    <ul>
        <li><span class="path">src/types/messages.ts</span> —— TS 类型</li>
        <li><span class="path">src/lib/graphql/{queries,mutations}.ts</span> —— GQL</li>
        <li><span class="path">src/hooks/useMessages.ts</span> —— 自定义 Hooks</li>
        <li><span class="path">src/components/messaging/*</span> —— UI 组件</li>
        <li><span class="path">src/app/messages/[[...id]]/page.tsx</span> —— 页面</li>
    </ul>
        
    <h2>2. 类型定义</h2>
    <pre><code>// src/types/messages.ts (节选)
export interface ConversationParticipant { id: string; databaseId: number; name: string; avatar?: {url:string}|null }
export interface PrivateMessage { id:string; content:string; sentAt:string; isRead:boolean; sender:ConversationParticipant }
export interface PrivateMessageConversation {
  id:string; unreadCount:number; updatedAt:string;
  otherUser:ConversationParticipant;
  lastMessage:{content:string;sentAt:string};
  messages?: PrivateMessageConnection;
}
</code></pre>

    <h2>3. GraphQL 操作</h2>
    <h3>3.1 查询 (Queries)</h3>
    <pre><code>// src/lib/graphql/queries.ts (核心片段)
export const SEARCH_USERS = gql`
  query SearchUsers($search:String!){
    searchableUsers(search:$search){ id databaseId name avatar{url} }
  }
`
</code></pre>
    <p>其余 <code>GET_MY_CONVERSATIONS</code>、<code>GET_CONVERSATION_MESSAGES</code>、<code>GET_UNREAD_MESSAGE_COUNT</code> 与后端完全一致，文档略。</p>

    <h3>3.2 变更 (Mutations)</h3>
    <pre><code>// src/lib/graphql/mutations.ts (新增删除会话)
export const DELETE_CONVERSATION = gql`
  mutation DeleteConversation($conversationId:ID!){
    deleteConversation(input:{conversationId:$conversationId}){
      success
      deletedConversationId
    }
  }
`
</code></pre>

    <h2>4. 自定义 Hooks</h2>
    <pre><code>// src/hooks/useMessages.ts (节选)
export function useDeleteConversation(){
  const [deleteConversation,result] = useMutation(DELETE_CONVERSATION,{
    refetchQueries:[{query:GET_MY_CONVERSATIONS,variables:{first:20}}]
  })
  return {deleteConversation, ...result}
}
</code></pre>

    <h2>5. 关键组件</h2>
    <h3>5.1 ConversationList</h3>
    <ul>
        <li>始终显示「+ 新建会话」按钮（即使没有任何会话）。</li>
        <li>点击按钮 -> <code>NewConversationModal</code>。</li>
        <li>若列表为空则在按钮下方显示「暂无会话」。</li>
    </ul>
    <h3>5.2 NewConversationModal</h3>
    <ul>
        <li><code>useLazyQuery(SEARCH_USERS)</code> + <code>lodash.debounce</code> 实现输入即搜。</li>
        <li>选择用户 + 输入首条消息 -> <code>useSendPrivateMessage</code>.</li>
        <li>发送成功后自动跳转至 <code>/messages/[conversationId]</code>.</li>
    </ul>
    <h3>5.3 ChatWindow</h3>
    <ul>
        <li>挂载时调用 <code>useMarkConversationAsRead</code>.</li>
        <li>发送消息使用 Optimistic UI 立即插入本地缓存。</li>
        <li>右上角「删除会话」按钮 -> <code>useDeleteConversation</code> 成功后返回列表页。</li>
    </ul>

    <h2>6. 页面路由</h2>
    <p><span class="path">/messages</span> 采用可选 catch-all 路由 <code>[[...id]]</code>：</p>
    <pre><code>{ conversationId ? &lt;ChatWindow /&gt; : &lt;请选择一个会话提示&gt; }</code></pre>

    <h2>7. UI / i18n</h2>
    <p>所有可见文字均使用中文：加载中…、发送、删除中… 等，确保一致的本地化体验。</p>

    <h2>8. 交互流程示例</h2>
    <ol>
        <li>A 点击「+ 新建会话」-> 搜索 B -> 输入消息 -> 发送。</li>
        <li>后台 <code>sendPrivateMessage</code> → 创建或获取会话。</li>
        <li>A 跳转到 ChatWindow 并 Optimistic 显示消息。</li>
        <li>B 登录后查询会话列表，因 <code>unreadCount &gt; 0</code> 显示红点。</li>
        <li>B 阅读会话 -> <code>markConversationAsRead</code> 清零未读。</li>
        <li>任一方可点击「删除会话」-> Mutation 删除 -> 前端列表同步。</li>
    </ol>

    <h2>9. 性能与扩展</h2>
    <ul>
        <li>分页：消息默认一次取 50 条，可向上滚动加载历史。</li>
        <li>实时：可接入 Subscription 或轮询 <code>GET_MY_CONVERSATIONS</code>。</li>
        <li>多端：所有数据均走 GraphQL，无额外 REST 依赖。</li>
        </ul>

    <p class="note">如需更多示例或扩展需求，请查看仓库中的测试页 <span class="path">/src/app/test-messaging/page.tsx</span>。</p>
</body>
</html> 