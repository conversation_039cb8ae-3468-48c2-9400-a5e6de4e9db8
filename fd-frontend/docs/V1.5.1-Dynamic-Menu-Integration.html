<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>V1.5.1 动态菜单集成 - Future Decade Frontend 文档</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3, h4 {
      color: #2c3e50;
      margin-top: 1.5em;
      margin-bottom: 0.5em;
    }
    h1 { 
      font-size: 2.2em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    h2 {
      font-size: 1.8em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    h3 { font-size: 1.5em; }
    h4 { font-size: 1.3em; }
    code {
      font-family: Menlo, Monaco, Con<PERSON>as, "Courier New", monospace;
      padding: 0.2em 0.4em;
      background-color: #f3f4f5;
      border-radius: 3px;
      font-size: 0.9em;
    }
    pre {
      background-color: #f6f8fa;
      border-radius: 3px;
      padding: 16px;
      overflow: auto;
    }
    pre code {
      background-color: transparent;
      padding: 0;
    }
    a {
      color: #0366d6;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 20px 0;
    }
    th, td {
      border: 1px solid #dfe2e5;
      padding: 8px 12px;
      text-align: left;
    }
    th {
      background-color: #f6f8fa;
      font-weight: 600;
    }
    .note {
      background-color: #f8f9fa;
      border-left: 4px solid #007bff;
      padding: 15px;
      margin: 20px 0;
    }
    .warning {
      background-color: #fff3cd;
      border-left: 4px solid #ffc107;
      padding: 15px;
      margin: 20px 0;
    }
    .important {
      background-color: #f8d7da;
      border-left: 4px solid #dc3545;
      padding: 15px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <h1>V1.5.1 动态菜单集成</h1>
  <p>版本: 1.5.1<br>更新日期: 2023</p>

  <h2>概述</h2>
  <p>本文档详细介绍了如何在Next.js前端应用中实现动态WordPress菜单集成。通过GraphQL API获取WordPress菜单数据，并在主布局组件中展示顶部导航菜单和底部菜单。</p>

  <h2>技术栈</h2>
  <ul>
    <li>Next.js (App Router)</li>
    <li>React</li>
    <li>Apollo Client (GraphQL)</li>
    <li>TypeScript</li>
    <li>TailwindCSS</li>
  </ul>

  <h2>改进内容</h2>
  <ul>
    <li>更新GraphQL菜单查询，使用名称代替位置</li>
    <li>重构useMenu钩子，支持按菜单名称查询</li>
    <li>在主布局组件中集成动态菜单</li>
    <li>处理导航中的菜单层级关系</li>
    <li><strong>支持二级下拉菜单显示</strong></li>
  </ul>

  <h2>1. GraphQL查询优化</h2>
  <h3>1.1 移除不再使用的GET_MENU_BY_LOCATION查询</h3>
  <p>以前，我们使用基于WordPress菜单位置的查询方式，但这种方式存在一些限制。我们现在改为使用更灵活的菜单名称查询。</p>

  <pre><code>// 旧的查询方式（已移除）
export const GET_MENU_BY_LOCATION = gql`
  query GetMenuByLocation($location: MenuLocationEnum!) {
    menus(where: {location: $location}) {
      nodes {
        id
        name
        menuItems {
          nodes {
            ...MenuItemFields
          }
        }
      }
    }
  }
  ${MENU_ITEM_FRAGMENT}
`;</code></pre>

  <h3>1.2 保留的菜单查询</h3>
  <p>我们保留并使用GET_MENUS查询，获取所有菜单，然后在前端根据名称筛选：</p>

  <pre><code>// 获取所有菜单
export const GET_MENUS = gql`
  query GetMenus {
    menus {
      nodes {
        id
        name
        menuItems {
          nodes {
            ...MenuItemFields
          }
        }
      }
    }
  }
  ${MENU_ITEM_FRAGMENT}
`;</code></pre>

  <h2>2. Hook实现改进</h2>
  <h3>2.1 更新useMenu钩子</h3>
  <p>我们更新了useMenu钩子，支持通过菜单名称查询菜单，并添加了将扁平菜单转换为层次结构的功能：</p>

  <pre><code>import { useQuery } from '@apollo/client';
import { GET_MENUS } from '../lib/graphql/queries';

interface MenuItem {
  id: string;
  title: string;
  label: string;
  url: string;
  target?: string;
  parentId?: string;
  cssClasses?: string[];
  children?: MenuItem[]; // 替代childItems，用于层次结构
}

interface Menu {
  id: string;
  name: string;
  menuItems?: {
    nodes: MenuItem[];
  };
}

interface MenusData {
  menus: {
    nodes: Menu[];
  };
}

/**
 * 将扁平菜单列表转换为层次结构
 */
const flatListToHierarchical = (
  data: MenuItem[] = [],
  {idKey = 'id', parentKey = 'parentId', childrenKey = 'children'} = {}
): MenuItem[] => {
  const tree: MenuItem[] = [];
  const childrenOf: { [key: string]: MenuItem[] } = {};
  
  data.forEach((item) => {
    const newItem = {...item} as any;
    const id = newItem[idKey];
    const parentId = newItem[parentKey] || 0;
    
    childrenOf[id] = childrenOf[id] || [];
    newItem[childrenKey] = childrenOf[id];
    
    parentId
      ? (
          childrenOf[parentId] = childrenOf[parentId] || []
        ).push(newItem)
      : tree.push(newItem);
  });
  
  return tree;
};

/**
 * 获取指定名称菜单的Hook
 * @param menuName 菜单名称，如"顶部菜单"或"底部菜单"
 * @returns 菜单数据、加载状态和错误信息
 */
export const useMenu = (menuName: string) => {
  const { data, loading, error, refetch } = useQuery<MenusData>(
    GET_MENUS,
    {
      skip: !menuName,
    }
  );

  // 从所有菜单中找到匹配名称的菜单
  const menu = data?.menus?.nodes?.find((menu: Menu) => menu.name === menuName);
  const flatMenuItems = menu?.menuItems?.nodes || [];
  
  // 将扁平菜单项转换为层次结构
  const hierarchicalMenuItems = flatListToHierarchical(flatMenuItems);

  return {
    menu,
    menuItems: flatMenuItems,
    hierarchicalMenuItems,
    loading,
    error,
    refetch,
  };
};</code></pre>

  <h3>2.2 菜单层次结构转换算法</h3>
  <p>我们添加了一个flatListToHierarchical函数，能够将从GraphQL API获取的扁平菜单列表转换为具有父子关系的层次结构：</p>
  <ul>
    <li>函数接收一个扁平的MenuItem数组作为输入</li>
    <li>创建一个树形结构，每个子菜单项被添加到其父菜单项的children数组中</li>
    <li>顶级菜单项（没有parentId的项）被添加到返回的树数组中</li>
    <li>最终返回一个包含完整层次结构的数组</li>
  </ul>

  <h2>3. 主布局组件集成</h2>
  <h3>3.1 顶部菜单集成</h3>
  <p>在MainLayout组件的Header部分集成顶部菜单：</p>

  <pre><code>// 头部组件
const Header: React.FC = () => {
  const pathname = usePathname();
  const { menuItems: topMenuItems, hierarchicalMenuItems, loading: topMenuLoading } = useMenu('顶部菜单');
  const [mounted, setMounted] = useState(false);
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);
  const { getLogoUrl, getColor, settings } = useVITheme();
  
  // 创建引用来跟踪菜单区域
  const menuRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // 添加点击外部区域关闭下拉菜单的功能
  useEffect(() => {
    if (!openSubmenu) return;
    
    const handleClickOutside = (event: MouseEvent) => {
      // 如果点击的是已打开菜单项的容器，则不关闭
      if (menuRefs.current[openSubmenu] && menuRefs.current[openSubmenu]?.contains(event.target as Node)) {
        return;
      }
      
      setOpenSubmenu(null);
    };
    
    // 添加全局点击事件监听
    document.addEventListener('mousedown', handleClickOutside);
    
    // 组件卸载或状态改变时移除事件监听
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openSubmenu]);
  
  const handleToggleSubmenu = (id: string) => {
    setOpenSubmenu(prev => prev === id ? null : id);
  };</code></pre>

  <h3>3.2 二级菜单UI实现</h3>
  <p>在导航区域中，我们使用层次结构的菜单数据和条件渲染来显示下拉菜单：</p>

  <pre><code>&lt;nav className="flex items-center justify-center space-x-4 w-full md:w-3/5 mb-4 md:mb-0 flex-wrap"&gt;
  {mounted && !topMenuLoading && hierarchicalMenuItems.map((item) => (
    &lt;div 
      key={item.id} 
      className="relative group my-1"
      ref={el => menuRefs.current[item.id] = el}
    &gt;
      &lt;div className="flex items-center"&gt;
        &lt;Link 
          href={item.url} 
          target={item.target || '_self'}
          className={`text-base font-medium whitespace-nowrap ${
            pathname === item.url 
              ? 'text-primary hover:text-primary-600 dark:text-primary-400' 
              : 'text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-400'
          }`}
        &gt;
          {item.label || item.title}
        &lt;/Link&gt;
        
        {/* 显示下拉箭头 */}
        {item.children && item.children.length > 0 && (
          &lt;button 
            onClick={() => handleToggleSubmenu(item.id)}
            className="ml-1 text-gray-500 hover:text-primary focus:outline-none"
          &gt;
            &lt;svg 
              xmlns="http://www.w3.org/2000/svg" 
              className={`h-4 w-4 transition-transform ${openSubmenu === item.id ? 'rotate-180' : ''}`} 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            &gt;
              &lt;path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" /&gt;
            &lt;/svg&gt;
          &lt;/button&gt;
        )}
      &lt;/div&gt;
      
      {/* 二级菜单下拉框 */}
      {item.children && item.children.length > 0 && openSubmenu === item.id && (
        &lt;div className="absolute z-10 left-0 mt-2 w-48 bg-white dark:bg-gray-800 border dark:border-gray-700 rounded-md shadow-lg py-1"&gt;
          {item.children.map(child => (
            &lt;Link
              key={child.id}
              href={child.url}
              target={child.target || '_self'}
              className={`block px-4 py-2 text-sm ${
                pathname === child.url
                  ? 'text-primary hover:bg-gray-100 dark:hover:bg-gray-700'
                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            &gt;
              {child.label || child.title}
            &lt;/Link&gt;
          ))}
        &lt;/div&gt;
      )}
    &lt;/div&gt;
  ))}</code></pre>

  <h3>3.3 增强的用户体验</h3>
  <p>我们添加了以下功能来改善二级菜单的用户体验：</p>
  <ul>
    <li><strong>点击外部区域自动关闭下拉菜单</strong>：通过全局事件监听器实现</li>
    <li><strong>箭头旋转动画</strong>：提供视觉反馈，指示菜单打开/关闭状态</li>
    <li><strong>悬停效果</strong>：子菜单项在鼠标悬停时有背景色变化</li>
    <li><strong>适应性布局</strong>：导航区域使用flex-wrap确保在较小屏幕上菜单项可以换行</li>
  </ul>

  <h2>4. 最佳实践</h2>
  <h3>4.1 菜单数据处理</h3>
  <ul>
    <li>使用useRef和事件监听器来处理点击外部区域关闭菜单的功能</li>
    <li>将扁平菜单转换为层次结构的处理放在hook中，保持UI组件的简洁</li>
    <li>使用条件渲染确保只有当菜单数据加载完成后才渲染菜单</li>
  </ul>

  <h3>4.2 性能考量</h3>
  <ul>
    <li>使用memo缓存不频繁变化的计算结果</li>
    <li>事件监听器仅在openSubmenu状态变化时添加/移除，避免不必要的重新渲染</li>
    <li>使用按需加载子菜单，只有当用户点击父菜单时才渲染相应的子菜单</li>
  </ul>

  <div class="note">
    <p><strong>注意</strong>：确保在WordPress后台的菜单设置中正确配置菜单层级关系，使二级菜单项正确指定其父菜单项。</p>
  </div>

  <h2>5. 总结</h2>
  <p>通过这些改进，我们实现了以下功能：</p>
  <ul>
    <li>动态加载WordPress菜单，并在前端应用中展示</li>
    <li>支持多级菜单结构，特别是二级下拉菜单</li>
    <li>提供良好的用户体验，如点击外部区域关闭下拉菜单</li>
    <li>遵循React最佳实践，保持代码清晰和易于维护</li>
  </ul>
  <p>这些改进使我们的前端应用能够更好地展示WordPress中配置的菜单结构，提供直观的导航体验。</p>

  <h2>6. 相关文件清单</h2>
  <table>
    <thead>
      <tr>
        <th>文件路径</th>
        <th>说明</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>/src/lib/graphql/queries.ts</td>
        <td>GraphQL查询定义，包含GET_MENUS</td>
      </tr>
      <tr>
        <td>/src/lib/graphql/fragments.ts</td>
        <td>包含MENU_ITEM_FRAGMENT片段定义</td>
      </tr>
      <tr>
        <td>/src/hooks/useMenu.ts</td>
        <td>获取WordPress菜单的自定义Hook</td>
      </tr>
      <tr>
        <td>/src/components/layouts/MainLayout.tsx</td>
        <td>主布局组件，集成了顶部和底部菜单</td>
      </tr>
    </tbody>
  </table>

  <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #eaecef; text-align: center; color: #6a737d;">
    <p>Future Decade Frontend 技术文档 &copy; 2023</p>
  </footer>
</body>
</html> 