<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自定义文章类型点赞功能实现与排查文档</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            line-height: 1.7;
            color: #333;
            max-width: 900px;
            margin: 0 auto;
            padding: 25px;
            background-color: #f9f9f9;
        }
        h1, h2, h3, h4 {
            color: #1a1a1a;
            border-bottom: 1px solid #eaeaea;
            padding-bottom: 10px;
        }
        h1 {
            font-size: 2.2em;
            text-align: center;
            border-bottom-width: 2px;
            margin-bottom: 30px;
        }
        h2 {
            font-size: 1.8em;
            margin-top: 40px;
        }
        h3 {
            font-size: 1.4em;
            margin-top: 30px;
            border-bottom: none;
        }
        pre {
            background-color: #2d2d2d;
            color: #f8f8f2;
            border-radius: 8px;
            padding: 20px;
            overflow: auto;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 14px;
            line-height: 1.5;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        code {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            background-color: #eef;
            padding: 3px 6px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .note {
            background-color: #e8f4fd;
            padding: 15px 20px;
            border-left: 5px solid #2196f3;
            margin: 25px 0;
            border-radius: 0 4px 4px 0;
        }
        .warning {
            background-color: #fffde7;
            padding: 15px 20px;
            border-left: 5px solid #ffc107;
            margin: 25px 0;
            border-radius: 0 4px 4px 0;
        }
        .success {
            background-color: #e8f5e9;
            padding: 15px 20px;
            border-left: 5px solid #4caf50;
            margin: 25px 0;
            border-radius: 0 4px 4px 0;
        }
    </style>
</head>
<body>
    <h1>自定义文章类型点赞功能实现与排查文档</h1>
    
    <div class="note">
        本文档记录了为项目中的自定义文章类型（CPT）详情页添加点赞功能的完整流程，包括后端分析、前端实现、问题排查和最终解决方案。
    </div>

    <h2>1. 目标与初始分析</h2>
    <p>我们的目标是在自定义文章类型的详情页 <code>/post-type/[type]/[uuid]/[slug]</code> 中，添加与标准文章一致的点赞功能。</p>
    <p>初始分析基于现有的 <code>fd-like-implementation.html</code> 文档，我们了解到项目已有一套成熟的点赞功能实现，但需要验证其是否适用于自定义文章类型。</p>

    <h2>2. 后端能力审查</h2>
    <p>我们首先检查了后端 <code>fd-member</code> 插件的代码，以确认点赞功能的可扩展性。</p>
    
    <h3>2.1. 核心逻辑审查 (<code>like-core.php</code>)</h3>
    <p>核心的点赞/取消点赞函数（如 <code>fd_member_set_like_status</code>）均基于 <code>post_id</code> 操作。这表明后端逻辑与具体文章类型无关，天然支持所有文章类型。</p>
    
    <h3>2.2. GraphQL接口审查 (<code>like-graphql.php</code>)</h3>
    <p>这是关键的发现。代码显示，点赞字段是通过遍历所有注册到GraphQL的文章类型（包括自定义类型）来动态添加的。</p>
    <pre>
// ...
// 获取所有公开的、且在 GraphQL 中可见的文章类型
$args = [
    'public'   => true,
    'show_in_graphql' => true,
];
$post_types = get_post_types($args, 'objects');

// 循环遍历找到的所有文章类型 (如 Post, Page, Note 等)
foreach ($post_types as $post_type) {
    if (!empty($post_type->graphql_single_name)) {
        
        // 根据 graphql_single_name 构建 GraphQL 类型名称
        $graphql_type_name = ucfirst($post_type->graphql_single_name);

        // 为该类型注册 'likesCount' 字段
        register_graphql_field($graphql_type_name, 'likesCount', [ /* ... */ ]);

        // 为该类型注册 'userHasLiked' 字段
        register_graphql_field($graphql_type_name, 'userHasLiked', [ /* ... */ ]);
    }
}
// ...
add_action('graphql_register_types', 'fd_member_likes_register_graphql_fields');
    </pre>
    <p><strong>结论：</strong> 后端已完全支持在自定义文章类型上查询 <code>likesCount</code> 和 <code>userHasLiked</code> 字段。问题必定出在前端的查询方式上。</p>

    <h2>3. 前端实现与问题排查</h2>
    <p>前端的实现过程遇到了两个核心问题，但根源相同，都与GraphQL的查询方式有关。</p>

    <h3>问题一：页面直接404错误</h3>
    <div class="warning">
        <strong>问题描述：</strong> 在 <code>/lib/api.ts</code> 的 <code>getCustomPostByUuid</code> 函数中为 <code>contentNodeByUuid</code> 查询直接添加 <code>likesCount</code> 和 <code>userHasLiked</code> 字段后，自定义文章详情页立即返回404错误。
    </div>
    
    <h4>分析与解决</h4>
    <p>GraphQL的 <code>contentNodeByUuid</code> 返回的是一个 <code>ContentNode</code> **接口（Interface）**。<code>likesCount</code> 字段是注册在实现了该接口的**具体类型**（如 `Note`, `Post`）上的，而不是在接口本身。因此，直接在接口上查询这些字段会导致GraphQL解析失败，服务器返回错误，前端表现为404。</p>
    <p><strong>解决方案：</strong> 使用GraphQL的 **内联片段 (Inline Fragment)**，根据动态传入的类型名，只在对应的具体类型上查询这些字段。</p>
    <div class="success">
        <h4>最终代码 (<code>lib/api.ts</code>)</h4>
        <pre>
export async function getCustomPostByUuid(type: string, uuid: string) {
  // ...
  const typeName = type.charAt(0).toUpperCase() + type.slice(1);

  try {
    const response = await fetch(
      // ...
      body: JSON.stringify({
        query: `
          query GetContentNodeByUuid($uuid: String!) {
            contentNodeByUuid(uuid: $uuid) {
              __typename
              id
              databaseId
              // ... other fields on ContentNode
              
              // 使用内联片段查询特定类型上的字段
              ... on ${typeName} {
                likesCount
                userHasLiked
              }
            }
          }
        `,
        // ...
      })
    );
    // ...
  }
}
        </pre>
    </div>

    <h3>问题二：点赞后前端UI不更新</h3>
    <div class="warning">
        <strong>问题描述：</strong> 解决了404问题后，点赞操作可以成功写入数据库，但前端UI（点赞数和图标）在操作后没有变化。控制台显示了与问题一类似的GraphQL错误。
    </div>

    <h4>分析与解决</h4>
    <p>问题根源完全相同。点赞后，<code>LikeButton</code> 组件内部的 <code>useQuery</code> 会被重新触发以获取最新数据。而这个查询最初也是直接在 <code>contentNode</code> 上查询点赞字段，导致查询失败，因此无法用新数据更新UI。</p>
    <p><strong>解决方案：</strong> 同样采用内联片段的思路，改造 <code>LikeButton.tsx</code> 组件，使其能为自定义文章类型动态构建正确的GraphQL查询。</p>
    <div class="success">
        <h4>最终代码 (<code>components/post/LikeButton.tsx</code>)</h4>
        <pre>
// 为自定义文章类型动态构建点赞状态查询
const buildCustomPostLikeQuery = (typeName: string) => gql`
  query CustomPostLikeStatus($postId: ID!) {
    contentNode(id: $postId, idType: DATABASE_ID) {
      id
      databaseId
      ... on ${typeName} {
        likesCount
        userHasLiked
      }
    }
  }
`;

export const LikeButton: React.FC<LikeButtonProps> = ({ 
  // ... props
  contentType = 'post'
}) => {
  // ...
  const isCustomPost = contentType !== 'post';
  
  // 根据内容类型选择或构建合适的查询
  const query = isCustomPost
    ? buildCustomPostLikeQuery(contentType.charAt(0).toUpperCase() + contentType.slice(1))
    : POST_LIKE_BUTTON_QUERY;
  
  // ...
};
        </pre>
    </div>

    <h2>4. 总结</h2>
    <p>本次任务的核心是理解并正确使用GraphQL的 **内联片段**。当查询一个返回接口（Interface）或联合（Union）类型的字段时，访问具体类型独有的字段必须使用内联片段。</p>
    <p>通过两次关键的修复，我们成功地将点赞功能无缝扩展到了所有自定义文章类型，保证了数据获取的正确性和前端UI的实时响应。</p>

</body>
</html> 