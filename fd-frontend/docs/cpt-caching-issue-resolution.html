<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>自定义文章类型(CPT)页面用户状态缓存问题解决方案</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f9f9f9;
    }
    h1, h2, h3 {
      color: #2c3e50;
      border-bottom: 2px solid #e0e0e0;
      padding-bottom: 10px;
    }
    code {
      background-color: #eef;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: "SFMono-Regular", <PERSON><PERSON><PERSON>, "Liberation Mono", Menlo, Courier, monospace;
    }
    pre {
      background-color: #2d2d2d;
      color: #f8f8f2;
      padding: 15px;
      border-radius: 5px;
      overflow-x: auto;
    }
    .problem-summary, .solution-summary {
      background-color: #fff;
      border: 1px solid #ddd;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .problem-summary {
      border-left: 5px solid #e74c3c; /* Red for problem */
    }
    .solution-summary {
      border-left: 5px solid #2ecc71; /* Green for solution */
    }
    .timeline {
      list-style-type: none;
      padding-left: 0;
    }
    .timeline li {
      margin-bottom: 15px;
      padding-left: 25px;
      position: relative;
    }
    .timeline li::before {
      content: '●';
      position: absolute;
      left: 0;
      top: 0;
      color: #3498db;
      font-size: 1.2em;
    }
  </style>
</head>
<body>

  <h1>自定义文章类型(CPT)页面用户状态缓存问题解决方案</h1>

  <div class="problem-summary">
    <h2>问题概述</h2>
    <p>
      在自定义文章类型（CPT）的详情页上，用户进行点赞、推荐或收藏操作后，UI会通过乐观更新立即反馈成功状态，并且数据库也成功记录了操作。
      然而，当用户刷新页面后，这些状态（点赞数、是否已推荐等）会全部丢失，UI恢复到未操作前的状态。此问题仅存在于CPT页面，普通文章页面功能完全正常。
    </p>
  </div>

  <h2>诊断过程与分析</h2>
  <p>问题的诊断经历了一个逐步深入、不断排除错误假设的过程，最终定位到了前端数据获取的缓存策略上。</p>
  <ol class="timeline">
    <li>
      <h3>初步怀疑：后端GraphQL实现</h3>
      <p>最初的怀疑集中在后端的GraphQL API层，认为可能是后端未能给自定义文章类型正确添加<code>userHasLiked</code>等字段。通过检查<code>fd-member</code>插件的代码，我们确认了后端实现是通用的，该假设被排除。</p>
    </li>
    <li>
      <h3>中期怀疑：前端GraphQL查询语句</h3>
      <p>
        接着，我们怀疑是前端在请求CPT数据时，其GraphQL查询语句本身遗漏了<code>userHasLiked</code>、<code>userHasRecommended</code>、<code>userHasBookmarked</code>字段。
        检查<code>fd-frontend/src/lib/api.ts</code>中的<code>getCustomPostByUuid</code>函数后发现，查询语句确实存在字段缺失，但这并不能解释为何点赞和推荐状态也同样丢失。
      </p>
    </li>
    <li>
      <h3>深入分析：认证与缓存</h3>
      <p>
        一个关键的转折点是用户的持续测试和反馈。用户在WPGraphQL IDE中的测试证明：<strong>只要提供了正确的用户认证，后端API就能返回完全正确的用户状态数据</strong>。这表明问题既不在于后端API本身，也不在于查询语句的字段。
      </p>
      <p>
        这让我们将焦点转移到了<strong>数据缓存</strong>上。普通文章页之所以正常，是因为其数据获取函数<code>getPostByUuid</code>未使用Next.js的<code>fetch</code>缓存。而CPT页面的<code>getCustomPostByUuid</code>函数却使用了<code>fetch</code>并设置了<code>revalidate: 3600</code>的缓存策略。
      </p>
    </li>
    <li>
      <h3>最终结论：错误的共享缓存</h3>
      <p>
        最终确认，问题的根本原因是：<strong>前端代码将一个本应是用户专属的、动态的页面数据，错误地应用了对所有用户共享的静态缓存策略。</strong>
      </p>
      <p>
        当第一个用户访问页面时，系统会根据该用户的状态（通常是未点赞/未收藏）生成一份数据缓存。由于设置了<code>revalidate</code>，这份为特定用户生成的缓存，被错误地提供给了所有后续访问该页面的用户，无论他们是谁、是否已登录、以及真实的数据库记录如何。这就导致了刷新后状态“丢失”的现象，因为用户看到的其实是一个过期的、属于别人的缓存快照。
      </p>
    </li>
  </ol>

  <div class="solution-summary">
    <h2>解决方案</h2>
    <p>
      解决方案是修改<code>fd-frontend/src/lib/api.ts</code>文件中的<code>getCustomPostByUuid</code>函数，使其数据获取方式与正常工作的<code>getPostByUuid</code>函数保持一致，彻底禁用共享缓存。
    </p>
    <ol>
      <li><strong>弃用<code>fetch</code> API：</strong>将函数内的数据请求方式从原生的<code>fetch</code>调用改为使用项目中的Apollo Client实例（<code>getServerApollo</code>）。</li>
      <li><strong>移除缓存策略：</strong>通过弃用<code>fetch</code>，从根本上移除了导致问题的<code>next: { revalidate: 3600 }</code>缓存指令。</li>
      <li><strong>强制动态请求：</strong>在新的Apollo Client查询选项中，明确设置<code>fetchPolicy: 'no-cache'</code>，以确保每次请求都是动态的，直接从服务器获取最新数据，从而正确反映每个用户的真实状态。</li>
      <li><strong>补全查询字段：</strong>修复了GraphQL查询语句，添加了之前遗漏的<code>bookmarksCount</code>和<code>userHasBookmarked</code>字段。</li>
    </ol>
  </div>
  
  <h3>修改后的核心代码</h3>
  <pre><code>// fd-frontend/src/lib/api.ts

export async function getCustomPostByUuid(type: string, uuid: string) {
  // ... (构建查询语句，已补全所有字段)
  const GET_CONTENT_NODE_BY_UUID_QUERY = gql`...`;

  try {
    const client = typeof window === 'undefined' ? getServerApollo() : apolloClient;
    const { data } = await client.query({
      query: GET_CONTENT_NODE_BY_UUID_QUERY,
      variables: { uuid },
      // 关键修复：确保每次动态获取，解决用户状态缓存问题
      fetchPolicy: 'no-cache', 
    });
    
    const post = data?.contentNodeByUuid;
    // ... (后续逻辑)
  } catch (error) {
    // ...
  }
}
  </code></pre>

</body>
</html> 