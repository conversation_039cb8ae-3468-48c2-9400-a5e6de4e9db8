# Gutenberg核心区块开发指南

本文档提供了关于WordPress Gutenberg核心区块的完整开发指南，帮助前端开发人员实现区块组件的渲染。

## 目录

- [概述](#概述)
- [开发流程](#开发流程)
- [区块渲染架构](#区块渲染架构)
- [区块列表](#区块列表)
  - [完整区块列表](#完整区块列表)
  - [按优先级分组的区块](#按优先级分组的区块)
- [区块组件开发](#区块组件开发)
- [混合渲染策略](#混合渲染策略)
- [高级应用](#高级应用)

## 概述

Gutenberg是WordPress的新一代编辑器，它使用区块（Block）作为内容的基本单位。在headless WordPress架构中，我们需要在前端实现对应的React组件来渲染这些区块。

本指南主要关注前端实现部分，假设后端已经安装并正确配置了WP-GraphQL和WP-GraphQL-Gutenberg插件。

## 开发流程

1. **分析内容需求**：确定网站中需要支持的区块类型
2. **按优先级开发**：先实现最常用的核心区块组件
3. **实现混合渲染策略**：对于未开发专用组件的区块，提供HTML回退渲染方案
4. **测试与优化**：确保各种场景下区块的正确渲染

## 区块渲染架构

我们采用了以下架构来处理区块的渲染：

```
BlockRenderer
├── 识别区块类型
├── 分发到专用组件 (ParagraphBlock, HeadingBlock等)
└── 未知区块的回退处理 (使用HTML内容)
```

主要文件结构：
- `src/components/blocks/BlockRenderer.tsx` - 区块渲染器
- `src/components/blocks/[BlockName].tsx` - 各种区块组件
- `src/types/block.ts` - 区块类型定义

## 区块列表

### 完整区块列表

以下是WordPress Gutenberg提供的所有核心区块：

#### 内容区块
| 区块名称 | 类型名称 | 描述 |
|---------|---------|------|
| `core/paragraph` | `CoreParagraphBlock` | 段落 |
| `core/heading` | `CoreHeadingBlock` | 标题 |
| `core/image` | `CoreImageBlock` | 图像 |
| `core/gallery` | `CoreGalleryBlock` | 图库 |
| `core/list` | `CoreListBlock` | 列表 |
| `core/quote` | `CoreQuoteBlock` | 引用 |
| `core/audio` | `CoreAudioBlock` | 音频 |
| `core/cover` | `CoreCoverBlock` | 封面 |
| `core/file` | `CoreFileBlock` | 文件 |
| `core/video` | `CoreVideoBlock` | 视频 |

#### 格式化区块
| 区块名称 | 类型名称 | 描述 |
|---------|---------|------|
| `core/code` | `CoreCodeBlock` | 代码 |
| `core/html` | `CoreHTMLBlock` | 自定义HTML |
| `core/preformatted` | `CorePreformattedBlock` | 预格式化文本 |
| `core/pullquote` | `CorePullquoteBlock` | 引文 |
| `core/table` | `CoreTableBlock` | 表格 |
| `core/verse` | `CoreVerseBlock` | 诗歌 |

#### 布局区块
| 区块名称 | 类型名称 | 描述 |
|---------|---------|------|
| `core/buttons` | `CoreButtonsBlock` | 按钮组 |
| `core/button` | `CoreButtonBlock` | 按钮 |
| `core/columns` | `CoreColumnsBlock` | 多列布局 |
| `core/column` | `CoreColumnBlock` | 列 |
| `core/group` | `CoreGroupBlock` | 组 |
| `core/media-text` | `CoreMediaTextBlock` | 媒体与文本 |
| `core/more` | `CoreMoreBlock` | 阅读更多 |
| `core/nextpage` | `CoreNextpageBlock` | 分页符 |
| `core/separator` | `CoreSeparatorBlock` | 分隔符 |
| `core/spacer` | `CoreSpacerBlock` | 间隔 |

#### 小工具区块
| 区块名称 | 类型名称 | 描述 |
|---------|---------|------|
| `core/shortcode` | `CoreShortcodeBlock` | 短代码 |
| `core/archives` | `CoreArchivesBlock` | 归档 |
| `core/calendar` | `CoreCalendarBlock` | 日历 |
| `core/categories` | `CoreCategoriesBlock` | 分类目录 |
| `core/latest-comments` | `CoreLatestCommentsBlock` | 最新评论 |
| `core/latest-posts` | `CoreLatestPostsBlock` | 最新文章 |
| `core/rss` | `CoreRSSBlock` | RSS |
| `core/search` | `CoreSearchBlock` | 搜索 |
| `core/social-links` | `CoreSocialLinksBlock` | 社交链接 |
| `core/social-link` | `CoreSocialLinkBlock` | 单个社交链接 |
| `core/tag-cloud` | `CoreTagCloudBlock` | 标签云 |

#### 嵌入区块
| 区块名称 | 类型名称 | 描述 |
|---------|---------|------|
| `core/embed` | `CoreEmbedBlock` | 嵌入内容 |
| `core-embed/twitter` | `CoreTwitterEmbedBlock` | Twitter嵌入 |
| `core-embed/youtube` | `CoreYouTubeEmbedBlock` | YouTube嵌入 |
| `core-embed/facebook` | `CoreFacebookEmbedBlock` | Facebook嵌入 |
| `core-embed/instagram` | `CoreInstagramEmbedBlock` | Instagram嵌入 |
| `core-embed/vimeo` | `CoreVimeoEmbedBlock` | Vimeo嵌入 |

#### 重用区块
| 区块名称 | 类型名称 | 描述 |
|---------|---------|------|
| `core/block` | `CoreReusableBlock` | 可重用区块 |

### 按优先级分组的区块

根据使用频率和重要性，我们将区块分为三个优先级组：

#### 高优先级（P0）：几乎所有网站都需要
| 区块名称 | 类型名称 | 描述 |
|---------|---------|------|
| `core/paragraph` | `CoreParagraphBlock` | 段落 |
| `core/heading` | `CoreHeadingBlock` | 标题 |
| `core/image` | `CoreImageBlock` | 图像 |
| `core/list` | `CoreListBlock` | 列表 |
| `core/quote` | `CoreQuoteBlock` | 引用 |

#### 中优先级（P1）：大多数网站需要
| 区块名称 | 类型名称 | 描述 |
|---------|---------|------|
| `core/gallery` | `CoreGalleryBlock` | 图库 |
| `core/table` | `CoreTableBlock` | 表格 |
| `core/code` | `CoreCodeBlock` | 代码 |
| `core/columns` | `CoreColumnsBlock` | 多列布局 |
| `core/column` | `CoreColumnBlock` | 列 |
| `core/buttons` | `CoreButtonsBlock` | 按钮组 |
| `core/button` | `CoreButtonBlock` | 按钮 |
| `core/separator` | `CoreSeparatorBlock` | 分隔符 |
| `core/cover` | `CoreCoverBlock` | 封面 |

#### 低优先级（P2）：特定场景需要
| 区块名称 | 类型名称 | 描述 |
|---------|---------|------|
| `core/audio` | `CoreAudioBlock` | 音频 |
| `core/video` | `CoreVideoBlock` | 视频 |
| `core/file` | `CoreFileBlock` | 文件 |
| `core/html` | `CoreHTMLBlock` | 自定义HTML |
| `core/preformatted` | `CorePreformattedBlock` | 预格式化文本 |
| `core/pullquote` | `CorePullquoteBlock` | 引文 |
| `core/verse` | `CoreVerseBlock` | 诗歌 |
| `core/group` | `CoreGroupBlock` | 组 |
| `core/media-text` | `CoreMediaTextBlock` | 媒体与文本 |
| `core/more` | `CoreMoreBlock` | 阅读更多 |
| `core/nextpage` | `CoreNextpageBlock` | 分页符 |
| `core/spacer` | `CoreSpacerBlock` | 间隔 |
| `core/embed` | `CoreEmbedBlock` | 嵌入内容 |

## 区块组件开发

开发区块组件的一般步骤：

1. **创建组件文件**：在`src/components/blocks/`目录下创建组件文件
2. **定义属性接口**：根据区块特性定义TypeScript接口
3. **实现渲染逻辑**：处理区块属性并返回React元素
4. **注册到BlockRenderer**：在BlockRenderer中导入并添加组件

示例：段落区块组件

```tsx
// src/components/blocks/ParagraphBlock.tsx
import React from 'react';

interface ParagraphBlockProps {
  attributes: {
    content: string;
    align?: string;
    dropCap?: boolean;
    backgroundColor?: string;
    textColor?: string;
  };
}

const ParagraphBlock: React.FC<ParagraphBlockProps> = ({ attributes }) => {
  const { content, align, dropCap, backgroundColor, textColor } = attributes;
  
  const className = [
    'paragraph-block',
    align ? `text-${align}` : '',
    dropCap ? 'first-letter:text-4xl first-letter:font-bold first-letter:mr-1 first-letter:float-left' : ''
  ].filter(Boolean).join(' ');
  
  const style: React.CSSProperties = {};
  
  if (backgroundColor) {
    style.backgroundColor = backgroundColor;
  }
  
  if (textColor) {
    style.color = textColor;
  }
  
  return (
    <div 
      className={className}
      style={style}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

export default ParagraphBlock;
```

## 混合渲染策略

对于尚未实现专用组件的区块，我们使用"混合渲染策略"：

```tsx
// BlockRenderer.tsx中的回退策略
default:
  // 没有对应组件时的混合渲染策略
  console.warn(`未实现组件的区块: ${__typename}`, name);
  
  // 优先使用动态内容，其次使用保存内容
  const htmlContent = dynamicContent || saveContent || '';
  return (
    <div 
      key={index}
      className="fallback-block my-4"
      dangerouslySetInnerHTML={{ __html: htmlContent }}
    />
  );
```

这种策略确保即使某些区块没有对应的专用React组件，内容也能正确显示。

## 高级应用

### 1. 嵌套区块处理

许多区块（如列、组等）包含子区块，需要递归地渲染它们：

```tsx
// 示例：处理嵌套区块
const ColumnsBlock: React.FC<ColumnsBlockProps> = ({ attributes, innerBlocks }) => {
  return (
    <div className="columns-block grid grid-cols-12 gap-4">
      {innerBlocks && innerBlocks.length > 0 && (
        <BlockRenderer blocks={innerBlocks} />
      )}
    </div>
  );
};
```

### 2. 区块数据变换

有时候需要对区块数据进行处理或转换再渲染：

```tsx
// 示例：处理Image区块的响应式尺寸
const ImageBlock: React.FC<ImageBlockProps> = ({ attributes }) => {
  const { url, alt, caption, width, height } = attributes;
  
  // 计算响应式尺寸
  const responsiveSizes = calculateResponsiveSizes(width, height);
  
  return (
    <figure>
      <img 
        src={url} 
        alt={alt || ''} 
        {...responsiveSizes}
        className="max-w-full h-auto"
      />
      {caption && <figcaption>{caption}</figcaption>}
    </figure>
  );
};
```

### 3. 自定义区块与核心区块集成

当你有自定义区块时，可以与核心区块共享相同的渲染架构：

```tsx
// 在BlockRenderer中添加自定义区块支持
switch (__typename) {
  // 核心区块...
  
  // 自定义区块
  case 'AcmeFeatureBlock':
    return <FeatureBlock key={index} attributes={attributes} />;
  case 'AcmeTestimonialBlock':
    return <TestimonialBlock key={index} attributes={attributes} />;
    
  default:
    // 回退策略...
}
``` 