<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Future Decade - 数据查询与缓存标准 V1.4.2</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3, h4 {
      color: #2c3e50;
      margin-top: 1.5em;
    }
    h1 {
      border-bottom: 2px solid #3498db;
      padding-bottom: 10px;
    }
    h2 {
      border-bottom: 1px solid #eee;
      padding-bottom: 5px;
    }
    pre {
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 15px;
      overflow-x: auto;
      font-family: 'Courier New', Courier, monospace;
    }
    code {
      font-family: 'Courier New', Courier, monospace;
      background-color: #f8f9fa;
      padding: 2px 4px;
      border-radius: 3px;
    }
    .note {
      background-color: #e8f4f8;
      border-left: 4px solid #3498db;
      padding: 10px 15px;
      margin: 15px 0;
    }
    .warning {
      background-color: #fff3cd;
      border-left: 4px solid #ffc107;
      padding: 10px 15px;
      margin: 15px 0;
    }
    .error {
      background-color: #f8d7da;
      border-left: 4px solid #dc3545;
      padding: 10px 15px;
      margin: 15px 0;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 15px 0;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .toc {
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 15px;
      margin: 20px 0;
    }
    .toc ul {
      list-style-type: none;
      padding-left: 20px;
    }
    .toc li {
      margin: 5px 0;
    }
    .toc a {
      text-decoration: none;
      color: #3498db;
    }
    .toc a:hover {
      text-decoration: underline;
    }
    @media print {
      body {
        font-size: 12pt;
      }
      pre {
        white-space: pre-wrap;
        word-wrap: break-word;
      }
      .toc {
        page-break-after: avoid;
      }
      h1, h2, h3, h4 {
        page-break-after: avoid;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>Future Decade - 数据查询与缓存标准 V1.4.2</h1>
      <p>本文档详细说明了Future Decade项目中的数据查询、缓存标准和错误处理机制。</p>
    </header>

    <div class="toc">
      <h2>目录</h2>
      <ol>
        <li><a href="#apollo-client">Apollo Client 配置</a></li>
        <li><a href="#query-standards">数据查询标准</a></li>
        <li><a href="#cache-standards">缓存标准</a></li>
        <li><a href="#error-handling">错误处理机制</a></li>
        <li><a href="#best-practices">最佳实践</a></li>
        <li><a href="#examples">示例</a></li>
      </ol>
    </div>

    <section id="apollo-client">
      <h2>1. Apollo Client 配置</h2>
      
      <h3>1.1 基础配置</h3>
      <pre><code>import { ApolloClient, InMemoryCache, createHttpLink, from } from '@apollo/client';
import { onError } from '@apollo/client/link/error';
import { setContext } from '@apollo/client/link/context';

// 创建HTTP链接
const httpLink = createHttpLink({
  uri: process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'https://api.futuredecade.com/graphql',
  credentials: 'same-origin',
});

// 错误处理链接
const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (graphQLErrors) {
    for (const err of graphQLErrors) {
      console.error(`[GraphQL error]: Message: ${err.message}, Location: ${err.locations}, Path: ${err.path}`);
      
      // 处理特定错误类型
      if (err.extensions?.code === 'UNAUTHENTICATED') {
        // 处理认证错误
        return forward(operation);
      }
    }
  }
  
  if (networkError) {
    console.error(`[Network error]: ${networkError}`);
  }
  
  return forward(operation);
});

// 认证链接
const authLink = setContext((_, { headers }) => {
  // 从localStorage获取token
  const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
  
  // 返回headers到上下文
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : '',
    }
  };
});

// 创建Apollo Client实例
const client = new ApolloClient({
  link: from([errorLink, authLink, httpLink]),
  cache: new InMemoryCache({
    typePolicies: {
      Query: {
        fields: {
          posts: {
            keyArgs: ['where'],
            merge(existing = { nodes: [], pageInfo: {} }, incoming) {
              return {
                ...incoming,
                nodes: [...(existing.nodes || []), ...(incoming.nodes || [])],
                pageInfo: incoming.pageInfo
              };
            }
          },
          // 其他查询的合并策略...
        }
      }
    }
  }),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'cache-and-network',
      errorPolicy: 'all',
      notifyOnNetworkStatusChange: true,
    },
    query: {
      fetchPolicy: 'cache-first',
      errorPolicy: 'all',
    },
    mutate: {
      errorPolicy: 'all',
    },
  },
});

export default client;</code></pre>
      
      <h3>1.2 缓存配置</h3>
      <p>Apollo Client使用InMemoryCache来缓存查询结果，通过typePolicies可以自定义不同类型数据的缓存行为。</p>
      <pre><code>const cache = new InMemoryCache({
  typePolicies: {
    Query: {
      fields: {
        // 自定义查询字段的缓存策略
        posts: {
          keyArgs: ['where'], // 用于区分不同查询的参数
          merge(existing = { nodes: [], pageInfo: {} }, incoming) {
            // 合并现有缓存和新数据
            return {
              ...incoming,
              nodes: [...(existing.nodes || []), ...(incoming.nodes || [])],
              pageInfo: incoming.pageInfo
            };
          }
        },
        // 其他查询字段...
      }
    },
    // 其他类型...
  }
});</code></pre>
      
      <h3>1.3 查询策略</h3>
      <p>Apollo Client提供了多种查询策略，可以根据不同场景选择合适的策略：</p>
      <table>
        <tr>
          <th>策略</th>
          <th>描述</th>
          <th>适用场景</th>
        </tr>
        <tr>
          <td><code>cache-first</code></td>
          <td>优先使用缓存数据，如果缓存中没有则发起网络请求</td>
          <td>静态数据，不经常变化的内容</td>
        </tr>
        <tr>
          <td><code>cache-and-network</code></td>
          <td>同时返回缓存数据和发起网络请求，网络请求完成后更新UI</td>
          <td>需要快速显示数据但又希望保持数据最新的场景</td>
        </tr>
        <tr>
          <td><code>network-only</code></td>
          <td>忽略缓存，始终发起网络请求</td>
          <td>需要实时数据的场景，如表单提交后的数据刷新</td>
        </tr>
        <tr>
          <td><code>cache-only</code></td>
          <td>只使用缓存数据，不发起网络请求</td>
          <td>离线应用或性能优化场景</td>
        </tr>
        <tr>
          <td><code>no-cache</code></td>
          <td>不缓存结果，每次查询都发起网络请求</td>
          <td>临时数据或不需要缓存的数据</td>
        </tr>
      </table>
    </section>

    <section id="query-standards">
      <h2>2. 数据查询标准</h2>
      <p>在Future Decade项目中，我们使用自定义Hook来封装GraphQL查询，确保查询逻辑的一致性和可重用性。</p>
      
      <h3>2.1 查询Hook命名规范</h3>
      <p>所有查询Hook应遵循以下命名规范：</p>
      <ul>
        <li>使用<code>use</code>前缀</li>
        <li>使用驼峰命名法</li>
        <li>名称应清晰表达查询的目的</li>
      </ul>
      <p>例如：<code>usePosts</code>、<code>usePostBySlug</code>、<code>useCategories</code></p>
      
      <h3>2.2 查询Hook结构</h3>
      <pre><code>import { useQuery } from '@apollo/client';
import { GET_POSTS } from '@/lib/graphql/queries';
import { PostConnection } from '@/types/graphql';

interface UsePostsOptions {
  first?: number;
  after?: string;
  categoryName?: string;
  tagName?: string;
  authorName?: string;
  search?: string;
}

export function usePosts(options: UsePostsOptions = {}) {
  const { first = 10, after, categoryName, tagName, authorName, search } = options;
  
  // 构建查询变量
  const variables = {
    first,
    after,
    where: {
      ...(categoryName && { categoryName }),
      ...(tagName && { tagName }),
      ...(authorName && { authorName }),
      ...(search && { search })
    }
  };
  
  // 执行查询
  const { data, loading, error, fetchMore, refetch } = useQuery(GET_POSTS, {
    variables,
    notifyOnNetworkStatusChange: true,
  });
  
  // 处理分页加载更多
  const loadMore = () => {
    if (data?.posts?.pageInfo?.hasNextPage) {
      return fetchMore({
        variables: {
          ...variables,
          after: data.posts.pageInfo.endCursor,
        },
      });
    }
    return Promise.resolve();
  };
  
  // 返回处理后的数据和方法
  return {
    posts: data?.posts?.nodes || [],
    pageInfo: data?.posts?.pageInfo || {},
    loading,
    error,
    loadMore,
    refetch,
    hasMore: data?.posts?.pageInfo?.hasNextPage || false,
  };
}</code></pre>
      
      <h3>2.3 查询参数处理</h3>
      <p>查询参数应遵循以下标准：</p>
      <ul>
        <li>使用TypeScript接口定义参数类型</li>
        <li>提供合理的默认值</li>
        <li>对可选参数使用条件展开运算符</li>
        <li>参数名称应与GraphQL API保持一致</li>
      </ul>
      
      <h3>2.4 查询结果处理</h3>
      <p>查询结果应遵循以下标准：</p>
      <ul>
        <li>提供默认空值，避免空引用错误</li>
        <li>返回处理后的数据，而不是原始查询结果</li>
        <li>包含加载状态、错误信息和分页信息</li>
        <li>提供便捷方法，如<code>loadMore</code>和<code>refetch</code></li>
      </ul>
    </section>

    <section id="cache-standards">
      <h2>3. 缓存标准</h2>
      <p>Future Decade项目使用Apollo Client的缓存机制来优化数据获取和用户体验。</p>
      
      <h3>3.1 缓存工具</h3>
      <p>我们提供了一套缓存工具函数，用于常见的缓存操作：</p>
      <pre><code>import { InMemoryCache, Reference, StoreObject } from '@apollo/client';

/**
 * 缓存标识符类型
 */
export interface CacheIdentifier {
  __typename: string;
  id: string | number;
}

/**
 * 缓存查询结果类型
 */
export interface QueryResult {
  [key: string]: {
    nodes: Array<any>;
    [key: string]: any;
  };
}

/**
 * 获取标准化的缓存ID
 */
export function getCacheId(typename: string, id: string | number): string {
  return `${typename}:${id}`;
}

/**
 * 从Apollo缓存中读取指定的对象
 */
export function readFromCache<T extends StoreObject>(
  cache: InMemoryCache,
  typename: string,
  id: string | number
): any {
  const cacheId = getCacheId(typename, id);
  return cache.readFragment<T>({
    id: cacheId,
    fragment: { kind: 'Document' } as any,
    fragmentName: typename
  });
}

/**
 * 更新缓存中的单个对象
 */
export function updateCacheObject<T extends StoreObject>(
  cache: InMemoryCache,
  typename: string,
  id: string | number,
  data: Partial<T>
): void {
  const cacheId = getCacheId(typename, id);
  
  // 读取缓存的当前值
  const cachedObject = cache.readFragment<T>({
    id: cacheId,
    fragment: { kind: 'Document' } as any,
    fragmentName: typename
  });

  if (cachedObject) {
    // 创建合并数据对象
    const mergedData = {
      ...cachedObject,
      ...data
    };
    
    // 更新缓存对象
    cache.writeFragment({
      id: cacheId,
      fragment: { kind: 'Document' } as any,
      fragmentName: typename,
      data: mergedData as any
    });
  }
}

/**
 * 从列表缓存中添加一个项目
 */
export function addItemToCache<T extends CacheIdentifier>(
  cache: InMemoryCache,
  queryName: string,
  newItem: T,
  variables?: Record<string, any>
): void {
  try {
    // 尝试读取当前缓存的查询结果
    const queryData = cache.readQuery({
      query: { kind: 'Document' } as any,
      variables
    }) as Record<string, any>;

    if (queryData && queryData[queryName]?.nodes) {
      // 检查项目是否已存在
      const exists = queryData[queryName].nodes.some(
        (item: CacheIdentifier) => item.id === newItem.id
      );

      if (!exists) {
        // 准备新的缓存数据
        const newData = {
          ...queryData,
          [queryName]: {
            ...queryData[queryName],
            nodes: [newItem, ...queryData[queryName].nodes]
          }
        };
        
        // 向缓存添加新项目
        cache.writeQuery({
          query: { kind: 'Document' } as any,
          variables,
          data: newData as any
        });
      }
    }
  } catch (e) {
    console.error('添加项目到缓存时出错:', e);
  }
}

/**
 * 从列表缓存中删除一个项目
 */
export function removeItemFromCache(
  cache: InMemoryCache,
  queryName: string,
  itemId: string | number,
  variables?: Record<string, any>
): void {
  try {
    // 尝试读取当前缓存的查询结果
    const queryData = cache.readQuery({
      query: { kind: 'Document' } as any,
      variables
    }) as Record<string, any>;

    if (queryData && queryData[queryName]?.nodes) {
      // 过滤掉要删除的项目
      const filteredNodes = queryData[queryName].nodes.filter(
        (item: CacheIdentifier) => item.id !== itemId
      );

      // 准备新的缓存数据
      const newData = {
        ...queryData,
        [queryName]: {
          ...queryData[queryName],
          nodes: filteredNodes
        }
      };
      
      // 更新缓存
      cache.writeQuery({
        query: { kind: 'Document' } as any,
        variables,
        data: newData as any
      });
    }
  } catch (e) {
    console.error('从缓存删除项目时出错:', e);
  }
}

/**
 * 无效化缓存中的查询
 */
export function invalidateQuery(
  cache: InMemoryCache,
  queryName: string,
  variables?: Record<string, any>
): void {
  try {
    cache.evict({ 
      fieldName: queryName,
      args: variables
    });
    cache.gc();
  } catch (e) {
    console.error('无效化查询时出错:', e);
  }
}</code></pre>
      
      <h3>3.2 缓存更新策略</h3>
      <p>在Future Decade项目中，我们采用以下缓存更新策略：</p>
      <ul>
        <li><strong>乐观更新</strong>：在等待服务器响应之前，先更新本地缓存，提升用户体验</li>
        <li><strong>缓存合并</strong>：对于分页查询，合并新旧数据而不是替换</li>
        <li><strong>缓存失效</strong>：当数据可能已过期时，使用<code>invalidateQuery</code>函数使缓存失效</li>
      </ul>
      
      <h3>3.3 缓存使用示例</h3>
      <pre><code>import { useMutation, useApolloClient } from '@apollo/client';
import { LIKE_POST } from '@/lib/graphql/mutations';
import { updateCacheObject } from '@/utils/cache-utils';

export function useLikePost() {
  const client = useApolloClient();
  
  const [likePost, { loading }] = useMutation(LIKE_POST, {
    onCompleted: (data) => {
      // 更新缓存中的帖子数据
      updateCacheObject(
        client.cache,
        'Post',
        data.likePost.post.id,
        {
          likeCount: data.likePost.post.likeCount,
          isLiked: true
        }
      );
    }
  });
  
  const handleLike = (postId: string) => {
    return likePost({
      variables: { id: postId },
      optimisticResponse: {
        likePost: {
          __typename: 'LikePostPayload',
          post: {
            __typename: 'Post',
            id: postId,
            likeCount: 0, // 将在服务器响应后更新
            isLiked: true
          }
        }
      }
    });
  };
  
  return {
    likePost: handleLike,
    loading
  };
}</code></pre>
    </section>

    <section id="error-handling">
      <h2>4. 错误处理机制</h2>
      <p>Future Decade项目实现了全面的错误处理机制，确保应用程序在各种错误情况下都能提供良好的用户体验。</p>
      
      <h3>4.1 错误类型</h3>
      <p>我们处理的错误类型包括：</p>
      <ul>
        <li><strong>GraphQL错误</strong>：由服务器返回的查询或变更错误</li>
        <li><strong>网络错误</strong>：连接问题、超时等</li>
        <li><strong>认证错误</strong>：未授权、令牌过期等</li>
        <li><strong>验证错误</strong>：输入数据不符合要求</li>
      </ul>
      
      <h3>4.2 错误处理工具</h3>
      <pre><code>import { ApolloError } from '@apollo/client';

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  GRAPHQL = 'GRAPHQL',
  AUTHENTICATION = 'AUTHENTICATION',
  VALIDATION = 'VALIDATION',
  UNKNOWN = 'UNKNOWN'
}

/**
 * 错误详情接口
 */
export interface ErrorDetails {
  type: ErrorType;
  message: string;
  code?: string;
  path?: string[];
  originalError?: any;
}

/**
 * 解析Apollo错误
 */
export function parseApolloError(error: ApolloError): ErrorDetails {
  // 网络错误
  if (error.networkError) {
    return {
      type: ErrorType.NETWORK,
      message: error.networkError.message || '网络连接错误',
      originalError: error.networkError
    };
  }
  
  // GraphQL错误
  if (error.graphQLErrors && error.graphQLErrors.length > 0) {
    const graphQLError = error.graphQLErrors[0];
    
    // 认证错误
    if (graphQLError.extensions?.code === 'UNAUTHENTICATED') {
      return {
        type: ErrorType.AUTHENTICATION,
        message: '请先登录',
        code: graphQLError.extensions.code,
        path: graphQLError.path,
        originalError: graphQLError
      };
    }
    
    // 其他GraphQL错误
    return {
      type: ErrorType.GRAPHQL,
      message: graphQLError.message,
      code: graphQLError.extensions?.code,
      path: graphQLError.path,
      originalError: graphQLError
    };
  }
  
  // 未知错误
  return {
    type: ErrorType.UNKNOWN,
    message: error.message || '发生未知错误',
    originalError: error
  };
}

/**
 * 获取用户友好的错误消息
 */
export function getUserFriendlyErrorMessage(error: ErrorDetails): string {
  switch (error.type) {
    case ErrorType.NETWORK:
      return '网络连接失败，请检查您的网络连接并重试';
    case ErrorType.AUTHENTICATION:
      return '您的登录已过期，请重新登录';
    case ErrorType.GRAPHQL:
      return error.message || '服务器返回错误，请稍后重试';
    case ErrorType.VALIDATION:
      return error.message || '输入数据无效，请检查并重试';
    default:
      return '发生未知错误，请稍后重试';
  }
}</code></pre>
      
      <h3>4.3 错误处理Hook</h3>
      <pre><code>import { useState, useCallback } from 'react';
import { ApolloError } from '@apollo/client';
import { ErrorDetails, parseApolloError, getUserFriendlyErrorMessage } from '@/utils/error-handler';

export function useErrorHandler() {
  const [error, setError] = useState<ErrorDetails | null>(null);
  
  const handleError = useCallback((error: ApolloError | Error | unknown) => {
    let errorDetails: ErrorDetails;
    
    if (error instanceof ApolloError) {
      errorDetails = parseApolloError(error);
    } else if (error instanceof Error) {
      errorDetails = {
        type: ErrorType.UNKNOWN,
        message: error.message,
        originalError: error
      };
    } else {
      errorDetails = {
        type: ErrorType.UNKNOWN,
        message: '发生未知错误',
        originalError: error
      };
    }
    
    setError(errorDetails);
    return errorDetails;
  }, []);
  
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  const getErrorMessage = useCallback(() => {
    return error ? getUserFriendlyErrorMessage(error) : '';
  }, [error]);
  
  return {
    error,
    handleError,
    clearError,
    getErrorMessage
  };
}</code></pre>
      
      <h3>4.4 错误处理示例</h3>
      <pre><code>import { useQuery } from '@apollo/client';
import { GET_POSTS } from '@/lib/graphql/queries';
import { useErrorHandler } from '@/hooks/useErrorHandler';

export function usePostsWithErrorHandling(options = {}) {
  const { handleError, error, getErrorMessage } = useErrorHandler();
  
  const { data, loading, refetch } = useQuery(GET_POSTS, {
    variables: options,
    onError: (error) => {
      handleError(error);
    }
  });
  
  return {
    posts: data?.posts?.nodes || [],
    loading,
    error,
    errorMessage: getErrorMessage(),
    refetch
  };
}</code></pre>
    </section>

    <section id="best-practices">
      <h2>5. 最佳实践</h2>
      <p>以下是Future Decade项目中数据查询和缓存的最佳实践：</p>
      
      <h3>5.1 查询最佳实践</h3>
      <ul>
        <li>使用自定义Hook封装查询逻辑，提高代码复用性</li>
        <li>为查询提供合理的默认值和类型定义</li>
        <li>使用<code>fetchPolicy</code>控制数据获取策略</li>
        <li>对于大型查询，考虑使用分页加载</li>
        <li>使用<code>notifyOnNetworkStatusChange</code>跟踪加载状态变化</li>
      </ul>
      
      <h3>5.2 缓存最佳实践</h3>
      <ul>
        <li>为不同类型的数据定义适当的缓存策略</li>
        <li>使用<code>typePolicies</code>自定义缓存行为</li>
        <li>在变更操作后更新相关缓存</li>
        <li>使用乐观更新提升用户体验</li>
        <li>定期清理不再需要的缓存数据</li>
      </ul>
      
      <h3>5.3 错误处理最佳实践</h3>
      <ul>
        <li>使用统一的错误处理机制</li>
        <li>为用户提供友好的错误消息</li>
        <li>记录错误以便调试和分析</li>
        <li>为不同类型的错误提供不同的处理策略</li>
        <li>在关键操作中实现重试机制</li>
      </ul>
    </section>

    <section id="examples">
      <h2>6. 示例</h2>
      <p>以下是Future Decade项目中数据查询、缓存和错误处理的完整示例：</p>
      
      <h3>6.1 文章列表查询与缓存</h3>
      <pre><code>import { useQuery } from '@apollo/client';
import { GET_POSTS } from '@/lib/graphql/queries';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { PostConnection } from '@/types/graphql';

interface UsePostsOptions {
  first?: number;
  after?: string;
  categoryName?: string;
  tagName?: string;
  authorName?: string;
  search?: string;
}

export function usePosts(options: UsePostsOptions = {}) {
  const { handleError, error, getErrorMessage } = useErrorHandler();
  const { first = 10, after, categoryName, tagName, authorName, search } = options;
  
  // 构建查询变量
  const variables = {
    first,
    after,
    where: {
      ...(categoryName && { categoryName }),
      ...(tagName && { tagName }),
      ...(authorName && { authorName }),
      ...(search && { search })
    }
  };
  
  // 执行查询
  const { data, loading, fetchMore, refetch } = useQuery(GET_POSTS, {
    variables,
    notifyOnNetworkStatusChange: true,
    onError: (error) => {
      handleError(error);
    }
  });
  
  // 处理分页加载更多
  const loadMore = () => {
    if (data?.posts?.pageInfo?.hasNextPage) {
      return fetchMore({
        variables: {
          ...variables,
          after: data.posts.pageInfo.endCursor,
        },
      });
    }
    return Promise.resolve();
  };
  
  // 返回处理后的数据和方法
  return {
    posts: data?.posts?.nodes || [],
    pageInfo: data?.posts?.pageInfo || {},
    loading,
    error,
    errorMessage: getErrorMessage(),
    loadMore,
    refetch,
    hasMore: data?.posts?.pageInfo?.hasNextPage || false,
  };
}</code></pre>
      
      <h3>6.2 文章点赞与缓存更新</h3>
      <pre><code>import { useMutation, useApolloClient } from '@apollo/client';
import { LIKE_POST } from '@/lib/graphql/mutations';
import { updateCacheObject } from '@/utils/cache-utils';
import { useErrorHandler } from '@/hooks/useErrorHandler';

export function useLikePost() {
  const client = useApolloClient();
  const { handleError, error, getErrorMessage } = useErrorHandler();
  
  const [likePost, { loading }] = useMutation(LIKE_POST, {
    onCompleted: (data) => {
      // 更新缓存中的帖子数据
      updateCacheObject(
        client.cache,
        'Post',
        data.likePost.post.id,
        {
          likeCount: data.likePost.post.likeCount,
          isLiked: true
        }
      );
    },
    onError: (error) => {
      handleError(error);
    }
  });
  
  const handleLike = (postId: string) => {
    return likePost({
      variables: { id: postId },
      optimisticResponse: {
        likePost: {
          __typename: 'LikePostPayload',
          post: {
            __typename: 'Post',
            id: postId,
            likeCount: 0, // 将在服务器响应后更新
            isLiked: true
          }
        }
      }
    });
  };
  
  return {
    likePost: handleLike,
    loading,
    error,
    errorMessage: getErrorMessage()
  };
}</code></pre>
      
      <h3>6.3 评论创建与缓存更新</h3>
      <pre><code>import { useMutation, useApolloClient } from '@apollo/client';
import { CREATE_COMMENT } from '@/lib/graphql/mutations';
import { addItemToCache } from '@/utils/cache-utils';
import { useErrorHandler } from '@/hooks/useErrorHandler';

export function useCreateComment() {
  const client = useApolloClient();
  const { handleError, error, getErrorMessage } = useErrorHandler();
  
  const [createComment, { loading }] = useMutation(CREATE_COMMENT, {
    onCompleted: (data) => {
      // 将新评论添加到缓存中
      addItemToCache(
        client.cache,
        'comments',
        data.createComment.comment,
        { postId: data.createComment.comment.postId }
      );
    },
    onError: (error) => {
      handleError(error);
    }
  });
  
  const handleCreateComment = (postId: string, content: string) => {
    return createComment({
      variables: { 
        postId,
        content
      }
    });
  };
  
  return {
    createComment: handleCreateComment,
    loading,
    error,
    errorMessage: getErrorMessage()
  };
}</code></pre>
    </section>
  </div>
</body>
</html> 