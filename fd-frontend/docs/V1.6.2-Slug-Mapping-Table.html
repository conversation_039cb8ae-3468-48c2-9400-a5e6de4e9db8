<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V1.6.2 Slug映射表集成 - 前端开发文档</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            border-bottom: 2px solid #3182ce;
            padding-bottom: 10px;
            color: #2c5282;
        }
        h2 {
            margin-top: 30px;
            color: #2b6cb0;
            border-bottom: 1px solid #cbd5e0;
            padding-bottom: 5px;
        }
        h3 {
            margin-top: 25px;
            color: #3182ce;
        }
        code {
            background-color: #f7fafc;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
            font-size: 0.9em;
            color: #e53e3e;
        }
        pre {
            background-color: #f7fafc;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #edf2f7;
        }
        pre code {
            padding: 0;
            background-color: transparent;
            color: #2d3748;
        }
        .note {
            background-color: #ebf8ff;
            border-left: 4px solid #4299e1;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        .warning {
            background-color: #fff5f5;
            border-left: 4px solid #f56565;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #e2e8f0;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f7fafc;
            font-weight: 600;
        }
        tr:nth-child(even) {
            background-color: #f9fafb;
        }
        .method {
            background-color: #f7fafc;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            border: 1px solid #e2e8f0;
        }
        a {
            color: #3182ce;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        .tab-container {
            border: 1px solid #e2e8f0;
            border-radius: 5px;
            overflow: hidden;
            margin: 25px 0;
        }
        .tab-menu {
            display: flex;
            background-color: #f7fafc;
            border-bottom: 1px solid #e2e8f0;
        }
        .tab-button {
            padding: 10px 15px;
            border: none;
            background: none;
            cursor: pointer;
            font-weight: 500;
            color: #4a5568;
        }
        .tab-button.active {
            background-color: white;
            border-bottom: 2px solid #3182ce;
            color: #3182ce;
        }
    </style>
</head>
<body>
    <h1>V1.6.2 Slug映射表集成</h1>
    <p>本文档描述了前端应用与WordPress后端Slug映射表功能的集成，用于解决无前缀环境下的路由识别问题。</p>
    
    <h2>目录</h2>
    <ul>
        <li><a href="#introduction">功能介绍</a></li>
        <li><a href="#implementation">实现方案</a>
            <ul>
                <li><a href="#hook-implementation">Hook实现</a></li>
                <li><a href="#graphql-queries">GraphQL查询</a></li>
            </ul>
        </li>
        <li><a href="#route-test-page">路由测试页面</a>
            <ul>
                <li><a href="#url-info">URL信息标签页</a></li>
                <li><a href="#prefix-settings">路由前缀设置标签页</a></li>
                <li><a href="#slug-mapping">Slug映射表标签页</a></li>
                <li><a href="#uuid-test">文章UUID测试标签页</a></li>
            </ul>
        </li>
        <li><a href="#usage">使用方法</a></li>
        <li><a href="#examples">代码示例</a></li>
    </ul>
    
    <h2 id="introduction">功能介绍</h2>
    <p>Slug映射表功能是为了解决headless WordPress前端应用中的路由识别问题而设计的。当分类前缀和标签前缀同时为空时，系统无法直接判断单级路径（如<code>/news</code>、<code>/wordpress</code>）应该解析为什么类型的内容。</p>
    
    <p>此功能通过维护一个映射表，记录所有slug及其对应的内容类型，实现了精确的路由识别，使前端可以正确地处理无前缀的URL路径。</p>
    
    <div class="note">
        <strong>注意：</strong> 此功能解决了一级路径（如<code>/about</code>、<code>/news</code>）到内容类型的映射问题，适用于分类前缀和标签前缀为空的情况。
    </div>
    
    <h3>支持的内容类型</h3>
    <p>Slug映射表支持以下内容类型：</p>
    <ul>
        <li><strong>页面（page）</strong>：WordPress页面，如<code>/about</code></li>
        <li><strong>分类（category）</strong>：分类页面，如<code>/news</code></li>
        <li><strong>标签（tag）</strong>：标签页面，如<code>/wordpress</code></li>
        <li><strong>自定义分类法（taxonomy_archive）</strong>：自定义分类法归档页</li>
        <li><strong>自定义文章类型（post_type）</strong>：自定义文章类型归档页</li>
    </ul>
    
    <h2 id="implementation">实现方案</h2>
    
    <h3 id="hook-implementation">Hook实现</h3>
    <p>我们创建了一个<code>useSlugMappingTable</code> hook用于获取完整的slug映射表：</p>
    
    <pre><code>import { useQuery } from '@apollo/client';
import { GET_SLUG_MAPPING_TABLE } from '@/lib/graphql/queries';

// 定义类型
export interface SlugMappingItem {
  slug: string;
  type: string;  // 'page' | 'category' | 'tag' | 'taxonomy_archive' | 'post_type'
  id: string;
  taxonomy?: string;
}

interface UseSlugMappingTableResult {
  mappings: SlugMappingItem[];
  loading: boolean;
  error: Error | null;
  refetch: () => void;
}

/**
 * 获取完整的 slug 映射表的钩子
 * 
 * @returns {UseSlugMappingTableResult} 包含映射表数据、加载状态和错误信息的对象
 */
export function useSlugMappingTable(): UseSlugMappingTableResult {
  // 使用 Apollo Client 执行查询
  const { data, loading, error, refetch } = useQuery(GET_SLUG_MAPPING_TABLE, {
    fetchPolicy: 'cache-and-network',
  });

  // 处理并返回数据
  return {
    mappings: data?.slugMappingTable || [],
    loading,
    error: error || null,
    refetch,
  };
}</code></pre>
    
    <h3 id="graphql-queries">GraphQL查询</h3>
    <p>用于获取Slug映射表的GraphQL查询：</p>
    
    <pre><code>// 获取Slug映射表
export const GET_SLUG_MAPPING_TABLE = gql`
  query GetSlugMappingTable {
    slugMappingTable {
      slug
      type
      id
      taxonomy
    }
  }
`;</code></pre>
    
    <h2 id="route-test-page">路由测试页面</h2>
    <p>我们重新设计了路由测试页面，采用标签页（Tab）形式整合了不同的测试功能：</p>
    
    <h3 id="url-info">URL信息标签页</h3>
    <p>显示当前URL信息和路径段：</p>
    <pre><code>const renderUrlInfoTab = () => (
  &lt;div className="bg-white p-4 rounded-lg shadow"&gt;
    &lt;h2 className="text-xl font-medium mb-4"&gt;当前URL信息&lt;/h2&gt;
    &lt;div className="mb-2"&gt;
      &lt;span className="font-medium"&gt;完整URL:&lt;/span&gt; {urlInfo.currentUrl}
    &lt;/div&gt;
    &lt;div&gt;
      &lt;span className="font-medium"&gt;路径段:&lt;/span&gt;{" "}
      {urlInfo.pathSegments.map((segment, index) =&gt; (
        &lt;span key={index} className="inline-block bg-gray-100 px-2 py-1 rounded mr-2"&gt;
          {segment}
        &lt;/span&gt;
      ))}
    &lt;/div&gt;
  &lt;/div&gt;
);</code></pre>
    
    <h3 id="prefix-settings">路由前缀设置标签页</h3>
    <p>显示当前路由前缀设置：</p>
    <pre><code>const renderPrefixesTab = () => (
  &lt;div className="bg-white p-4 rounded-lg shadow"&gt;
    &lt;h2 className="text-xl font-medium mb-4"&gt;路由前缀设置&lt;/h2&gt;
    
    {prefixesLoading ? (
      &lt;div className="p-4"&gt;加载路由设置中...&lt;/div&gt;
    ) : prefixesError ? (
      &lt;div className="p-4 text-red-500"&gt;
        &lt;h2 className="text-xl font-bold mb-2"&gt;加载设置出错&lt;/h2&gt;
        &lt;p&gt;{prefixesError.message}&lt;/p&gt;
      &lt;/div&gt;
    ) : (
      // 显示前缀设置的代码...
    )}
  &lt;/div&gt;
);</code></pre>
    
    <h3 id="slug-mapping">Slug映射表标签页</h3>
    <p>显示从后端获取的完整Slug映射表：</p>
    <pre><code>const renderSlugMapTab = () => (
  &lt;div className="bg-white p-4 rounded-lg shadow"&gt;
    &lt;h2 className="text-xl font-medium mb-4"&gt;Slug映射表测试&lt;/h2&gt;
    
    {mappingsLoading ? (
      &lt;div className="p-4"&gt;加载Slug映射表中...&lt;/div&gt;
    ) : mappingsError ? (
      &lt;div className="p-4 text-red-500"&gt;
        &lt;h2 className="text-xl font-bold mb-2"&gt;加载映射表出错&lt;/h2&gt;
        &lt;p&gt;{mappingsError.message}&lt;/p&gt;
      &lt;/div&gt;
    ) : (
      // 显示映射表的代码...
    )}
  &lt;/div&gt;
);</code></pre>
    
    <h3 id="uuid-test">文章UUID测试标签页</h3>
    <p>测试通过UUID查询文章的功能：</p>
    <pre><code>const renderUuidTestTab = () => (
  &lt;div className="bg-white p-4 rounded-lg shadow"&gt;
    &lt;h2 className="text-xl font-medium mb-4"&gt;文章路由 UUID 测试&lt;/h2&gt;
    
    &lt;div className="mb-6"&gt;
      &lt;div className="flex space-x-4 items-center"&gt;
        &lt;div className="flex-grow"&gt;
          &lt;label htmlFor="uuid" className="block text-sm font-medium text-gray-700 mb-1"&gt;
            文章UUID
          &lt;/label&gt;
          &lt;input
            type="text"
            id="uuid"
            value={uuid}
            onChange={(e) =&gt; setUuid(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="输入文章UUID"
          /&gt;
        &lt;/div&gt;
        &lt;button
          onClick={runPostRouteTests}
          className="mt-5 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md"
        &gt;
          运行测试
        &lt;/button&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    // 测试结果显示代码...
  &lt;/div&gt;
);</code></pre>
    
    <h2 id="usage">使用方法</h2>
    <p>在前端应用中使用Slug映射表功能的主要步骤：</p>
    
    <ol>
        <li>
            <strong>导入Hook：</strong>
            <pre><code>import { useSlugMappingTable } from '@/hooks';</code></pre>
        </li>
        <li>
            <strong>在组件中使用：</strong>
            <pre><code>const { mappings, loading, error } = useSlugMappingTable();</code></pre>
        </li>
        <li>
            <strong>解析路径：</strong>
            <pre><code>// 查找映射表中是否有匹配的slug
const pathSlug = 'news';
const mapping = mappings.find(item => item.slug === pathSlug);

if (mapping) {
  switch (mapping.type) {
    case 'category':
      // 渲染分类页面
      return &lt;CategoryPage id={mapping.id} /&gt;;
    case 'tag':
      // 渲染标签页面
      return &lt;TagPage id={mapping.id} /&gt;;
    case 'page':
      // 渲染页面
      return &lt;SinglePage id={mapping.id} /&gt;;
    // 其他类型处理...
  }
}</code></pre>
        </li>
    </ol>
    
    <h2 id="examples">代码示例</h2>
    
    <h3>在中间件中使用映射表</h3>
    <p>以下是在Next.js中间件中使用Slug映射表的示例：</p>
    
    <pre><code>// 在服务端获取映射表
async function getSlugMappingTable() {
  // 这里通过API获取映射表
  const response = await fetch(`${process.env.WORDPRESS_API_URL}/api/slug-mapping`);
  const data = await response.json();
  return data.mappings || [];
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // 如果路径包含已知前缀，则跳过解析
  if (pathname.startsWith('/api') || pathname.startsWith('/_next')) {
    return NextResponse.next();
  }
  
  // 提取第一级路径
  const segments = pathname.split('/').filter(Boolean);
  const firstSegment = segments[0];
  
  if (!firstSegment) {
    return NextResponse.next(); // 首页，不需要解析
  }
  
  // 获取映射表
  const mappings = await getSlugMappingTable();
  
  // 查找匹配
  const mapping = mappings.find(item => item.slug === firstSegment);
  
  if (mapping) {
    // 根据映射类型重写URL或添加上下文信息
    // ...
  }
  
  return NextResponse.next();
}</code></pre>
    
    <div class="warning">
        <strong>性能注意事项：</strong> 在中间件中频繁获取映射表可能导致性能问题。考虑使用缓存或静态生成策略来减少API调用。
    </div>
    
    <h3>在页面组件中使用</h3>
    <p>在路由组件中使用slug映射表：</p>
    
    <pre><code>import { useSlugMappingTable } from '@/hooks';
import { useRouter } from 'next/router';

export default function DynamicPathHandler() {
  const router = useRouter();
  const { mappings, loading, error } = useSlugMappingTable();
  
  if (loading) return &lt;div&gt;加载中...&lt;/div&gt;;
  if (error) return &lt;div&gt;加载出错: {error.message}&lt;/div&gt;;
  
  // 获取当前路径的第一段
  const { slug } = router.query;
  const currentSlug = Array.isArray(slug) ? slug[0] : slug;
  
  // 在映射表中查找匹配
  const mapping = mappings.find(item => item.slug === currentSlug);
  
  if (!mapping) {
    return &lt;div&gt;未找到匹配的内容&lt;/div&gt;;
  }
  
  // 根据内容类型渲染不同组件
  switch (mapping.type) {
    case 'page':
      return &lt;PageRenderer id={mapping.id} /&gt;;
    case 'category':
      return &lt;CategoryRenderer id={mapping.id} /&gt;;
    case 'tag':
      return &lt;TagRenderer id={mapping.id} /&gt;;
    default:
      return &lt;div&gt;未知内容类型&lt;/div&gt;;
  }
}</code></pre>
    
    <hr>
    <footer>
        <p>文档版本：V1.6.2 - 最后更新：2023年11月</p>
        <p>如有问题，请联系开发团队</p>
    </footer>
</body>
</html> 