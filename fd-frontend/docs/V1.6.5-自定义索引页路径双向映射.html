<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FD前端文档 V1.6.5 - 自定义索引页路径双向映射</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 2px solid #eaecef;
            padding-bottom: 10px;
        }
        h2 {
            margin-top: 30px;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 8px;
        }
        h3 {
            margin-top: 25px;
        }
        code {
            background-color: #f6f8fa;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            font-size: 0.9em;
        }
        pre {
            background-color: #f6f8fa;
            padding: 16px;
            border-radius: 3px;
            overflow: auto;
        }
        pre code {
            background-color: transparent;
            padding: 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #dfe2e5;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f6f8fa;
            font-weight: 600;
        }
        .important {
            background-color: #fffacd;
            border-left: 4px solid #e6db55;
            padding: 12px;
            margin: 20px 0;
        }
        .version-badge {
            display: inline-block;
            background-color: #28a745;
            color: white;
            border-radius: 3px;
            padding: 2px 5px;
            font-size: 0.8em;
            margin-left: 8px;
            vertical-align: middle;
        }
        .method {
            background-color: #f8f9fa;
            border-radius: 3px;
            padding: 16px;
            margin: 20px 0;
        }
        .file-path {
            color: #6c757d;
            font-style: italic;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <h1>自定义索引页路径双向映射 <span class="version-badge">V1.6.5</span></h1>
    
    <p>前端应用现已支持自定义索引页路径的双向映射功能，使网站的URL结构更加灵活且用户友好。</p>
    
    <div class="important">
        <p><strong>更新日期:</strong> 2023-07-15</p>
        <p><strong>依赖版本:</strong> 需要FD主题 V1.2.8+</p>
    </div>
    
    <h2 id="summary">功能概述</h2>
    <p>在此版本中，我们增强了前端应用的URL处理能力，实现了分类索引页和标签索引页路径的双向映射。主要变更包括：</p>
    <ul>
        <li>使用中间件实现URL重写，保持用户友好的URL展示</li>
        <li>实现从标准路径（如/category-index）到自定义路径的重定向</li>
        <li>实现从自定义路径到标准路径的内部重写</li>
        <li>优化URL构建工具，确保一致的URL生成</li>
    </ul>
    
    <h2 id="feature-details">功能详情</h2>
    <p>本次更新支持以下四种URL处理场景：</p>
    
    <table>
        <thead>
            <tr>
                <th>场景</th>
                <th>用户访问URL</th>
                <th>系统行为</th>
                <th>用户最终看到的URL</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>1</td>
                <td>/category-index</td>
                <td>重定向到自定义路径（如/categories）</td>
                <td>/categories</td>
            </tr>
            <tr>
                <td>2</td>
                <td>/tag-index</td>
                <td>重定向到自定义路径（如/tags）</td>
                <td>/tags</td>
            </tr>
            <tr>
                <td>3</td>
                <td>/categories（自定义路径）</td>
                <td>内部重写到/category-index</td>
                <td>/categories（URL不变）</td>
            </tr>
            <tr>
                <td>4</td>
                <td>/tags（自定义路径）</td>
                <td>内部重写到/tag-index</td>
                <td>/tags（URL不变）</td>
            </tr>
        </tbody>
    </table>
    
    <div class="important">
        <p><strong>注意:</strong> 双向映射确保了无论用户从哪个URL进入，都会看到正确的内容，同时始终展示用户友好的URL形式。</p>
    </div>
    
    <h2 id="implementation">实现细节</h2>
    
    <h3 id="middleware">中间件实现</h3>
    <p>我们修改了Next.js中间件，实现了双向URL映射：</p>
    
    <div class="method">
        <p class="file-path">src/middleware.ts</p>
        <pre><code>/**
 * 中间件，处理路由前缀和索引页路径
 * 功能:
 * 1. 将错误的文章路径前缀重定向到正确的前缀
 * 2. 将自定义的分类索引页路径内部重写到category-index（保持用户友好URL）
 * 3. 将自定义的标签索引页路径内部重写到tag-index（保持用户友好URL）
 * 4. 将category-index重定向到自定义的分类索引页路径
 * 5. 将tag-index重定向到自定义的标签索引页路径
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const pathSegments = pathname.split('/').filter(Boolean);
  
  try {
    // 获取路由前缀和索引页路径设置
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`, 
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: `
            query GetRoutePrefixes {
              routePrefixes {
                postPrefix
                categoryIndexRoute
                tagIndexRoute
              }
            }
          `
        }),
        // 缓存结果，避免每个请求都查询
        next: { revalidate: 3600 } // 1小时缓存
      } as RequestInit
    );
    
    const data = await response.json();
    const prefixes: RoutePrefixes = data?.data?.routePrefixes 
      ? { ...DEFAULT_ROUTE_PREFIXES, ...data.data.routePrefixes }
      : DEFAULT_ROUTE_PREFIXES;
    
    // 如果已配置了自定义路径且不是默认值
    const hasCustomCategoryRoute = prefixes.categoryIndexRoute !== 'category-index';
    const hasCustomTagRoute = prefixes.tagIndexRoute !== 'tag-index';
    
    // 情况1: 用户访问 category-index，但已设置自定义路径
    if (pathSegments.length === 1 && pathSegments[0] === 'category-index' && hasCustomCategoryRoute) {
      // 重定向到自定义路径
      return NextResponse.redirect(
        new URL(`/${prefixes.categoryIndexRoute}`, request.url)
      );
    }
    
    // 情况2: 用户访问 tag-index，但已设置自定义路径
    if (pathSegments.length === 1 && pathSegments[0] === 'tag-index' && hasCustomTagRoute) {
      // 重定向到自定义路径
      return NextResponse.redirect(
        new URL(`/${prefixes.tagIndexRoute}`, request.url)
      );
    }
    
    // 情况3: 用户访问自定义的分类索引页路径
    if (pathSegments.length === 1 && pathSegments[0] === prefixes.categoryIndexRoute && hasCustomCategoryRoute) {
      // 使用重写而非重定向，保持URL不变但内部映射到category-index
      return NextResponse.rewrite(
        new URL('/category-index', request.url)
      );
    }
    
    // 情况4: 用户访问自定义的标签索引页路径
    if (pathSegments.length === 1 && pathSegments[0] === prefixes.tagIndexRoute && hasCustomTagRoute) {
      // 使用重写而非重定向，保持URL不变但内部映射到tag-index
      return NextResponse.rewrite(
        new URL('/tag-index', request.url)
      );
    }
    
    // 检查文章路径...（省略其他代码）
  } catch (error) {
    console.error('Error in middleware:', error);
  }
  
  return NextResponse.next();
}</code></pre>
    </div>
    
    <h3 id="url-builder">URL构建工具优化</h3>
    <p>优化URL构建工具，明确区分内部路由与对外展示的URL：</p>
    
    <div class="method">
        <p class="file-path">src/utils/url-builder.ts</p>
        <pre><code>/**
 * 构建分类索引页URL - 用于Next.js内部路由
 * 
 * 注意：在页面跳转时，应该使用buildPublicCategoryIndexUrl函数
 * 这个函数仅用于特殊场景下需要直接访问内部路由的情况
 * 
 * @returns 始终返回标准的内部路由路径
 */
export const buildCategoryIndexUrl = (): string => {
  return '/category-index';
};

/**
 * 构建标签索引页URL - 用于Next.js内部路由
 * 
 * 注意：在页面跳转时，应该使用buildPublicTagIndexUrl函数
 * 这个函数仅用于特殊场景下需要直接访问内部路由的情况
 * 
 * @returns 始终返回标准的内部路由路径
 */
export const buildTagIndexUrl = (): string => {
  return '/tag-index';
};

/**
 * 构建用于展示给用户的分类索引页URL
 * 所有链接和导航都应使用此函数生成分类索引页URL
 * 
 * @param prefixes 路由前缀配置
 * @returns 自定义的用户友好URL路径
 */
export const buildPublicCategoryIndexUrl = (prefixes: RoutePrefixes): string => {
  return `/${prefixes.categoryIndexRoute}`;
};

/**
 * 构建用于展示给用户的标签索引页URL
 * 所有链接和导航都应使用此函数生成标签索引页URL
 * 
 * @param prefixes 路由前缀配置
 * @returns 自定义的用户友好URL路径
 */
export const buildPublicTagIndexUrl = (prefixes: RoutePrefixes): string => {
  return `/${prefixes.tagIndexRoute}`;
};</code></pre>
    </div>
    
    <h2 id="usage">使用方法</h2>
    
    <h3 id="navigation-components">导航组件</h3>
    <p>在导航组件中，应使用公共URL构建函数生成链接：</p>
    
    <div class="method">
        <pre><code>import { useRoutePrefixes } from '@/hooks';
import { buildPublicCategoryIndexUrl, buildPublicTagIndexUrl } from '@/utils/url-builder';
import Link from 'next/link';

export function MainNavigation() {
  const { prefixes } = useRoutePrefixes();
  
  return (
    &lt;nav className="flex space-x-4"&gt;
      &lt;Link href="/"&gt;首页&lt;/Link&gt;
      
      {/* 正确：使用buildPublicCategoryIndexUrl */}
      &lt;Link href={buildPublicCategoryIndexUrl(prefixes)}&gt;分类&lt;/Link&gt;
      
      {/* 正确：使用buildPublicTagIndexUrl */}
      &lt;Link href={buildPublicTagIndexUrl(prefixes)}&gt;标签&lt;/Link&gt;
      
      {/* 错误：直接硬编码路径 */}
      {/* &lt;Link href="/category-index"&gt;分类&lt;/Link&gt; */}
      
      &lt;Link href="/about"&gt;关于我们&lt;/Link&gt;
    &lt;/nav&gt;
  );
}</code></pre>
    </div>
    
    <h3 id="detail-pages">详情页面返回链接</h3>
    <p>在分类和标签详情页中，返回链接使用自定义路径：</p>
    
    <div class="method">
        <p class="file-path">src/app/category/[slug]/page.tsx</p>
        <pre><code>{/* 返回链接 */}
&lt;div className="mt-8"&gt;
  &lt;Link 
    href={`/${prefixes.categoryIndexRoute}`}
    className="text-blue-600 hover:text-blue-800 flex items-center"
  &gt;
    ← 返回分类索引
  &lt;/Link&gt;
&lt;/div&gt;</code></pre>
    </div>
    
    <h2 id="benefits">功能优势</h2>
    
    <h3 id="seo">SEO优化</h3>
    <ul>
        <li><strong>URL一致性</strong>：确保每个内容只有一个规范URL</li>
        <li><strong>防止内容重复</strong>：避免搜索引擎检索到相同内容的多个URL</li>
        <li><strong>用户友好URL</strong>：更简洁、可读性更高的URL结构</li>
    </ul>
    
    <h3 id="ux">用户体验</h3>
    <ul>
        <li><strong>简洁URL</strong>：更易记忆的URL结构</li>
        <li><strong>无缝跳转</strong>：用户不会注意到内部路由映射</li>
        <li><strong>支持旧链接</strong>：确保旧URL仍然可用，实现平滑过渡</li>
    </ul>
    
    <h3 id="dev-exp">开发体验</h3>
    <ul>
        <li><strong>代码一致性</strong>：内部路由保持不变，仅通过中间件处理URL转换</li>
        <li><strong>配置灵活</strong>：通过后台设置即可调整URL结构，无需修改代码</li>
        <li><strong>类型安全</strong>：提供类型定义和构建函数，减少错误</li>
    </ul>
    
    <h2 id="testing">测试与验证</h2>
    <p>您可以通过以下步骤测试URL映射功能：</p>
    <ol>
        <li>在WordPress后台修改分类索引页和标签索引页的路径设置，如设置为"categories"和"tags"</li>
        <li>尝试访问以下URL，验证映射行为：
            <ul>
                <li>访问 <code>/categories</code> - 应显示分类索引页，URL保持不变</li>
                <li>访问 <code>/category-index</code> - 应重定向到 <code>/categories</code></li>
                <li>访问 <code>/tags</code> - 应显示标签索引页，URL保持不变</li>
                <li>访问 <code>/tag-index</code> - 应重定向到 <code>/tags</code></li>
            </ul>
        </li>
        <li>检查导航链接和返回链接是否使用了正确的自定义路径</li>
    </ol>
    
    <h2 id="troubleshooting">常见问题</h2>
    
    <h3>TypeScript错误</h3>
    <p>如果遇到TypeScript类型错误，请安装以下依赖：</p>
    <pre><code>npm i --save-dev @types/node @types/react next</code></pre>
    
    <h3>缓存问题</h3>
    <p>如果URL映射不生效，可能是由于缓存问题。尝试以下方法：</p>
    <ul>
        <li>清除浏览器缓存</li>
        <li>重启Next.js开发服务器</li>
        <li>检查中间件的缓存设置（revalidate值）</li>
    </ul>
    
    <h2 id="version-history">版本历史</h2>
    <h3>v1.6.5 <small>(2023-07-15)</small></h3>
    <ul>
        <li>实现自定义索引页路径的双向映射</li>
        <li>优化URL构建工具的使用方式</li>
        <li>完善SEO和用户体验</li>
    </ul>
    
    <h3>v1.6.4 <small>(2023-06-18)</small></h3>
    <ul>
        <li>添加对自定义分类索引页和标签索引页路径的支持</li>
        <li>扩展GraphQL查询和路由钩子</li>
        <li>更新导航链接使用动态路径</li>
    </ul>
</body>
</html> 