<h1>Gutenberg核心区块开发指南</h1>

<p>本文档提供了关于WordPress Gutenberg核心区块的完整开发指南，帮助前端开发人员实现区块组件的渲染。</p>

<h2>目录</h2>
<ul>
  <li><a href="#概述">概述</a></li>
  <li><a href="#开发流程">开发流程</a></li>
  <li><a href="#区块渲染架构">区块渲染架构</a></li>
  <li><a href="#区块列表">区块列表</a>
    <ul>
      <li><a href="#完整区块列表">完整区块列表</a></li>
      <li><a href="#按优先级分组的区块">按优先级分组的区块</a></li>
    </ul>
  </li>
  <li><a href="#区块组件开发">区块组件开发</a></li>
  <li><a href="#混合渲染策略">混合渲染策略</a></li>
  <li><a href="#高级应用">高级应用</a></li>
</ul>

<h2 id="概述">概述</h2>
<p>Gutenberg是WordPress的新一代编辑器，它使用区块（Block）作为内容的基本单位。在headless WordPress架构中，我们需要在前端实现对应的React组件来渲染这些区块。</p>

<p>本指南主要关注前端实现部分，假设后端已经安装并正确配置了WP-GraphQL和WP-GraphQL-Gutenberg插件。</p>

<h2 id="开发流程">开发流程</h2>
<ol>
  <li><strong>分析内容需求</strong>：确定网站中需要支持的区块类型</li>
  <li><strong>按优先级开发</strong>：先实现最常用的核心区块组件</li>
  <li><strong>实现混合渲染策略</strong>：对于未开发专用组件的区块，提供HTML回退渲染方案</li>
  <li><strong>测试与优化</strong>：确保各种场景下区块的正确渲染</li>
</ol>

<h2 id="区块渲染架构">区块渲染架构</h2>
<p>我们采用了以下架构来处理区块的渲染：</p>

<pre><code>BlockRenderer
├── 识别区块类型
├── 分发到专用组件 (ParagraphBlock, HeadingBlock等)
└── 未知区块的回退处理 (使用HTML内容)
</code></pre>

<p>主要文件结构：</p>
<ul>
  <li><code>src/components/blocks/BlockRenderer.tsx</code> - 区块渲染器</li>
  <li><code>src/components/blocks/[BlockName].tsx</code> - 各种区块组件</li>
  <li><code>src/types/block.ts</code> - 区块类型定义</li>
</ul>

<h2 id="区块列表">区块列表</h2>

<h3 id="完整区块列表">完整区块列表</h3>
<p>以下是WordPress Gutenberg提供的所有核心区块：</p>

<h4>内容区块</h4>
<table>
  <thead>
    <tr>
      <th>区块名称</th>
      <th>类型名称</th>
      <th>描述</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>core/paragraph</code></td>
      <td><code>CoreParagraphBlock</code></td>
      <td>段落</td>
    </tr>
    <tr>
      <td><code>core/heading</code></td>
      <td><code>CoreHeadingBlock</code></td>
      <td>标题</td>
    </tr>
    <tr>
      <td><code>core/image</code></td>
      <td><code>CoreImageBlock</code></td>
      <td>图像</td>
    </tr>
    <tr>
      <td><code>core/gallery</code></td>
      <td><code>CoreGalleryBlock</code></td>
      <td>图库</td>
    </tr>
    <tr>
      <td><code>core/list</code></td>
      <td><code>CoreListBlock</code></td>
      <td>列表</td>
    </tr>
    <tr>
      <td><code>core/quote</code></td>
      <td><code>CoreQuoteBlock</code></td>
      <td>引用</td>
    </tr>
    <tr>
      <td><code>core/audio</code></td>
      <td><code>CoreAudioBlock</code></td>
      <td>音频</td>
    </tr>
    <tr>
      <td><code>core/cover</code></td>
      <td><code>CoreCoverBlock</code></td>
      <td>封面</td>
    </tr>
    <tr>
      <td><code>core/file</code></td>
      <td><code>CoreFileBlock</code></td>
      <td>文件</td>
    </tr>
    <tr>
      <td><code>core/video</code></td>
      <td><code>CoreVideoBlock</code></td>
      <td>视频</td>
    </tr>
  </tbody>
</table>

<h4>格式化区块</h4>
<table>
  <thead>
    <tr>
      <th>区块名称</th>
      <th>类型名称</th>
      <th>描述</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>core/code</code></td>
      <td><code>CoreCodeBlock</code></td>
      <td>代码</td>
    </tr>
    <tr>
      <td><code>core/html</code></td>
      <td><code>CoreHTMLBlock</code></td>
      <td>自定义HTML</td>
    </tr>
    <tr>
      <td><code>core/preformatted</code></td>
      <td><code>CorePreformattedBlock</code></td>
      <td>预格式化文本</td>
    </tr>
    <tr>
      <td><code>core/pullquote</code></td>
      <td><code>CorePullquoteBlock</code></td>
      <td>引文</td>
    </tr>
    <tr>
      <td><code>core/table</code></td>
      <td><code>CoreTableBlock</code></td>
      <td>表格</td>
    </tr>
    <tr>
      <td><code>core/verse</code></td>
      <td><code>CoreVerseBlock</code></td>
      <td>诗歌</td>
    </tr>
  </tbody>
</table>

<h3 id="按优先级分组的区块">按优先级分组的区块</h3>

<h4>高优先级（P0）：几乎所有网站都需要</h4>
<table>
  <thead>
    <tr>
      <th>区块名称</th>
      <th>类型名称</th>
      <th>描述</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>core/paragraph</code></td>
      <td><code>CoreParagraphBlock</code></td>
      <td>段落</td>
    </tr>
    <tr>
      <td><code>core/heading</code></td>
      <td><code>CoreHeadingBlock</code></td>
      <td>标题</td>
    </tr>
    <tr>
      <td><code>core/image</code></td>
      <td><code>CoreImageBlock</code></td>
      <td>图像</td>
    </tr>
    <tr>
      <td><code>core/list</code></td>
      <td><code>CoreListBlock</code></td>
      <td>列表</td>
    </tr>
    <tr>
      <td><code>core/quote</code></td>
      <td><code>CoreQuoteBlock</code></td>
      <td>引用</td>
    </tr>
  </tbody>
</table>

<h4>中优先级（P1）：大多数网站需要</h4>
<table>
  <thead>
    <tr>
      <th>区块名称</th>
      <th>类型名称</th>
      <th>描述</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>core/gallery</code></td>
      <td><code>CoreGalleryBlock</code></td>
      <td>图库</td>
    </tr>
    <tr>
      <td><code>core/table</code></td>
      <td><code>CoreTableBlock</code></td>
      <td>表格</td>
    </tr>
    <tr>
      <td><code>core/columns</code></td>
      <td><code>CoreColumnsBlock</code></td>
      <td>多列布局</td>
    </tr>
    <tr>
      <td><code>core/button</code></td>
      <td><code>CoreButtonBlock</code></td>
      <td>按钮</td>
    </tr>
  </tbody>
</table>

<h2 id="区块组件开发">区块组件开发</h2>
<p>开发区块组件的一般步骤：</p>

<ol>
  <li>创建组件文件</li>
  <li>定义属性接口</li>
  <li>实现渲染逻辑</li>
  <li>注册到BlockRenderer</li>
</ol>

<h2 id="混合渲染策略">混合渲染策略</h2>
<p>对于尚未实现专用组件的区块，我们使用"混合渲染策略"，使用HTML内容回退显示，确保所有内容都能正确渲染。</p> 