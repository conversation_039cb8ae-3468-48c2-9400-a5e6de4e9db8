<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <title>功能文档：Category 页面服务器组件改造与 SEO 集成</title>
  <style>
    body{font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",<PERSON><PERSON>,"PingFang SC","Hiragino Sans GB","Microsoft YaHei",sans-serif;line-height:1.7;color:#333;padding:40px;max-width:960px;margin:auto;background:#fdfdfd;}
    h1,h2,h3{color:#0d47a1;border-bottom:2px solid #e0e0e0;padding-bottom:6px;margin-top:2em;}
    h1{text-align:center;margin-top:0;}
    pre{background:#fafafa;border:1px solid #e0e0e0;padding:12px;overflow:auto;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.05);}
    code{font-family:Consolas,Monaco,"Andale Mono","Ubuntu Mono",monospace;background:#f1f1f1;padding:2px 4px;border-radius:3px}
    table{width:100%;border-collapse:collapse;margin-top:1em;}
    th,td{border:1px solid #ddd;padding:8px;text-align:left;}
    th{background:#f5f5f5;}
    .note{background:#fffde7;border-left:4px solid #ffeb3b;padding:12px;margin:20px 0;border-radius:4px;}
  </style>
</head>
<body>
<h1>Category 页面服务器组件改造 &amp; SEO 集成</h1>
<p>本文档记录 <code>src/app/category/[slug]/page.tsx</code> 从单一客户端组件改造为「服务器组件 + 客户端组件」混合架构，以及整合 AI SEO 元数据的全过程。</p>

<h2>背景与目标</h2>
<ul>
  <li><strong>性能痛点</strong>：旧版 Category 页面完全依赖客户端 Hook 获取数据，首屏呈现前会出现明显的「加载中」状态。</li>
  <li><strong>SEO 痛点</strong>：由于 HTML 首屏空白，搜索引擎无法抓取标题、描述及结构化数据。</li>
  <li><strong>分页问题</strong>：分页大小不一致导致无法加载所有文章，如62篇文章的分类只显示22篇就停止。</li>
</ul>
<div class="note">
  <strong>目标</strong>：<br/>
  1. 首屏即展示完整文章列表，肉眼无法察觉加载过程。<br/>
  2. 在服务器端生成 <code>&lt;title&gt;</code>、<code>&lt;meta name="description"&gt;</code> 及 JSON-LD。<br/>
  3. 保留列表/网格视图切换、无限滚动等交互功能。<br/>
  4. 修复分页逻辑，确保能正确加载所有文章。<br/>
</div>

<h2>方案概览</h2>
<table>
  <thead>
    <tr><th>模块</th><th>职责</th><th>所在文件</th></tr>
  </thead>
  <tbody>
    <tr><td>服务器组件</td><td>1. 动态获取分页设置<br/>2. GraphQL 获取分类与首屏文章<br/>3. generateMetadata 输出 SEO<br/>4. 将数据作为 props 传递</td><td><code>src/app/category/[slug]/page.tsx</code></td></tr>
    <tr><td>客户端组件</td><td>1. 渲染页面 UI<br/>2. 手动分页加载更多<br/>3. 视图模式切换 &amp; 本地偏好存储<br/>4. 正确的状态管理</td><td><code>src/components/pages/CategoryClientPage.tsx</code></td></tr>
  </tbody>
</table>

<h2>实施步骤</h2>
<h3>1. 统一分页设置</h3>
<p>解决服务器组件和客户端组件分页大小不一致的问题：</p>
<pre><code>// 获取分页设置的查询
const GET_POSTS_PER_PAGE_QUERY = `
  query GetPostsPerPageSetting {
    postsPerPageSetting
  }
`;

// 获取分页设置
async function fetchPostsPerPageSetting(): Promise&lt;number&gt; {
  try {
    const res = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: GET_POSTS_PER_PAGE_QUERY }),
      next: { revalidate: 3600 }, // 缓存1小时
    });
    const json = await res.json();
    return json.data?.postsPerPageSetting || 12;
  } catch (error) {
    return 12; // 默认值
  }
}
</code></pre>

<h3>2. 重写服务器组件</h3>
<pre><code>// src/app/category/[slug]/page.tsx
export async function generateMetadata({ params }) {
  const postsPerPage = await fetchPostsPerPageSetting();
  const category = await fetchCategoryPageData(params.slug, postsPerPage);
  // ...返回 title / description ...
}

export default async function CategoryPage({ params }) {
  const postsPerPage = await fetchPostsPerPageSetting();
  const category = await fetchCategoryPageData(params.slug, postsPerPage);
  if (!category) return notFound();

  return (
    &lt;CategoryClientPage
      initialCategory={category}
      initialPosts={category.posts.nodes}
      initialPageInfo={category.posts.pageInfo}
    /&gt;
  );
}
</code></pre>

<h3>3. 重构客户端分页逻辑</h3>
<p>移除 <code>usePosts</code> hook，实现手动分页加载：</p>
<pre><code>// src/components/pages/CategoryClientPage.tsx
'use client';

export default function CategoryClientPage({
  initialCategory: category,
  initialPosts,
  initialPageInfo,
}) {
  const [allPosts, setAllPosts] = useState(initialPosts);
  const [currentPageInfo, setCurrentPageInfo] = useState(initialPageInfo || null);
  const [loadingMore, setLoadingMore] = useState(false);

  // 手动加载更多数据的函数
  const loadMorePosts = useCallback(async (afterCursor: string) => {
    // 动态获取分页设置
    const settingsResponse = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: GET_POSTS_PER_PAGE_QUERY }),
    });
    const settingsResult = await settingsResponse.json();
    const postsPerPage = settingsResult.data?.postsPerPageSetting || 12;

    // 执行分页查询
    const response = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query: GET_POSTS_BY_CATEGORY_QUERY,
        variables: { categoryId: category?.databaseId, first: postsPerPage, after: afterCursor }
      }),
    });

    const result = await response.json();
    return result.data?.posts;
  }, [category?.databaseId]);

  // 加载更多处理函数
  const handleLoadMore = useCallback(async () => {
    if (!currentPageInfo?.hasNextPage || loadingMore) return;

    try {
      setLoadingMore(true);
      const result = await loadMorePosts(currentPageInfo.endCursor);

      if (result) {
        // 合并新文章到现有列表
        setAllPosts((prev) => {
          const map = new Map();
          prev.forEach((p) => map.set(p.id, p));
          result.nodes.forEach((p) => map.set(p.id, p));
          return Array.from(map.values());
        });

        // 更新分页信息
        setCurrentPageInfo(result.pageInfo);
      }
    } catch (error) {
      console.error('加载更多文章出错:', error);
    } finally {
      setLoadingMore(false);
    }
  }, [currentPageInfo, loadingMore, loadMorePosts]);

  const hasMore = currentPageInfo?.hasNextPage ?? false;

  // ...渲染逻辑
}
</code></pre>

<h3>4. GraphQL 查询优化</h3>
<p>服务器组件使用动态分页大小获取首屏数据：</p>
<pre><code>query GetCategoryPage($slug: ID!, $first: Int) {
  category(id: $slug, idType: SLUG) {
    name
    description
    count
    aiSeoTitle
    aiSeoDescription
    aiSeoJsonLd
    posts(first: $first) {
      nodes {
        id
        title
        date
        slug
        shortUuid
        excerpt
        featuredImage { node { sourceUrl altText } }
        author { node { name slug avatar { url } } }
        categories { nodes { id name slug } }
      }
      pageInfo { hasNextPage endCursor }
    }
  }
}
</code></pre>

<h3>5. 类型补充</h3>
<p>在 <code>src/types/post.ts</code> 的 <code>Category</code> 接口中新增：</p>
<pre><code>aiSeoTitle?: string;
aiSeoDescription?: string;
aiSeoJsonLd?: string;
</code></pre>

<h2>分页问题修复</h2>
<h3>问题诊断</h3>
<p>在测试62篇文章的分类时发现只显示22篇就停止，经诊断发现以下问题：</p>
<ul>
  <li><strong>分页大小不一致</strong>：服务器组件使用固定值10，客户端使用动态值12</li>
  <li><strong>重复查询问题</strong>：客户端组件立即执行查询而不是等待用户触发</li>
  <li><strong>状态管理混乱</strong>：分页信息管理不当导致错误的 hasNextPage 状态</li>
</ul>

<h3>修复方案</h3>
<ol>
  <li><strong>统一分页设置</strong>：服务器和客户端都使用动态获取的分页设置</li>
  <li><strong>重新设计分页逻辑</strong>：移除立即查询，改为用户触发时才加载</li>
  <li><strong>改进状态管理</strong>：正确跟踪和更新分页状态</li>
</ol>

<h3>修复验证</h3>
<p>通过控制台日志验证修复效果：</p>
<pre><code>// 分页过程日志
[CategoryClientPage] Component initialized: {categoryCount: 62, initialPostsCount: 12}
[CategoryClientPage] loaded more posts: {newPostsCount: 12} // 第1次：总计24篇
[CategoryClientPage] loaded more posts: {newPostsCount: 12} // 第2次：总计36篇
[CategoryClientPage] loaded more posts: {newPostsCount: 12} // 第3次：总计48篇
[CategoryClientPage] loaded more posts: {newPostsCount: 12} // 第4次：总计60篇
[CategoryClientPage] loaded more posts: {newPostsCount: 2, newPageInfo: {hasNextPage: false}} // 第5次：总计62篇 ✅
</code></pre>

<h2>效果验证</h2>
<ul>
  <li>✅ 页面首屏 HTML 已包含完整文章列表；FCP 与 LCP 显著降低。</li>
  <li>✅ <code>&lt;title&gt;</code> 与 <code>&lt;meta description&gt;</code> 在源码可见，JSON-LD 正确注入。</li>
  <li>✅ 经过 Lighthouse SEO 检测，无缺失项；Facebook/Twitter Debugger 能抓取到元数据。</li>
  <li>✅ 分页逻辑正常工作，能够正确加载所有文章（62篇全部显示）。</li>
  <li>✅ 分页大小统一，服务器端和客户端使用相同的动态设置。</li>
</ul>

<h2>技术要点</h2>
<ul>
  <li><strong>动态分页设置</strong>：通过 GraphQL 查询 <code>postsPerPageSetting</code> 确保一致性</li>
  <li><strong>手动分页控制</strong>：避免自动查询，只在用户滚动触发时加载</li>
  <li><strong>状态管理优化</strong>：使用 <code>currentPageInfo</code> 正确跟踪分页状态</li>
  <li><strong>数据去重合并</strong>：使用 Map 确保文章列表无重复</li>
  <li><strong>TypeScript 类型安全</strong>：明确的类型注解避免编译错误</li>
</ul>

<h2>下一步</h2>
<p>同样方式改造：</p>
<ol>
  <li><strong>Tag 页面</strong>：<code>src/app/tag/[slug]/page.tsx</code></li>
  <li><strong>自定义分类法页面</strong></li>
</ol>
<p>届时只需复用 <code>CategoryClientPage</code> 的分页逻辑，或抽象为通用 <em>TaxonomyClientPage</em> 组件。</p>

</body>
</html> 