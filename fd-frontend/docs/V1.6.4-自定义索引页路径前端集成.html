<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FD前端文档 V1.6.4 - 自定义索引页路径前端集成</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 2px solid #eaecef;
            padding-bottom: 10px;
        }
        h2 {
            margin-top: 30px;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 8px;
        }
        h3 {
            margin-top: 25px;
        }
        code {
            background-color: #f6f8fa;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            font-size: 0.9em;
        }
        pre {
            background-color: #f6f8fa;
            padding: 16px;
            border-radius: 3px;
            overflow: auto;
        }
        pre code {
            background-color: transparent;
            padding: 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #dfe2e5;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f6f8fa;
            font-weight: 600;
        }
        .important {
            background-color: #fffacd;
            border-left: 4px solid #e6db55;
            padding: 12px;
            margin: 20px 0;
        }
        .version-badge {
            display: inline-block;
            background-color: #28a745;
            color: white;
            border-radius: 3px;
            padding: 2px 5px;
            font-size: 0.8em;
            margin-left: 8px;
            vertical-align: middle;
        }
        .method {
            background-color: #f8f9fa;
            border-radius: 3px;
            padding: 16px;
            margin: 20px 0;
        }
        .file-path {
            color: #6c757d;
            font-style: italic;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <h1>自定义索引页路径前端集成 <span class="version-badge">V1.6.4</span></h1>
    
    <p>前端应用现已支持后端自定义的分类索引页和标签索引页路径，使网站的URL结构更加灵活和可定制。</p>
    
    <div class="important">
        <p><strong>更新日期:</strong> 2023-06-18</p>
        <p><strong>依赖版本:</strong> 需要FD主题 V1.2.8+</p>
    </div>
    
    <h2 id="summary">功能概述</h2>
    <p>在此版本中，我们增强了前端应用，使其能够使用后端配置的分类索引页和标签索引页自定义路径。主要变更包括：</p>
    <ul>
        <li>更新GraphQL查询，获取新的路由配置</li>
        <li>扩展<code>useRoutePrefixes</code>钩子，支持新的路由设置</li>
        <li>更新导航链接，使用动态路径而非硬编码路径</li>
        <li>在路由测试中心添加新设置的可视化</li>
    </ul>
    
    <h2 id="implementation">实现细节</h2>
    
    <h3 id="graphql-query">GraphQL查询更新</h3>
    <p>扩展了<code>GET_ROUTE_PREFIXES</code>查询以获取新的路由设置：</p>
    
    <div class="method">
        <p class="file-path">src/lib/graphql/queries.ts</p>
        <pre><code>// 获取路由前缀设置
export const GET_ROUTE_PREFIXES = gql`
  query GetRoutePrefixes {
    routePrefixes {
      categoryPrefix
      tagPrefix
      postPrefix
      categoryIndexRoute
      tagIndexRoute
    }
  }
`;</code></pre>
    </div>
    
    <h3 id="route-prefixes-hook">路由前缀钩子更新</h3>
    <p>扩展了<code>useRoutePrefixes</code>钩子的类型定义和默认值：</p>
    
    <div class="method">
        <p class="file-path">src/hooks/useRoutePrefixes.ts</p>
        <pre><code>interface RoutePrefixes {
  categoryPrefix: string | null;
  tagPrefix: string | null;
  postPrefix: string;
  categoryIndexRoute: string;
  tagIndexRoute: string;
}

export const useRoutePrefixes = (): UseRoutePrefixesResult => {
  const { data, loading, error } = useQuery(GET_ROUTE_PREFIXES);
  
  // 默认值
  const defaultPrefixes: RoutePrefixes = {
    categoryPrefix: null,
    tagPrefix: 'topics',
    postPrefix: 'articles',
    categoryIndexRoute: 'category-index',
    tagIndexRoute: 'tag-index'
  };
  
  return {
    prefixes: data?.routePrefixes || defaultPrefixes,
    loading,
    error
  };
};</code></pre>
    </div>
    
    <h3 id="navigation-links">导航链接更新</h3>
    <p>更新了分类和标签详情页的返回链接，使用动态路径：</p>
    
    <div class="method">
        <h4>分类详情页</h4>
        <p class="file-path">src/app/category/[slug]/page.tsx</p>
        <pre><code>{/* 返回链接 */}
&lt;div className="mt-8"&gt;
  &lt;Link 
    href={`/${prefixes.categoryIndexRoute}`}
    className="text-blue-600 hover:text-blue-800 flex items-center"
  &gt;
    ← 返回分类索引
  &lt;/Link&gt;
&lt;/div&gt;</code></pre>
    </div>
    
    <div class="method">
        <h4>标签详情页</h4>
        <p class="file-path">src/app/tag/[slug]/page.tsx</p>
        <pre><code>{/* 返回链接 */}
&lt;div className="mt-8"&gt;
  &lt;Link 
    href={`/${prefixes.tagIndexRoute}`}
    className="text-blue-600 hover:text-blue-800 flex items-center"
  &gt;
    ← 返回标签索引
  &lt;/Link&gt;
&lt;/div&gt;</code></pre>
    </div>
    
    <h3 id="route-test">路由测试中心更新</h3>
    <p>更新了路由测试中心页面，显示和测试新的路由设置：</p>
    
    <div class="method">
        <p class="file-path">src/app/route-test/page.tsx</p>
        <pre><code>// 路由前缀设置标签页
const renderPrefixesTab = () => (
  &lt;div className="bg-white p-4 rounded-lg shadow"&gt;
    &lt;h2 className="text-xl font-medium mb-4"&gt;路由前缀设置&lt;/h2&gt;
    
    {prefixesLoading ? (
      &lt;div className="p-4"&gt;加载路由设置中...&lt;/div&gt;
    ) : prefixesError ? (
      &lt;div className="p-4 text-red-500"&gt;
        &lt;h2 className="text-xl font-bold mb-2"&gt;加载设置出错&lt;/h2&gt;
        &lt;p&gt;{prefixesError.message}&lt;/p&gt;
      &lt;/div&gt;
    ) : (
      &lt;div className="space-y-4"&gt;
        {/* 现有的前缀设置... */}
        
        &lt;div&gt;
          &lt;h3 className="font-medium"&gt;分类索引页路径:&lt;/h3&gt;
          &lt;p className="mt-1"&gt;
            &lt;code className="bg-gray-100 px-2 py-1 rounded"&gt;/{prefixes.categoryIndexRoute}&lt;/code&gt;
          &lt;/p&gt;
          &lt;p className="mt-1 text-sm text-gray-600"&gt;
            分类索引页URL: &lt;span className="ml-1"&gt;/{prefixes.categoryIndexRoute}&lt;/span&gt;
          &lt;/p&gt;
        &lt;/div&gt;
        
        &lt;div&gt;
          &lt;h3 className="font-medium"&gt;标签索引页路径:&lt;/h3&gt;
          &lt;p className="mt-1"&gt;
            &lt;code className="bg-gray-100 px-2 py-1 rounded"&gt;/{prefixes.tagIndexRoute}&lt;/code&gt;
          &lt;/p&gt;
          &lt;p className="mt-1 text-sm text-gray-600"&gt;
            标签索引页URL: &lt;span className="ml-1"&gt;/{prefixes.tagIndexRoute}&lt;/span&gt;
          &lt;/p&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    )}
    
    {/* 提示信息... */}
  &lt;/div&gt;
);</code></pre>
    </div>
    
    <h2 id="usage">使用方法</h2>
    
    <h3 id="route-hook-usage">使用路由钩子</h3>
    <p>在任何组件中，您可以使用<code>useRoutePrefixes</code>钩子获取路由设置，包括新的索引页路径：</p>
    
    <div class="method">
        <pre><code>import { useRoutePrefixes } from '@/hooks';

function MyComponent() {
  const { prefixes } = useRoutePrefixes();
  
  return (
    &lt;div&gt;
      &lt;a href={`/${prefixes.categoryIndexRoute}`}&gt;查看所有分类&lt;/a&gt;
      &lt;a href={`/${prefixes.tagIndexRoute}`}&gt;查看所有标签&lt;/a&gt;
    &lt;/div&gt;
  );
}</code></pre>
    </div>
    
    <h3 id="navigation-example">导航组件示例</h3>
    <p>在网站导航中使用动态路径的示例：</p>
    
    <div class="method">
        <pre><code>import { useRoutePrefixes } from '@/hooks';
import Link from 'next/link';

export function MainNavigation() {
  const { prefixes } = useRoutePrefixes();
  
  return (
    &lt;nav className="flex space-x-4"&gt;
      &lt;Link href="/"&gt;首页&lt;/Link&gt;
      &lt;Link href={`/${prefixes.categoryIndexRoute}`}&gt;分类&lt;/Link&gt;
      &lt;Link href={`/${prefixes.tagIndexRoute}`}&gt;标签&lt;/Link&gt;
      &lt;Link href="/about"&gt;关于我们&lt;/Link&gt;
    &lt;/nav&gt;
  );
}</code></pre>
    </div>
    
    <h2 id="testing">测试和验证</h2>
    <p>您可以通过以下步骤测试新功能：</p>
    <ol>
        <li>在WordPress后台修改分类索引页和标签索引页的路径设置</li>
        <li>访问前端应用的<code>/route-test</code>页面，查看新设置是否正确显示</li>
        <li>验证分类和标签详情页的返回链接是否使用了正确的自定义路径</li>
        <li>测试导航到新路径，确认页面正确加载</li>
    </ol>
    
    <div class="important">
        <p><strong>注意:</strong> 如果路由前缀设置发生变化，可能需要重新启动Next.js开发服务器以清除路由缓存。</p>
    </div>
    
    <h2 id="compatibility">兼容性说明</h2>
    <p>此更新完全向后兼容，即使后端没有提供新的路由设置字段，前端也会使用默认值继续正常工作。</p>
    
    <h2 id="version-history">版本历史</h2>
    <h3>v1.6.4 <small>(2023-06-18)</small></h3>
    <ul>
        <li>添加对自定义分类索引页和标签索引页路径的支持</li>
        <li>扩展GraphQL查询和路由钩子</li>
        <li>更新导航链接使用动态路径</li>
        <li>增强路由测试中心</li>
    </ul>
    
    <h3>v1.6.3 <small>(2023-05-20)</small></h3>
    <ul>
        <li>改进路由实现和路由解析</li>
        <li>优化UUID查询性能</li>
    </ul>
    
    <h3>v1.6.0 <small>(2023-04-25)</small></h3>
    <ul>
        <li>引入路由测试中心</li>
        <li>添加路由前缀设置支持</li>
    </ul>
</body>
</html>