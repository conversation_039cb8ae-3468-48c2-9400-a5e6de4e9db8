<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机快捷登录 - 前端实现文档</title>
    <style>
        body {
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1100px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 1px solid #eaecef;
            padding-bottom: 0.3em;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            background-color: #f6f8fa;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 0.9em;
        }
        pre {
            background-color: #f6f8fa;
            border-radius: 6px;
            padding: 16px;
            overflow: auto;
            font-size: 85%;
        }
        pre code {
            background-color: transparent;
            padding: 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #dfe2e5;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f6f8fa;
        }
        .note {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 20px 0;
        }
        .warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
        }
        .section {
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>手机快捷登录 - 前端实现文档</h1>

    <div class="section">
        <h2>功能概述</h2>
        <p>手机快捷登录是一种便捷的用户认证方式，允许用户使用手机号和短信验证码进行登录或注册。本文档详细介绍了该功能在前端的实现机制，主要包括：</p>
        <ul>
            <li>手机验证码发送与验证</li>
            <li>手机号是否已注册的检查</li>
            <li>根据手机号存在状态进行登录或注册</li>
            <li>登录状态管理与持久化</li>
            <li>JWT token认证方式</li>
        </ul>
    </div>

    <div class="section">
        <h2>核心组件</h2>
        <h3>1. PhoneLoginForm 组件</h3>
        <p>手机登录表单组件是实现手机快捷登录的主要界面组件，位于 <code>fd-frontend/src/components/auth/PhoneLoginForm.tsx</code>。</p>
        
        <h4>主要功能</h4>
        <ul>
            <li>手机号格式验证</li>
            <li>发送短信验证码并管理倒计时</li>
            <li>检测手机号是否已注册</li>
            <li>根据手机号注册状态执行登录或注册流程</li>
            <li>表单提交与错误处理</li>
            <li>登录成功后的状态同步和路由跳转</li>
        </ul>

        <h4>关键代码结构</h4>
        <pre><code>const PhoneLoginForm: React.FC&lt;PhoneLoginFormProps&gt; = ({ callbackUrl = '/' }) => {
  const router = useRouter();
  const { 
    phoneLogin, 
    phoneRegister, 
    sendPhoneCode, 
    clearError 
  } = useAuth();
  
  const { validateAccount, validationState } = useAccountValidation();
  
  // 组件级状态管理，避免全局状态导致的表单重置
  const [formData, setFormData] = useState({
    phone: '',
    nationCode: '86',
    code: '',
  });
  
  // 手机号是否存在的状态
  const [phoneExists, setPhoneExists] = useState&lt;boolean | null&gt;(null);
  
  // 处理提交
  const handleSubmit = async (e: React.FormEvent) => {
    // ... 表单验证逻辑

    try {
      // 如果手机号尚未验证存在性，先验证
      if (phoneExists === null) {
        await checkPhoneExists();
      }
      
      if (phoneExists) {
        // 手机号已存在，执行登录流程
        await phoneLogin({
          phone: formData.phone,
          code: formData.code,
          nationCode: formData.nationCode
        });
        setSuccess('登录成功，正在跳转...');
      } else {
        // 手机号不存在，执行注册流程
        await phoneRegister({
          phone: formData.phone,
          code: formData.code,
          nationCode: formData.nationCode
        });
        setSuccess('注册并登录成功，正在跳转...');
      }
      
      // 延迟跳转，让用户看到成功消息
      setTimeout(() => {
        // 添加额外延迟确保状态同步完成
        setTimeout(() => {
          router.refresh(); // 刷新当前路由数据
          router.push(callbackUrl);
        }, 500);
      }, 1500);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '操作失败，请重试';
      setError(errorMsg);
    }
  };
  
  // ... 其他方法和渲染逻辑
}</code></pre>
        
        <h3>2. AuthContext 上下文</h3>
        <p>位于 <code>fd-frontend/src/contexts/AuthContext.tsx</code>，为整个应用提供认证相关的状态管理和方法。</p>
        
        <h4>与手机登录相关的主要方法</h4>
        <ul>
            <li><code>sendPhoneCode</code>: 发送手机验证码</li>
            <li><code>phoneLogin</code>: 手机号登录</li>
            <li><code>phoneRegister</code>: 手机号注册</li>
        </ul>

        <h4>关键方法实现</h4>
        <pre><code>// 发送手机验证码
const sendPhoneCode = async (input: SendPhoneCodeInput): Promise&lt;PhoneAuthResponse&gt; => {
  try {
    const { data } = await sendPhoneCodeMutation({
      variables: {
        input: {
          phone: input.phone,
          nationCode: input.nationCode || '86'
        }
      }
    });
    
    const result = data?.sendPhoneCode || {};
    return {
      success: !!result.success,
      message: result.message || ''
    };
  } catch (err) {
    console.error('发送验证码失败:', err);
    throw new Error(err instanceof ApolloError 
      ? err.message 
      : '发送验证码失败，请重试');
  }
};

// 手机号登录
const phoneLogin = async (input: PhoneLoginInput): Promise&lt;void&gt; => {
  setIsLoading(true);
  setError(null);
  
  try {
    const { data } = await phoneLoginMutation({
      variables: {
        input: {
          phone: input.phone,
          code: input.code,
          nationCode: input.nationCode || '86'
        }
      }
    });
    
    const result = data?.phoneLogin;
    
    if (result?.success && result?.authToken) {
      console.log('手机登录成功，正在保存认证数据');
      
      // 保存JWT认证令牌
      setAuthToken(result.authToken);
      
      // 如果返回了用户对象，使用它
      if (result.user) {
        setUser(result.user);
        setUserData(result.user);
        
        // 重置Apollo Client缓存，确保查询使用新的认证状态
        client.resetStore().catch(console.error);
      } else {
        // 没有返回用户对象，尝试获取
        console.log('手机登录未返回用户对象，尝试获取当前用户');
        await fetchCurrentUser();
      }
      
      console.log('手机登录流程完成');
    } else {
      throw new Error(result?.message || '登录失败');
    }
  } catch (err) {
    console.error('手机登录失败:', err);
    setError(err instanceof ApolloError 
      ? err.message 
      : '登录失败，请重试');
    throw err;
  } finally {
    setIsLoading(false);
  }
};

// 手机号注册
const phoneRegister = async (input: PhoneRegisterInput): Promise&lt;void&gt; => {
  setIsLoading(true);
  setError(null);
  
  try {
    const { data } = await phoneRegisterMutation({
      variables: {
        input: {
          phone: input.phone,
          code: input.code,
          nationCode: input.nationCode || '86',
          displayName: input.displayName || '',
          username: input.username || '',
          password: input.password || '',
          skipVerification: input.skipVerification || false
        }
      }
    });
    
    const result = data?.phoneRegister;
    
    if (result?.success && result?.authToken) {
      console.log('手机注册成功，正在保存认证数据');
      
      // 保存JWT认证令牌
      setAuthToken(result.authToken);
      
      // 如果返回了用户对象，使用它
      if (result.user) {
        setUser(result.user);
        setUserData(result.user);
        
        // 重置Apollo Client缓存，确保查询使用新的认证状态
        client.resetStore().catch(console.error);
      } else {
        // 没有返回用户对象，尝试获取
        console.log('手机注册未返回用户对象，尝试获取当前用户');
        await fetchCurrentUser();
      }
      
      console.log('手机注册流程完成');
    } else {
      throw new Error(result?.message || '注册失败');
    }
  } catch (err) {
    console.error('手机注册失败:', err);
    setError(err instanceof ApolloError
      ? err.message
      : '注册失败，请重试');
    throw err;
  } finally {
    setIsLoading(false);
  }
};</code></pre>
    </div>

    <div class="section">
        <h2>相关GraphQL变更</h2>
        <p>前端使用以下GraphQL变更与后端进行通信：</p>
        <pre><code>// 发送手机验证码
export const SEND_PHONE_CODE = gql`
  mutation SendPhoneCode($input: SendPhoneCodeInput!) {
    sendPhoneCode(input: $input) {
      success
      message
    }
  }
`;

// 手机登录
export const PHONE_LOGIN = gql`
  mutation PhoneLogin($input: PhoneLoginInput!) {
    phoneLogin(input: $input) {
      success
      message
      authToken
      user {
        id
        databaseId
        username
        name
        email
        avatar {
          url
        }
        phone
      }
    }
  }
`;

// 手机注册
export const PHONE_REGISTER = gql`
  mutation PhoneRegister($input: PhoneRegisterInput!) {
    phoneRegister(input: $input) {
      success
      message
      authToken
      user {
        id
        databaseId
        username
        name
        email
        avatar {
          url
        }
        phone
      }
    }
  }
`;</code></pre>
    </div>

    <div class="section">
        <h2>验证码处理流程</h2>
        <ol>
            <li>用户输入手机号</li>
            <li>前端验证手机号格式</li>
            <li>验证通过后，调用<code>sendPhoneCode</code>方法发送验证码</li>
            <li>后端生成6位随机验证码，通过短信接口发送</li>
            <li>用户收到短信，输入验证码</li>
            <li>提交表单时，根据手机号是否已注册决定调用<code>phoneLogin</code>还是<code>phoneRegister</code>方法</li>
            <li>后端验证验证码，成功后返回JWT token</li>
            <li>前端保存token并获取用户信息</li>
            <li>更新认证状态并重定向到首页或指定页面</li>
        </ol>
    </div>

    <div class="section">
        <h2>用户体验优化</h2>
        <ul>
            <li><strong>实时验证</strong>：手机号格式实时验证，提供即时反馈</li>
            <li><strong>防抖处理</strong>：验证码发送有60秒冷却时间，防止频繁请求</li>
            <li><strong>状态提示</strong>：加载状态、成功和错误信息清晰展示</li>
            <li><strong>延迟跳转</strong>：登录成功后延迟跳转，确保用户看到成功提示并且状态完全同步</li>
        </ul>
        
        <div class="note">
            <p><strong>注意</strong>：为解决登录状态同步问题，实现了双层延时跳转机制：</p>
            <pre><code>setTimeout(() => {
  // 添加额外延迟确保状态同步完成
  setTimeout(() => {
    router.refresh(); // 刷新当前路由数据
    router.push(callbackUrl);
  }, 500);
}, 1500);</code></pre>
        </div>
    </div>

    <div class="section">
        <h2>JWT Token认证</h2>
        <p>系统使用JWT格式的token进行认证，相较于传统的Cookie认证有以下优势：</p>
        <ul>
            <li>无状态：服务器不需要维护会话状态</li>
            <li>跨域：可以在不同域名下使用</li>
            <li>适合移动环境：便于移动应用集成</li>
            <li>安全性：签名验证确保数据不被篡改</li>
        </ul>
        
        <p>认证流程：</p>
        <ol>
            <li>用户登录成功，服务器返回JWT token</li>
            <li>前端将token保存在localStorage中</li>
            <li>Apollo客户端配置请求拦截器，自动将token添加到请求头</li>
            <li>服务器验证token后返回受保护的资源</li>
        </ol>
    </div>

    <div class="section">
        <h2>常见问题与解决方案</h2>
        <table>
            <tr>
                <th>问题</th>
                <th>解决方案</th>
            </tr>
            <tr>
                <td>登录成功后，跳转页面仍显示未登录状态</td>
                <td>
                    <ul>
                        <li>实现双层延迟跳转确保状态同步</li>
                        <li>添加StorageEvent监听器，在localStorage变化时更新认证状态</li>
                        <li>登录成功后重置Apollo Client缓存</li>
                    </ul>
                </td>
            </tr>
            <tr>
                <td>手机号验证码发送失败</td>
                <td>
                    <ul>
                        <li>检查网络连接</li>
                        <li>后端短信API配置是否正确</li>
                        <li>查看后端日志确认错误原因</li>
                    </ul>
                </td>
            </tr>
            <tr>
                <td>提交验证码后报错</td>
                <td>
                    <ul>
                        <li>验证码有效期为10分钟，可能已过期</li>
                        <li>检查验证码是否正确输入</li>
                        <li>后端验证逻辑错误或服务器问题</li>
                    </ul>
                </td>
            </tr>
        </table>
    </div>

    <div class="section">
        <h2>未来优化方向</h2>
        <ul>
            <li><strong>无感登录</strong>：集成WebAuthn或设备指纹，减少验证码输入</li>
            <li><strong>多因素认证</strong>：增加额外安全层</li>
            <li><strong>社交账号绑定</strong>：允许用户将手机号与社交账号关联</li>
            <li><strong>国际化支持</strong>：完善国际区号选择和多语言短信模板</li>
        </ul>
    </div>

    <div class="section">
        <h2>相关文档</h2>
        <ul>
            <li><a href="jwt-token-avatar-fix.html">JWT Token配置与头像修复</a></li>
            <li><a href="phone-binding.md">手机号绑定功能</a></li>
            <li><a href="email-phone-verification.html">邮箱与手机验证流程</a></li>
        </ul>
    </div>
</body>
</html> 