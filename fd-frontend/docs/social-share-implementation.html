<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <title>Future Decade 社交分享功能实现文档</title>
  <style>
    body{font-family:-apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Not<PERSON> Sans', sans-serif;line-height:1.7;margin:0;padding:2rem;background:#f7f8fa;color:#333}
    h1,h2,h3,h4{color:#111;margin-top:2rem}
    pre{background:#272822;color:#f8f8f2;padding:1rem;border-radius:4px;overflow:auto;font-size:0.9rem}
    code{color:#c7254e;background:#f9f2f4;padding:0.2em 0.4em;border-radius:4px}
    a{color:#0070f3;text-decoration:none}
    a:hover{text-decoration:underline}
    ul, ol{margin-left:1.2rem}
    section{margin-bottom:3rem}
    details{border:1px solid #ddd;padding:0.8rem 1.2rem;border-radius:4px;margin-top:1rem}
    summary{font-weight:bold;cursor:pointer;margin:-0.8rem -1.2rem;padding:0.8rem 1.2rem}
    summary:hover{background:#f0f0f0}
    .note{background:#fffbe6;border-left:4px solid #ffc107;padding:1rem;margin:1rem 0;border-radius:4px}
  </style>
</head>
<body>
  <h1>Future&nbsp;Decade&nbsp;社交分享功能复盘<br/>（微信&nbsp;·&nbsp;微博&nbsp;·&nbsp;QQ&nbsp;·&nbsp;X&nbsp;·&nbsp;Poster）</h1>

  <section>
    <h2>1&nbsp;·&nbsp;功能概览</h2>
    <p>本功能为 Future Decade 的 Headless WordPress + Next.js 架构提供了一套完整的社交分享解决方案，旨在提升用户分享体验和内容传播效率。功能覆盖：</p>
    <ul>
      <li><strong>微信原生分享</strong>：深度集成微信 JS-SDK，支持分享给朋友和朋友圈，并能正确显示标题、描述和缩略图。</li>
      <li><strong>多平台网页分享</strong>：支持微博、QQ、QZone、X (Twitter) 等主流平台的网页版分享。</li>
      <li><strong>智能场景判断</strong>：自动识别环境，在微信内调用 SDK，在外部浏览器中为移动端提供提示、为桌面端展示二维码。</li>
      <li><strong>动态海报生成</strong>：一键生成包含文章核心信息和二维码的分享长图 (Poster)，由前端动态渲染。</li>
    </ul>
  </section>

  <section>
    <h2>2&nbsp;·&nbsp;后端实现 (WordPress Theme)</h2>
    <p>后端的核心职责是：<strong>提供可靠的数据源</strong>。通过扩展 GraphQL，为前端提供所有分享功能所需的安全、稳定、格式化的数据。</p>
    
    <h3>2.1&nbsp;GraphQL&nbsp;字段扩展 (<code>inc/graphql.php</code>)</h3>
    <p>我们为 <code>Post</code> 类型增加了两个关键字段，以确保分享内容的质量。</p>
    
    <details>
      <summary><strong>字段 1：<code>shareImage</code> (核心图片策略)</strong></summary>
      <p>此字段解决了微信分享时图片时好时坏的根本问题。它确保返回一个稳定、符合微信要求的缩略图 URL。</p>
      <h4>解析逻辑：</h4>
      <ol>
        <li>优先获取文章特色图的 <code>medium_large</code> (768px) 尺寸。</li>
        <li>若不存在，则降级尝试 <code>medium</code> (300px) 尺寸。</li>
        <li>若以上尺寸均不存在（可能由于图片太小未生成），则直接使用 <code>full</code> 尺寸原图。</li>
        <li>如果文章根本没有特色图，则返回在主题后台设置的 <code>defaultThumb</code> 作为最终备用。</li>
      </ol>
      <pre><code>// in fd_register_graphql_fields()
register_graphql_field('Post', 'shareImage', [
    'type' => 'String',
    'description' => '用于社交分享的特色缩略图URL',
    'resolve' => function($post) {
        $thumbnail_url = null;
        if (isset($post->ID) && has_post_thumbnail($post->ID)) {
            $thumbnail_url = get_the_post_thumbnail_url($post->ID, 'medium_large');
            if (!$thumbnail_url) {
                $thumbnail_url = get_the_post_thumbnail_url($post->ID, 'medium');
            }
            if (!$thumbnail_url) {
                $thumbnail_url = get_the_post_thumbnail_url($post->ID, 'full');
            }
        }

        if (!$thumbnail_url) {
            $options = get_option('fd_share_settings');
            if (!empty($options['share_poster_default_thumb'])) {
                $thumbnail_url = $options['share_poster_default_thumb'];
            }
        }
        return $thumbnail_url;
    }
]);</code></pre>
    </details>

    <details>
      <summary><strong>字段 2：<code>featuredImageUrl</code> (常规特色图)</strong></summary>
      <p>此字段提供文章的完整尺寸特色图，主要用于文章内容展示和生成海报时的背景图。</p>
    </details>

    <h3>2.2&nbsp;GraphQL&nbsp;查询与变更</h3>
    
    <details>
      <summary><strong>查询 1：<code>wechatSdkConfig</code> (微信 JS-SDK 签名)</strong></summary>
      <p>此查询接收前端页面的完整 URL，并返回微信 JS-SDK 初始化所需的配置和签名。</p>
      <h4>实现流程：</h4>
      <ol>
        <li>从主题设置中获取 AppID 和 AppSecret。</li>
        <li>通过 <code>fd_get_wechat_access_token</code> 获取 access_token (带7000秒缓存)。</li>
        <li>通过 <code>fd_get_wechat_jsapi_ticket</code> 获取 jsapi_ticket (带7000秒缓存)。</li>
        <li>生成随机字符串 <code>nonceStr</code> 和当前时间戳 <code>timestamp</code>。</li>
        <li>拼接签名字符串：<code>jsapi_ticket=...&noncestr=...&timestamp=...&url=...</code></li>
        <li>对签名字符串进行 <code>sha1</code> 哈希，生成最终的 <code>signature</code>。</li>
        <li>将 AppID、timestamp、nonceStr、signature 一并返回。</li>
      </ol>
      <div class="note"><strong>注意：</strong> 用于签名的 URL 必须与前端页面 URL (不含 hash) 完全一致，否则会导致签名无效。</div>
    </details>
    
    <details>
      <summary><strong>变更 1：<code>generatePostPoster</code> (海报数据生成)</strong></summary>
      <p>此变更负责在后端组装生成海报所需的全部数据，并进行预处理（如图片转 Base64），以减少前端的复杂性和网络请求。</p>
      <h4>实现流程：</h4>
      <ol>
        <li>接收 <code>postId</code>，获取文章对象。</li>
        <li>提取标题、摘要、作者名。</li>
        <li>将作者头像、文章特色图、后台设置的海报 Logo 通过 <code>fd_image_to_base64</code> 函数转换为 Base64 字符串。</li>
        <li>将所有数据整合为 <code>PosterData</code> 对象返回。</li>
      </ol>
      <pre><code>// in fd_register_graphql_mutations()
'resolve' => function($root, $args, $context, $info) {
    // ...
    $post = get_post($post_id);
    // ...
    return [
        'posterData' => [
            'title' => get_the_title($post_id),
            'excerpt' => get_the_excerpt($post_id),
            'authorName' => get_the_author_meta('display_name', $post->post_author),
            'authorAvatar' => fd_image_to_base64(get_avatar_url($post->post_author)),
            'featuredImage' => fd_image_to_base64(get_the_post_thumbnail_url($post_id, 'large')),
            'posterLogo' => fd_image_to_base64($options['share_poster_logo'] ?? ''),
        ],
    ];
}</code></pre>
    </details>
  </section>

  <section>
    <h2>3&nbsp;·&nbsp;前端实现 (Next.js)</h2>
    <p>前端的核心职责是：<strong>消费后端数据，构建交互界面</strong>。通过自定义 Hooks 和组件，将复杂的分享逻辑封装起来，提供简洁的调用接口。</p>

    <h3>3.1&nbsp;核心 Hook：<code>useWeChatShare</code> (位于 <code>ShareButtons.tsx</code>)</h3>
    <p>这个 Hook 封装了微信分享的全部客户端逻辑。</p>
    <details>
      <summary><strong>实现流程</strong></summary>
      <ol>
        <li><strong>环境判断</strong>：通过 <code>/MicroMessenger/i.test(navigator.userAgent)</code> 判断是否在微信浏览器内。</li>
        <li><strong>数据获取</strong>：如果是在微信内，则执行 <code>GET_WECHAT_CONFIG</code> 查询，获取 JS-SDK 签名。</li>
        <li><strong>动态加载SDK</strong>：签名数据返回后，动态创建一个 <code>&lt;script&gt;</code> 标签加载微信的 JS-SDK 文件。</li>
        <li><strong>配置SDK</strong>：SDK 加载完成后，调用 <code>window.wx.config()</code> 进行配置。</li>
        <li><strong>设置分享内容</strong>：在 <code>window.wx.ready()</code> 回调中，调用 <code>updateAppMessageShareData</code> (分享给朋友) 和 <code>updateTimelineShareData</code> (分享到朋友圈) 来设置分享的标题、链接和图片。</li>
      </ol>
      <pre><code>// in useWeChatShare hook
useEffect(() => {
  if (isWeChatBrowser() && configData?.wechatSdkConfig) {
    const script = document.createElement('script');
    script.src = 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js';
    script.async = true;
    script.onload = () => {
      const { appId, timestamp, nonceStr, signature } = configData.wechatSdkConfig;
      window.wx.config({ /* ... */ });
      window.wx.ready(() => {
        // 分享给朋友
        window.wx.updateAppMessageShareData({
          title: postTitle,
          desc: settings?.wechatDesc || postExcerpt || '',
          link: postUrl,
          imgUrl: shareImage || settings?.defaultThumb || '',
        });
        // 分享到朋友圈
        window.wx.updateTimelineShareData({
          title: postTitle.split(' - ')[0] || postTitle,
          link: postUrl,
          imgUrl: shareImage || settings?.defaultThumb || '',
        });
      });
    };
    document.body.appendChild(script);
    // ...
  }
}, [configData, /* ... */]);</code></pre>
    </details>

    <h3>3.2&nbsp;海报生成 (Poster)</h3>
    <p>海报生成功能通过两个组件协同工作，实现了"所见即所得"的动态渲染。</p>
    <details>
      <summary><strong>实现流程与组件协同</strong></summary>
      <ol>
        <li><strong><code>ShareModal.tsx</code></strong>：点击按钮时，它负责执行 <code>GENERATE_POSTER_MUTATION</code> 获取海报数据。</li>
        <li><strong>数据传递</strong>：获取到 <code>posterData</code> 后，将其传递给 <code>PosterLayout</code> 组件。同时传递一个回调函数 <code>onImagesLoaded={generateImage}</code>。</li>
        <li><strong><code>PosterLayout.tsx</code></strong>：这是一个纯粹的布局组件，它被渲染在屏幕外 (<code>-left-[9999px]</code>)。它接收到数据后，会渲染出海报的完整 DOM 结构。</li>
        <li><strong>图片加载监听</strong>：在 <code>PosterLayout</code> 的 <code>useEffect</code> 中，通过 <code>new Image()</code> 和 <code>Promise.all</code> 来监听所有图片（特色图、头像、Logo）的加载状态。</li>
        <li><strong>触发回调</strong>：所有图片加载完成后，调用从父组件传入的 <code>onImagesLoaded</code> (即 <code>generateImage</code> 函数)。</li>
        <li><strong>Canvas 绘制</strong>：<code>generateImage</code> 函数使用 <code>html2canvas</code> 库来捕捉 <code>PosterLayout</code> 的 DOM 节点，并将其绘制成 Canvas。</li>
        <li><strong>状态更新与显示</strong>：Canvas 转换为 dataURL 后，更新 <code>ShareModal</code> 的 <code>posterImage</code> 状态，最终将生成的海报图片显示给用户。</li>
      </ol>
      <div class="note">这个流程确保了即使图片加载缓慢，也能在所有元素都准备好之后才生成海报，避免了图片丢失的问题。</div>
    </details>

    <h3>3.3&nbsp;文件与数据流</h3>
    <pre><code>// 1. page.tsx (获取文章数据)
<ShareButtons
  shareImage={post.shareImage}
  /* ... */
/>

// 2. ShareButtons.tsx (消费数据)
useWeChatShare(postUrl, postTitle, postExcerpt, shareImage);
// 将 shareImage 传递给微信 SDK

// 3. ShareModal.tsx & PosterLayout.tsx (海报生成)
// ShareModal 获取 posterData -> 传给 PosterLayout
// -> PosterLayout 加载图片后回调 -> ShareModal 生成 Canvas
</code></pre>
  </section>
  
  <section>
    <h2>4&nbsp;·&nbsp;关键决策与优化</h2>
    <ul>
        <li><strong>图片策略中心化</strong>：将分享图片的选取和尺寸处理逻辑集中到后端的 <code>shareImage</code> 字段，是解决微信分享图片问题的关键。它避免了前端复杂的判断，并保证了图片来源的稳定可靠。</li>
        <li><strong>海报数据预处理</strong>：在后端将海报所需图片全部转为 Base64，避免了前端处理跨域（CORS）问题的麻烦，并减少了客户端的网络请求，提升了海报生成的稳定性。</li>
        <li><strong>事件驱动的海报生成</strong>：用 <code>onImagesLoaded</code> 回调取代固定的 <code>setTimeout</code> 延时，是优化海报生成速度和准确性的核心。</li>
        <li><strong>朋友圈标题处理</strong>：通过 <code>postTitle.split(' - ')[0]</code> 来截取标题，移除了站点名称后缀，优化了朋友圈的分享文案。</li>
    </ul>
  </section>

  <footer>
    <p>© Future&nbsp;Decade&nbsp;团队 · 最后更新：<span id="ts"></span></p>
  </footer>
  <script>
    document.getElementById('ts').textContent = new Date().toLocaleString();
  </script>
</body>
</html> 