<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GraphQL查询参考文档 - Future Decade</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #2c3e50;
      border-bottom: 2px solid #eee;
      padding-bottom: 10px;
      margin-top: 30px;
    }
    h2 {
      color: #3498db;
      margin-top: 25px;
    }
    h3 {
      color: #2980b9;
      margin-top: 20px;
    }
    h4 {
      color: #1f618d;
      margin-top: 15px;
    }
    code {
      font-family: 'Courier New', Courier, monospace;
      background-color: #f5f5f5;
      padding: 2px 4px;
      border-radius: 3px;
    }
    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
      line-height: 1.4;
    }
    pre code {
      background-color: transparent;
      padding: 0;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px 12px;
      text-align: left;
    }
    th {
      background-color: #f8f8f8;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .fragment, .query {
      margin-bottom: 30px;
      border-left: 4px solid #3498db;
      padding-left: 15px;
    }
    .query {
      border-left-color: #2ecc71;
    }
    .fragment-name, .query-name {
      font-weight: bold;
      color: #3498db;
    }
    .query-name {
      color: #2ecc71;
    }
    .description {
      margin: 10px 0;
      font-style: italic;
    }
    .nav-container {
      position: fixed;
      top: 20px;
      right: 20px;
      width: 250px;
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .nav-container h3 {
      margin-top: 0;
      border-bottom: 1px solid #eee;
      padding-bottom: 5px;
    }
    .nav-list {
      list-style-type: none;
      padding-left: 0;
    }
    .nav-list li {
      margin-bottom: 5px;
    }
    .nav-list a {
      text-decoration: none;
      color: #3498db;
    }
    .nav-list a:hover {
      text-decoration: underline;
    }
    .category {
      margin-top: 10px;
      font-weight: bold;
    }
    .back-to-top {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #3498db;
      color: white;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-decoration: none;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    @media (max-width: 1200px) {
      .nav-container {
        position: static;
        width: auto;
        margin-bottom: 20px;
      }
    }
  </style>
</head>
<body>
  <a href="#top" class="back-to-top">↑</a>
  
  <div class="nav-container">
    <h3>目录</h3>
    <ul class="nav-list">
      <li><a href="#introduction">简介</a></li>
      <li><a href="#fragments">片段定义</a></li>
      <li class="category">查询分类：</li>
      <li><a href="#post-queries">文章查询</a></li>
      <li><a href="#page-queries">页面查询</a></li>
      <li><a href="#taxonomy-queries">分类法查询</a></li>
      <li><a href="#user-queries">用户查询</a></li>
      <li><a href="#media-queries">媒体查询</a></li>
      <li><a href="#menu-queries">菜单查询</a></li>
      <li><a href="#custom-post-queries">自定义内容类型查询</a></li>
      <li><a href="#settings-queries">设置查询</a></li>
      <li><a href="#comment-queries">评论查询</a></li>
    </ul>
  </div>

  <h1 id="top">GraphQL查询参考文档</h1>
  <p>本文档详细列出了Future Decade前端项目中使用的所有GraphQL片段和查询。每个查询都包含其用途说明和完整定义。</p>
  
  <h2 id="introduction">简介</h2>
  <p>GraphQL是一种用于API的查询语言，它允许客户端明确地指定需要哪些数据，这使得API更加高效且灵活。Future Decade项目使用GraphQL与WordPress后端进行通信，通过WPGraphQL插件提供的端点获取所需的数据。</p>
  
  <h3>如何使用本文档</h3>
  <p>本文档按照功能类别组织了GraphQL查询，您可以通过目录快速导航到相关部分。每个查询包含：</p>
  <ul>
    <li>查询名称和变量</li>
    <li>用途说明</li>
    <li>完整的GraphQL查询定义</li>
  </ul>
  
  <h2 id="fragments">片段定义</h2>
  <p>片段是可重用的查询单元，用于避免重复定义相同的字段集。以下是项目中定义的所有片段：</p>
  
  <div class="fragment">
    <h3 id="post-fragment" class="fragment-name">POST_FRAGMENT</h3>
    <p class="description">文章基本信息片段，包含文章的基本字段如ID、标题、日期、摘要等。</p>
    <pre><code>fragment PostFields on Post {
  id
  title
  date
  slug
  excerpt
  featuredImage {
    node {
      sourceUrl
      altText
    }
  }
  categories {
    nodes {
      id
      name
      slug
    }
  }
}</code></pre>
  </div>
  
  <div class="fragment">
    <h3 id="post-detail-fragment" class="fragment-name">POST_DETAIL_FRAGMENT</h3>
    <p class="description">文章详细信息片段，包含文章的完整信息，包括内容、标签和作者信息。</p>
    <pre><code>fragment PostDetailFields on Post {
  id
  title
  date
  slug
  excerpt
  content
  featuredImage {
    node {
      sourceUrl
      altText
    }
  }
  categories {
    nodes {
      id
      name
      slug
    }
  }
  tags {
    nodes {
      id
      name
      slug
    }
  }
  author {
    node {
      id
      name
      slug
      avatar {
        url
      }
    }
  }
}</code></pre>
  </div>
  
  <div class="fragment">
    <h3 id="category-fragment" class="fragment-name">CATEGORY_FRAGMENT</h3>
    <p class="description">分类信息片段，包含分类的基本字段。</p>
    <pre><code>fragment CategoryFields on Category {
  id
  name
  slug
  count
  description
}</code></pre>
  </div>
  
  <div class="fragment">
    <h3 id="tag-fragment" class="fragment-name">TAG_FRAGMENT</h3>
    <p class="description">标签信息片段，包含标签的基本字段。</p>
    <pre><code>fragment TagFields on Tag {
  id
  name
  slug
  count
}</code></pre>
  </div>
  
  <div class="fragment">
    <h3 id="user-fragment" class="fragment-name">USER_FRAGMENT</h3>
    <p class="description">用户信息片段，包含用户的基本信息。</p>
    <pre><code>fragment UserFields on User {
  id
  name
  slug
  description
  avatar {
    url
  }
}</code></pre>
  </div>
  
  <div class="fragment">
    <h3 id="media-fragment" class="fragment-name">MEDIA_FRAGMENT</h3>
    <p class="description">媒体文件信息片段，包含媒体文件的基本信息。</p>
    <pre><code>fragment MediaFields on MediaItem {
  id
  title
  altText
  sourceUrl
  mediaItemUrl
  mediaType
  mimeType
}</code></pre>
  </div>
  
  <div class="fragment">
    <h3 id="menu-item-fragment" class="fragment-name">MENU_ITEM_FRAGMENT</h3>
    <p class="description">菜单项信息片段，包含菜单项的基本信息。</p>
    <pre><code>fragment MenuItemFields on MenuItem {
  id
  title
  label
  url
  target
  parentId
  cssClasses
}</code></pre>
  </div>
  
  <div class="fragment">
    <h3 id="custom-post-fragment" class="fragment-name">CUSTOM_POST_FRAGMENT</h3>
    <p class="description">自定义文章类型信息片段，包含通用内容节点的基本字段。</p>
    <pre><code>fragment CustomPostFields on ContentNode {
  id
  title
  date
  slug
  uri
  ... on NodeWithFeaturedImage {
    featuredImage {
      node {
        sourceUrl
        altText
      }
    }
  }
  ... on NodeWithAuthor {
    author {
      node {
        id
        name
        slug
      }
    }
  }
}</code></pre>
  </div>
  
  <div class="fragment">
    <h3 id="comment-fragment" class="fragment-name">COMMENT_FRAGMENT</h3>
    <p class="description">评论信息片段，包含评论的基本信息和作者信息。</p>
    <pre><code>fragment CommentFields on Comment {
  id
  databaseId
  content
  date
  parentId
  status
  author {
    node {
      name
      url
      avatar {
        url
      }
    }
  }
}</code></pre>
  </div>

  <h2 id="post-queries">文章查询</h2>
  
  <div class="query">
    <h3 id="get-latest-posts" class="query-name">GET_LATEST_POSTS</h3>
    <p class="description">获取最新文章列表，按日期降序排序。</p>
    <pre><code>query GetLatestPosts($first: Int = 10) {
  posts(first: $first, where: { orderby: { field: DATE, order: DESC } }) {
    nodes {
      ...PostFields
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-post-by-slug" class="query-name">GET_POST_BY_SLUG</h3>
    <p class="description">通过slug获取单个文章的详细信息。</p>
    <pre><code>query GetPostBySlug($slug: ID!) {
  post(id: $slug, idType: SLUG) {
    ...PostDetailFields
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-post-by-id" class="query-name">GET_POST_BY_ID</h3>
    <p class="description">通过ID获取单个文章的详细信息。</p>
    <pre><code>query GetPostById($id: ID!) {
  post(id: $id, idType: DATABASE_ID) {
    ...PostDetailFields
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="search-posts" class="query-name">SEARCH_POSTS</h3>
    <p class="description">根据关键词搜索文章。</p>
    <pre><code>query SearchPosts($search: String!, $first: Int = 10) {
  posts(first: $first, where: { search: $search }) {
    nodes {
      ...PostFields
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-posts-by-category" class="query-name">GET_POSTS_BY_CATEGORY</h3>
    <p class="description">获取指定分类下的文章列表。</p>
    <pre><code>query GetPostsByCategory($categoryId: Int!) {
  posts(where: { categoryId: $categoryId }) {
    nodes {
      ...PostFields
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-posts-by-tag" class="query-name">GET_POSTS_BY_TAG</h3>
    <p class="description">获取指定标签下的文章列表。</p>
    <pre><code>query GetPostsByTag($tagId: String!) {
  posts(where: { tagId: $tagId }) {
    nodes {
      ...PostFields
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-posts-by-author" class="query-name">GET_POSTS_BY_AUTHOR</h3>
    <p class="description">获取指定作者的文章列表。</p>
    <pre><code>query GetPostsByAuthor($authorId: Int!, $first: Int = 10) {
  posts(first: $first, where: { authorId: $authorId }) {
    nodes {
      ...PostFields
    }
  }
}</code></pre>
  </div>

  <div class="query">
    <h3 id="get-home-data" class="query-name">GET_HOME_DATA</h3>
    <p class="description">获取首页数据，包括精选文章、最新文章和分类列表。</p>
    <pre><code>query GetHomeData($featuredPostsCount: Int = 5, $recentPostsCount: Int = 10) {
  featuredPosts: posts(first: $featuredPostsCount, where: { featured: true }) {
    nodes {
      ...PostFields
    }
  }
  recentPosts: posts(first: $recentPostsCount, where: { orderby: { field: DATE, order: DESC } }) {
    nodes {
      ...PostFields
    }
  }
  categories(first: 10) {
    nodes {
      ...CategoryFields
    }
  }
}</code></pre>
  </div>
  
  <h2 id="page-queries">页面查询</h2>
  
  <div class="query">
    <h3 id="get-pages" class="query-name">GET_PAGES</h3>
    <p class="description">获取所有页面的列表。</p>
    <pre><code>query GetPages($first: Int = 10) {
  pages(first: $first) {
    nodes {
      id
      title
      date
      slug
      uri
      featuredImage {
        node {
          sourceUrl
          altText
        }
      }
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-page-by-slug" class="query-name">GET_PAGE_BY_SLUG</h3>
    <p class="description">通过slug获取单个页面的详细信息。</p>
    <pre><code>query GetPageBySlug($slug: ID!) {
  page(id: $slug, idType: URI) {
    id
    title
    content
    slug
    uri
    date
    author {
      node {
        name
        avatar {
          url
        }
      }
    }
    featuredImage {
      node {
        sourceUrl
        altText
      }
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-page-by-id" class="query-name">GET_PAGE_BY_ID</h3>
    <p class="description">通过ID获取单个页面的详细信息。</p>
    <pre><code>query GetPageById($id: ID!) {
  page(id: $id, idType: DATABASE_ID) {
    id
    title
    date
    slug
    uri
    content
    featuredImage {
      node {
        sourceUrl
        altText
      }
    }
    author {
      node {
        id
        name
        slug
        avatar {
          url
        }
      }
    }
  }
}</code></pre>
  </div>
  
  <h2 id="taxonomy-queries">分类法查询</h2>
  
  <div class="query">
    <h3 id="get-categories" class="query-name">GET_CATEGORIES</h3>
    <p class="description">获取所有分类的列表。</p>
    <pre><code>query GetCategories {
  categories {
    nodes {
      ...CategoryFields
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-category-by-slug" class="query-name">GET_CATEGORY_BY_SLUG</h3>
    <p class="description">通过slug获取单个分类的详细信息。</p>
    <pre><code>query GetCategoryBySlug($slug: ID!) {
  category(id: $slug, idType: SLUG) {
    ...CategoryFields
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-tags" class="query-name">GET_TAGS</h3>
    <p class="description">获取所有标签的列表。</p>
    <pre><code>query GetTags {
  tags {
    nodes {
      ...TagFields
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-taxonomies" class="query-name">GET_TAXONOMIES</h3>
    <p class="description">获取所有分类法的列表。</p>
    <pre><code>query GetTaxonomies {
  taxonomies {
    nodes {
      name
      description
      hierarchical
      label
      restBase
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-taxonomy-terms" class="query-name">GET_TAXONOMY_TERMS</h3>
    <p class="description">获取指定分类法下的所有项。</p>
    <pre><code>query GetTaxonomyTerms($taxonomy: TaxonomyEnum!) {
  terms(where: { taxonomies: [$taxonomy] }) {
    nodes {
      __typename
      id
      name
      slug
      uri
      ... on Category {
        count
        description
      }
      ... on Tag {
        count
      }
      ... on PostFormat {
        description
      }
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-taxonomy-term-by-id" class="query-name">GET_TAXONOMY_TERM_BY_ID</h3>
    <p class="description">通过ID获取分类法条目的详细信息。</p>
    <pre><code>query GetTaxonomyTermById($id: ID!, $taxonomy: TaxonomyEnum!) {
  terms(where: { taxonomies: [$taxonomy], include: [$id] }) {
    nodes {
      __typename
      id
      databaseId
      name
      slug
      uri
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-taxonomy-term-by-slug" class="query-name">GET_TAXONOMY_TERM_BY_SLUG</h3>
    <p class="description">通过slug获取分类法条目的详细信息。</p>
    <pre><code>query GetTaxonomyTermBySlug($slug: [String]!, $taxonomy: TaxonomyEnum!) {
  terms(where: { taxonomies: [$taxonomy], slug: $slug }) {
    nodes {
      __typename
      id
      databaseId
      name
      slug
      uri
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-posts-by-taxonomy" class="query-name">GET_POSTS_BY_TAXONOMY</h3>
    <p class="description">获取指定分类法条目下的文章列表。</p>
    <pre><code>query GetPostsByTaxonomy($taxonomy: TaxonomyEnum!, $termId: ID!, $first: Int = 10) {
  posts(
    first: $first
  ) {
    pageInfo {
      hasNextPage
      endCursor
    }
    nodes {
      ...PostFields
    }
  }
  terms(where: { taxonomies: [$taxonomy], include: [$termId] }) {
    nodes {
      __typename
      id
      name
      slug
      uri
      ... on Category {
        count
        description
      }
      ... on Tag {
        count
      }
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-tag-detail" class="query-name">GET_TAG_DETAIL</h3>
    <p class="description">获取标签详情及其文章列表。</p>
    <pre><code>query GetTagDetail($slug: ID!, $first: Int = 10) {
  tag(id: $slug, idType: SLUG) {
    ...TagFields
    description
    posts(first: $first) {
      nodes {
        ...PostFields
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-category-detail" class="query-name">GET_CATEGORY_DETAIL</h3>
    <p class="description">获取分类详情及其文章列表。</p>
    <pre><code>query GetCategoryDetail($slug: ID!, $first: Int = 10) {
  category(id: $slug, idType: SLUG) {
    ...CategoryFields
    description
    posts(first: $first) {
      nodes {
        ...PostFields
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
}</code></pre>
  </div>

  <h2 id="user-queries">用户查询</h2>
  
  <div class="query">
    <h3 id="get-user" class="query-name">GET_USER</h3>
    <p class="description">通过ID获取用户详细信息。</p>
    <pre><code>query GetUser($id: ID!) {
  user(id: $id, idType: DATABASE_ID) {
    ...UserFields
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-users" class="query-name">GET_USERS</h3>
    <p class="description">获取用户列表。</p>
    <pre><code>query GetUsers($first: Int = 10) {
  users(first: $first) {
    nodes {
      ...UserFields
    }
  }
}</code></pre>
  </div>
  
  <h2 id="media-queries">媒体查询</h2>
  
  <div class="query">
    <h3 id="get-media" class="query-name">GET_MEDIA</h3>
    <p class="description">通过ID获取媒体详细信息。</p>
    <pre><code>query GetMedia($id: ID!) {
  mediaItem(id: $id, idType: DATABASE_ID) {
    ...MediaFields
  }
}</code></pre>
  </div>
  
  <h2 id="menu-queries">菜单查询</h2>
  
  <div class="query">
    <h3 id="get-menus" class="query-name">GET_MENUS</h3>
    <p class="description">获取所有菜单及其菜单项。</p>
    <pre><code>query GetMenus {
  menus {
    nodes {
      id
      name
      menuItems {
        nodes {
          ...MenuItemFields
        }
      }
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-menu-by-location" class="query-name">GET_MENU_BY_LOCATION</h3>
    <p class="description">获取指定位置的菜单及其菜单项。</p>
    <pre><code>query GetMenuByLocation($location: MenuLocationEnum!) {
  menus(where: {location: $location}) {
    nodes {
      id
      name
      menuItems {
        nodes {
          ...MenuItemFields
        }
      }
    }
  }
}</code></pre>
  </div>
  
  <h2 id="custom-post-queries">自定义内容类型查询</h2>
  
  <div class="query">
    <h3 id="get-custom-posts" class="query-name">GET_CUSTOM_POSTS</h3>
    <p class="description">获取指定自定义内容类型的列表。</p>
    <pre><code>query GetCustomPosts($type: ContentTypeEnum!, $first: Int = 10) {
  contentNodes(first: $first, where: { contentTypes: [$type] }) {
    nodes {
      __typename
      id
      date
      slug
      uri
      ... on NodeWithTitle {
        title
      }
      ... on NodeWithFeaturedImage {
        featuredImage {
          node {
            sourceUrl
            altText
          }
        }
      }
      ... on NodeWithAuthor {
        author {
          node {
            id
            name
            slug
          }
        }
      }
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-custom-post-by-slug" class="query-name">GET_CUSTOM_POST_BY_SLUG</h3>
    <p class="description">通过slug获取自定义内容类型的详细信息。</p>
    <pre><code>query GetCustomPostBySlug($type: ContentTypeEnum!, $slug: String!) {
  contentNodes(
    first: 1,
    where: {
      contentTypes: [$type],
      name: $slug
    }
  ) {
    nodes {
      __typename
      id
      slug
      uri
      date
      ... on NodeWithTitle {
        title
      }
      ... on NodeWithExcerpt {
        excerpt
      }
      ... on NodeWithContentEditor {
        content
      }
      ... on _note {
        title
        content
        excerpt
      }
      ... on NodeWithFeaturedImage {
        featuredImage {
          node {
            sourceUrl
            altText
          }
        }
      }
      ... on NodeWithAuthor {
        author {
          node {
            id
            name
            slug
            avatar {
              url
            }
          }
        }
      }
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-custom-post-by-id" class="query-name">GET_CUSTOM_POST_BY_ID</h3>
    <p class="description">通过ID获取自定义内容类型的详细信息。</p>
    <pre><code>query GetCustomPostById($type: ContentTypeEnum!, $id: ID!) {
  contentNode(id: $id, idType: DATABASE_ID, contentType: $type) {
    __typename
    id
    uri
    slug
    date
    ... on NodeWithTitle {
      title
    }
    ... on NodeWithExcerpt {
      excerpt
    }
    ... on NodeWithContentEditor {
      content
    }
    ... on _note {
      title
      content
      excerpt
    }
    ... on NodeWithFeaturedImage {
      featuredImage {
        node {
          sourceUrl
          altText
        }
      }
    }
    ... on NodeWithAuthor {
      author {
        node {
          id
          name
          slug
          avatar {
            url
          }
        }
      }
    }
  }
}</code></pre>
  </div>
  
  <h2 id="settings-queries">设置查询</h2>
  
  <div class="query">
    <h3 id="get-all-settings" class="query-name">GET_ALL_SETTINGS</h3>
    <p class="description">获取所有WordPress设置。</p>
    <pre><code>query GetAllSettings {
  allSettings {
    generalSettingsDateFormat
    generalSettingsDescription
    generalSettingsLanguage
    generalSettingsStartOfWeek
    generalSettingsTimeFormat
    generalSettingsTimezone
    generalSettingsTitle
    generalSettingsUrl
    readingSettingsPostsPerPage
    discussionSettingsDefaultCommentStatus
    discussionSettingsDefaultPingStatus
    writingSettingsDefaultCategory
    writingSettingsDefaultPostFormat
    writingSettingsUseSmilies
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-general-settings" class="query-name">GET_GENERAL_SETTINGS</h3>
    <p class="description">获取WordPress一般设置。</p>
    <pre><code>query GetGeneralSettings {
  generalSettings {
    dateFormat
    description
    language
    startOfWeek
    timeFormat
    timezone
    title
    url
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-reading-settings" class="query-name">GET_READING_SETTINGS</h3>
    <p class="description">获取WordPress阅读设置。</p>
    <pre><code>query GetReadingSettings {
  readingSettings {
    postsPerPage
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-discussion-settings" class="query-name">GET_DISCUSSION_SETTINGS</h3>
    <p class="description">获取WordPress讨论设置。</p>
    <pre><code>query GetDiscussionSettings {
  discussionSettings {
    defaultCommentStatus
    defaultPingStatus
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-writing-settings" class="query-name">GET_WRITING_SETTINGS</h3>
    <p class="description">获取WordPress写作设置。</p>
    <pre><code>query GetWritingSettings {
  writingSettings {
    defaultCategory
    defaultPostFormat
    useSmilies
  }
}</code></pre>
  </div>
  
  <h2 id="comment-queries">评论查询</h2>
  
  <div class="query">
    <h3 id="get-post-comments" class="query-name">GET_POST_COMMENTS</h3>
    <p class="description">获取文章的评论列表。</p>
    <pre><code>query GetPostComments($postId: ID!, $first: Int = 100) {
  post(id: $postId, idType: DATABASE_ID) {
    id
    title
    commentCount
    commentStatus
    comments(first: $first) {
      nodes {
        ...CommentFields
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-comment" class="query-name">GET_COMMENT</h3>
    <p class="description">通过ID获取评论详细信息。</p>
    <pre><code>query GetComment($id: ID!) {
  comment(id: $id) {
    ...CommentFields
    post {
      id
      title
      slug
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-comment-replies" class="query-name">GET_COMMENT_REPLIES</h3>
    <p class="description">获取评论的回复列表。</p>
    <pre><code>query GetCommentReplies($id: ID!, $first: Int = 50) {
  comment(id: $id) {
    ...CommentFields
    replies(first: $first) {
      nodes {
        ...CommentFields
      }
    }
  }
}</code></pre>
  </div>
  
  <div class="query">
    <h3 id="get-comments-by-status" class="query-name">GET_COMMENTS_BY_STATUS</h3>
    <p class="description">按状态获取评论列表。</p>
    <pre><code>query GetCommentsByStatus($status: [CommentStatusEnum], $first: Int = 50) {
  comments(where: { statusIn: $status }, first: $first) {
    nodes {
      ...CommentFields
      post {
        id
        title
        slug
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}</code></pre>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 平滑滚动
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
          });
        });
      });
    });
  </script>
</body>
</html> 