<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱与手机验证注册系统 - 前端实现</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #1a73e8;
        }
        h1 {
            border-bottom: 2px solid #eaecef;
            padding-bottom: 10px;
        }
        h2 {
            margin-top: 30px;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 8px;
        }
        h3 {
            margin-top: 24px;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            background-color: #f6f8fa;
            padding: 2px 4px;
            border-radius: 3px;
        }
        pre {
            background-color: #f6f8fa;
            padding: 16px;
            border-radius: 6px;
            overflow: auto;
        }
        pre code {
            padding: 0;
            background-color: transparent;
        }
        .flow-diagram {
            background-color: #f9f9f9;
            padding: 15px;
            border-left: 4px solid #1a73e8;
            margin: 20px 0;
        }
        .note {
            background-color: #fffde7;
            padding: 15px;
            border-left: 4px solid #ffeb3b;
            margin: 20px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>邮箱与手机验证注册系统 - 前端实现</h1>
    
    <p>本文档详细说明了系统中邮箱验证和手机验证注册的前端实现机制。系统采用了React Hooks和TypeScript，实现了一套完整的表单验证和状态管理机制。</p>
    
    <h2>1. 系统架构概述</h2>
    
    <p>前端验证注册系统主要由以下组件构成：</p>
    
    <ul>
        <li><strong>验证表单组件</strong> - 包含邮箱注册表单和手机注册表单</li>
        <li><strong>自定义Hooks</strong> - 处理表单状态和验证逻辑</li>
        <li><strong>GraphQL集成</strong> - 处理与后端的数据交互</li>
        <li><strong>UI组件</strong> - 提供统一的用户界面体验</li>
    </ul>
    
    <h2>2. 核心组件实现</h2>
    
    <h3>2.1 账号验证Hook</h3>
    
    <pre><code>// useAccountValidation.ts
export const useAccountValidation = () => {
    const [validating, setValidating] = useState(false);
    const [isValid, setIsValid] = useState(false);
    const [error, setError] = useState('');

    const validateAccount = useCallback(async (type: 'email' | 'phone', value: string) => {
        setValidating(true);
        try {
            const response = await checkAccountExists({
                variables: { type, value }
            });
            
            const exists = response.data?.checkAccountExists?.exists;
            setIsValid(!exists);
            setError(exists ? `该${type === 'email' ? '邮箱' : '手机号'}已被注册` : '');
            
            return !exists;
        } catch (err) {
            setError('验证失败，请稍后重试');
            return false;
        } finally {
            setValidating(false);
        }
    }, []);

    return {
        validating,
        isValid,
        error,
        validateAccount
    };
};</code></pre>

    <h3>2.2 邮箱注册表单</h3>
    
    <pre><code>// EmailRegisterForm.tsx
export const EmailRegisterForm: React.FC = () => {
    const [email, setEmail] = useState('');
    const [code, setCode] = useState('');
    const [verificationToken, setVerificationToken] = useState('');
    const { validating, isValid, error, validateAccount } = useAccountValidation();
    
    const handleSendCode = async () => {
        if (!isValid) return;
        
        try {
            const response = await sendRegistrationCode({
                variables: { email }
            });
            
            if (response.data?.sendRegistrationCode?.success) {
                message.success('验证码已发送到您的邮箱');
            }
        } catch (err) {
            message.error('发送验证码失败');
        }
    };
    
    const handleVerifyCode = async () => {
        try {
            const response = await verifyRegistrationCodeAndGetToken({
                variables: { email, code }
            });
            
            if (response.data?.verifyRegistrationCodeAndGetToken?.success) {
                setVerificationToken(response.data.verifyRegistrationCodeAndGetToken.token);
            }
        } catch (err) {
            message.error('验证码验证失败');
        }
    };
    
    return (
        <Form>
            {/* 表单实现 */}
        </Form>
    );
};</code></pre>

    <h3>2.3 手机注册表单</h3>
    
    <pre><code>// PhoneRegisterForm.tsx
export const PhoneRegisterForm: React.FC = () => {
    const [phone, setPhone] = useState('');
    const [code, setCode] = useState('');
    const [verificationToken, setVerificationToken] = useState('');
    const { validating, isValid, error, validateAccount } = useAccountValidation();
    
    const handleSendCode = async () => {
        if (!isValid) return;
        
        try {
            const response = await sendPhoneCode({
                variables: { phone, nationCode: '86' }
            });
            
            if (response.data?.sendPhoneCode?.success) {
                message.success('验证码已发送到您的手机');
            }
        } catch (err) {
            message.error('发送验证码失败');
        }
    };
    
    const handleVerifyCode = async () => {
        try {
            const response = await verifyPhoneCodeAndGetToken({
                variables: { phone, code }
            });
            
            if (response.data?.verifyPhoneCodeAndGetToken?.success) {
                setVerificationToken(response.data.verifyPhoneCodeAndGetToken.token);
            }
        } catch (err) {
            message.error('验证码验证失败');
        }
    };
    
    return (
        <Form>
            {/* 表单实现 */}
        </Form>
    );
};</code></pre>

    <h2>3. GraphQL查询与变更</h2>
    
    <h3>3.1 验证相关查询</h3>
    
    <pre><code>// mutations.ts
export const CHECK_ACCOUNT_EXISTS = gql`
    query checkAccountExists($type: String!, $value: String!) {
        checkAccountExists(type: $type, value: $value) {
            exists
            message
        }
    }
`;

export const SEND_REGISTRATION_CODE = gql`
    mutation sendRegistrationCode($email: String!) {
        sendRegistrationCode(email: $email) {
            success
            message
        }
    }
`;

export const VERIFY_REGISTRATION_CODE = gql`
    mutation verifyRegistrationCodeAndGetToken($email: String!, $code: String!) {
        verifyRegistrationCodeAndGetToken(email: $email, code: $code) {
            success
            message
            token
        }
    }
`;</code></pre>

    <h3>3.2 注册相关变更</h3>
    
    <pre><code>export const REGISTER_WITH_TOKEN = gql`
    mutation registerWithToken($input: RegisterWithTokenInput!) {
        registerWithToken(input: $input) {
            success
            message
            userId
        }
    }
`;

export const PHONE_REGISTER_WITH_TOKEN = gql`
    mutation phoneRegisterWithToken($input: PhoneRegisterWithTokenInput!) {
        phoneRegisterWithToken(input: $input) {
            success
            message
            userId
        }
    }
`;</code></pre>

    <h2>4. 状态管理与错误处理</h2>
    
    <h3>4.1 验证状态管理</h3>
    
    <div class="flow-diagram">
        <ol>
            <li>输入验证（实时）</li>
            <li>账号可用性验证（防抖）</li>
            <li>验证码发送状态</li>
            <li>验证码验证状态</li>
            <li>注册令牌状态</li>
        </ol>
    </div>

    <h3>4.2 错误处理机制</h3>
    
    <pre><code>// errorHandling.ts
export const handleGraphQLError = (error: ApolloError) => {
    if (error.networkError) {
        message.error('网络连接失败，请检查网络设置');
        return;
    }

    if (error.graphQLErrors) {
        error.graphQLErrors.forEach(err => {
            switch (err.extensions?.code) {
                case 'VALIDATION_ERROR':
                    message.error('输入验证失败：' + err.message);
                    break;
                case 'RATE_LIMITED':
                    message.error('操作过于频繁，请稍后再试');
                    break;
                default:
                    message.error(err.message || '操作失败，请重试');
            }
        });
    }
};</code></pre>

    <h2>5. UI组件实现</h2>
    
    <h3>5.1 按钮组件</h3>
    
    <pre><code>// Button.tsx
export const Button: React.FC<ButtonProps> = ({
    loading,
    disabled,
    children,
    ...props
}) => {
    const disabledStyles = {
        backgroundColor: 'var(--color-disabled)',
        cursor: 'not-allowed',
        opacity: 0.7
    };

    return (
        <button
            style={{
                ...(disabled && disabledStyles)
            }}
            disabled={disabled || loading}
            {...props}
        >
            {loading ? <Spinner /> : children}
        </button>
    );
};</code></pre>

    <h3>5.2 输入框组件</h3>
    
    <pre><code>// Input.tsx
export const Input: React.FC<InputProps> = ({
    error,
    validating,
    ...props
}) => {
    const borderColor = error
        ? 'var(--color-error)'
        : validating
        ? 'var(--color-validating)'
        : 'var(--color-border)';

    return (
        <div className="input-wrapper">
            <input
                style={{ borderColor }}
                {...props}
            />
            {error && <span className="error-message">{error}</span>}
            {validating && <Spinner size="small" />}
        </div>
    );
};</code></pre>

    <h2>6. 性能优化</h2>
    
    <div class="note">
        <p>系统在性能方面采取了以下优化措施：</p>
        <ol>
            <li><strong>输入防抖</strong> - 使用debounce处理实时验证，避免频繁API调用</li>
            <li><strong>状态缓存</strong> - 使用React.memo和useMemo优化组件重渲染</li>
            <li><strong>Apollo缓存</strong> - 合理配置Apollo Client缓存策略</li>
            <li><strong>代码分割</strong> - 使用React.lazy实现组件懒加载</li>
        </ol>
    </div>

    <h2>7. 安全性考虑</h2>
    
    <div class="note">
        <p>前端实现中的安全措施：</p>
        <ol>
            <li><strong>输入验证</strong> - 客户端输入验证，防止无效数据提交</li>
            <li><strong>令牌管理</strong> - 安全存储和传输验证令牌</li>
            <li><strong>状态保护</strong> - 防止用户跳过验证步骤</li>
            <li><strong>错误处理</strong> - 不暴露敏感错误信息</li>
        </ol>
    </div>

    <h2>8. 总结</h2>
    
    <p>前端实现采用了现代化的技术栈和最佳实践：</p>
    
    <ul>
        <li>使用TypeScript确保类型安全</li>
        <li>采用React Hooks实现状态管理</li>
        <li>GraphQL实现高效的数据交互</li>
        <li>组件化设计提高代码复用性</li>
        <li>完善的错误处理和用户反馈</li>
    </ul>
    
    <p>该实现为用户提供了流畅、安全的注册体验，同时保证了代码的可维护性和可扩展性。</p>
</body>
</html> 