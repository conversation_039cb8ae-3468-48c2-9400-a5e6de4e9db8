<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>V1.6.9 自定义文章类型实现 - Future Decade Frontend</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      font-size: 2em;
      margin-top: 1.5em;
      margin-bottom: 0.8em;
      color: #2c3e50;
      padding-bottom: 0.3em;
      border-bottom: 1px solid #eaecef;
    }
    h2 {
      font-size: 1.5em;
      margin-top: 1.2em;
      margin-bottom: 0.8em;
      color: #2c3e50;
      padding-bottom: 0.3em;
      border-bottom: 1px solid #eaecef;
    }
    h3 {
      font-size: 1.3em;
      margin-top: 1.2em;
      margin-bottom: 0.8em;
      color: #2c3e50;
    }
    pre {
      background-color: #f6f8fa;
      border-radius: 6px;
      padding: 16px;
      overflow: auto;
      line-height: 1.45;
    }
    code {
      font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
      font-size: 0.9em;
      background-color: #f6f8fa;
      padding: 0.2em 0.4em;
      border-radius: 3px;
    }
    pre code {
      background-color: transparent;
      padding: 0;
    }
    .note {
      background-color: #f8f9fa;
      border-left: 4px solid #007bff;
      padding: 15px;
      margin: 20px 0;
    }
    .warning {
      background-color: #fff8f8;
      border-left: 4px solid #dc3545;
      padding: 15px;
      margin: 20px 0;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px 12px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
    }
    img {
      max-width: 100%;
    }
  </style>
</head>
<body>
  <h1>V1.6.9 自定义文章类型实现</h1>
  
  <div class="note">
    <strong>更新日期：</strong> 2023年12月
  </div>
  
  <h2>1. 概述</h2>
  <p>本次更新实现了自定义文章类型的前端展示功能，通过路由<code>/post-type/[type]</code>和<code>/post-type/[type]/[uuid]/[slug]</code>，用户可以浏览各种自定义文章类型内容。实现借鉴了现有的<code>category</code>和<code>tag</code>页面的处理方式，统一使用URL构建工具函数来生成链接。</p>

  <h2>2. 路由结构</h2>
  <p>自定义文章类型使用以下路由结构：</p>
  <ul>
    <li><strong>列表页</strong>：<code>/post-type/[type]</code> - 显示特定类型的所有内容</li>
    <li><strong>详情页</strong>：<code>/post-type/[type]/[uuid]/[slug]</code> - 显示单个内容详情</li>
  </ul>
  <p>其中：</p>
  <ul>
    <li><code>[type]</code> - 自定义文章类型名称，如 "note"、"product" 等</li>
    <li><code>[uuid]</code> - 内容的短UUID，用于唯一标识</li>
    <li><code>[slug]</code> - 内容的别名，用于SEO友好的URL</li>
  </ul>

  <h2>3. 实现详情</h2>
  
  <h3>3.1 URL构建工具</h3>
  <p>在<code>utils/url-builder.ts</code>中添加了构建自定义类型URL的函数：</p>
  <pre><code>/**
 * 构建自定义类型文章详情页URL
 * @param type 自定义类型
 * @param uuid 文章UUID
 * @param slug 文章别名
 * @param prefixes 路由前缀配置
 */
export const buildCustomPostUrl = (
  type: string, 
  uuid: string, 
  slug: string, 
  prefixes: RoutePrefixes
): string => {
  return `/${prefixes.customTypePrefix}/${type}/${uuid}/${slug}`;
};</code></pre>

  <h3>3.2 API函数实现</h3>
  <p>在<code>lib/api.ts</code>中实现了两个关键函数：</p>
  
  <h4>3.2.1 获取自定义类型列表</h4>
  <pre><code>// 获取自定义类型内容列表
export async function getCustomPostsList(type: string) {
  try {
    const contentType = type.toUpperCase();
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: `
            query GetCustomPosts($type: ContentTypeEnum!, $first: Int = 20) {
              contentNodes(first: $first, where: { contentTypes: [$type] }) {
                nodes {
                  __typename
                  id
                  databaseId
                  date
                  slug
                  uri
                  ... on NodeWithTitle {
                    title
                  }
                  ... on NodeWithExcerpt {
                    excerpt
                  }
                  ... on NodeWithFeaturedImage {
                    featuredImage {
                      node {
                        sourceUrl
                        altText
                      }
                    }
                  }
                  ... on NodeWithAuthor {
                    author {
                      node {
                        id
                        name
                        slug
                      }
                    }
                  }
                  ... on _note {
                    title
                    excerpt
                    content
                    shortUuid
                  }
                }
              }
            }
          `,
          variables: {
            type: contentType,
            first: 20
          }
        }),
        next: { revalidate: 1800 } // 30分钟缓存
      }
    );
    
    const data = await response.json();
    return data?.data?.contentNodes?.nodes || [];
  } catch (error) {
    console.error(`Error fetching ${type} list:`, error);
    return [];
  }
}</code></pre>

  <h4>3.2.2 通过UUID获取自定义类型内容</h4>
  <pre><code>// 获取单个自定义类型内容（通过UUID）
export async function getCustomPostByUuid(type: string, uuid: string) {
  try {
    const contentType = type.toUpperCase();
    
    // 直接通过自定义查询获取内容
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: `
            query GetCustomPostBySlug($type: ContentTypeEnum!, $metaValue: String!) {
              contentNodes(
                first: 1,
                where: {
                  contentTypes: [$type],
                  metaQuery: {
                    metaArray: [
                      { key: "_fd_short_uuid", value: $metaValue, compareOperator: EQUAL_TO }
                    ]
                  }
                }
              ) {
                nodes {
                  __typename
                  id
                  databaseId
                  slug
                  uri
                  date
                  ... on NodeWithTitle {
                    title
                  }
                  ... on NodeWithExcerpt {
                    excerpt
                  }
                  ... on NodeWithContentEditor {
                    content
                  }
                  ... on _note {
                    title
                    content
                    excerpt
                  }
                  ... on NodeWithFeaturedImage {
                    featuredImage {
                      node {
                        sourceUrl
                        altText
                      }
                    }
                  }
                  ... on NodeWithAuthor {
                    author {
                      node {
                        id
                        name
                        slug
                      }
                    }
                  }
                }
              }
            }
          `,
          variables: {
            type: contentType,
            metaValue: uuid
          }
        }),
        next: { revalidate: 3600 } // 1小时缓存
      }
    );
    
    const data = await response.json();
    const post = data?.data?.contentNodes?.nodes?.[0];
    
    return post || null;
  } catch (error) {
    console.error(`Error fetching ${type} by UUID ${uuid}:`, error);
    return null;
  }
}</code></pre>

  <h3>3.3 页面组件实现</h3>
  
  <h4>3.3.1 自定义类型列表页 <code>/post-type/[type]/page.tsx</code></h4>
  <p>该页面显示特定类型的内容列表，使用<code>getCustomPostsList</code>函数获取数据：</p>
  <pre><code>export default async function CustomPostTypePage({ params }: { 
  params: { type: string }
}) {
  const { type } = params;
  
  // 获取自定义类型内容列表
  const posts = await getCustomPostsList(type);
  
  // 如果无法获取列表或该类型不存在，返回404
  if (!posts || posts.length === 0) {
    return notFound();
  }
  
  // 获取当前的路由前缀设置...
  
  // 构建自定义类型URL的辅助函数
  const getCustomPostUrl = (postSlug: string, uuid: string) => {
    return buildCustomPostUrl(type, uuid, postSlug, routePrefixes);
  };
  
  return (
    &lt;div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8"&gt;
      {/* 标题和内容 */}
      &lt;div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"&gt;
        {posts.map((post: CustomPost) => (
          &lt;div key={post.id} className="..."&gt;
            {/* 使用统一的URL构建函数 */}
            &lt;Link href={getCustomPostUrl(post.slug, post.shortUuid || post.databaseId?.toString() || '')}&gt;
              {/* 内容展示 */}
            &lt;/Link&gt;
          &lt;/div&gt;
        ))}
      &lt;/div&gt;
    &lt;/div&gt;
  );
}</code></pre>

  <h4>3.3.2 自定义类型详情页 <code>/post-type/[type]/[uuid]/[slug]/page.tsx</code></h4>
  <p>该页面显示单个内容详情，使用<code>getCustomPostByUuid</code>函数获取数据：</p>
  <pre><code>export default async function CustomPostTypePage({ params }: { 
  params: { type: string; uuid: string; slug: string } 
}) {
  const { type, uuid, slug } = params;
  const post = await getCustomPostByUuid(type, uuid) as CustomPost;
  
  // 如果内容不存在，返回404
  if (!post) {
    return notFound();
  }
  
  // 获取路由前缀配置...
  
  // 检查slug是否匹配，处理重定向逻辑...
  
  return (
    &lt;article className="article-container max-w-4xl mx-auto py-10 px-4"&gt;
      &lt;h1 className="text-3xl font-bold mb-6"&gt;{post.title}&lt;/h1&gt;
      
      {/* 内容头部信息 */}
      &lt;div className="article-meta mb-8"&gt;
        &lt;div className="flex items-center text-gray-600 mb-4"&gt;
          &lt;span className="mr-4"&gt;
            发布于: {new Date(post.date).toLocaleDateString('zh-CN')}
          &lt;/span&gt;
          {post.author?.node && (
            &lt;span&gt;作者: {post.author.node.name}&lt;/span&gt;
          )}
        &lt;/div&gt;
      &lt;/div&gt;
      
      {/* 特色图片 */}
      {/* 内容展示 */}
    &lt;/article&gt;
  );
}</code></pre>

  <h3>3.4 自定义组件实现</h3>
  <p><code>CustomPostCard</code>组件用于在列表页中显示内容预览：</p>
  <pre><code>const CustomPostCard: React.FC<CustomPostCardProps> = ({ post, type, prefixes }) => {
  if (!post) return null;
  
  const uuid = post.shortUuid || post.databaseId?.toString() || '';
  
  // 使用URL构建函数生成链接
  const postUrl = buildCustomPostUrl(type, uuid, post.slug, prefixes);
  
  return (
    &lt;div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300"&gt;
      &lt;Link href={postUrl}&gt;
        {/* 内容展示 */}
      &lt;/Link&gt;
    &lt;/div&gt;
  );
};</code></pre>

  <h3>3.5 中间件实现</h3>
  <p>在<code>middleware.ts</code>中添加了对自定义类型URL的处理逻辑：</p>
  <pre><code>// 情况7: 处理自定义类型路径 - 详情页
if (pathSegments.length === 4 && pathSegments[0] === 'post-type') {
  const [_, type, uuid, slug] = pathSegments;
  const isUuidPattern = /^[a-zA-Z0-9]{10}$/.test(uuid);
  
  if (isUuidPattern) {
    // 这是有效的自定义类型详情页URL，无需重写
    return NextResponse.next();
  }
}

// 情况8: 处理自定义类型路径 - 列表页
if (pathSegments.length === 2 && pathSegments[0] === 'post-type') {
  const [_, type] = pathSegments;
  // 这是有效的自定义类型列表页URL，无需重写
  return NextResponse.next();
}</code></pre>

  <h2>4. GraphQL查询关键点</h2>
  <p>自定义类型查询的关键点包括：</p>
  <ol>
    <li><strong>类型名称处理</strong>：将类型名称统一转为大写<code>const contentType = type.toUpperCase();</code></li>
    <li><strong>接口片段</strong>：使用内联片段处理不同接口类型的字段</li>
    <li><strong>特定类型片段</strong>：使用<code>... on _note</code>等特定类型片段获取类型特有的字段</li>
    <li><strong>元数据查询</strong>：通过<code>metaQuery</code>参数查询基于元数据的内容</li>
  </ol>

  <div class="note">
    <p><strong>重要</strong>：自定义类型名称在GraphQL查询中必须与WordPress中注册的ContentTypeEnum值完全匹配。</p>
  </div>

  <h2>5. 使用示例</h2>
  <h3>5.1 访问自定义类型列表</h3>
  <p>用户可以通过以下URL访问特定类型的内容列表：</p>
  <pre><code>https://www.futuredecade.com/post-type/note</code></pre>
  
  <h3>5.2 访问自定义类型详情</h3>
  <p>查看特定内容的详情页：</p>
  <pre><code>https://www.futuredecade.com/post-type/note/abc1234567/my-note-title</code></pre>
  
  <h3>5.3 从其他页面链接到自定义类型</h3>
  <p>在组件中添加链接：</p>
  <pre><code>import { buildCustomPostUrl } from '@/utils/url-builder';
import { useRoutePrefixes } from '@/hooks';

function MyComponent() {
  const { prefixes } = useRoutePrefixes();
  
  // 构建链接
  const noteUrl = buildCustomPostUrl('note', 'abc1234567', 'my-note-title', prefixes);
  
  return (
    &lt;Link href={noteUrl}&gt;查看笔记&lt;/Link&gt;
  );
}</code></pre>

  <h2>6. 注意事项</h2>
  <ol>
    <li>确保在WordPress管理界面的"前端显示设置"中启用了相应的自定义文章类型</li>
    <li>自定义类型必须有"_fd_short_uuid"元数据字段，用于通过UUID查询</li>
    <li>类型名称区分大小写，URL中使用小写（如note），而GraphQL查询中使用大写（如NOTE）</li>
    <li>确保GraphQL架构中包含对应的特定类型片段（如<code>... on _note</code>）</li>
  </ol>
  
  <div class="warning">
    <p><strong>警告</strong>：如果您创建新的自定义文章类型，您需要更新API函数中的GraphQL查询，添加对应的特定类型片段。</p>
  </div>
</body>
</html> 