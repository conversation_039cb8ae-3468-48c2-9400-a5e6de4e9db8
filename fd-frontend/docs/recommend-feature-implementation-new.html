<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>"推荐"功能实现摘要</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; line-height: 1.6; color: #333; max-width: 1200px; margin: 20px auto; padding: 0 20px; }
        h1, h2, h3 { color: #222; }
        h1 { border-bottom: 2px solid #eee; padding-bottom: 10px; }
        h2 { margin-top: 40px; border-bottom: 1px solid #eee; padding-bottom: 8px;}
        pre { background-color: #f6f8fa; padding: 16px; border-radius: 6px; overflow-x: auto; white-space: pre-wrap; word-wrap: break-word; }
        code { font-family: "SFMono-Regular", <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, <PERSON><PERSON>, monospace; font-size: 14px; }
        .summary, .troubleshooting { padding: 15px; margin: 20px 0; border-radius: 6px; }
        .summary { background-color: #eef8ff; border-left: 4px solid #0969da; }
        .troubleshooting { background-color: #fff8e1; border-left: 4px solid #ffc107; }
        ul { padding-left: 20px; }
        .file-path { font-size: 0.9em; font-family: monospace; color: #57606a; margin-bottom: 10px; }
    </style>
</head>
<body>
    <h1>"推荐"功能实现摘要</h1>
    <div class="summary">
        <h3>整体架构</h3>
        <p>"推荐"功能是作为现有"点赞"功能的完整克隆而实现的。这涉及为后端创建一个新的数据库表、新的PHP文件（用于处理业务逻辑和GraphQL集成），并为前端创建新的TypeScript/React文件（用于UI、钩子和状态管理）。成功的关键是确保在逻辑、命名约定，特别是Apollo Client缓存更新机制上实现完美的1对1对等。</p>
    </div>
    <div class="troubleshooting">
        <h3>关键问题排查步骤</h3>
        <ul>
            <li><strong>后端致命错误：</strong> 解决了多种PHP错误，包括因文件末尾多余的<code>&lt;?&gt;</code>引起的<code>headers already sent</code>错误，因不正确的<code>require_once</code>路径引起的<code>Failed to open stream</code>错误，以及因加载时机不当导致的<code>Class "WP_List_Table" not found</code>错误。</li>
            <li><strong>前端缓存更新失败：</strong> 最关键的问题是点击后UI不实时更新。这被追溯到<code>useRecommendAction.ts</code>中错误的缓存ID生成方式。通过使用项目现有的<code>getCacheId</code>工具函数替换手动字符串拼接，确保了Apollo Client能够找到并更新其缓存中正确的数据片段，从而解决了此问题。</li>
        </ul>
    </div>

    <h1>后端实现 (fd-member Plugin)</h1>
    
    <h2>主插件文件: index.php</h2>
    <div class="file-path">fd-member/index.php</div>
    <pre><code class="language-php">&lt;?php
/**
 * Plugin Name: FD Member
 * Plugin URI: https://www.futuredecade.com
 * Description: 会员管理插件基础版，提供用户管理、资料、登录注册等功能
 * Version: 1.0.0
 * Author: 未来学人
 * Author URI: https://www.futuredecade.com
 * Text Domain: fd-member
 * Domain Path: /lang
 * Requires PHP: 7.4
 * Requires at least: 6.2
 */

defined('ABSPATH') || exit;

define('FD_MEMBER_VERSION', '1.0.0');
define('FD_MEMBER_DIR', plugin_dir_path(__FILE__));
define('FD_MEMBER_URI', plugins_url('/', __FILE__));
define('FD_MEMBER_TD', 'fd-member');

// 初始化插件
add_action('plugins_loaded', 'fd_member_init');

function fd_member_init() {
    // 加载翻译文件
    load_plugin_textdomain(FD_MEMBER_TD, false, dirname(plugin_basename(__FILE__)) . '/lang');
    
    // 加载核心功能模块
    require_once FD_MEMBER_DIR . 'includes/verification/verification-core.php';
    require_once FD_MEMBER_DIR . 'includes/verification/email-verification.php';
    
    // 加载用户相关功能
    require_once FD_MEMBER_DIR . 'includes/user/password-reset-core.php';
    require_once FD_MEMBER_DIR . 'includes/user/avatar-settings.php';
    require_once FD_MEMBER_DIR . 'includes/user/email-binding-core.php';
    require_once FD_MEMBER_DIR . 'includes/user/avatar-functions.php';
    
    // 加载点赞相关功能
    if (get_option('fd_member_enable_likes', true)) {
        require_once FD_MEMBER_DIR . 'includes/likes/like-core.php';
        require_once FD_MEMBER_DIR . 'includes/likes/like-notifications.php';
        require_once FD_MEMBER_DIR . 'includes/likes/like-stats.php';
    }
    
    // 加载推荐相关功能
    if (get_option('fd_member_enable_recommends', true)) {
        require_once FD_MEMBER_DIR . 'includes/recommends/recommend-core.php';
        require_once FD_MEMBER_DIR . 'includes/recommends/recommend-stats.php';
    }
    
    // 加载验证相关功能
    require_once FD_MEMBER_DIR . 'includes/verification/email-verification/registration-email-verification.php';
    
    // 加载JWT认证功能
    require_once FD_MEMBER_DIR . 'includes/auth/jwt-auth.php';
    
    // 加载会员等级相关功能
    require_once FD_MEMBER_DIR . 'includes/membership/member-levels.php';
    require_once FD_MEMBER_DIR . 'includes/membership/user-profile-level.php';
    
    // 加载会员支付相关功能
    require_once FD_MEMBER_DIR . 'includes/membership/member-payment.php';
    
    // 确保admin目录下的通知列表文件存在
    if (!file_exists(FD_MEMBER_DIR . 'admin/notifications')) {
        mkdir(FD_MEMBER_DIR . 'admin/notifications', 0755, true);
    }
    
    // 加载通知系统功能
    require_once FD_MEMBER_DIR . 'includes/notifications/notification-core.php';
    require_once FD_MEMBER_DIR . 'includes/notifications/notification-hooks.php';
    require_once FD_MEMBER_DIR . 'includes/notifications/notification-admin.php';
    
    // 在管理员界面加载通知测试页面
    if (is_admin()) {
        require_once FD_MEMBER_DIR . 'admin/notifications/test-notification.php';
        require_once FD_MEMBER_DIR . 'admin/dashboard.php';
        require_once FD_MEMBER_DIR . 'admin/settings-page.php';
        
        // 加载点赞管理界面
        if (get_option('fd_member_enable_likes', true)) {
            require_once FD_MEMBER_DIR . 'admin/likes-manager.php';
        }
        
        // 加载推荐管理界面
        if (get_option('fd_member_enable_recommends', true)) {
            require_once FD_MEMBER_DIR . 'admin/recommends-manager.php';
        }

        // 加载通知管理
        if ( ! class_exists( 'WP_List_Table' ) ) {
            require_once( ABSPATH . 'wp-admin/includes/class-wp-list-table.php' );
        }
        require_once FD_MEMBER_DIR . 'admin/notifications/notification-list.php';
    }
    
    // 如果存在，加载手机验证相关功能
    if (file_exists(FD_MEMBER_DIR . 'includes/verification/phone-verification/phone-auth.php')) {
        require_once FD_MEMBER_DIR . 'includes/verification/phone-verification/phone-auth.php';
    }
    if (file_exists(FD_MEMBER_DIR . 'includes/verification/phone-verification/phone-auth-settings.php')) {
        require_once FD_MEMBER_DIR . 'includes/verification/phone-verification/phone-auth-settings.php';
    }
    
    // 仅在开发环境加载测试功能
    if (defined('WP_DEBUG') &amp;&amp; WP_DEBUG &amp;&amp; file_exists(FD_MEMBER_DIR . 'includes/verification/phone-verification/phone-auth-test.php')) {
        require_once FD_MEMBER_DIR . 'includes/verification/phone-verification/phone-auth-test.php';
    }
    
    // 加载管理界面功能
    require_once FD_MEMBER_DIR . 'admin/menu.php';
    require_once FD_MEMBER_DIR . 'admin/styles.php';
    require_once FD_MEMBER_DIR . 'admin/member-levels.php';
    
    // 加载GraphQL支持
    if (class_exists('WPGraphQL')) {
        require_once FD_MEMBER_DIR . 'graphql/member-graphql-loader.php';
        
        // 加载会员等级GraphQL支持
        if (file_exists(FD_MEMBER_DIR . 'includes/membership/member-graphql.php')) {
            require_once FD_MEMBER_DIR . 'includes/membership/member-graphql.php';
        }
        
        // 加载会员支付GraphQL支持
        if (file_exists(FD_MEMBER_DIR . 'graphql/member-payment.php')) {
            require_once FD_MEMBER_DIR . 'graphql/member-payment.php';
        }
        
        // 加载通知系统GraphQL支持
        require_once FD_MEMBER_DIR . 'includes/notifications/notification-api.php';
        
        // 加载点赞系统GraphQL支持
        if (get_option('fd_member_enable_likes', true)) {
            require_once FD_MEMBER_DIR . 'includes/likes/like-graphql.php';
        }
        
        // 加载用户功能GraphQL支持
        require_once FD_MEMBER_DIR . 'includes/user/password-reset-graphql.php';
        require_once FD_MEMBER_DIR . 'includes/user/email-binding-graphql.php';

        // 加载验证码GraphQL支持
        require_once FD_MEMBER_DIR . 'includes/verification/verification-graphql.php';

        // 加载推荐系统GraphQL支持
        if (get_option('fd_member_enable_recommends', true)) {
            require_once FD_MEMBER_DIR . 'includes/recommends/recommend-graphql.php';
        }
    }
}

/**
 * 修复GraphQL登录认证相关问题
 */
function fd_member_fix_graphql_authentication_issues() {
    // 如果是GraphQL请求，确保禁用可能影响登录的各种功能
    if (defined('GRAPHQL_REQUEST') &amp;&amp; GRAPHQL_REQUEST) {
        // 禁用应用程序密码
        add_filter('wp_is_application_passwords_available', '__return_false');
        
        // 禁用双因素认证检查（如果有）
        if (function_exists('wp_is_two_factor_enabled')) {
            add_filter('two_factor_user_enabled', '__return_false');
        }
        
        // 移除可能干扰登录流程的其他钩子
        remove_all_filters('check_password');
    }
}
add_action('init', 'fd_member_fix_graphql_authentication_issues', 2);

/**
 * 提供直接的登录错误处理，确保GraphQL能获取到具体错误
 */
function fd_member_login_failed_handler($user, $username, $password) {
    // 如果不是GraphQL请求，保持原样
    if (!defined('GRAPHQL_REQUEST') || !GRAPHQL_REQUEST) {
        return $user;
    }
    
    // 如果已经是WP_Error对象，添加我们的错误代码
    if (is_wp_error($user)) {
        if ($user-&gt;get_error_code() == 'application_passwords_disabled') {
            $user = new WP_Error(
                'invalid_login',
                '用户名或密码不正确'
            );
        }
    }
    
    return $user;
}
add_filter('authenticate', 'fd_member_login_failed_handler', 999, 3);

/**
 * 替换默认Gravatar头像为自定义默认头像
 */
function fd_member_custom_default_avatar($avatar_defaults) {
    $custom_avatar = 'https://img.futuredecade.com/s3/ai-generated-8388405_1280.jpg';
    $avatar_defaults['custom_default'] = '自定义默认头像';
    return $avatar_defaults;
}
add_filter('avatar_defaults', 'fd_member_custom_default_avatar');

/**
 * 设置默认头像
 */
function fd_member_set_default_avatar() {
    update_option('avatar_default', 'custom_default');
}
add_action('admin_init', 'fd_member_set_default_avatar');

// 注册插件激活钩子
register_activation_hook(__FILE__, 'fd_member_activate');
function fd_member_activate() {
    // 在调用初始化函数前先加载包含该函数的文件
    require_once FD_MEMBER_DIR . 'includes/notifications/notification-core.php';
    
    // 检查旧表是否存在，如果存在则进行迁移
    global $wpdb;
    $old_table = $wpdb-&gt;prefix . 'fd_member_notifications';
    $old_table_exists = $wpdb-&gt;get_var("SHOW TABLES LIKE '{$old_table}'") == $old_table;
    
    if ($old_table_exists &amp;&amp; function_exists('fd_member_notification_migrate_table')) {
        // 如果旧表存在，进行迁移
        fd_member_notification_migrate_table();
    } else {
        // 如果旧表不存在，直接初始化
        fd_member_notification_init_db();
    }
    
    // 创建点赞表
    if (get_option('fd_member_enable_likes', true)) {
        if (file_exists(FD_MEMBER_DIR . 'includes/likes/like-core.php')) {
            require_once FD_MEMBER_DIR . 'includes/likes/like-core.php';
            fd_member_likes_create_table();
        }
    }

    // 创建推荐表
    if (get_option('fd_member_enable_recommends', true)) {
        if (file_exists(FD_MEMBER_DIR . 'includes/recommends/recommend-core.php')) {
            require_once FD_MEMBER_DIR . 'includes/recommends/recommend-core.php';
            fd_member_recommends_create_table();
        }
    }
    
    // 插件激活时的操作
    flush_rewrite_rules();
}
...
</code></pre>
    
    <h2>核心逻辑: recommend-core.php</h2>
    <div class="file-path">fd-member/includes/recommends/recommend-core.php</div>
    <pre><code class="language-php">&lt;?php
/**
 * 推荐功能核心文件
 * 提供推荐相关的数据库操作和核心函数
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 创建推荐表
 */
function fd_member_recommends_create_table() {
    global $wpdb;
    $table_name = $wpdb-&gt;prefix . 'recommends';
    $charset_collate = $wpdb-&gt;get_charset_collate();

    // 检查表是否已存在
    if ($wpdb-&gt;get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            post_id bigint(20) NOT NULL,
            status tinyint(1) NOT NULL DEFAULT 1,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_post (user_id, post_id),
            KEY post_id (post_id),
            KEY user_id (user_id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}

/**
 * 添加/更新用户推荐
 * 
 * @param int $user_id 用户ID
 * @param int $post_id 文章ID
 * @param int $status 推荐状态: 1 推荐, 0 取消推荐
 * @return bool|int 成功返回记录ID，失败返回false
 */
function fd_member_set_recommend_status($user_id, $post_id, $status = 1) {
    global $wpdb;
    $table_name = $wpdb-&gt;prefix . 'recommends';
    
    // 参数验证
    $user_id = absint($user_id);
    $post_id = absint($post_id);
    $status = absint($status);
    
    if (!$user_id || !$post_id) {
        return false;
    }
    
    // 检查用户和文章是否存在
    if (!get_user_by('id', $user_id) || !get_post($post_id)) {
        return false;
    }
    
    // 查询是否已存在记录
    $existing = $wpdb-&gt;get_row(
        $wpdb-&gt;prepare(
            "SELECT * FROM $table_name WHERE user_id = %d AND post_id = %d",
            $user_id,
            $post_id
        )
    );
    
    if ($existing) {
        // 如果状态相同，直接返回ID
        if ((int)$existing-&gt;status === $status) {
            return $existing-&gt;id;
        }
        
        // 更新状态
        $result = $wpdb-&gt;update(
            $table_name,
            ['status' =&gt; $status],
            ['id' =&gt; $existing-&gt;id],
            ['%d'],
            ['%d']
        );
        
        if ($result !== false) {
            do_action('fd_member_recommend_updated', $user_id, $post_id, $status);
            return $existing-&gt;id;
        }
        
        return false;
    } else {
        // 插入新记录
        $result = $wpdb-&gt;insert(
            $table_name,
            [
                'user_id' =&gt; $user_id,
                'post_id' =&gt; $post_id,
                'status' =&gt; $status
            ],
            ['%d', '%d', '%d']
        );
        
        if ($result) {
            $recommend_id = $wpdb-&gt;insert_id;
            do_action('fd_member_recommend_added', $user_id, $post_id, $status);
            return $recommend_id;
        }
        
        return false;
    }
}

/**
 * 添加推荐
 * 
 * @param int $user_id 用户ID
 * @param int $post_id 文章ID
 * @return bool|int 成功返回记录ID，失败返回false
 */
function fd_member_recommend_post($user_id, $post_id) {
    return fd_member_set_recommend_status($user_id, $post_id, 1);
}

/**
 * 取消推荐
 * 
 * @param int $user_id 用户ID
 * @param int $post_id 文章ID
 * @return bool|int 成功返回记录ID，失败返回false
 */
function fd_member_unrecommend_post($user_id, $post_id) {
    return fd_member_set_recommend_status($user_id, $post_id, 0);
}

/**
 * 获取用户对文章的推荐状态
 * 
 * @param int $user_id 用户ID
 * @param int $post_id 文章ID
 * @return bool|null 已推荐返回true，未推荐或已取消返回false，记录不存在返回null
 */
function fd_member_get_recommend_status($user_id, $post_id) {
    global $wpdb;
    $table_name = $wpdb-&gt;prefix . 'recommends';
    
    $status = $wpdb-&gt;get_var(
        $wpdb-&gt;prepare(
            "SELECT status FROM $table_name WHERE user_id = %d AND post_id = %d",
            absint($user_id),
            absint($post_id)
        )
    );
    
    if ($status === null) {
        return null;
    }
    
    return (bool)$status;
}

/**
 * 获取文章的推荐数
 * 
 * @param int $post_id 文章ID
 * @return int 推荐数
 */
function fd_member_calculate_recommends_count($post_id) {
    global $wpdb;
    $table_name = $wpdb-&gt;prefix . 'recommends';
    
    return (int)$wpdb-&gt;get_var(
        $wpdb-&gt;prepare(
            "SELECT COUNT(*) FROM $table_name WHERE post_id = %d AND status = 1",
            absint($post_id)
        )
    );
}

/**
 * 检查用户是否已推荐文章
 * 
 * @param int $user_id 用户ID
 * @param int $post_id 文章ID
 * @return bool 已推荐返回true，未推荐返回false
 */
function fd_member_user_has_recommended($user_id, $post_id) {
    $status = fd_member_get_recommend_status($user_id, $post_id);
    return $status === true;
}

/**
 * 获取用户推荐的所有文章ID
 * 
 * @param int $user_id 用户ID
 * @param int $limit 限制数量
 * @param int $offset 偏移量
 * @return array 文章ID数组
 */
function fd_member_get_user_recommended_posts($user_id, $limit = 10, $offset = 0) {
    global $wpdb;
    $table_name = $wpdb-&gt;prefix . 'recommends';
    
    return $wpdb-&gt;get_col(
        $wpdb-&gt;prepare(
            "SELECT post_id FROM $table_name 
            WHERE user_id = %d AND status = 1 
            ORDER BY updated_at DESC 
            LIMIT %d OFFSET %d",
            absint($user_id),
            absint($limit),
            absint($offset)
        )
    );
}

/**
 * 获取文章的所有推荐用户ID
 * 
 * @param int $post_id 文章ID
 * @param int $limit 限制数量
 * @param int $offset 偏移量
 * @return array 用户ID数组
 */
function fd_member_get_post_recommenders($post_id, $limit = 10, $offset = 0) {
    global $wpdb;
    $table_name = $wpdb-&gt;prefix . 'recommends';
    
    return $wpdb-&gt;get_col(
        $wpdb-&gt;prepare(
            "SELECT user_id FROM $table_name 
            WHERE post_id = %d AND status = 1 
            ORDER BY updated_at DESC 
            LIMIT %d OFFSET %d",
            absint($post_id),
            absint($limit),
            absint($offset)
        )
    );
}
</code></pre>

    <h2>GraphQL接口: recommend-graphql.php</h2>
    <div class="file-path">fd-member/includes/recommends/recommend-graphql.php</div>
    <pre><code class="language-php">&lt;?php
/**
 * 推荐功能的GraphQL API接口
 * 提供查询和变更操作
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 注册推荐相关的GraphQL类型、查询和变更
 */
function fd_member_recommends_register_graphql_fields() {
    if (!function_exists('register_graphql_object_type') || 
        !function_exists('register_graphql_field') ||
        !function_exists('register_graphql_mutation')) {
        return;
    }

    // 注册推荐状态类型 (RecommendStatus)
    register_graphql_object_type('RecommendStatus', [
        'description' =&gt; '文章推荐状态',
        'fields' =&gt; [
            'postId' =&gt; [
                'type' =&gt; 'Int',
                'description' =&gt; '文章ID'
            ],
            'recommendsCount' =&gt; [
                'type' =&gt; 'Int',
                'description' =&gt; '推荐数量'
            ],
            'userHasRecommended' =&gt; [
                'type' =&gt; 'Boolean',
                'description' =&gt; '当前用户是否已推荐'
            ],
        ]
    ]);

    // 注册获取文章推荐状态的根查询
    register_graphql_field('RootQuery', 'postRecommendStatus', [
        'type' =&gt; 'RecommendStatus',
        'description' =&gt; '获取文章的推荐状态',
        'args' =&gt; [
            'postId' =&gt; [
                'type' =&gt; ['non_null' =&gt; 'Int'],
                'description' =&gt; '文章ID'
            ]
        ],
        'resolve' =&gt; function($root, $args) {
            $post_id = absint($args['postId']);
            $user_id = get_current_user_id();
            
            $recommends_count = fd_member_get_post_recommends_count($post_id);
            $has_recommended = $user_id ? fd_member_user_has_recommended($user_id, $post_id) : false;
            
            return [
                'postId' =&gt; $post_id,
                'recommendsCount' =&gt; $recommends_count,
                'userHasRecommended' =&gt; $has_recommended
            ];
        }
    ]);
    
    // 注册获取文章推荐用户的根查询
    register_graphql_field('RootQuery', 'postRecommenders', [
        'type' =&gt; ['list_of' =&gt; 'User'],
        'description' =&gt; '获取文章的推荐用户列表',
        'args' =&gt; [
            'postId' =&gt; [
                'type' =&gt; ['non_null' =&gt; 'Int'],
                'description' =&gt; '文章ID'
            ],
            'limit' =&gt; [
                'type' =&gt; 'Int',
                'description' =&gt; '限制数量',
                'defaultValue' =&gt; 10
            ],
            'offset' =&gt; [
                'type' =&gt; 'Int',
                'description' =&gt; '偏移量',
                'defaultValue' =&gt; 0
            ]
        ],
        'resolve' =&gt; function($root, $args) {
            $post_id = absint($args['postId']);
            $limit = isset($args['limit']) ? absint($args['limit']) : 10;
            $offset = isset($args['offset']) ? absint($args['offset']) : 0;
            
            $user_ids = fd_member_get_post_recommenders($post_id, $limit, $offset);
            
            if (empty($user_ids)) {
                return [];
            }
            
            $users = [];
            $context = \WPGraphQL::get_app_context();
            
            foreach ($user_ids as $user_id) {
                $user = \WPGraphQL\Data\DataSource::resolve_user($user_id, $context);
                if ($user) {
                    $users[] = $user;
                }
            }
            
            return $users;
        }
    ]);
    
    // 注册获取用户推荐文章的根查询
    register_graphql_field('RootQuery', 'userRecommendedPosts', [
        'type' =&gt; ['list_of' =&gt; 'Post'],
        'description' =&gt; '获取用户推荐的文章列表',
        'args' =&gt; [
            'userId' =&gt; [
                'type' =&gt; 'Int',
                'description' =&gt; '用户ID，默认为当前用户'
            ],
            'limit' =&gt; [
                'type' =&gt; 'Int',
                'description' =&gt; '限制数量',
                'defaultValue' =&gt; 10
            ],
            'offset' =&gt; [
                'type' =&gt; 'Int',
                'description' =&gt; '偏移量',
                'defaultValue' =&gt; 0
            ]
        ],
        'resolve' =&gt; function($root, $args) {
            $user_id = isset($args['userId']) ? absint($args['userId']) : get_current_user_id();
            
            if (!$user_id) {
                throw new \GraphQL\Error\UserError('请先登录或指定用户ID');
            }
            
            $limit = isset($args['limit']) ? absint($args['limit']) : 10;
            $offset = isset($args['offset']) ? absint($args['offset']) : 0;
            
            $post_ids = fd_member_get_user_recommended_posts($user_id, $limit, $offset);
            
            if (empty($post_ids)) {
                return [];
            }
            
            $posts = [];
            $context = \WPGraphQL::get_app_context();
            
            foreach ($post_ids as $post_id) {
                $post = \WPGraphQL\Data\DataSource::resolve_post_object($post_id, $context);
                if ($post) {
                    $posts[] = $post;
                }
            }
            
            return $posts;
        }
    ]);
    
    // 注册推荐变更 (Mutation)
    register_graphql_mutation('recommendPost', [
        'inputFields' =&gt; [
            'postId' =&gt; [
                'type' =&gt; ['non_null' =&gt; 'Int'],
                'description' =&gt; '要推荐的文章ID'
            ]
        ],
        'outputFields' =&gt; [
            'success' =&gt; [
                'type' =&gt; 'Boolean',
                'description' =&gt; '操作是否成功'
            ],
            'message' =&gt; [
                'type' =&gt; 'String',
                'description' =&gt; '操作结果消息'
            ],
            'recommendStatus' =&gt; [
                'type' =&gt; 'RecommendStatus',
                'description' =&gt; '更新后的推荐状态',
                'resolve' =&gt; function($payload) {
                    if (!isset($payload['post_id'])) {
                        return null;
                    }
                    
                    $post_id = $payload['post_id'];
                    $user_id = get_current_user_id();
                    
                    return [
                        'postId' =&gt; $post_id,
                        'recommendsCount' =&gt; fd_member_get_post_recommends_count($post_id),
                        'userHasRecommended' =&gt; $user_id ? fd_member_user_has_recommended($user_id, $post_id) : false
                    ];
                }
            ]
        ],
        'mutateAndGetPayload' =&gt; function($input) {
            if (!is_user_logged_in()) {
                throw new \GraphQL\Error\UserError('请先登录后再推荐');
            }
            
            $user_id = get_current_user_id();
            $post_id = absint($input['postId']);
            
            $post = get_post($post_id);
            if (!$post) {
                throw new \GraphQL\Error\UserError('文章不存在');
            }
            
            $result = fd_member_recommend_post($user_id, $post_id);
            
            if (!$result) {
                return [
                    'success' =&gt; false,
                    'message' =&gt; '推荐失败，请稍后重试',
                    'post_id' =&gt; $post_id
                ];
            }
            
            return [
                'success' =&gt; true,
                'message' =&gt; '推荐成功',
                'post_id' =&gt; $post_id
            ];
        }
    ]);
    
    // 注册取消推荐变更 (Mutation)
    register_graphql_mutation('unrecommendPost', [
        'inputFields' =&gt; [
            'postId' =&gt; [
                'type' =&gt; ['non_null' =&gt; 'Int'],
                'description' =&gt; '要取消推荐的文章ID'
            ]
        ],
        'outputFields' =&gt; [
            'success' =&gt; [
                'type' =&gt; 'Boolean',
                'description' =&gt; '操作是否成功'
            ],
            'message' =&gt; [
                'type' =&gt; 'String',
                'description' =&gt; '操作结果消息'
            ],
            'recommendStatus' =&gt; [
                'type' =&gt; 'RecommendStatus',
                'description' =&gt; '更新后的推荐状态',
                'resolve' =&gt; function($payload) {
                    if (!isset($payload['post_id'])) {
                        return null;
                    }
                    
                    $post_id = $payload['post_id'];
                    $user_id = get_current_user_id();
                    
                    return [
                        'postId' =&gt; $post_id,
                        'recommendsCount' =&gt; fd_member_get_post_recommends_count($post_id),
                        'userHasRecommended' =&gt; $user_id ? fd_member_user_has_recommended($user_id, $post_id) : false
                    ];
                }
            ]
        ],
        'mutateAndGetPayload' =&gt; function($input) {
            if (!is_user_logged_in()) {
                throw new \GraphQL\Error\UserError('请先登录后再取消推荐');
            }
            
            $user_id = get_current_user_id();
            $post_id = absint($input['postId']);
            
            $post = get_post($post_id);
            if (!$post) {
                throw new \GraphQL\Error\UserError('文章不存在');
            }
            
            $result = fd_member_unrecommend_post($user_id, $post_id);
            
            if (!$result) {
                return [
                    'success' =&gt; false,
                    'message' =&gt; '取消推荐失败，请稍后重试',
                    'post_id' =&gt; $post_id
                ];
            }
            
            return [
                'success' =&gt; true,
                'message' =&gt; '取消推荐成功',
                'post_id' =&gt; $post_id
            ];
        }
    ]);
}
</code></pre>

    <h2>后台管理: recommends-manager.php</h2>
    <div class="file-path">fd-member/admin/recommends-manager.php</div>
    <pre><code class="language-php">&lt;?php
/**
 * 推荐管理界面
 * 提供查看和管理推荐数据的功能
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 添加推荐管理菜单
 */
function fd_member_recommends_add_admin_menu() {
    add_submenu_page(
        'fd-member',   // 父菜单slug
        '推荐管理',               // 页面标题
        '推荐管理',               // 菜单标题
        'manage_options',        // 权限
        'fd-recommends-manager', // 菜单slug
        'fd_member_recommends_admin_page' // 回调函数
    );
}
add_action('admin_menu', 'fd_member_recommends_add_admin_menu', 21); // 调整优先级以保证顺序

/**
 * 渲染推荐管理页面
 */
function fd_member_recommends_admin_page() {
    // 处理批量操作
    if (isset($_POST['action']) &amp;&amp; isset($_POST['recommend_ids']) &amp;&amp; is_array($_POST['recommend_ids'])) {
        $action = sanitize_text_field($_POST['action']);
        $recommend_ids = array_map('absint', $_POST['recommend_ids']);
        
        if ($action === 'delete' &amp;&amp; !empty($recommend_ids) &amp;&amp; current_user_can('manage_options')) {
            fd_member_recommends_bulk_delete($recommend_ids);
            echo '&lt;div class="notice notice-success is-dismissible"&gt;&lt;p&gt;已成功删除所选推荐记录。&lt;/p&gt;&lt;/div&gt;';
        }
    }
    
    $page = isset($_GET['paged']) ? absint($_GET['paged']) : 1;
    $per_page = 20;
    
    $filter_user = isset($_GET['filter_user']) ? absint($_GET['filter_user']) : 0;
    $filter_post = isset($_GET['filter_post']) ? absint($_GET['filter_post']) : 0;
    $filter_status = isset($_GET['filter_status']) ? sanitize_text_field($_GET['filter_status']) : '';
    
    $recommends_data = fd_member_recommends_get_admin_data($page, $per_page, $filter_user, $filter_post, $filter_status);
    $recommends = $recommends_data['recommends'];
    $total_recommends = $recommends_data['total'];
    
    $total_pages = ceil($total_recommends / $per_page);
    
    ?&gt;
    &lt;div class="wrap"&gt;
        &lt;h1 class="wp-heading-inline"&gt;推荐管理&lt;/h1&gt;
        &lt;hr class="wp-header-end"&gt;
        
        &lt;form method="get"&gt;
            &lt;input type="hidden" name="page" value="fd-recommends-manager"&gt;
            &lt;div class="tablenav top"&gt;
                &lt;div class="alignleft actions"&gt;
                    &lt;input type="number" name="filter_user" value="&lt;?php echo esc_attr($filter_user); ?&gt;" placeholder="用户ID" min="0"&gt;
                    &lt;input type="number" name="filter_post" value="&lt;?php echo esc_attr($filter_post); ?&gt;" placeholder="文章ID" min="0"&gt;
                    &lt;select name="filter_status"&gt;
                        &lt;option value="" &lt;?php selected($filter_status, ''); ?&gt;&gt;所有状态&lt;/option&gt;
                        &lt;option value="1" &lt;?php selected($filter_status, '1'); ?&gt;&gt;已推荐&lt;/option&gt;
                        &lt;option value="0" &lt;?php selected($filter_status, '0'); ?&gt;&gt;已取消&lt;/option&gt;
                    &lt;/select&gt;
                    &lt;?php submit_button('筛选', 'action', '', false); ?&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/form&gt;
        
        &lt;form method="post"&gt;
            &lt;table class="wp-list-table widefat fixed striped"&gt;
                &lt;thead&gt;
                    &lt;tr&gt;
                        &lt;td class="manage-column column-cb check-column"&gt;&lt;input type="checkbox"&gt;&lt;/td&gt;
                        &lt;th&gt;ID&lt;/th&gt;
                        &lt;th&gt;用户&lt;/th&gt;
                        &lt;th&gt;文章&lt;/th&gt;
                        &lt;th&gt;状态&lt;/th&gt;
                        &lt;th&gt;日期&lt;/th&gt;
                        &lt;th&gt;操作&lt;/th&gt;
                    &lt;/tr&gt;
                &lt;/thead&gt;
                &lt;tbody&gt;
                    &lt;?php if (!empty($recommends)) : ?&gt;
                        &lt;?php foreach ($recommends as $recommend) : ?&gt;
                            &lt;tr&gt;
                                &lt;th scope="row" class="check-column"&gt;&lt;input type="checkbox" name="recommend_ids[]" value="&lt;?php echo esc_attr($recommend-&gt;id); ?&gt;"&gt;&lt;/th&gt;
                                &lt;td&gt;&lt;?php echo esc_html($recommend-&gt;id); ?&gt;&lt;/td&gt;
                                &lt;td&gt;
                                    &lt;?php
                                    $user = get_userdata($recommend-&gt;user_id);
                                    echo $user ? '&lt;a href="' . esc_url(get_edit_user_link($recommend-&gt;user_id)) . '"&gt;' . esc_html($user-&gt;display_name) . '&lt;/a&gt;' : '未知用户';
                                    ?&gt;
                                &lt;/td&gt;
                                &lt;td&gt;
                                    &lt;?php
                                    $post = get_post($recommend-&gt;post_id);
                                    echo $post ? '&lt;a href="' . esc_url(get_edit_post_link($recommend-&gt;post_id)) . '"&gt;' . esc_html(get_the_title($recommend-&gt;post_id)) . '&lt;/a&gt;' : '未知文章';
                                    ?&gt;
                                &lt;/td&gt;
                                &lt;td&gt;&lt;?php echo $recommend-&gt;status ? '&lt;span style="color:green;"&gt;已推荐&lt;/span&gt;' : '&lt;span style="color:red;"&gt;已取消&lt;/span&gt;'; ?&gt;&lt;/td&gt;
                                &lt;td&gt;&lt;?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($recommend-&gt;created_at))); ?&gt;&lt;/td&gt;
                                &lt;td&gt;&lt;a href="&lt;?php echo esc_url(wp_nonce_url(admin_url('admin.php?page=fd-recommends-manager&amp;action=delete&amp;recommend_id=' . $recommend-&gt;id), 'delete-recommend_' . $recommend-&gt;id)); ?&gt;"&gt;删除&lt;/a&gt;&lt;/td&gt;
                            &lt;/tr&gt;
                        &lt;?php endforeach; ?&gt;
                    &lt;?php else : ?&gt;
                        &lt;tr&gt;&lt;td colspan="7"&gt;暂无推荐数据&lt;/td&gt;&lt;/tr&gt;
                    &lt;?php endif; ?&gt;
                &lt;/tbody&gt;
            &lt;/table&gt;
            &lt;div class="tablenav bottom"&gt;
                &lt;div class="alignleft actions bulkactions"&gt;
                    &lt;select name="action"&gt;&lt;option value="-1"&gt;批量操作&lt;/option&gt;&lt;option value="delete"&gt;删除&lt;/option&gt;&lt;/select&gt;
                    &lt;?php submit_button('应用', 'action', '', false); ?&gt;
                &lt;/div&gt;
                &lt;div class="tablenav-pages"&gt;
                    &lt;span class="displaying-num"&gt;&lt;?php echo esc_html(sprintf('共 %s 项', $total_recommends)); ?&gt;&lt;/span&gt;
                    &lt;?php
                    // Simplified pagination
                    echo paginate_links([
                        'base' =&gt; add_query_arg('paged', '%#%'),
                        'format' =&gt; '',
                        'prev_text' =&gt; __('&amp;laquo;'),
                        'next_text' =&gt; __('&amp;raquo;'),
                        'total' =&gt; $total_pages,
                        'current' =&gt; $page
                    ]);
                    ?&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/form&gt;
    &lt;/div&gt;
    &lt;?php
}

add_action('admin_init', function() {
    if (isset($_GET['page']) &amp;&amp; $_GET['page'] === 'fd-recommends-manager' &amp;&amp; 
        isset($_GET['action']) &amp;&amp; $_GET['action'] === 'delete' &amp;&amp; 
        isset($_GET['recommend_id']) &amp;&amp; is_numeric($_GET['recommend_id'])) {
        
        $recommend_id = absint($_GET['recommend_id']);
        check_admin_referer('delete-recommend_' . $recommend_id);
        if (!current_user_can('manage_options')) wp_die('权限不足');
        
        fd_member_recommends_delete($recommend_id);
        wp_redirect(admin_url('admin.php?page=fd-recommends-manager&amp;deleted=1'));
        exit;
    }
});

function fd_member_recommends_get_admin_data($page = 1, $per_page = 20, $user_id = 0, $post_id = 0, $status = '') {
    global $wpdb;
    $table_name = $wpdb-&gt;prefix . 'recommends';
    $offset = ($page - 1) * $per_page;
    $where_clauses = [];
    $query_values = [];

    if ($user_id) {
        $where_clauses[] = 'user_id = %d';
        $query_values[] = $user_id;
    }
    if ($post_id) {
        $where_clauses[] = 'post_id = %d';
        $query_values[] = $post_id;
    }
    if ($status !== '') {
        $where_clauses[] = 'status = %d';
        $query_values[] = $status;
    }

    $where_sql = !empty($where_clauses) ? 'WHERE ' . implode(' AND ', $where_clauses) : '';
    
    $total_sql = "SELECT COUNT(*) FROM $table_name $where_sql";
    if (!empty($query_values)) {
        $total = $wpdb-&gt;get_var($wpdb-&gt;prepare($total_sql, $query_values));
    } else {
        $total = $wpdb-&gt;get_var($total_sql);
    }
    
    $data_sql = "SELECT * FROM $table_name $where_sql ORDER BY id DESC LIMIT %d OFFSET %d";
    array_push($query_values, $per_page, $offset);
    $recommends = $wpdb-&gt;get_results($wpdb-&gt;prepare($data_sql, $query_values));
    
    return ['recommends' =&gt; $recommends, 'total' =&gt; $total];
}

function fd_member_recommends_delete($recommend_id) {
    global $wpdb;
    $table_name = $wpdb-&gt;prefix . 'recommends';
    $recommend = $wpdb-&gt;get_row($wpdb-&gt;prepare("SELECT * FROM $table_name WHERE id = %d", $recommend_id));
    if ($recommend) {
        $wpdb-&gt;delete($table_name, ['id' =&gt; $recommend_id], ['%d']);
        fd_member_update_post_recommends_count_cache($recommend-&gt;post_id);
    }
}

function fd_member_recommends_bulk_delete($recommend_ids) {
    global $wpdb;
    $table_name = $wpdb-&gt;prefix . 'recommends';
    $post_ids = $wpdb-&gt;get_col("SELECT DISTINCT post_id FROM $table_name WHERE id IN (" . implode(',', array_map('intval', $recommend_ids)) . ")");
    
    $placeholders = implode(',', array_fill(0, count($recommend_ids), '%d'));
    $wpdb-&gt;query($wpdb-&gt;prepare("DELETE FROM $table_name WHERE id IN ($placeholders)", $recommend_ids));
    
    foreach ($post_ids as $post_id) {
        fd_member_update_post_recommends_count_cache($post_id);
    }
}
</code></pre>

    <h1>前端实现 (fd-frontend App)</h1>

    <h2>GraphQL变更: mutations.ts</h2>
    <div class="file-path">fd-frontend/src/lib/graphql/mutations.ts</div>
    <pre><code class="language-typescript">
import { gql } from '@apollo/client';
import { USER_FRAGMENT, COMMENT_FRAGMENT, USER_DETAIL_FRAGMENT, NOTIFICATION_FRAGMENT } from './fragments';

// ... other mutations

// 点赞文章
export const LIKE_POST = gql`
  mutation LikePost($postId: Int!) {
    likePost(input: { postId: $postId }) {
      success
      likeStatus {
        likesCount
        userHasLiked
      }
    }
  }
`;

// 取消点赞文章
export const UNLIKE_POST = gql`
  mutation UnlikePost($postId: Int!) {
    unlikePost(input: { postId: $postId }) {
      success
      likeStatus {
        likesCount
        userHasLiked
      }
    }
  }
`;

// 推荐文章
export const RECOMMEND_POST = gql`
  mutation RecommendPost($postId: Int!) {
    recommendPost(input: { postId: $postId }) {
      success
      recommendStatus {
        recommendsCount
        userHasRecommended
      }
    }
  }
`;

// 取消推荐文章
export const UNRECOMMEND_POST = gql`
  mutation UnrecommendPost($postId: Int!) {
    unrecommendPost(input: { postId: $postId }) {
      success
      recommendStatus {
        recommendsCount
        userHasRecommended
      }
    }
  }
`;

// ... other mutations
</code></pre>

    <h2>GraphQL片段: fragments.ts</h2>
    <div class="file-path">fd-frontend/src/lib/graphql/fragments.ts</div>
    <pre><code class="language-typescript">
import { gql } from '@apollo/client';

// ... other fragments

// 自定义文章类型信息片段
export const CUSTOM_POST_FRAGMENT = gql`
  fragment CustomPostFields on ContentNode {
    id
    databaseId
    likesCount
    userHasLiked
    recommendsCount
    userHasRecommended
    title
    date
    slug
    uri
    ... on NodeWithFeaturedImage {
      featuredImage {
        node {
          sourceUrl
          altText
        }
      }
    }
    ... on NodeWithAuthor {
      author {
        node {
          id
          name
          slug
        }
      }
    }
  }
`;

// ... other fragments
</code></pre>

    <h2>核心Hook: useRecommendAction.ts</h2>
    <div class="file-path">fd-frontend/src/hooks/useRecommendAction.ts</div>
    <pre><code class="language-typescript">
import { useMutation, gql, ApolloCache, ApolloError } from '@apollo/client';
import React, { useContext } from 'react';
import { RECOMMEND_POST, UNRECOMMEND_POST } from '../lib/graphql/mutations';
import { AuthContext } from '../contexts/AuthContext';
import { Post } from '../types/post';
import { getCacheId } from '../utils/cache-utils';

interface RecommendPostPayload {
  recommendPost: {
    __typename: 'RecommendPostPayload';
    success: boolean;
    recommendStatus: {
      recommendsCount: number;
      userHasRecommended: boolean;
    }
  };
}

interface UnrecommendPostPayload {
  unrecommendPost: {
    __typename: 'UnrecommendPostPayload';
    success: boolean;
    recommendStatus: {
      recommendsCount: number;
      userHasRecommended: boolean;
    }
  };
}

interface UseRecommendActionOptions {
  id: string; // GraphQL ID
  postId: number; // Database Post ID
  currentIsRecommended: boolean;
  currentRecommendsCount: number;
  contentType?: string; // 新增参数，用于指定内容类型
}

/**
 * 处理文章推荐/取消推荐的自定义Hook
 * @param id 文章的GraphQL ID
 * @param postId 文章的数据库ID
 * @param currentIsRecommended 当前是否已推荐
 * @param currentRecommendsCount 当前推荐数
 * @param contentType 内容类型，默认为'post'
 * @returns { handleRecommend, handleUnrecommend, loading }
 */
export const useRecommendAction = ({ 
  id, 
  postId, 
  currentIsRecommended, 
  currentRecommendsCount,
  contentType = 'post' // 默认为标准文章类型
}: UseRecommendActionOptions) =&gt; {
  const authContext = useContext(AuthContext);

  if (!authContext) {
    throw new Error('useRecommendAction must be used within an AuthProvider');
  }

  const { isAuthenticated } = authContext;

  // 获取正确的类型名称，用于缓存ID
  const getTypeName = () =&gt; {
    // 如果是标准文章类型，直接返回'Post'
    if (contentType === 'post') return 'Post';
    // 对于自定义类型，首字母大写
    return contentType.charAt(0).toUpperCase() + contentType.slice(1);
  };

  // 构建缓存ID
  const cacheId = getCacheId(getTypeName(), id);

  const [recommendPost, { loading: recommendLoading }] = useMutation&lt;RecommendPostPayload&gt;(RECOMMEND_POST, {
    update: (cache: ApolloCache&lt;RecommendPostPayload&gt;, { data }: { data?: RecommendPostPayload | null }) =&gt; {
      console.log(`[useRecommendAction] Recommend mutation update function called for ${contentType}.`, data);
      if (data?.recommendPost?.success) {
        const { recommendsCount, userHasRecommended } = data.recommendPost.recommendStatus;
        
        // 使用动态类型名称构建片段
        const typeName = getTypeName();
        
        cache.writeFragment({
          id: cacheId,
          fragment: gql`
            fragment UpdateRecommendStatus on ${typeName} {
              recommendsCount
              userHasRecommended
            }
          `,
          data: {
            recommendsCount: recommendsCount,
            userHasRecommended: userHasRecommended,
          },
        });
        
        console.log(`[useRecommendAction] Cache updated for ${typeName} with ID: ${id}`);
      }
    },
    onError: (error: ApolloError) =&gt; {
      console.error(`[useRecommendAction] Error on recommend mutation for ${contentType}:`, error);
    },
    onCompleted: (data: RecommendPostPayload) =&gt; {
      console.log(`[useRecommendAction] Recommend mutation completed for ${contentType}.`, data);
    }
  });

  const [unrecommendPost, { loading: unrecommendLoading }] = useMutation&lt;UnrecommendPostPayload&gt;(UNRECOMMEND_POST, {
    update: (cache: ApolloCache&lt;UnrecommendPostPayload&gt;, { data }: { data?: UnrecommendPostPayload | null }) =&gt; {
      console.log(`[useRecommendAction] Unrecommend mutation update function called for ${contentType}.`, data);
      if (data?.unrecommendPost?.success) {
        const { recommendsCount, userHasRecommended } = data.unrecommendPost.recommendStatus;
        
        // 使用动态类型名称构建片段
        const typeName = getTypeName();
        
        cache.writeFragment({
          id: cacheId,
          fragment: gql`
            fragment UpdateUnrecommendStatus on ${typeName} {
              recommendsCount
              userHasRecommended
            }
          `,
          data: {
            recommendsCount: recommendsCount,
            userHasRecommended: userHasRecommended,
          },
        });
        
        console.log(`[useRecommendAction] Cache updated for ${typeName} with ID: ${id}`);
      }
    },
    onError: (error: ApolloError) =&gt; {
      console.error(`[useRecommendAction] Error on unrecommend mutation for ${contentType}:`, error);
    },
    onCompleted: (data: UnrecommendPostPayload) =&gt; {
      console.log(`[useRecommendAction] Unrecommend mutation completed for ${contentType}.`, data);
    }
  });

  const handleRecommend = () =&gt; {
    console.log(`[useRecommendAction] handleRecommend called for ${contentType}.`);
    if (!isAuthenticated) {
      console.log(`[useRecommendAction] User not authenticated for recommend action on ${contentType}.`);
      return;
    }
    recommendPost({ 
      variables: { postId },
      optimisticResponse: {
        recommendPost: {
          __typename: 'RecommendPostPayload',
          success: true,
          recommendStatus: {
            recommendsCount: currentRecommendsCount + 1,
            userHasRecommended: true,
          }
        },
      }
    });
  };

  const handleUnrecommend = () =&gt; {
    console.log(`[useRecommendAction] handleUnrecommend called for ${contentType}.`);
    if (!isAuthenticated) {
      console.log(`[useRecommendAction] User not authenticated for unrecommend action on ${contentType}.`);
      return;
    }
    unrecommendPost({ 
      variables: { postId },
      optimisticResponse: {
        unrecommendPost: {
          __typename: 'UnrecommendPostPayload',
          success: true,
          recommendStatus: {
            recommendsCount: currentRecommendsCount - 1,
            userHasRecommended: false,
          }
        },
      }
    });
  };
  
  return {
    handleRecommend,
    handleUnrecommend,
    loading: recommendLoading || unrecommendLoading,
  };
};
</code></pre>

    <h2>UI组件: RecommendButton.tsx</h2>
    <div class="file-path">fd-frontend/src/components/post/RecommendButton.tsx</div>
    <pre><code class="language-typescript">
'use client';

import React, { useContext } from 'react';
import { useQuery, gql } from '@apollo/client';
import { useRecommendAction } from '@/hooks/useRecommendAction';
import { AuthContext } from '@/contexts/AuthContext';
import { Heart } from 'lucide-react';

interface RecommendButtonProps {
  id: string;
  postId: number;
  initialRecommendsCount: number;
  initialIsRecommended: boolean;
  contentType?: string;
}

// 为标准文章动态构建推荐状态查询
const buildPostRecommendQuery = () =&gt; gql`
  query PostRecommendStatus($postId: ID!) {
    post(id: $postId, idType: DATABASE_ID) {
      id
      databaseId
      recommendsCount
      userHasRecommended
    }
  }
`;

// 为自定义文章类型动态构建推荐状态查询
const buildCustomPostRecommendQuery = (typeName: string) =&gt; gql`
  query CustomPostRecommendStatus($postId: ID!) {
    contentNode(id: $postId, idType: DATABASE_ID) {
      id
      databaseId
      ... on ${typeName} {
        recommendsCount
        userHasRecommended
      }
    }
  }
`;

export const RecommendButton: React.FC&lt;RecommendButtonProps&gt; = ({ 
  id, 
  postId, 
  initialRecommendsCount, 
  initialIsRecommended,
  contentType = 'post'
}) =&gt; {
  const authContext = useContext(AuthContext);

  const isCustomPost = contentType !== 'post';
  
  const query = isCustomPost
    ? buildCustomPostRecommendQuery(contentType.charAt(0).toUpperCase() + contentType.slice(1))
    : buildPostRecommendQuery();

  const variables = { postId: String(postId) };

  const { data } = useQuery(query, {
    variables,
    skip: !postId,
    onError: (error) =&gt; {
      console.error(`[RecommendButton] Query error for ${contentType} with ID: ${postId}`, error);
    }
  });
  
  const result = isCustomPost ? data?.contentNode : data?.post;
  const isRecommended = result?.userHasRecommended ?? initialIsRecommended;
  const recommendsCount = result?.recommendsCount ?? initialRecommendsCount;

  const { handleRecommend, handleUnrecommend, loading } = useRecommendAction({
    id,
    postId,
    currentIsRecommended: isRecommended,
    currentRecommendsCount: recommendsCount,
    contentType,
  });
  
  const handleClick = () =&gt; {
    if (loading || !authContext?.isAuthenticated) {
        if (!authContext?.isAuthenticated) {
            alert('请先登录再推荐');
        }
        return;
    }
    if (isRecommended) {
      handleUnrecommend();
    } else {
      handleRecommend();
    }
  };

  return (
    &lt;button
      onClick={handleClick}
      disabled={loading || !authContext?.isAuthenticated}
      className={`flex items-center space-x-2 text-gray-600 hover:text-red-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed`}
      aria-label={isRecommended ? '取消推荐' : '推荐'}
    &gt;
      &lt;Heart
        className={`w-5 h-5 mr-1 ${isRecommended ? 'text-red-500' : 'text-gray-500'}`}
        fill={isRecommended ? 'currentColor' : 'none'}
      /&gt;
      &lt;span className="font-semibold text-lg"&gt;{recommendsCount}&lt;/span&gt;
    &lt;/button&gt;
  );
};

export default RecommendButton;
</code></pre>

    <h2>页面集成: CPT模板</h2>
    <div class="file-path">fd-frontend/src/app/post-type/[type]/[uuid]/[slug]/page.tsx</div>
    <pre><code class="language-typescript">
import React from 'react';
import { notFound, redirect } from 'next/navigation';
import { getCustomPostByUuid, isValidUuid } from '@/lib/api';
import { buildCustomPostUrl } from '@/utils/url-builder';
import CommentSection from '@/components/comments/CommentSection';
import MainLayout from '@/components/layouts/MainLayout';
import LikeButton from '@/components/post/LikeButton';
import RecommendButton from '@/components/post/RecommendButton';

// 设置ISR模式，60分钟重新验证
export const revalidate = 3600;

// 自定义文章类型接口
interface CustomPost {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  content?: string;
  date: string;
  databaseId?: number;
  shortUuid?: string;
  commentStatus?: string;
  likesCount?: number;
  userHasLiked?: boolean;
  recommendsCount?: number;
  userHasRecommended?: boolean;
  featuredImage?: {
    node: {
      sourceUrl: string;
      altText?: string;
    }
  };
  author?: {
    node: {
      id: string;
      name: string;
      slug: string;
    }
  };
}

/**
 * 比较两个slug是否匹配，考虑编码差异
 * @param slug1 第一个slug
 * @param slug2 第二个slug
 * @returns 是否匹配
 */
function slugsMatch(slug1: string, slug2: string): boolean {
  // 解码用于比较的slug
  const decoded1 = decodeURIComponent(slug1);
  const decoded2 = decodeURIComponent(slug2);
  
  // 比较解码后的slug
  if (decoded1 === decoded2) {
    return true;
  }
  
  // 检查是否包含中文字符
  const hasChinese = /[\u4e00-\u9fa5]/.test(decoded1) || /[\u4e00-\u9fa5]/.test(decoded2);
  
  // 对于中文slug，进行更宽松的比较
  if (hasChinese) {
    // 如果长度相似，视为匹配以避免重定向循环
    const lengthDifference = Math.abs(decoded1.length - decoded2.length);
    if (lengthDifference &lt;= 5) {
      return true;
    }
    
    // 移除常见标点和空格后比较
    const clean1 = decoded1.replace(/[-_《》【】\s'"：，。、？！（）()]/g, '');
    const clean2 = decoded2.replace(/[-_《》【】\s'"：，。、？！（）()]/g, '');
    
    if (clean1 === clean2) {
      return true;
    }
  }
  
  return false;
}

// 设置动态元数据
export async function generateMetadata({ params }: { params: { type: string, uuid: string } }) {
  const { type, uuid } = params;
  const post = await getCustomPostByUuid(type, uuid);
  
  if (!post) return { title: '内容未找到' };
  
  return {
    title: `${post.title} - Future Decade`,
    description: post.excerpt || '',
    openGraph: {
      title: post.title,
      description: post.excerpt || '',
      images: post.featuredImage?.node?.sourceUrl ? [post.featuredImage.node.sourceUrl] : []
    }
  };
}

export default async function CustomPostTypePage({ params }: { 
  params: { type: string; uuid: string; slug: string } 
}) {
  const { type, uuid, slug } = params;
  const post = await getCustomPostByUuid(type, uuid) as CustomPost;
  
  // 如果内容不存在，返回404
  if (!post) {
    return notFound();
  }
  
  // 获取当前的路由前缀设置
  let routePrefixes = {
    postPrefix: 'articles',
    categoryPrefix: null,
    tagPrefix: 'topics',
    categoryIndexRoute: 'category-index',
    tagIndexRoute: 'tag-index',
    customTypePrefix: 'post-type'
  };
  
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`, 
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: `
            query GetRoutePrefixes {
              routePrefixes {
                postPrefix
                categoryPrefix
                tagPrefix
                categoryIndexRoute
                tagIndexRoute
                customTypePrefix
              }
            }
          `
        }),
        cache: 'force-cache'
      }
    );
    const data = await response.json();
    if (data?.data?.routePrefixes) {
      routePrefixes = { ...routePrefixes, ...data.data.routePrefixes };
    }
  } catch (error) {
    console.error('Error fetching route prefixes:', error);
  }
  
  // 检查slug是否匹配，同时考虑编码差异
  if (!slugsMatch(post.slug, slug)) {
    try {
      // 尝试重定向到正确的URL
      // 检查是否包含中文字符
      const hasChinese = /[\u4e00-\u9fa5]/.test(decodeURIComponent(post.slug));
      
      // 对于中文slug，尽量避免重定向，除非完全不同
      if (!hasChinese || slug.length &lt; post.slug.length / 2) {
        // 使用URL构建函数重定向
        return redirect(buildCustomPostUrl(type, uuid, encodeURIComponent(post.slug), routePrefixes));
      }
    } catch (error) {
      // 如果重定向出错，继续显示页面而不是显示错误
      console.error('Redirect error:', error);
    }
  }
  
  return (
    &lt;MainLayout&gt;
      &lt;article className="article-container max-w-4xl mx-auto py-10 px-4"&gt;
        &lt;h1 className="text-3xl font-bold mb-6"&gt;{post.title}&lt;/h1&gt;
        
        {/* 内容头部信息 */}
        &lt;div className="article-meta mb-8"&gt;
          &lt;div className="flex items-center text-gray-600 mb-4"&gt;
            &lt;span className="mr-4"&gt;
              发布于: {new Date(post.date).toLocaleDateString('zh-CN')}
            &lt;/span&gt;
            {post.author?.node && (
              &lt;span&gt;作者: {post.author.node.name}&lt;/span&gt;
            )}
          &lt;/div&gt;
        &lt;/div&gt;
        
        {/* 特色图片 */}
        {post.featuredImage?.node?.sourceUrl && (
          &lt;div className="featured-image mb-8"&gt;
            &lt;img 
              src={post.featuredImage.node.sourceUrl} 
              alt={post.featuredImage.node.altText || post.title} 
              className="w-full rounded-lg shadow-md"
            /&gt;
          &lt;/div&gt;
        )}
        
        {/* 内容 */}
        &lt;div 
          className="article-content prose lg:prose-xl mx-auto"
          dangerouslySetInnerHTML={{ __html: post.content || '' }}
        /&gt;
        
        {/* 点赞与推荐按钮容器 */}
        &lt;div className="my-8 flex justify-center items-center space-x-6"&gt;
          &lt;LikeButton 
            id={post.id}
            postId={post.databaseId || 0}
            initialLikesCount={post.likesCount || 0}
            initialIsLiked={post.userHasLiked || false}
            contentType={type}
          /&gt;
          &lt;RecommendButton
            id={post.id}
            postId={post.databaseId || 0}
            initialRecommendsCount={post.recommendsCount || 0}
            initialIsRecommended={post.userHasRecommended || false}
            contentType={type}
          /&gt;
        &lt;/div&gt;
        
        {/* 添加评论区 */}
        &lt;CommentSection 
          postId={post.databaseId || 0} 
          commentStatus={post.commentStatus || 'closed'} 
          isCustomType={true}
          customTypeSlug={type}
        /&gt;
      &lt;/article&gt;
    &lt;/MainLayout&gt;
  );
}
</code></pre>

    <h2>页面集成: 标准文章模板</h2>
    <p>为了确保功能覆盖所有文章类型，推荐按钮也被添加到了所有标准文章模板中。这些模板共享同一个 <code>PostType</code> 接口，该接口已更新以包含推荐相关字段。</p>
    
    <div class="file-path">fd-frontend/src/components/templates/StandardTemplate.tsx</div>
    <pre><code class="language-typescript">
import React from 'react';
import LikeButton from '../post/LikeButton';
import RecommendButton from '../post/RecommendButton';

// ...
export interface PostType {
  id: string;
  databaseId: number;
  title: string;
  date: string;
  content: string;
  likesCount: number;
  userHasLiked: boolean;
  recommendsCount?: number;
  userHasRecommended?: boolean;
  shortUuid?: string;
  author?: PostAuthor;
  featuredImage?: FeaturedImage;
  categories?: {
    nodes: PostCategory[];
  };
  tags?: {
    nodes: PostTag[];
  };
  postTemplate?: PostTemplateType;
}
// ...
        {/* 点赞与推荐按钮容器 */}
        &lt;div className="flex items-center space-x-6 mt-6 pt-4 border-t"&gt;
          &lt;LikeButton
            id={post.id}
            postId={post.databaseId}
            initialIsLiked={post.userHasLiked}
            initialLikesCount={post.likesCount}
          /&gt;
          &lt;RecommendButton
            id={post.id}
            postId={post.databaseId}
            initialRecommendsCount={post.recommendsCount || 0}
            initialIsRecommended={post.userHasRecommended || false}
          /&gt;
        &lt;/div&gt;
// ...
</code></pre>
    <p>此模式同样应用于 <code>VideoTemplate.tsx</code>, <code>HeroTitleTemplate.tsx</code>, 和 <code>CoverTemplate.tsx</code>，确保了所有文章详情页都有一致的用户交互体验。</p>
</body>
</html> 