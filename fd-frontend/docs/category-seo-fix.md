# 分类页面SEO修复

## 问题诊断

通过检查 `https://www.futuredecade.com/business-decoder` 页面，发现以下SEO问题：

### ✅ 已正确实现的SEO元素
- **页面标题**：使用AI生成的SEO标题
- **Meta描述**：使用AI生成的SEO描述  
- **基础Meta标签**：字符集、视口设置正确
- **语言设置**：正确设置中文语言

### ❌ 缺失的重要SEO元素
1. **JSON-LD结构化数据**：虽然后端有数据，但未注入HTML
2. **Open Graph标签**：缺失所有OG标签
3. **Twitter Card标签**：缺失所有Twitter标签
4. **Canonical URL**：缺失规范链接

## 修复方案

### 1. 增强 generateMetadata 函数

在 `src/app/category/[slug]/page.tsx` 中添加完整的SEO元数据：

```typescript
export async function generateMetadata({ params }: { params: { slug: string } }) {
  const postsPerPage = await fetchPostsPerPageSetting();
  const category = await fetchCategoryPageData(params.slug, postsPerPage);
  
  if (!category) {
    return {
      title: '分类未找到 - Future Decade',
      description: 'Future Decade - AI驱动的新型科技媒体',
    };
  }

  const metaTitle = category.aiSeoTitle || category.name;
  const metaDescription = category.aiSeoDescription || `${category.name} - Future Decade`;
  const canonicalUrl = `https://www.futuredecade.com/${category.slug}`;
  
  // 获取分类的第一篇文章作为默认图片
  const defaultImage = category.posts?.nodes?.[0]?.featuredImage?.node?.sourceUrl || 
                      'https://www.futuredecade.com/images/default-og-image.jpg';

  return {
    title: `${metaTitle} - Future Decade`,
    description: metaDescription,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      url: canonicalUrl,
      siteName: 'Future Decade',
      type: 'website',
      images: [
        {
          url: defaultImage,
          width: 1200,
          height: 630,
          alt: metaTitle,
        },
      ],
      locale: 'zh_CN',
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: [defaultImage],
      site: '@FutureDecade',
      creator: '@FutureDecade',
    },
  };
}
```

### 2. 添加JSON-LD结构化数据注入

在主组件中添加JSON-LD数据的处理和注入：

```typescript
export default async function CategoryPage({ params }: { params: { slug: string } }) {
  // ... 获取数据逻辑 ...

  // 准备JSON-LD结构化数据
  let jsonLd = null;
  if (category.aiSeoJsonLd) {
    try {
      jsonLd = JSON.parse(category.aiSeoJsonLd);
      // 更新URL为正确的前端URL
      if (jsonLd.url) {
        jsonLd.url = `https://www.futuredecade.com/${category.slug}`;
      }
    } catch (error) {
      console.error('Failed to parse JSON-LD:', error);
    }
  }

  return (
    <>
      {jsonLd && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
      )}
      <CategoryClientPage
        initialCategory={category as any}
        initialPosts={initialPosts as any}
        initialPageInfo={initialPageInfo as any}
      />
    </>
  );
}
```

## 修复内容详解

### 1. Canonical URL
- 添加了 `alternates.canonical` 设置
- 确保搜索引擎知道页面的规范URL

### 2. Open Graph 标签
- `og:title`：使用AI生成的SEO标题
- `og:description`：使用AI生成的SEO描述
- `og:url`：页面的规范URL
- `og:type`：设置为 'website'
- `og:image`：使用分类第一篇文章的特色图片
- `og:locale`：设置为中文

### 3. Twitter Card 标签
- `twitter:card`：使用 'summary_large_image'
- `twitter:title`：与OG标题一致
- `twitter:description`：与OG描述一致
- `twitter:images`：与OG图片一致
- `twitter:site` 和 `twitter:creator`：设置为 @FutureDecade

### 4. JSON-LD 结构化数据
- 解析后端提供的 `aiSeoJsonLd` 字段
- 更新URL为正确的前端地址
- 安全地注入到页面HTML中

## 预期效果

修复后，分类页面将具备：

1. **完整的社交分享支持**：Facebook、Twitter等平台能正确显示预览
2. **结构化数据支持**：搜索引擎能更好理解页面内容
3. **规范URL设置**：避免重复内容问题
4. **优化的SEO表现**：提升搜索排名和可发现性

## 验证方法

1. **查看页面源码**：检查是否包含所有meta标签和JSON-LD
2. **Facebook调试工具**：https://developers.facebook.com/tools/debug/
3. **Twitter Card验证器**：https://cards-dev.twitter.com/validator
4. **Google Rich Results Test**：https://search.google.com/test/rich-results
5. **结构化数据测试工具**：https://validator.schema.org/

## 注意事项

- 确保默认OG图片路径存在
- Twitter账号信息可根据实际情况调整
- JSON-LD数据的URL已更新为前端地址
- 所有SEO元数据都优先使用AI生成的内容
