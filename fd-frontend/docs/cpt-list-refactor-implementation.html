<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>自定义文章类型(CPT)列表页重构文档</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; line-height: 1.8; color: #333; max-width: 1000px; margin: 0 auto; padding: 20px; }
    h1, h2, h3 { border-bottom: 1px solid #eaecef; padding-bottom: 0.3em; margin-top: 1.8em; }
    code { font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace; background-color: #f6f8fa; padding: 0.2em 0.4em; border-radius: 3px; font-size: 0.9em; }
    pre { background-color: #f6f8fa; padding: 16px; border-radius: 6px; overflow: auto; line-height: 1.45; }
    pre code { background-color: transparent; padding: 0; font-size: 0.9em; }
    .version, .status { background-color: #e7f5ff; padding: 10px 15px; border-radius: 4px; display: inline-block; margin-bottom: 20px; font-weight: bold; }
    .note { background-color: #fff8e6; border-left: 4px solid #ffd174; padding: 15px; margin: 20px 0; }
    .summary-box { background-color: #f0f9ff; border: 1px solid #cce7ff; padding: 20px; border-radius: 8px; margin-bottom: 2em; }
    table { border-collapse: collapse; width: 100%; margin: 20px 0; }
    table, th, td { border: 1px solid #ddd; }
    th, td { padding: 12px; text-align: left; }
    th { background-color: #f5f7f9; }
  </style>
</head>
<body>
  <h1>自定义文章类型(CPT)列表页重构文档</h1>
  <div class="version">V2.3.1</div>
  <div class="meta-info">
    <p>创建日期: 2024-07-20</p>
    <p>完成状态: 已完成并部署</p>
  </div>

  <div class="summary-box">
    <h2>项目概述</h2>
    <p>为了统一全站用户体验，本次重构将分类、标签等列表页已经实现的**无限滚动分页**、**骨架屏加载**和**双层数据去重**机制，全面应用到所有自定义文章类型（CPT）的列表页面（如笔记、书单等）。我们采用服务器端"注水"+客户端接管的混合渲染方案，在保留SEO和首屏性能优势的同时，实现了与其它列表页完全一致的现代化交互体验。</p>
  </div>

  <h2>1. 最终架构：混合渲染方案</h2>
  <p>我们最终采纳并成功实施了"服务器端组件 + 客户端组件"的混合渲染模式，其核心数据流如下：</p>
  <ol>
    <li>服务器端页面 (<code>/app/post-type/[type]/page.tsx</code>) 负责获取<strong>第一页</strong>的文章数据和分页信息。</li>
    <li>服务器将这些初始数据作为Props"注水"(Hydrate)给客户端核心视图组件 (<code>CustomPostListView.tsx</code>)。</li>
    <li><code>CustomPostListView</code> 组件在浏览器端被激活，使用初始数据完成首屏渲染。</li>
    <li>后续的所有交互，包括无限滚动、触发加载、状态管理等，全部由 <code>CustomPostListView</code> 接管。</li>
  </ol>
  <div class="note">
    <p><strong>关键优势：</strong>此方案完美结合了SSR/ISR的<strong>高性能首屏</strong>和客户端渲染的<strong>丰富交互性</strong>，是Next.js应用处理此类页面的最佳实践。</p>
  </div>

  <h2>2. 实施阶段回顾</h2>

  <h3>阶段一：数据与逻辑层准备</h3>
  <h4>2.1 创建 <code>useCustomPosts</code> Hook</h4>
  <p>我们创建了新的 <code>useCustomPosts.ts</code> Hook，作为客户端获取任意CPT分页数据的统一引擎。它复用了 `usePosts` 的核心逻辑，包括 `loadMore` 函数和 `useMemo` 去重机制。</p>
  <pre><code>// src/hooks/useCustomPosts.ts
const GET_CONTENT_NODES = gql`
  query GetContentNodes(
    $first: Int
    $after: String
    $contentTypes: [ContentTypeEnum]
  ) {
    contentNodes(
      first: $first, after: $after, where: { contentTypes: $contentTypes }
    ) {
      nodes { /* ... fields ... */ }
      pageInfo { hasNextPage, endCursor }
    }
  }
`;
// ... Hook实现 ...</code></pre>

  <h4>2.2 配置Apollo客户端缓存</h4>
  <p>为实现第一层去重防御，我们在 <code>apollo-client.ts</code> 中为 <code>contentNodes</code> 查询添加了缓存合并策略，自动处理分页数据的合并与去重。</p>
  <pre><code>// src/lib/apollo-client.ts
const cache = new InMemoryCache({
  typePolicies: {
    Query: {
      fields: {
        contentNodes: { // 新增策略
          keyArgs: ['where', ['contentTypes']],
          merge(existing = { nodes: [] }, incoming, { args }) {
            if (!args?.after) return incoming;
            const existingIds = new Set(existing.nodes.map(node => node.id));
            const uniqueNewNodes = incoming.nodes.filter(node => !existingIds.has(node.id));
            return {
              ...incoming,
              nodes: [...existing.nodes, ...uniqueNewNodes],
            };
          },
        },
        // ... 其他策略
      },
    },
  },
});</code></pre>

  <h3>阶段二：UI层解耦</h3>
  <h4>2.3 创建骨架屏与核心视图组件</h4>
  <p>我们为不同视图创建了专属的骨架屏（如 <code>NotesViewSkeleton.tsx</code>），并创建了核心客户端组件 <code>CustomPostListView.tsx</code>，将所有视图的渲染逻辑（<code>switch</code>语句）从服务器端页面中剥离出来，完成了UI与逻辑的解耦。</p>

  <h3>阶段三：整合、验证与功能实现</h3>
  <h4>2.4 "注水"机制验证</h4>
  <p>这是最关键的一步。我们改造了服务器端页面 <code>page.tsx</code>，使其仅获取首屏数据，并将其作为Props传递给 <code>CustomPostListView</code>。同时，我们添加了一个临时的调试组件 <code>DebugInfo</code>，直观地验证了从服务器到客户端的数据流完全正确无误。</p>
  <pre><code>// /app/post-type/[type]/page.tsx (部分)
export default async function CustomPostTypePage({ params }) {
  // 1. 服务器仅获取第一页数据
  const initialData = await getCustomPostsList(type, 10);
  const initialPosts = initialData?.posts || [];
  const initialPageInfo = initialData?.pageInfo;
  
  // ...
  
  // 2. 将数据"注水"给客户端组件
  return (
    <MainLayout>
      <CustomPostListView
        initialPosts={initialPosts}
        initialPageInfo={initialPageInfo}
        // ...其他props
      />
    </MainLayout>
  );
}</code></pre>
  
  <h4>2.5 实现无限滚动</h4>
  <p>在验证"注水"成功后，我们在 <code>CustomPostListView</code> 中添加了完整的交互逻辑：调用 <code>useCustomPosts</code> Hook，并配置 <code>InfiniteScroll</code> 组件，实现了最终的无限滚动和骨架屏加载效果。</p>

  <h4>2.6 关键代码实现（V2.3.1 最终版本）</h4>
  <p>以下代码片段展示了当前生产环境中正在运行的核心实现，供开发者查看与参考：</p>

  <h5>2.6.1 <code>InfiniteScroll.tsx</code>（职责分离版）</h5>
  <pre><code class="language-tsx">// src/components/InfiniteScroll.tsx
'use client';
import React, { useState, useRef, useEffect } from 'react';

interface InfiniteScrollProps {
  children: React.ReactNode;
  hasMore: boolean;
  loading: boolean;
  onLoadMore: () => void;
  loadingComponent?: React.ReactNode;
  threshold?: number; // rootMargin 像素值
}

const InfiniteScroll: React.FC<InfiniteScrollProps> = ({
  children,
  hasMore,
  loading,
  onLoadMore,
  loadingComponent = <p>Loading...</p>,
  threshold = 250,
}) => {
  const triggerRef = useRef<HTMLDivElement>(null);
  const [isIntersecting, setIntersecting] = useState(false);

  // 1. 仅负责侦测
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => setIntersecting(entries[0].isIntersecting),
      { rootMargin: `${threshold}px` }
    );
    const el = triggerRef.current;
    if (el) observer.observe(el);
    return () => el && observer.unobserve(el);
  }, [threshold]);

  // 2. 仅负责决策
  useEffect(() => {
    if (isIntersecting && hasMore && !loading) {
      onLoadMore();
    }
  }, [isIntersecting, hasMore, loading, onLoadMore]);

  return (
    <>
      {children}
      <div ref={triggerRef} />
      {loading && hasMore && loadingComponent}
    </>
  );
};
export default InfiniteScroll;</code></pre>

  <h5>2.6.2 <code>CustomPostListView.tsx</code>（数据注水 + 无限滚动）</h5>
  <pre><code class="language-tsx">// src/components/CustomPostListView.tsx
'use client';
import React, { useState, useCallback } from 'react';
import InfiniteScroll from './InfiniteScroll';
import { useCustomPosts } from '@/hooks/useCustomPosts';
// 省略动态导入 UI 视图与骨架屏...

const CustomPostListView: React.FC<Props> = ({
  initialPosts,
  initialPageInfo,
  postType,
  viewComponent,
  routePrefixes,
}) => {
  // 调用统一 Hook
  const { posts: apolloPosts, pageInfo, loading, loadMore } =
    useCustomPosts({ postType, first: 10 });
  const [loadingMore, setLoadingMore] = useState(false);

  // 1. 首屏优先显示服务器 "注水" 数据
  const displayPosts =
    (!apolloPosts || apolloPosts.length === 0) && loading
      ? initialPosts
      : apolloPosts;

  const displayPageInfo = pageInfo || initialPageInfo;

  // 2. 加载更多
  const handleLoadMore = useCallback(async () => {
    if (loading || loadingMore || !displayPageInfo?.hasNextPage) return;
    setLoadingMore(true);
    await loadMore(displayPageInfo.endCursor!);
    setLoadingMore(false);
  }, [loading, loadingMore, displayPageInfo, loadMore]);

  return (
    <InfiniteScroll
      hasMore={!!displayPageInfo?.hasNextPage}
      loading={loadingMore}
      onLoadMore={handleLoadMore}
      loadingComponent={<Skeleton count={3} />}
    >
      {renderView(displayPosts)}
    </InfiniteScroll>
  );
};
export default CustomPostListView;</code></pre>

  <h5>2.6.3 <code>useCustomPosts.ts</code>（去重逻辑简化）</h5>
  <pre><code class="language-ts">// src/hooks/useCustomPosts.ts
export const useCustomPosts = ({ first = 10, postType }: Options) => {
  const contentTypes = [postType.toUpperCase()];
  const { data, loading, error, fetchMore } = useQuery(GET_CONTENT_NODES, {
    variables: { first, contentTypes },
    notifyOnNetworkStatusChange: true,
  });

  const loadMore = (after: string) =>
    fetchMore({ variables: { after } });

  return {
    posts: data?.contentNodes.nodes || [],
    pageInfo: data?.contentNodes.pageInfo,
    loading,
    error,
    loadMore,
  };
};</code></pre>

  <h5>2.6.4 Apollo 缓存策略（节选）</h5>
  <pre><code class="language-ts">// src/lib/apollo-client.ts (节选)
contentNodes: {
  keyArgs: ['where', ['contentTypes']],
  merge(existing, incoming) {
    if (!existing) return incoming;
    const ids = new Set(existing.nodes.map((n) => n.id));
    const merged = [...existing.nodes, ...incoming.nodes.filter((n) => !ids.has(n.id))];
    return { ...incoming, nodes: merged };
  },
},</code></pre>

  <p>以上代码即为当前生产环境在运行的核心实现，确保了首屏注水、骨架屏加载、无限滚动、缓存去重等关键功能的稳定执行。</p>
  
  <h2>3. 问题排查与代码重构</h2>
  <p>在v2.3.0版本发布后的实际使用中，我们定位并修复了几个在特定场景下才会出现的深层次BUG，并对核心的无限滚动组件进行了架构级重构，显著提升了其健壮性。</p>
  
  <h3>3.1 修复GraphQL查询错误导致的无限循环</h3>
  <ul>
    <li><strong>现象</strong>: CPT列表页打开后页面会持续闪动，同时Network面板显示同一个错误的GraphQL查询被高频重复发送。</li>
    <li><strong>根源</strong>: 定位到 <code>useCustomPosts.ts</code> 中的GraphQL查询语句存在语法错误——它试图在 <code>NodeWithTitle</code> 接口上直接请求仅存在于具体类型（如 <code>ContentNode</code>）上的 <code>slug</code> 和 <code>date</code> 字段。Apollo Client的默认重试策略在查询失败后不断重试，导致了无限循环。</li>
    <li><strong>修复</strong>: 修改了 <code>GET_CONTENT_NODES</code> 查询，使用GraphQL的内联片段(Inline Fragment)—— <code>... on ContentNode { slug date }</code>，确保了查询的合法性，从根源上解决了循环问题。</li>
  </ul>
  
  <h3>3.2 重构无限滚动组件(InfiniteScroll.tsx)</h3>
  <p>修复了GraphQL查询后，暴露出了无限滚动组件在设计上的一系列缺陷，我们通过三次迭代最终完成了对其的彻底重构。</p>
  <h4>阶段一：修复"自动连环加载"</h4>
  <ul>
    <li><strong>现象</strong>: 触发一次无限加载后，后续请求会自动、连续地发出，直到数据全部加载完毕。</li>
    <li><strong>根源</strong>: 这是一个典型的竞态条件(Race Condition)。当新内容加载完成，组件立即重渲染，新创建的<code>IntersectionObserver</code>在用户还未移动滚动条时就检测到触发器元素仍在视口内，从而立即触发了下一次加载。</li>
    <li><strong>初步修复 (已废弃)</strong>: 通过引入<code>justLoaded</code>标志位和100毫秒的<code>setTimeout</code>延迟，在加载完成后短暂地禁用加载，临时规避了此问题。</li>
  </ul>
  <h4>阶段二：修复"快速滑动卡顿"</h4>
  <ul>
      <li><strong>现象</strong>: 在快速向下滑动时，页面有时会卡住，需要用户轻微上下滑动一下才能继续加载。</li>
      <li><strong>根-源</strong>: 上一阶段的<code>setTimeout</code>补丁方案不够健壮。在延迟期间，如果浏览器没有因为用户操作而触发新的滚动事件，加载就会"卡住"。</li>
      <li><strong>二次修复 (已废弃)</strong>: 尝试使用<code>useRef</code>来确保回调函数能访问到最新的props状态，但这依然没有从根本上解决问题。</li>
  </ul>
  <h4>阶段三：最终的架构升级</h4>
  <ul>
    <li><strong>根源分析</strong>: 问题的核心在于组件的设计模式——它将"侦测"和"决策"两个职责耦合在了一起。</li>
    <li><strong>最终修复 (现行方案)</strong>: 对<code>InfiniteScroll</code>组件进行了彻底重构，采用了**职责分离**的现代React Hooks模式：
      <ol>
        <li>一个<code>useEffect</code>只负责管理<code>IntersectionObserver</code>，它的唯一任务是在目标元素进入/离开视口时，更新一个名为<code>isIntersecting</code>的React状态。</li>
        <li>另一个<code>useEffect</code>只负责决策，它监控<code>isIntersecting</code>、<code>hasMore</code>、<code>loading</code>这三个状态。一旦条件满足(<code>isIntersecting && hasMore && !loading</code>)，就触发加载。</li>
      </ol>
    </li>
    <li><strong>成果</strong>: 新的架构完全解耦，从根本上消除了所有竞态条件和边缘BUG，提供了真正平滑、可靠的无限滚动体验。</li>
  </ul>

  <h3>3.3 简化数据流：移除冗余的去重逻辑</h3>
  <ul>
    <li><strong>现象</strong>: 修复无限滚动问题后，发现加载更多数据时，新数据会替换旧数据，而不是追加。日志显示，一个<code>useMemo</code>去重逻辑将10条数据错误地缩减为了1条。</li>
    <li><strong>根源</strong>: 在<code>useCustomPosts.ts</code>中存在一个手动的<code>useMemo</code>去重逻辑，它与<code>apollo-client.ts</code>中基于缓存<code>typePolicies</code>的去重策略产生了冲突和冗余。</li>
    <li><strong>修复</strong>: 删除了<code>useCustomPosts.ts</code>中多余的<code>useMemo</code>去重块，完全信赖Apollo Client的缓存策略作为唯一的数据合并来源，使数据流更简单、更可靠。</li>
  </ul>

  <hr style="margin: 2em 0;">

  <p><em>(以下为v2.3.0版本发布前的原始排错记录)</em></p>

  <h3>3.4 修复 <code>/undefined</code> URL 错误</h3>
  <p>我们发现文章详情页URL出现 <code>/undefined/note/...</code> 的错误。通过溯源，定位到问题根源是后端的GraphQL <code>routePrefixes</code> 查询从未返回 `customTypePrefix` 字段。</p>
  <ul>
      <li><strong>后端修复</strong>: 在 <code>fd-theme/inc/graphql.php</code> 中为 `routePrefixes` 查询添加了 `customTypePrefix` 字段。</li>
      <li><strong>前端加固</strong>: 在 <code>url-builder.ts</code> 中增加了防御性代码，确保即使该字段为空，也能优雅降级。</li>
  </ul>
  
  <h3>3.5 重构：消除重复的数据请求</h3>
  <p>我们发现 <code>middleware.ts</code> 和 <code>lib/api.ts</code> 中存在两处独立获取 `routePrefixes` 的重复代码。这违反了DRY原则，且导致了 `middleware.ts` 中缺少 `customTypePrefix` 的隐蔽BUG。</p>
  <ul>
    <li><strong>统一数据源</strong>: 我们将 <code>lib/api.ts</code> 中的 <code>getRoutePrefixes</code> 函数改造为唯一的、包含错误处理和默认值合并的可靠数据源。</li>
    <li><strong>重构中间件</strong>: 移除了 <code>middleware.ts</code> 中的 `fetch` 逻辑，改为直接调用统一的 <code>getRoutePrefixes()</code> 函数，彻底消除了代码冗余和逻辑不一致的风险。</li>
  </ul>

  <h2>4. Feature Evolution: Dynamic ACF Field Querying (V2.4.0)</h2>
  <p>在v2.3.1版本稳定运行后，我们进一步扩展了CPT列表页的功能，以支持动态展示不同CPT关联的<strong>高级自定义字段(ACF)</strong>。此功能解决了过去无法在列表页（如笔记、书单）上灵活显示各自独有字段（如笔记来源、书籍作者）的痛点。</p>
  
  <h3>4.1 需求与挑战</h3>
  <ul>
    <li><strong>需求</strong>: 在 "笔记" 列表页显示 "笔记来源" 字段，在 "书籍" 列表页显示 "作者" 和 "出版社" 字段。</li>
    <li><strong>挑战</strong>: 每个CPT的ACF字段都不同，无法使用一个静态的GraphQL查询来同时满足所有类型的需求。硬编码多个查询会严重破坏代码的可维护性。</li>
  </ul>
  
  <h3>4.2 架构设计：三步动态查询系统</h3>
  <p>我们设计并实现了一套全自动的动态查询系统，它能够在无需修改前端代码的情况下，自动适应后台新增或修改的ACF字段。</p>
  <ol>
    <li><strong>第一步：服务器端自省 (Introspection)</strong>
      <p>在服务器端页面 (<code>page.tsx</code>) 中，我们增加了一个 <code>getAcfFieldsForType</code> 函数。当用户访问一个CPT页面时，此函数会向GraphQL服务器发送一个<strong>自省查询</strong>，动态地获取该CPT所关联的ACF字段的名称和类型。</p>
    </li>
    <li><strong>第二步：动态构建查询 (Dynamic Query Building)</strong>
      <p>我们创建了一个新的工具函数 <code>buildDynamicPostQuery</code> (位于 <code>lib/graphql-utils.ts</code>)。它接收CPT类型和上一步自省获取的ACF字段列表，然后即时生成一个包含所有标准字段和动态ACF字段的完整GraphQL查询字符串。</p>
    </li>
    <li><strong>第三步：查询执行与"注水"</strong>
      <p>服务器端页面使用这个动态生成的查询字符串来获取首屏数据，然后将<strong>初始数据</strong>和这个<strong>查询字符串本身</strong>一同作为props"注水"给客户端组件 <code>CustomPostListView</code>。客户端接管后，<code>useCustomPosts</code> Hook会使用这个相同的查询字符串来执行后续的所有数据获取操作（如无限滚动），从而确保了数据结构的一致性。</p>
    </li>
  </ol>
  
  <h4>4.3 核心代码实现</h4>

  <h5>4.3.1 `graphql-utils.ts` - 动态查询构建器</h5>
  <pre><code class="language-ts">// src/lib/graphql-utils.ts
export function buildDynamicPostQuery(postType, acfContainerTypeName, acfFields) {
  const postGraphQLTypeName = toGraphQLTypeName(postType);
  let acfFieldsString = '';
  const acfContainerFieldName = /* ... */;

  // 基于acfFields数组生成字段字符串
  const fieldNames = acfFields
    .filter(field => field.type.kind === 'SCALAR')
    .map(field => field.name);
  
  acfFieldsString = `
    ... on ${postGraphQLTypeName} {
      title      // 关键修复：将核心字段放入类型片段
      excerpt
      shortUuid
      ${acfContainerFieldName} {
        ${fieldNames.join('\n            ')}
      }
    }
  `;

  // 返回包含动态片段的完整查询
  return `query GetDynamicContentNodes(...) { 
    contentNodes(...) {
      nodes {
        __typename
        id
        databaseId
        date
        slug
        ...
        ${acfFieldsString}
      }
      pageInfo { ... }
    }
  }`;
}</code></pre>

  <h5>4.3.2 `page.tsx` - 服务器端业务流</h5>
  <pre><code class="language-tsx">// /app/post-type/[type]/page.tsx (简化流程)
export default async function CustomPostTypePage({ params }) {
  const { type } = params;
  
  // 1. 获取ACF字段
  const { acfFields, acfContainerTypeName } = await getAcfFieldsForType(type);
  
  // 2. 构建动态查询
  const dynamicQuery = buildDynamicPostQuery(type, acfContainerTypeName, acfFields);

  // 3. 使用动态查询获取首屏数据
  const initialData = await getDynamicPostsList(dynamicQuery, type, 10);
  
  // 4. 将数据和查询字符串一同"注水"给客户端
  return (
    <CustomPostListView
      initialPosts={initialData.nodes}
      initialPageInfo={initialData.pageInfo}
      query={dynamicQuery} // 传递查询字符串
      // ...其他props
    />
  );
}</code></pre>

  <h3>4.4 问题排查与关键修复</h3>
  <p>在实现过程中，我们遇到了一个关键的"水合不匹配" (Hydration Mismatch) 问题。</p>
  <ul>
    <li><strong>现象</strong>: 页面加载时，文章标题短暂闪现后立即消失。控制台日志显示，服务器端获取的数据包含 <code>title</code>，但客户端Apollo获取的数据中 <code>title</code> 为 <code>undefined</code>。</li>
    <li><strong>根源</strong>: 原始查询依赖通用的接口片段 (<code>... on NodeWithTitle</code>) 来获取标题。这种方式在客户端和服务器端的解析存在不一致，导致了问题。</li>
    <li><strong>最终修复</strong>: 我们重构了 <code>buildDynamicPostQuery</code> 函数，将 <code>title</code>, <code>excerpt</code>, <code>shortUuid</code> 等核心字段从通用片段移入了为每个CPT动态生成的<strong>类型特定片段</strong>中 (例如 <code>... on Note { title excerpt shortUuid }</code>)。这使得查询更加明确和健壮，彻底解决了水合不匹配的问题，确保了数据的稳定性和一致性。</li>
  </ul>

  <h2>5. 总结</h2>
  <p>通过本次分阶段、可验证的重构，我们成功地将所有自定义文章类型列表页的交互体验与全站标准对齐。后续增加的动态查询系统，更是在此基础上实现了对任意ACF字段的自动化支持。最终的混合渲染架构不仅性能卓越、体验流畅，而且通过代码重构，使项目在可维护性和健壮性方面迈上了一个新台阶。</p>
</body>
</html> 