<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GraphQL查询片段文档 - FD Frontend v1.2</title>
    <style>
        :root {
            --primary-color: #1a73e8;
            --secondary-color: #4285f4;
            --background-color: #f8f9fa;
            --text-color: #202124;
            --code-background: #f1f3f4;
            --border-color: #dadce0;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        h1 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .subtitle {
            color: #5f6368;
            font-size: 1.2rem;
        }
        
        .toc {
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        
        .toc h2 {
            margin-top: 0;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.5rem;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin-bottom: 0.5rem;
        }
        
        .toc a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
        
        section {
            background-color: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        
        h2 {
            color: var(--primary-color);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.5rem;
        }
        
        .fragment-container {
            margin-bottom: 3rem;
        }
        
        .fragment-container:last-child {
            margin-bottom: 0;
        }
        
        .fragment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .fragment-name {
            font-weight: bold;
            font-size: 1.2rem;
            color: var(--primary-color);
        }
        
        .fragment-type {
            background-color: #e8f0fe;
            color: var(--primary-color);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        
        .fragment-description {
            margin-bottom: 1rem;
            color: #5f6368;
        }
        
        pre {
            background-color: var(--code-background);
            padding: 1rem;
            border-radius: 4px;
            overflow-x: auto;
            margin: 0;
        }
        
        code {
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
            font-size: 0.9rem;
            white-space: pre;
        }
        
        .query-container {
            margin-top: 1rem;
            border-left: 3px solid #34a853;
            padding-left: 1rem;
        }
        
        .query-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #34a853;
        }
        
        .warning-note {
            background-color: #fef7e0;
            border-left: 4px solid #fbbc04;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 4px 4px 0;
        }
        
        footer {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            color: #5f6368;
            border-top: 1px solid var(--border-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>GraphQL查询片段文档</h1>
            <div class="subtitle">Future Decade Frontend v1.2</div>
        </header>
        
        <div class="toc">
            <h2>目录</h2>
            <ul>
                <li><a href="#fragments">GraphQL片段</a></li>
                <li><a href="#queries">GraphQL查询</a></li>
                <li><a href="#mutations">GraphQL变更</a></li>
                <li><a href="#usage">使用示例</a></li>
            </ul>
        </div>
        
        <section id="fragments">
            <h2>GraphQL片段</h2>
            <p>GraphQL片段允许重用查询的部分，使代码更简洁、更易维护。以下是项目中定义的所有可复用片段：</p>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">POST_FRAGMENT</div>
                    <div class="fragment-type">Post</div>
                </div>
                <div class="fragment-description">文章基本信息片段，包含ID、标题、日期、摘要、特色图像和分类等基本字段。</div>
                <pre><code>fragment PostFields on Post {
  id
  title
  date
  slug
  excerpt
  featuredImage {
    node {
      sourceUrl
      altText
    }
  }
  categories {
    nodes {
      id
      name
      slug
    }
  }
}</code></pre>
            </div>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">POST_DETAIL_FRAGMENT</div>
                    <div class="fragment-type">Post</div>
                </div>
                <div class="fragment-description">文章详细信息片段，包含文章的完整内容、作者信息和标签等更多字段。</div>
                <pre><code>fragment PostDetailFields on Post {
  id
  title
  date
  slug
  excerpt
  content
  featuredImage {
    node {
      sourceUrl
      altText
    }
  }
  categories {
    nodes {
      id
      name
      slug
    }
  }
  tags {
    nodes {
      id
      name
      slug
    }
  }
  author {
    node {
      id
      name
      slug
      avatar {
        url
      }
    }
  }
}</code></pre>
            </div>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">CATEGORY_FRAGMENT</div>
                    <div class="fragment-type">Category</div>
                </div>
                <div class="fragment-description">分类信息片段，包含ID、名称、别名、文章数量和描述等字段。</div>
                <pre><code>fragment CategoryFields on Category {
  id
  name
  slug
  count
  description
}</code></pre>
            </div>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">TAG_FRAGMENT</div>
                    <div class="fragment-type">Tag</div>
                </div>
                <div class="fragment-description">标签信息片段，包含ID、名称、别名和文章数量等字段。</div>
                <pre><code>fragment TagFields on Tag {
  id
  name
  slug
  count
}</code></pre>
            </div>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">USER_FRAGMENT</div>
                    <div class="fragment-type">User</div>
                </div>
                <div class="fragment-description">用户信息片段，包含ID、名称、别名、描述和头像等字段。</div>
                <pre><code>fragment UserFields on User {
  id
  name
  slug
  description
  avatar {
    url
  }
}</code></pre>
            </div>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">MEDIA_FRAGMENT</div>
                    <div class="fragment-type">MediaItem</div>
                </div>
                <div class="fragment-description">媒体文件信息片段，包含ID、标题、替代文本、URL和媒体类型等字段。</div>
                <pre><code>fragment MediaFields on MediaItem {
  id
  title
  altText
  sourceUrl
  mediaItemUrl
  mediaType
  mimeType
}</code></pre>
            </div>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">MENU_ITEM_FRAGMENT</div>
                    <div class="fragment-type">MenuItem</div>
                </div>
                <div class="fragment-description">菜单项信息片段，包含ID、标题、URL、目标、父菜单项ID和CSS类等字段。</div>
                <pre><code>fragment MenuItemFields on MenuItem {
  id
  title
  url
  target
  parentId
  cssClasses
}</code></pre>
            </div>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">CUSTOM_POST_FRAGMENT</div>
                    <div class="fragment-type">ContentNode</div>
                </div>
                <div class="fragment-description">自定义文章类型信息片段，使用内联片段语法扩展获取特色图像和作者等信息。</div>
                <pre><code>fragment CustomPostFields on ContentNode {
  id
  title
  date
  slug
  uri
  ... on NodeWithFeaturedImage {
    featuredImage {
      node {
        sourceUrl
        altText
      }
    }
  }
  ... on NodeWithAuthor {
    author {
      node {
        id
        name
        slug
      }
    }
  }
}</code></pre>
            </div>
        </section>
        
        <section id="queries">
            <h2>GraphQL查询</h2>
            <p>以下是使用上述片段的主要GraphQL查询：</p>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">GET_LATEST_POSTS</div>
                </div>
                <div class="fragment-description">获取最新发布的文章列表。</div>
                <pre><code>query GetLatestPosts($first: Int = 10) {
  posts(first: $first, where: { orderby: { field: DATE, order: DESC } }) {
    nodes {
      ...PostFields
    }
  }
}</code></pre>
            </div>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">GET_POST_BY_SLUG</div>
                </div>
                <div class="fragment-description">通过别名获取单篇文章详情。</div>
                <pre><code>query GetPostBySlug($slug: ID!) {
  post(id: $slug, idType: SLUG) {
    ...PostDetailFields
  }
}</code></pre>
            </div>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">GET_POSTS_BY_CATEGORY</div>
                </div>
                <div class="fragment-description">获取特定分类下的文章。</div>
                <pre><code>query GetPostsByCategory($categoryId: Int!, $first: Int = 10) {
  posts(first: $first, where: { categoryId: $categoryId }) {
    nodes {
      ...PostFields
    }
  }
}</code></pre>
            </div>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">SEARCH_POSTS</div>
                </div>
                <div class="fragment-description">搜索文章内容。</div>
                <pre><code>query SearchPosts($search: String!, $first: Int = 10) {
  posts(first: $first, where: { search: $search }) {
    nodes {
      ...PostFields
    }
  }
}</code></pre>
            </div>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">GET_HOME_DATA</div>
                </div>
                <div class="fragment-description">获取首页所需的综合数据。</div>
                <pre><code>query GetHomeData($featuredPostsCount: Int = 5, $recentPostsCount: Int = 10) {
  featuredPosts: posts(first: $featuredPostsCount, where: { featured: true }) {
    nodes {
      ...PostFields
    }
  }
  recentPosts: posts(first: $recentPostsCount, where: { orderby: { field: DATE, order: DESC } }) {
    nodes {
      ...PostFields
    }
  }
  categories(first: 10) {
    nodes {
      ...CategoryFields
    }
  }
}</code></pre>
            </div>
        </section>
        
        <section id="mutations">
            <h2>GraphQL变更</h2>
            <p>以下是主要的GraphQL变更操作：</p>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">LOGIN_USER</div>
                </div>
                <div class="fragment-description">用户登录变更，返回认证令牌和用户信息。</div>
                <pre><code>mutation LoginUser($username: String!, $password: String!) {
  login(
    input: {
      username: $username
      password: $password
    }
  ) {
    authToken
    refreshToken
    user {
      ...UserFields
    }
  }
}</code></pre>
            </div>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">CREATE_COMMENT</div>
                </div>
                <div class="fragment-description">创建文章评论。</div>
                <pre><code>mutation CreateComment($postId: ID!, $content: String!, $authorName: String, $authorEmail: String) {
  createComment(
    input: {
      commentOn: $postId
      content: $content
      author: $authorName
      authorEmail: $authorEmail
    }
  ) {
    success
    comment {
      id
      content
      date
      author {
        node {
          name
        }
      }
    }
  }
}</code></pre>
            </div>
        </section>
        
        <section id="usage">
            <h2>使用示例</h2>
            <p>以下是如何在组件中使用这些GraphQL查询的示例：</p>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">获取最新文章</div>
                </div>
                <pre><code>import { useQuery } from '@apollo/client';
import { GET_LATEST_POSTS } from '../lib/graphql/queries';

function LatestPosts() {
  const { loading, error, data } = useQuery(GET_LATEST_POSTS, {
    variables: { first: 5 }
  });

  if (loading) return <p>加载中...</p>;
  if (error) return <p>错误：{error.message}</p>;

  const posts = data.posts.nodes;

  return (
    <div>
      <h2>最新文章</h2>
      <ul>
        {posts.map(post => (
          <li key={post.id}>
            <h3>{post.title}</h3>
            <div dangerouslySetInnerHTML={{ __html: post.excerpt }} />
          </li>
        ))}
      </ul>
    </div>
  );
}</code></pre>
            </div>
            
            <div class="fragment-container">
                <div class="fragment-header">
                    <div class="fragment-name">用户登录</div>
                </div>
                <pre><code>import { useMutation } from '@apollo/client';
import { LOGIN_USER } from '../lib/graphql/mutations';

function LoginForm() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [login, { loading, error }] = useMutation(LOGIN_USER);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const { data } = await login({ 
        variables: { username, password } 
      });
      
      // 保存认证令牌到本地存储
      localStorage.setItem('authToken', data.login.authToken);
      
      // 处理登录成功
      // ...
    } catch (err) {
      // 处理错误
      console.error('登录失败', err);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* 表单内容 */}
    </form>
  );
}</code></pre>
            </div>
        </section>
        
        <div class="warning-note">
            <strong>注意：</strong> 使用GraphQL查询时，请只请求组件真正需要的字段，避免过度获取数据。同时，利用Apollo Client的缓存功能可以提高应用性能。
        </div>
        
        <footer>
            <p>Future Decade Frontend v1.2 - GraphQL查询片段文档</p>
            <p>更新日期: 2024年7月</p>
        </footer>
    </div>
</body>
</html> 