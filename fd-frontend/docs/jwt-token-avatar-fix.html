<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JWT令牌机制优化与头像加载问题解决方案</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1100px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 30px;
        }
        h3 {
            color: #3498db;
        }
        pre {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
            font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, <PERSON>urier, monospace;
        }
        code {
            font-family: <PERSON><PERSON>ono-<PERSON>, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON>lo, Courier, monospace;
            background-color: #f1f1f1;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .highlight {
            background-color: #fffde7;
            border-left: 5px solid #ffd600;
            padding: 15px;
            margin: 20px 0;
        }
        .warning {
            background-color: #ffebee;
            border-left: 5px solid #f44336;
            padding: 15px;
            margin: 20px 0;
        }
        .solution {
            background-color: #e8f5e9;
            border-left: 5px solid #4caf50;
            padding: 15px;
            margin: 20px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>JWT令牌机制优化与头像加载问题解决方案</h1>
    
    <div class="highlight">
        <p><strong>日期：</strong> 2025-05-19</p>
        <p><strong>版本：</strong> v1.0.0</p>
        <p><strong>模块：</strong> 认证系统，用户头像</p>
    </div>

    <h2>目录</h2>
    <ol>
        <li><a href="#background">背景</a></li>
        <li><a href="#jwt-problems">JWT令牌问题分析</a></li>
        <li><a href="#avatar-problems">头像加载延迟问题分析</a></li>
        <li><a href="#jwt-solution">JWT令牌优化方案</a></li>
        <li><a href="#avatar-solution">头像加载优化方案</a></li>
        <li><a href="#code-changes">代码修改详解</a></li>
    </ol>

    <h2 id="background">1. 背景</h2>
    <p>在对项目的JWT认证系统进行优化后，发现用户头像出现了一个新的问题：用户登录成功后，头像不会立即显示，需要刷新页面后才能正确加载。这影响了用户体验，因此需要进行修复。</p>
    
    <h2 id="jwt-problems">2. JWT令牌问题分析</h2>
    <p>通过对JWT认证系统的分析，我们发现以下问题：</p>
    
    <div class="warning">
        <h3>存在的问题：</h3>
        <ol>
            <li><strong>令牌过期时间过短：</strong> WPGraphQL JWT Authentication 插件默认的令牌有效期仅为300秒（5分钟），导致用户需要频繁重新登录。</li>
            <li><strong>缺少令牌刷新机制：</strong> 前端未实现有效的令牌自动刷新机制，导致令牌过期后用户体验中断。</li>
            <li><strong>错误的令牌过期处理：</strong> 在Apollo Client中，未正确处理令牌过期错误，未能自动尝试刷新令牌。</li>
        </ol>
    </div>

    <h2 id="avatar-problems">3. 头像加载延迟问题分析</h2>
    <p>头像加载延迟问题与JWT认证系统的修改直接相关。具体原因如下：</p>
    
    <div class="warning">
        <h3>存在的问题：</h3>
        <ol>
            <li><strong>数据同步问题：</strong> 登录后，用户数据直接从登录响应中获取，而不是从专用的用户数据查询中获取，可能导致头像信息不完整。</li>
            <li><strong>时序问题：</strong> 用户数据先被设置到状态中，然后才重置Apollo客户端缓存，可能导致不同步。</li>
            <li><strong>缓存刷新机制不完善：</strong> 初始化方法只在没有本地用户数据时才尝试获取最新数据，导致缓存的数据可能过时。</li>
        </ol>
    </div>
    
    <p>通过分析开发者控制台中的 Network 和 GraphQL 请求，我们发现：</p>
    <ol>
        <li>登录成功后，<code>login</code>变更请求返回了用户基本信息，但不包含完整的头像URL。</li>
        <li>应该通过<code>GET_CURRENT_USER</code>查询获取完整的用户信息，包括头像URL。</li>
        <li>登录流程中，Apollo Client缓存重置的时机与用户数据设置的顺序导致了头像延迟加载问题。</li>
    </ol>

    <h2 id="jwt-solution">4. JWT令牌优化方案</h2>
    
    <div class="solution">
        <h3>解决方案：</h3>
        <ol>
            <li><strong>增加令牌有效期：</strong> 将JWT令牌有效期从5分钟增加到7天。</li>
            <li><strong>设置合理的刷新令牌有效期：</strong> 设置刷新令牌的有效期为30天。</li>
            <li><strong>实现令牌刷新机制：</strong> 在Apollo Client中添加自动令牌刷新功能，当令牌过期时自动使用刷新令牌获取新的访问令牌。</li>
            <li><strong>统一令牌过期时间计算：</strong> 确保令牌过期时间始终基于签发时间（iat）计算，避免时间叠加问题。</li>
        </ol>
    </div>

    <pre><code>// lib/apollo-client.ts
// 刷新令牌变更
const REFRESH_AUTH_TOKEN = gql`
  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {
    refreshJwtAuthToken(input: $input) {
      authToken
    }
  }
`;

// 刷新认证令牌的函数
const refreshToken = async (client: ApolloClient&lt;any&gt;): Promise&lt;string | null&gt; => {
  // 获取刷新令牌
  const refreshToken = localStorage.getItem('fd_refresh_token');
  
  if (!refreshToken) {
    // 如果没有刷新令牌，清除auth数据并返回null
    clearAuthData();
    return null;
  }
  
  try {
    // 请求新令牌
    const refreshResult = await client.mutate({
      mutation: REFRESH_AUTH_TOKEN,
      variables: {
        input: {
          jwtRefreshToken: refreshToken,
        },
      },
    });
    
    const newToken = refreshResult?.data?.refreshJwtAuthToken?.authToken;
    
    if (newToken) {
      // 保存新的token
      setAuthToken(newToken);
      return newToken;
    }
  } catch (error) {
    console.error('刷新令牌失败:', error);
  }
  
  return null;
};</code></pre>

    <h2 id="avatar-solution">5. 头像加载优化方案</h2>
    
    <div class="solution">
        <h3>解决方案：</h3>
        <ol>
            <li><strong>优化登录流程：</strong> 修改登录流程，确保在设置用户状态前先获取完整的用户数据。</li>
            <li><strong>强制获取最新数据：</strong> 在登录成功后主动查询最新的用户数据，确保头像URL正确加载。</li>
            <li><strong>改进初始化逻辑：</strong> 无论是否有本地缓存数据，都尝试获取最新用户信息。</li>
            <li><strong>添加调试日志：</strong> 添加详细的日志记录，便于追踪数据流向和潜在问题。</li>
        </ol>
    </div>

    <pre><code>// contexts/AuthContext.tsx - 登录方法优化
const login = async (input: LoginUserInput): Promise&lt;void&gt; => {
  setIsLoading(true);
  setError(null);
  
  try {
    // 登录获取令牌
    const { data } = await loginMutation({
      variables: { input },
    });
    
    if (data?.login) {
      const { authToken, refreshToken, user } = data.login;
      
      if (authToken) {
        console.log('登录成功，保存认证数据');
        // 保存认证相关数据到本地存储
        saveAuthData(authToken, refreshToken, user);
        
        // 先重置Apollo Client缓存，确保不使用旧数据
        await client.resetStore();
        
        // 主动获取最新的用户信息（包含完整的头像等信息）
        try {
          console.log('获取最新用户数据');
          const currentUser = await fetchCurrentUser();
          if (currentUser) {
            setUser(currentUser); // 使用最新获取的用户数据更新状态
            console.log('用户数据已更新，包含头像URL:', currentUser?.avatar?.url);
          } else {
            setUser(user); // 备选：使用登录返回的用户数据
            console.log('使用登录返回的用户数据');
          }
        } catch (fetchError) {
          console.error('获取用户详情失败，使用登录返回的基本数据:', fetchError);
          setUser(user); // 使用登录返回的信息作为备选
        }
      } else {
        throw new Error('登录失败：未返回认证令牌');
      }
    } else {
      throw new Error('登录失败：未返回数据');
    }
  } catch (err) {
    console.error('登录失败:', err);
    setError(err instanceof Error ? err.message : '登录过程中发生错误');
  } finally {
    setIsLoading(false);
  }
};</code></pre>

    <pre><code>// contexts/AuthContext.tsx - 初始化方法优化
useEffect(() => {
  const initAuth = async () => {
    const token = getAuthToken();
    const userData = getUserData();
    
    if (token) {
      console.log('初始化: 找到认证令牌');
      
      // 先设置临时状态，避免UI闪烁
      if (userData) {
        console.log('初始化: 找到缓存的用户数据');
        setUser(userData);
      }
      
      // 始终尝试获取最新用户信息，无论是否有缓存的用户数据
      try {
        console.log('初始化: 从服务器获取最新用户数据');
        const currentUser = await fetchCurrentUser();
        if (currentUser) {
          console.log('初始化: 成功获取最新用户数据，包含头像:', currentUser?.avatar?.url);
          // 既更新状态又更新本地存储
          setUser(currentUser);
          setUserData(currentUser);
        } else {
          console.log('初始化: 无法获取用户数据，但令牌存在');
          // 令牌可能已过期 - 清除数据
          clearAuthData();
          setUser(null);
        }
      } catch (err) {
        console.error('初始化验证失败:', err);
        clearAuthData();
        setUser(null);
      }
    } else {
      console.log('初始化: 未找到认证令牌');
      clearAuthData();
      setUser(null);
    }
    
    setIsLoading(false);
  };
  
  initAuth();
}, []);</code></pre>

    <h2 id="code-changes">6. 代码修改详解</h2>
    
    <h3>6.1 utils/auth-utils.ts 修改</h3>
    <p>添加了刷新令牌相关的工具函数，用于支持令牌刷新机制：</p>
    
    <pre><code>// 新增的函数
export const setRefreshToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(REFRESH_TOKEN_KEY, token);
  }
};

export const getRefreshToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem(REFRESH_TOKEN_KEY);
  }
  return null;
};

export const clearRefreshToken = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(REFRESH_TOKEN_KEY);
  }
};

// 保存完整认证信息的辅助函数
export const saveAuthData = (authToken: string, refreshToken: string | null, userData: any): void => {
  setAuthToken(authToken);
  if (refreshToken) {
    setRefreshToken(refreshToken);
  }
  if (userData) {
    setUserData(userData);
  }
};</code></pre>

    <h3>6.2 lib/graphql/mutations.ts 修改</h3>
    <p>确保 LOGIN_USER 变更查询返回刷新令牌：</p>
    
    <pre><code>export const LOGIN_USER = gql`
  mutation LoginUser($input: LoginInput!) {
    login(input: $input) {
      authToken
      refreshToken
      user {
        id
        name
        email
        databaseId
        username
      }
    }
  }
`;</code></pre>

    <h3>6.3 Apollo Client 错误处理优化</h3>
    <p>在Apollo Client中实现了自动令牌刷新机制：</p>
    
    <pre><code>// 错误处理链接
const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (graphQLErrors) {
    for (const err of graphQLErrors) {
      const { message, extensions } = err;
      
      // 处理认证错误，如过期的token
      if (
        extensions?.code === 'UNAUTHENTICATED' || 
        message.includes('expired token') || 
        message.includes('Expired token') ||
        message.includes('Internal server error')
      ) {
        if (operation.operationName === 'RefreshAuthToken') {
          // 避免无限循环
          clearAuthData();
          continue;
        }
        
        // 返回一个新的Observable，在刷新令牌后重试操作
        return new Observable(observer => {
          refreshToken(operation.getContext().client)
            .then(newToken => {
              if (!newToken) {
                observer.error(err);
                observer.complete();
                return;
              }
              
              // 使用新的token重试原始操作
              const originalContext = operation.getContext();
              operation.setContext({
                ...originalContext,
                headers: {
                  ...originalContext.headers,
                  authorization: `Bearer ${newToken}`,
                },
              });
              
              forward(operation).subscribe({
                next: observer.next.bind(observer),
                error: observer.error.bind(observer),
                complete: observer.complete.bind(observer),
              });
            })
            .catch(refreshError => {
              observer.error(refreshError);
              observer.complete();
            });
        });
      }
    }
  }
  
  return forward(operation);
});</code></pre>

    <h2>7. 结论</h2>
    
    <div class="highlight">
        <p>通过优化JWT令牌机制和改进用户数据获取流程，我们成功解决了以下问题：</p>
        <ol>
            <li>延长了JWT令牌的有效期，减少用户需要重新登录的频率。</li>
            <li>实现了自动令牌刷新机制，提高了用户体验。</li>
            <li>修复了登录后头像延迟加载的问题，确保用户数据的一致性。</li>
            <li>优化了数据流向，确保Apollo Client缓存与用户状态同步。</li>
        </ol>
        <p>这些改进大大提升了用户认证体验，并为后续的功能开发提供了更稳定的认证基础。</p>
    </div>

</body>
</html> 