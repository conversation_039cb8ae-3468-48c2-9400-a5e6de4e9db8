<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GraphQL查询参考文档（可打印版） - Future Decade</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.5;
      color: #000;
      margin: 2cm;
      font-size: 10pt;
    }
    h1 {
      font-size: 18pt;
      margin-bottom: 0.5cm;
      border-bottom: 1px solid #000;
      padding-bottom: 0.2cm;
    }
    h2 {
      font-size: 14pt;
      margin-top: 1cm;
      margin-bottom: 0.3cm;
      page-break-after: avoid;
    }
    h3 {
      font-size: 12pt;
      margin-top: 0.8cm;
      margin-bottom: 0.2cm;
      page-break-after: avoid;
    }
    pre {
      background-color: #f5f5f5;
      border: 1px solid #ddd;
      padding: 0.3cm;
      overflow-x: auto;
      font-size: 9pt;
      font-family: monospace;
      white-space: pre-wrap;
      page-break-inside: avoid;
    }
    p {
      margin-bottom: 0.3cm;
    }
    .fragment, .query {
      margin-bottom: 1cm;
      page-break-inside: avoid;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 0.5cm 0;
      page-break-inside: avoid;
    }
    th, td {
      border: 1px solid #000;
      padding: 0.1cm 0.2cm;
      text-align: left;
      font-size: 9pt;
    }
    th {
      background-color: #f0f0f0;
    }
    @media print {
      body {
        margin: 0;
        padding: 1cm;
      }
      pre {
        white-space: pre-wrap;
      }
      a {
        text-decoration: none;
        color: #000;
      }
      .no-print {
        display: none;
      }
      @page {
        margin: 1.5cm;
      }
    }
  </style>
</head>
<body>
  <div class="no-print" style="text-align:right;">
    <button onclick="window.print()">打印此文档</button>
  </div>
  
  <h1>Future Decade GraphQL查询参考文档（可打印版）</h1>
  <p>本文档提供了所有GraphQL片段和查询的详细参考，供开发团队使用。</p>
  
  <h2>目录</h2>
  <ol>
    <li><a href="#fragments">片段定义</a></li>
    <li><a href="#post-queries">文章查询</a></li>
    <li><a href="#page-queries">页面查询</a></li>
    <li><a href="#taxonomy-queries">分类法查询</a></li>
    <li><a href="#user-queries">用户查询</a></li>
    <li><a href="#media-queries">媒体查询</a></li>
    <li><a href="#menu-queries">菜单查询</a></li>
    <li><a href="#custom-post-queries">自定义内容类型查询</a></li>
    <li><a href="#settings-queries">设置查询</a></li>
    <li><a href="#comment-queries">评论查询</a></li>
  </ol>
  
  <h2 id="fragments">1. 片段定义</h2>
  
  <div class="fragment">
    <h3>POST_FRAGMENT</h3>
    <p>文章基本信息片段，用于获取文章的基本数据。</p>
    <pre>fragment PostFragment on Post {
  id
  databaseId
  title
  slug
  date
  modified
  content
  excerpt
  featuredImage {
    node {
      ...MediaFragment
    }
  }
  author {
    node {
      ...UserFragment
    }
  }
  categories {
    nodes {
      ...CategoryFragment
    }
  }
  tags {
    nodes {
      ...TagFragment
    }
  }
}</pre>
  </div>
  
  <div class="fragment">
    <h3>POST_DETAIL_FRAGMENT</h3>
    <p>文章详细信息片段，包含更多文章信息。</p>
    <pre>fragment PostDetailFragment on Post {
  ...PostFragment
  commentStatus
  pingStatus
  commentCount
  template {
    templateName
  }
  status
}</pre>
  </div>
  
  <div class="fragment">
    <h3>CATEGORY_FRAGMENT</h3>
    <p>分类信息片段。</p>
    <pre>fragment CategoryFragment on Category {
  id
  databaseId
  name
  slug
  description
  count
  parentId
}</pre>
  </div>
  
  <div class="fragment">
    <h3>TAG_FRAGMENT</h3>
    <p>标签信息片段。</p>
    <pre>fragment TagFragment on Tag {
  id
  databaseId
  name
  slug
  description
  count
}</pre>
  </div>
  
  <div class="fragment">
    <h3>USER_FRAGMENT</h3>
    <p>用户信息片段。</p>
    <pre>fragment UserFragment on User {
  id
  databaseId
  name
  firstName
  lastName
  nickname
  slug
  description
  avatar {
    url
    width
    height
  }
}</pre>
  </div>
  
  <div class="fragment">
    <h3>MEDIA_FRAGMENT</h3>
    <p>媒体文件信息片段。</p>
    <pre>fragment MediaFragment on MediaItem {
  id
  databaseId
  title
  altText
  caption
  description
  mediaType
  mediaItemUrl
  sourceUrl
  mimeType
  sizes
  srcSet
}</pre>
  </div>
  
  <div class="fragment">
    <h3>MENU_ITEM_FRAGMENT</h3>
    <p>菜单项信息片段。</p>
    <pre>fragment MenuItemFragment on MenuItem {
  id
  databaseId
  title
  url
  path
  target
  label
  parentId
  childItems {
    nodes {
      id
      databaseId
      title
      url
      path
      target
      label
    }
  }
}</pre>
  </div>
  
  <div class="fragment">
    <h3>CUSTOM_POST_FRAGMENT</h3>
    <p>自定义文章类型信息片段。</p>
    <pre>fragment CustomPostFragment on ContentNode {
  id
  databaseId
  slug
  title
  date
  modified
  content
  ... on NodeWithFeaturedImage {
    featuredImage {
      node {
        ...MediaFragment
      }
    }
  }
  ... on NodeWithAuthor {
    author {
      node {
        ...UserFragment
      }
    }
  }
}</pre>
  </div>
  
  <div class="fragment">
    <h3>COMMENT_FRAGMENT</h3>
    <p>评论信息片段。</p>
    <pre>fragment CommentFragment on Comment {
  id
  databaseId
  content
  date
  status
  parentId
  author {
    node {
      ...UserFragment
    }
  }
}</pre>
  </div>
  
  <h2 id="post-queries">2. 文章查询</h2>
  
  <div class="query">
    <h3>GET_LATEST_POSTS</h3>
    <p>获取最新文章列表。</p>
    <pre>query GetLatestPosts($first: Int) {
  posts(first: $first, where: { orderby: { field: DATE, order: DESC } }) {
    nodes {
      ...PostFragment
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}</pre>
  </div>
  
  <div class="query">
    <h3>GET_POST_BY_SLUG</h3>
    <p>通过slug获取单个文章详情。</p>
    <pre>query GetPostBySlug($slug: ID!) {
  post(id: $slug, idType: SLUG) {
    ...PostDetailFragment
  }
}</pre>
  </div>
  
  <div class="query">
    <h3>GET_POST_BY_ID</h3>
    <p>通过ID获取单个文章详情。</p>
    <pre>query GetPostById($id: ID!) {
  post(id: $id, idType: DATABASE_ID) {
    ...PostDetailFragment
  }
}</pre>
  </div>
  
  <div class="query">
    <h3>SEARCH_POSTS</h3>
    <p>搜索文章。</p>
    <pre>query SearchPosts($search: String!, $first: Int) {
  posts(first: $first, where: { search: $search }) {
    nodes {
      ...PostFragment
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}</pre>
  </div>
  
  <h2 id="page-queries">3. 页面查询</h2>
  
  <div class="query">
    <h3>GET_PAGES</h3>
    <p>获取所有页面。</p>
    <pre>query GetPages($first: Int) {
  pages(first: $first) {
    nodes {
      id
      databaseId
      title
      slug
      content
      date
      modified
      featuredImage {
        node {
          ...MediaFragment
        }
      }
      template {
        templateName
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}</pre>
  </div>
  
  <div class="query">
    <h3>GET_PAGE_BY_SLUG</h3>
    <p>通过slug获取单个页面。</p>
    <pre>query GetPageBySlug($slug: ID!) {
  page(id: $slug, idType: URI) {
    id
    databaseId
    title
    slug
    content
    date
    modified
    featuredImage {
      node {
        ...MediaFragment
      }
    }
    template {
      templateName
    }
  }
}</pre>
  </div>
  
  <h2 id="taxonomy-queries">4. 分类法查询</h2>
  
  <div class="query">
    <h3>GET_CATEGORIES</h3>
    <p>获取所有分类。</p>
    <pre>query GetCategories {
  categories {
    nodes {
      ...CategoryFragment
    }
  }
}</pre>
  </div>
  
  <div class="query">
    <h3>GET_TAGS</h3>
    <p>获取所有标签。</p>
    <pre>query GetTags {
  tags {
    nodes {
      ...TagFragment
    }
  }
}</pre>
  </div>
  
  <div class="query">
    <h3>GET_TAXONOMY_TERMS</h3>
    <p>获取指定分类法下的所有项。</p>
    <pre>query GetTaxonomyTerms($taxonomy: TaxonomyEnum!) {
  taxonomies(where: { name: $taxonomy }) {
    nodes {
      name
      description
      labels {
        name
      }
      connectedTerms {
        nodes {
          id
          name
          slug
          count
        }
      }
    }
  }
}</pre>
  </div>
  
  <h2 id="custom-post-queries">5. 自定义内容类型查询</h2>
  
  <div class="query">
    <h3>GET_CUSTOM_POSTS</h3>
    <p>获取指定自定义内容类型的列表。</p>
    <pre>query GetCustomPosts($type: ContentTypeEnum!, $first: Int) {
  contentNodes(first: $first, where: { contentTypes: [$type], orderby: { field: DATE, order: DESC } }) {
    nodes {
      ...CustomPostFragment
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}</pre>
  </div>
  
  <div class="query">
    <h3>GET_CUSTOM_POST_BY_SLUG</h3>
    <p>通过slug获取自定义内容类型详情。</p>
    <pre>query GetCustomPostBySlug($type: ContentTypeEnum!, $slug: String!) {
  contentNode(id: $slug, idType: SLUG, contentType: $type) {
    ...CustomPostFragment
  }
}</pre>
  </div>
  
  <div class="query">
    <h3>GET_CUSTOM_POST_BY_ID</h3>
    <p>通过ID获取自定义内容类型详情。</p>
    <pre>query GetCustomPostById($type: ContentTypeEnum!, $id: ID!) {
  contentNode(id: $id, idType: DATABASE_ID, contentType: $type) {
    ...CustomPostFragment
  }
}</pre>
  </div>
  
  <h2 id="comment-queries">6. 评论查询</h2>
  
  <div class="query">
    <h3>GET_POST_COMMENTS</h3>
    <p>获取文章的评论列表。</p>
    <pre>query GetPostComments($postId: ID!, $first: Int) {
  post(id: $postId, idType: DATABASE_ID) {
    comments(first: $first, where: { orderby: { field: COMMENT_DATE, order: DESC } }) {
      nodes {
        ...CommentFragment
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
}</pre>
  </div>
  
  <div class="query">
    <h3>GET_COMMENT</h3>
    <p>通过ID获取评论详情。</p>
    <pre>query GetComment($id: ID!) {
  comment(id: $id, idType: DATABASE_ID) {
    ...CommentFragment
  }
}</pre>
  </div>
  
  <script>
    // 打印按钮功能
    document.addEventListener('DOMContentLoaded', function() {
      document.querySelector('.no-print button').addEventListener('click', function() {
        window.print();
      });
    });
  </script>
</body>
</html> 