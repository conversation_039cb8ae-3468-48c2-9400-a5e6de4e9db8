<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FD-Frontend支付功能实现文档</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            border-bottom: 2px solid #f8f9fa;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        h2 {
            margin-top: 30px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f8f9fa;
        }
        h3 {
            margin-top: 25px;
            color: #0366d6;
        }
        pre {
            background-color: #f8f9fa;
            border: 1px solid #e1e4e8;
            border-radius: 3px;
            padding: 10px;
            overflow-x: auto;
        }
        code {
            font-family: <PERSON><PERSON><PERSON>, Monaco, 'Andale Mono', monospace;
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #e1e4e8;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
        }
        .note {
            background-color: #fff3cd;
            border-left: 4px solid #ffeeba;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .important {
            background-color: #d1ecf1;
            border-left: 4px solid #0c5460;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .component-name {
            color: #e10098;
            font-weight: bold;
        }
        .directory-structure {
            background-color: #f8f9fa;
            border: 1px solid #e1e4e8;
            border-radius: 3px;
            padding: 10px;
            overflow-x: auto;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
    </style>
</head>
<body>
    <h1>FD-Frontend 支付功能完整实现</h1>
    
    <h2>1. 概述</h2>
    <p>本文档详细介绍了 fd-websiteV4 项目中基于 GraphQL 的支付功能前端实现。该项目是一个 headless WordPress 方案，包含 fd-frontend 前端应用和 fd-payment 后端支付插件。</p>
    
    <h3>1.1 实现目标</h3>
    <ul>
        <li>支持多种支付方式（支付宝、微信支付、余额支付等）</li>
        <li>提供完整的订单创建、支付和查询流程</li>
        <li>实现响应式界面，支持桌面和移动设备访问</li>
        <li>提供友好的用户交互体验</li>
    </ul>
    
    <h3>1.2 技术栈</h3>
    <ul>
        <li>Next.js 14 - React 框架</li>
        <li>Apollo Client - GraphQL 客户端</li>
        <li>TailwindCSS - 样式框架</li>
        <li>TypeScript - 类型系统</li>
    </ul>

    <h2>2. 目录结构</h2>
    <div class="directory-structure">
<pre>fd-frontend/
├── src/
│   ├── app/
│   │   ├── payment/
│   │   │   ├── page.tsx          # 支付中心页面
│   │   │   ├── orders/
│   │   │   │   └── page.tsx      # 订单列表页面
│   │   │   └── pay/[id]/
│   │   │       └── page.tsx      # 订单支付页面
│   │   │
│   │   ├── components/
│   │   │   ├── payment/
│   │   │   │   ├── OrderForm.tsx           # 订单表单组件
│   │   │   │   ├── PaymentMethodSelector.tsx # 支付方式选择器
│   │   │   │   ├── PaymentProcessor.tsx    # 支付处理组件
│   │   │   │   └── BalancePaymentForm.tsx  # 余额支付表单
│   │   │   │
│   │   │   └── lib/
│   │   │       └── graphql/
│   │   │           ├── mutations.ts      # GraphQL 变更操作
│   │   │           └── queries.ts        # GraphQL 查询操作
│   │   │
│   │   └── docs/
│   │       └── payment-implementation.html # 本文档</pre>
    </div>

    <h2>3. 前端组件</h2>
    <p>支付功能由以下主要组件组成：</p>
    
    <h3>3.1 PaymentMethodSelector 组件</h3>
    <p>用于选择支付方式的组件。能够从服务器获取可用的支付方式列表，并展示给用户选择。</p>
    <p><strong>主要功能：</strong></p>
    <ul>
        <li>从 GraphQL API 获取可用支付方式</li>
        <li>以卡片形式展示支付方式选项</li>
        <li>支持自定义样式和图标</li>
        <li>提供选择回调函数</li>
    </ul>
    <p><strong>用法示例：</strong></p>
<pre><code>import PaymentMethodSelector from '../../components/payment/PaymentMethodSelector';

// 在组件中使用
&lt;PaymentMethodSelector
  onSelectMethod={(method) => setSelectedMethod(method)}
  selectedMethod={selectedMethod}
/>
</code></pre>

    <h3>3.2 OrderForm 组件</h3>
    <p>用于创建新订单的表单组件。提供订单标题、金额等信息的输入，并选择支付方式。</p>
    <p><strong>主要功能：</strong></p>
    <ul>
        <li>提供订单信息输入表单</li>
        <li>集成支付方式选择器</li>
        <li>创建订单并准备支付</li>
        <li>提供订单创建成功的回调</li>
    </ul>
    <p><strong>用法示例：</strong></p>
<pre><code>import OrderForm from '../../components/payment/OrderForm';

// 在组件中使用
&lt;OrderForm 
  onOrderCreated={(orderId, method, orderData) => {
    // 处理订单创建成功
  }} 
/>
</code></pre>

    <h3>3.3 PaymentProcessor 组件</h3>
    <p>处理第三方支付（如支付宝、微信支付）的组件。支持多种形式的支付界面展示，如支付表单、二维码等。</p>
    <p><strong>主要功能：</strong></p>
    <ul>
        <li>获取支付链接或表单</li>
        <li>根据支付方式展示不同界面</li>
        <li>支持二维码展示</li>
        <li>定时检查支付状态</li>
        <li>处理支付完成回调</li>
    </ul>
    <p><strong>用法示例：</strong></p>
<pre><code>import PaymentProcessor from '../../components/payment/PaymentProcessor';

// 在组件中使用
&lt;PaymentProcessor
  orderId={orderId}
  paymentMethod={paymentMethod}
  onPaymentSuccess={handlePaymentSuccess}
  onCancel={handleCancelPayment}
/>
</code></pre>

    <h3>3.4 BalancePaymentForm 组件</h3>
    <p>用于处理余额支付的表单组件。支持检查余额并完成支付。</p>
    <p><strong>主要功能：</strong></p>
    <ul>
        <li>获取并显示用户当前余额</li>
        <li>验证余额是否充足</li>
        <li>支持直接支付已有订单（使用 payOrderWithBalance）</li>
        <li>支持创建新订单并支付（使用 payWithBalance）</li>
        <li>处理支付状态和回调</li>
    </ul>
    <p><strong>用法示例：</strong></p>
<pre><code>import BalancePaymentForm from '../../components/payment/BalancePaymentForm';

// 在组件中使用（支付已有订单）
&lt;BalancePaymentForm
  title={orderDetails.title}
  amount={orderDetails.amount}
  description={orderDetails.description}
  orderId={orderId}
  onPaymentSuccess={handleBalancePaymentSuccess}
  onError={handleError}
/>
</code></pre>

    <h3>3.5 PointsPaymentForm 组件</h3>
    <p>用于处理积分支付的表单组件。支持检查积分并完成支付。</p>
    <p><strong>主要功能：</strong></p>
    <ul>
        <li>获取并显示用户当前积分</li>
        <li>验证积分是否充足</li>
        <li>支持直接支付已有订单（使用 payOrderWithPoints）</li>
        <li>支持创建新订单并支付（使用 payWithPoints）</li>
        <li>处理支付状态和回调</li>
    </ul>
    <p><strong>用法示例：</strong></p>
<pre><code>import PointsPaymentForm from '../../components/payment/PointsPaymentForm';

// 在组件中使用（支付已有订单）
&lt;PointsPaymentForm
  title={orderDetails.title}
  amount={orderDetails.amount}
  description={orderDetails.description}
  orderId={orderId}
  onPaymentSuccess={handlePointsPaymentSuccess}
  onError={handleError}
/>
</code></pre>

    <h2>4. 主要页面</h2>
    
    <h3>4.1 支付中心页面（/payment）</h3>
    <p>支付中心是用户创建新订单并支付的主要入口。</p>
    <p><strong>主要功能：</strong></p>
    <ul>
        <li>提供订单创建表单</li>
        <li>支持选择支付方式</li>
        <li>处理支付流程</li>
        <li>展示支付结果</li>
    </ul>
    <p>实现路径：<code>src/app/payment/page.tsx</code></p>
    
    <h3>4.2 订单列表页面（/payment/orders）</h3>
    <p>显示当前用户的所有订单，允许用户查看和管理订单。</p>
    <p><strong>主要功能：</strong></p>
    <ul>
        <li>展示订单列表，包括状态、金额等信息</li>
        <li>提供订单过滤和排序功能</li>
        <li>支持分页加载更多订单</li>
        <li>允许继续支付未完成的订单</li>
    </ul>
    <p>实现路径：<code>src/app/payment/orders/page.tsx</code></p>
    
    <h3>4.3 订单支付页面（/payment/pay/[id]）</h3>
    <p>专用于支付特定订单的页面，支持多种支付方式。</p>
    <p><strong>主要功能：</strong></p>
    <ul>
        <li>加载订单详细信息</li>
        <li>展示订单摘要（金额、订单号等）</li>
        <li>根据订单支付方式提供对应支付界面</li>
        <li>支持余额支付、第三方支付等</li>
        <li>处理支付状态和结果展示</li>
    </ul>
    <p>实现路径：<code>src/app/payment/pay/[id]/page.tsx</code></p>

    <h2>5. GraphQL 操作</h2>

    <h3>5.1 查询操作</h3>
    <p>前端使用以下主要查询来获取支付相关数据：</p>
    <ul>
        <li><code>GET_PAYMENT_METHODS</code>: 获取可用支付方式列表</li>
        <li><code>GET_PAYMENT_ORDER</code>: 获取单个订单详情</li>
        <li><code>GET_USER_ORDERS</code>: 获取用户订单列表</li>
        <li><code>GET_USER_BALANCE_AND_POINTS</code>: 获取用户余额和积分</li>
    </ul>
    <p>这些查询定义在 <code>src/lib/graphql/queries.ts</code> 文件中。</p>

    <h3>5.2 变更操作</h3>
    <p>支付流程中使用的主要变更操作：</p>
    <ul>
        <li><code>CREATE_ORDER</code>: 创建新的支付订单</li>
        <li><code>GET_PAYMENT_URL</code>: 获取第三方支付的链接或表单</li>
        <li><code>CHECK_PAYMENT_STATUS</code>: 检查订单支付状态</li>
        <li><code>PAY_WITH_BALANCE</code>: 使用余额创建并支付订单</li>
        <li><code>PAY_ORDER_WITH_BALANCE</code>: 使用余额支付已有订单</li>
        <li><code>PAY_WITH_POINTS</code>: 使用积分创建并支付订单</li>
        <li><code>PAY_ORDER_WITH_POINTS</code>: 使用积分支付已有订单</li>
    </ul>
    <p>这些变更定义在 <code>src/lib/graphql/mutations.ts</code> 文件中。</p>

    <h2>6. 支付流程实现</h2>
    
    <h3>6.1 创建订单流程</h3>
    <ol>
        <li>用户填写订单表单 (OrderForm)</li>
        <li>选择支付方式 (PaymentMethodSelector)</li>
        <li>提交表单，调用 CREATE_ORDER mutation</li>
        <li>订单创建成功后，根据支付方式进入对应支付流程</li>
    </ol>

    <h3>6.2 支付宝/微信支付流程</h3>
    <ol>
        <li>调用 GET_PAYMENT_URL mutation 获取支付链接或表单</li>
        <li>根据返回的 returnType 展示不同形式：
            <ul>
                <li>HTML: 渲染支付表单</li>
                <li>QRCODE: 显示支付二维码</li>
                <li>URL: 跳转到支付页面</li>
            </ul>
        </li>
        <li>定期调用 CHECK_PAYMENT_STATUS 检查支付状态</li>
        <li>支付完成后显示成功页面</li>
    </ol>

    <h3>6.3 余额支付流程</h3>
    <p>余额支付有两种模式：创建新订单支付和支付已有订单。</p>

    <p><strong>创建新订单并支付:</strong></p>
    <ol>
        <li>用户在支付中心填写订单信息并选择余额支付</li>
        <li>提交表单后，使用 PAY_WITH_BALANCE mutation 创建并支付订单</li>
        <li>检查余额是否充足，不足则提示充值</li>
        <li>支付成功后显示结果</li>
    </ol>

    <p><strong>支付已有订单:</strong></p>
    <ol>
        <li>用户从订单列表或其他入口进入订单支付页面</li>
        <li>加载订单信息并确认使用余额支付</li>
        <li>调用 PAY_ORDER_WITH_BALANCE mutation 支付订单</li>
        <li>支付成功后更新界面状态</li>
    </ol>

    <div class="important">
        <p><strong>重要说明:</strong> 在实现中，我们为 <code>BalancePaymentForm</code> 组件添加了智能逻辑，可以根据是否提供 <code>orderId</code> 参数自动选择使用 <code>payOrderWithBalance</code> 或 <code>payWithBalance</code> 进行支付。这样一个组件就能灵活地处理两种支付场景。</p>
    </div>

    <h3>6.4 积分支付流程</h3>
    <p>积分支付与余额支付类似，同样有两种支付模式：创建新订单支付和支付已有订单。</p>

    <p><strong>创建新订单并支付:</strong></p>
    <ol>
        <li>用户在支付中心填写订单信息并选择积分支付</li>
        <li>提交表单后，使用 PAY_WITH_POINTS mutation 创建并支付订单</li>
        <li>检查积分是否充足，不足则提示获取更多积分</li>
        <li>支付成功后显示结果</li>
    </ol>

    <p><strong>支付已有订单:</strong></p>
    <ol>
        <li>用户从订单列表或其他入口进入订单支付页面</li>
        <li>加载订单信息并确认使用积分支付</li>
        <li>调用 PAY_ORDER_WITH_POINTS mutation 支付订单</li>
        <li>支付成功后更新界面状态</li>
    </ol>

    <div class="important">
        <p><strong>重要说明:</strong> 与余额支付类似，<code>PointsPaymentForm</code> 组件也添加了智能逻辑，可以根据是否提供 <code>orderId</code> 参数自动选择使用 <code>payOrderWithPoints</code> 或 <code>payWithPoints</code> 进行支付。</p>
    </div>

    <h2>7. 关键代码详解</h2>

    <h3>7.1 BalancePaymentForm 组件</h3>
    <p>该组件是余额支付的核心，它能处理两种支付场景：</p>
<pre><code>// BalancePaymentForm.tsx 核心逻辑

// 根据是否有订单ID选择合适的突变
const isExistingOrder = !!orderId;

// 余额支付（使用已有订单ID）
const [payOrderWithBalance, { loading: loadingOrderPayment }] = useMutation(PAY_ORDER_WITH_BALANCE, {
  onCompleted: (data) => {
    if (data?.payOrderWithBalance?.status) {
      setPaymentCompleted(true);
      onPaymentSuccess(
        data.payOrderWithBalance.orderId,
        data.payOrderWithBalance.orderNumber
      );
    }
    setIsSubmitting(false);
  },
  // ... 错误处理
});

// 余额支付（创建新订单）
const [payWithBalance, { loading: loadingPayment }] = useMutation(PAY_WITH_BALANCE, {
  // ... 完成和错误处理
});

// 处理支付
const handlePayment = async () => {
  // ... 验证和状态设置

  try {
    if (isExistingOrder) {
      // 使用已有订单ID进行支付
      await payOrderWithBalance({
        variables: {
          input: {
            orderId
          }
        }
      });
    } else {
      // 创建新订单并支付
      await payWithBalance({
        variables: {
          input: {
            title,      
            amount,     
            description 
          }
        }
      });
    }
  } catch (error) {
    // 错误处理
  }
};</code></pre>

    <h3>7.2 PointsPaymentForm 组件</h3>
    <p>该组件是积分支付的核心，它能处理两种支付场景：</p>
<pre><code>// PointsPaymentForm.tsx 核心逻辑

// 根据是否有订单ID选择合适的突变
const isExistingOrder = !!orderId;

// 积分支付（使用已有订单ID）
const [payOrderWithPoints, { loading: loadingOrderPayment }] = useMutation(PAY_ORDER_WITH_POINTS, {
  onCompleted: (data) => {
    if (data?.payOrderWithPoints?.status) {
      setPaymentCompleted(true);
      onPaymentSuccess(
        data.payOrderWithPoints.orderId,
        data.payOrderWithPoints.orderNumber
      );
    }
    setIsSubmitting(false);
  },
  // ... 错误处理
});

// 积分支付（创建新订单）
const [payWithPoints, { loading: loadingPayment }] = useMutation(PAY_WITH_POINTS, {
  // ... 完成和错误处理
});

// 处理支付
const handlePayment = async () => {
  // ... 验证和状态设置

  try {
    if (isExistingOrder) {
      // 使用已有订单ID进行支付
      await payOrderWithPoints({
        variables: {
          input: {
            orderId
          }
        }
      });
    } else {
      // 创建新订单并支付
      await payWithPoints({
        variables: {
          input: {
            title,      
            points: amount, // 注意：积分支付时amount表示积分数量     
            description 
          }
        }
      });
    }
  } catch (error) {
    // 错误处理
  }
};</code></pre>

    <h3>7.3 支付中心页面</h3>
    <p>支付中心页面实现了完整的支付流程和状态管理：</p>
<pre><code>// payment/page.tsx 核心逻辑

enum PaymentStep {
  ORDER_FORM,
  PAYMENT_PROCESSOR,
  PAYMENT_SUCCESS
}

export default function PaymentPage() {
  const [currentStep, setCurrentStep] = useState&lt;PaymentStep>(PaymentStep.ORDER_FORM);
  const [orderId, setOrderId] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('');
  // ... 其他状态和处理函数
  
  // 处理订单创建成功
  const handleOrderCreated = (newOrderId: string, method: string, orderData: any = {}) => {
    setOrderId(newOrderId);
    setPaymentMethod(method);
    // ... 保存订单信息
    setCurrentStep(PaymentStep.PAYMENT_PROCESSOR);
  };
  
  // 渲染当前步骤对应的界面
  return (
    &lt;div className="max-w-2xl mx-auto p-4 sm:p-6 lg:p-8">
      &lt;h1 className="text-2xl font-bold mb-6">支付中心&lt;/h1>
      
      {currentStep === PaymentStep.ORDER_FORM && (
        &lt;div className="bg-white shadow-sm rounded-lg p-6">
          &lt;OrderForm onOrderCreated={handleOrderCreated} />
        &lt;/div>
      )}
      
      {currentStep === PaymentStep.PAYMENT_PROCESSOR && (
        &lt;div className="bg-white shadow-sm rounded-lg p-6">
          {paymentMethod === 'wallet' ? (
            &lt;BalancePaymentForm
              title={orderTitle || `订单 #${orderId}`}
              amount={orderAmount}
              description={orderDescription}
              orderId={orderId}
              onPaymentSuccess={handleBalancePaymentSuccess}
              onError={handleError}
            />
          ) : paymentMethod === 'points' ? (
            &lt;PointsPaymentForm
              title={orderTitle || `订单 #${orderId}`}
              amount={orderAmount}
              description={orderDescription}
              orderId={orderId}
              onPaymentSuccess={handleBalancePaymentSuccess}
              onError={handleError}
            />
          ) : (
            &lt;PaymentProcessor
              orderId={orderId}
              paymentMethod={paymentMethod}
              onPaymentSuccess={handlePaymentSuccess}
              onCancel={handleCancelPayment}
            />
          )}
        &lt;/div>
      )}
      
      {currentStep === PaymentStep.PAYMENT_SUCCESS && (
        // 支付成功结果展示
      )}
    &lt;/div>
  );
}</code></pre>

    <h3>7.4 订单支付页面</h3>
    <p>订单支付页面处理特定订单的支付：</p>
<pre><code>// payment/pay/[id]/page.tsx 核心逻辑

export default function PayOrderPage() {
  const params = useParams();
  const orderId = params.id as string;
  
  // 获取订单信息
  const { loading, error: orderError, data } = useQuery(GET_PAYMENT_ORDER, {
    variables: { id: orderId },
    skip: !orderId
  });
  
  // 根据当前步骤渲染不同内容
  const renderContent = () => {
    switch (currentStep) {
      case PaymentStep.PAYMENT_PROCESSOR:
        if (orderDetails?.paymentMethod === 'wallet') {
          return (
            &lt;BalancePaymentForm
              title={orderDetails.title}
              amount={orderDetails.amount}
              description={orderDetails.description}
              orderId={orderId}
              onPaymentSuccess={handleBalancePaymentSuccess}
              onError={handleError}
            />
          );
        } else {
          return (
            &lt;PaymentProcessor
              orderId={orderId}
              paymentMethod={orderDetails?.paymentMethod || ''}
              onPaymentSuccess={handlePaymentSuccess}
              onCancel={handleCancelPayment}
            />
          );
        }
      // ... 其他状态的渲染
    }
  };
  
  return (
    // 页面布局和内容
  );
}</code></pre>

    <h2>8. GraphQL 后端扩展</h2>
    <p>为了支持完整的支付功能，我们对后端进行了以下扩展：</p>
    
    <h3>8.1 增加 payOrderWithBalance 突变</h3>
    <p>在 <code>class-wallet-points-mutation.php</code> 中添加了新的 GraphQL 突变，实现通过订单ID进行余额支付的功能：</p>
<pre><code>// 使用余额支付已有订单
register_graphql_mutation('payOrderWithBalance', [
    'inputFields' => [
        'orderId' => [
            'type' => ['non_null' => 'ID'],
            'description' => __('订单ID', 'fd-payment'),
        ],
    ],
    'outputFields' => [
        // ... 输出字段定义
    ],
    'mutateAndGetPayload' => function($input, $context, $info) {
        // 权限检查
        if (!is_user_logged_in()) {
            throw new \GraphQL\Error\UserError(__('必须登录才能使用余额支付', 'fd-payment'));
        }
        
        $user_id = get_current_user_id();
        $order_id = absint($input['orderId']);
        
        // 获取订单信息
        $order = fd_payment_get_order($order_id);
        
        // 检查订单所有者
        if ($order->user_id != $user_id && !current_user_can('manage_options')) {
            throw new \GraphQL\Error\UserError(__('无权支付此订单', 'fd-payment'));
        }
        
        // ... 验证余额并完成支付的逻辑
        
        return [
            'status' => true,
            'message' => __('支付成功', 'fd-payment'),
            'order_id' => $order_id,
            'new_balance' => $new_balance,
            'order_number' => $order->order_number,
        ];
    }
]);</code></pre>

    <h3>8.2 文档更新</h3>
    <p>我们更新了 <code>graphql-api.html</code> 文档，添加了新的 <code>payOrderWithBalance</code> 和 <code>payOrderWithPoints</code> 突变的详细说明和使用示例，确保开发人员能够正确使用这些新功能。</p>

    <h2>9. 响应式设计</h2>
    <p>支付界面设计充分考虑了不同设备的用户体验：</p>
    <ul>
        <li>使用 TailwindCSS 的响应式工具类，适应不同屏幕尺寸</li>
        <li>针对移动设备优化表单布局和交互</li>
        <li>支付处理组件能够智能检测设备类型，提供最佳支付体验</li>
        <li>对于微信浏览器提供专门处理逻辑，支持微信内支付</li>
    </ul>

    <h2>10. 安全性考虑</h2>
    <p>支付功能的实现特别注重安全性：</p>
    <ul>
        <li>所有支付相关操作都要求用户认证</li>
        <li>使用 GraphQL 变更操作时进行权限验证</li>
        <li>防止跨站脚本攻击，特别是在渲染支付表单时</li>
        <li>避免敏感支付信息在前端暴露</li>
        <li>使用安全的状态管理，防止支付状态被篡改</li>
    </ul>

    <div class="note">
        <p><strong>注意：</strong> 当使用 <code>returnType: "HTML"</code> 渲染支付表单时，应确保服务端返回的HTML内容已经过安全过滤，防止XSS攻击。</p>
    </div>

    <h2>11. 总结</h2>
    <p>fd-websiteV4 项目的支付功能实现了以下主要特性：</p>
    <ul>
        <li>完整的支付流程，包括订单创建、支付和状态查询</li>
        <li>多种支付方式支持，包括第三方支付、余额支付和积分支付</li>
        <li>针对已有订单的支付和新订单创建两种场景提供支持</li>
        <li>组件化设计，易于复用和扩展</li>
        <li>响应式界面，提供良好的跨设备用户体验</li>
        <li>安全的支付处理流程</li>
    </ul>
    <p>通过这些实现，项目能够为用户提供流畅、安全的线上支付体验，满足各种支付场景的需求。</p>

    <div class="note">
        <p><strong>重要提示：</strong> 余额支付和积分支付功能需要用户登录才能使用。在后端实现中，这些支付方式只对已登录用户可见。这是出于安全考虑，确保只有验证过身份的用户才能使用账户余额或积分进行支付。</p>
    </div>

    <h2>12. 后续开发计划</h2>
    <p>以下是支付功能的后续开发计划：</p>
    
    <h3>12.1 积分管理功能</h3>
    <ul>
        <li>实现积分增加与消费的规则系统</li>
        <li>支持自动积分奖励（如登录奖励、内容发布奖励等）</li>
        <li>添加积分兑换功能，允许用户用积分兑换商品或服务</li>
        <li>实现积分过期策略</li>
    </ul>
    
    <h3>12.2 个人中心扩展</h3>
    <ul>
        <li>在前端个人中心显示钱包余额和积分统计</li>
        <li>提供订单历史和详情查看</li>
        <li>实现消费记录和积分变动明细查询</li>
        <li>支持余额充值和提现操作</li>
    </ul>
    
    <h3>12.3 支付系统优化</h3>
    <ul>
        <li>添加支付超时自动取消机制</li>
        <li>实现订单退款流程</li>
        <li>提供订单导出功能</li>
        <li>添加更多第三方支付方式</li>
    </ul>

    <footer style="margin-top: 50px; border-top: 1px solid #f8f9fa; padding-top: 20px; color: #666;">
        <p>© 2024 FD-Frontend. 文档最后更新时间: 2024-06-09</p>
    </footer>
</body>
</html> 