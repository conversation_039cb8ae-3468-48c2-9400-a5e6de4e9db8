<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>V1.7.2 - 页面功能完整实现 - 前端开发文档</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #2c3e50;
      border-bottom: 2px solid #eaecef;
      padding-bottom: 10px;
    }
    h2 {
      color: #1a73e8;
      margin-top: 30px;
      padding-bottom: 6px;
      border-bottom: 1px solid #eaecef;
    }
    h3 {
      color: #34495e;
      margin-top: 24px;
    }
    code {
      font-family: Consolas, Monaco, 'Andale Mono', monospace;
      background-color: #f5f7f9;
      border: 1px solid #e4e6e8;
      border-radius: 3px;
      padding: 2px 5px;
    }
    pre {
      background-color: #f5f7f9;
      border: 1px solid #e4e6e8;
      border-radius: 5px;
      padding: 15px;
      overflow-x: auto;
    }
    pre code {
      border: none;
      padding: 0;
      background: none;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 20px 0;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px 12px;
      text-align: left;
    }
    th {
      background-color: #f5f7f9;
    }
    .note {
      background-color: #fff8e1;
      border-left: 4px solid #ffc107;
      padding: 10px 15px;
      margin: 20px 0;
    }
    .warning {
      background-color: #fef2f2;
      border-left: 4px solid #ef4444;
      padding: 10px 15px;
      margin: 20px 0;
    }
    .success {
      background-color: #f0fdf4;
      border-left: 4px solid #10b981;
      padding: 10px 15px;
      margin: 20px 0;
    }
    .image-container {
      text-align: center;
      margin: 20px 0;
    }
    .image-container img {
      max-width: 100%;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <h1>V1.7.2 - 页面功能完整实现</h1>
  
  <div class="success">
    <p><strong>版本: V1.7.2</strong></p>
    <p>发布日期: 2023-12-15</p>
    <p>此版本实现了基于GraphQL的页面功能，包括页面路由、数据查询和UI展示，以及URL构建器和中间件的集成。</p>
  </div>

  <h2>1. 功能概述</h2>
  
  <p>页面功能允许用户通过友好的URL（/page/[slug]）访问网站的静态页面内容，如"关于我们"、"联系方式"等。本次实现包含以下核心组件：</p>
  
  <ul>
    <li>页面路由系统: 使用Next.js的动态路由能力</li>
    <li>数据获取Hooks: 利用GraphQL API查询页面内容</li>
    <li>URL构建与中间件: 确保正确的URL结构与路由处理</li>
    <li>页面UI组件: 优雅展示页面内容</li>
  </ul>

  <h2>2. 技术实现</h2>

  <h3>2.1 页面路由实现</h3>
  <p>采用Next.js的动态路由功能，创建了<code>/page/[slug]</code>路径结构：</p>
  
  <pre><code>// fd-frontend/src/app/page/[slug]/page.tsx
'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import { usePage } from '../../../hooks/usePage';
import Loading from '../../../components/ui/Loading';
import { ErrorMessage } from '../../../components/ui/ErrorMessage';

export default function PageDetail() {
  // 获取URL参数中的slug
  const params = useParams();
  const slug = params?.slug as string;
  
  // 使用usePage hook获取页面数据
  const { page, loading, error } = usePage({ slug });

  // 处理加载状态
  if (loading) {
    return <Loading />;
  }

  // 处理错误状态
  if (error) {
    return (
      <ErrorMessage
        title="获取页面失败"
        message={error.message}
      />
    );
  }

  // 处理页面不存在的情况
  if (!page) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6 text-gray-800">页面不存在</h1>
        <p className="text-gray-600">
          抱歉，您请求的页面不存在或已被删除。
        </p>
      </div>
    );
  }

  // 渲染页面内容
  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <h1 className="text-3xl font-bold mb-6 text-gray-800">{page.title}</h1>

      {/* 特色图片 */}
      {page.featuredImage && (
        <div className="mb-8">
          <img
            src={page.featuredImage.node.sourceUrl}
            alt={page.featuredImage.node.altText || page.title}
            className="w-full h-auto rounded-lg shadow-md"
          />
        </div>
      )}

      {/* 页面内容 */}
      <div 
        className="prose max-w-none"
        dangerouslySetInnerHTML={{ __html: page.content || '' }}
      />
    </div>
  );
}</code></pre>

  <h3>2.2 URL构建器更新</h3>
  <p>修改URL构建器中的<code>buildPageUrl</code>函数，确保生成正确的页面URL：</p>
  
  <pre><code>/**
 * 构建简单页面URL
 * @param slug 页面slug
 * @returns 页面URL
 */
export function buildPageUrl(slug: string): string {
  const sanitizedSlug = slug ? encodeURIComponent(slug) : '';
  return `/page/${sanitizedSlug}`;
}</code></pre>

  <h3>2.3 中间件更新</h3>
  <p>在<code>middleware.ts</code>中添加对页面路由的处理逻辑：</p>
  
  <pre><code>// 情况9: 处理页面路由: /page/[slug]
if (pathSegments.length === 2 && pathSegments[0] === 'page') {
  const [_, slug] = pathSegments;
  // 这是有效的页面路由URL，无需重写
  return NextResponse.next();
}</code></pre>

  <h3>2.4 UI组件实现</h3>
  
  <h4>2.4.1 Loading组件</h4>
  <pre><code>// fd-frontend/src/components/ui/Loading.tsx
import React from 'react';

/**
 * 加载状态组件
 * 显示加载动画
 */
const Loading: React.FC = () => {
  return (
    <div className="flex justify-center items-center py-12">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-gray-100"></div>
      <span className="sr-only">加载中...</span>
    </div>
  );
};

export default Loading;</code></pre>

  <h4>2.4.2 ErrorMessage组件</h4>
  <pre><code>// fd-frontend/src/components/ui/ErrorMessage.tsx
import React from 'react';

interface ErrorMessageProps {
  title: string;
  message: string;
}

/**
 * 错误信息展示组件
 */
export const ErrorMessage: React.FC<ErrorMessageProps> = ({ title, message }) => {
  return (
    <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg p-4 my-4">
      <h2 className="text-lg font-semibold mb-2">{title}</h2>
      <p className="text-sm">{message}</p>
    </div>
  );
};</code></pre>

  <h2>3. 页面类型定义</h2>
  
  <p>页面内容类型定义：</p>
  
  <pre><code>/**
 * 页面类型定义
 */
export interface Page {
  id: string;
  title: string;
  content: string;
  date: string;
  modified: string;
  slug: string;
  uri: string;
  status: string;
  author: {
    node: {
      id: string;
      name: string;
      slug: string;
      avatar?: {
        url: string;
      };
    };
  };
  featuredImage: {
    node: {
      sourceUrl: string;
      altText?: string;
    };
  };
}</code></pre>

  <h2>4. 用户体验流程</h2>
  
  <div class="image-container">
    <img src="data:image/svg+xml;base64,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" alt="页面访问流程图">
  </div>

  <h2>5. 使用示例</h2>
  
  <h3>5.1 页面路由访问</h3>
  <p>用户可以通过以下格式的URL访问页面内容：</p>
  <pre><code>https://example.com/page/about-us
https://example.com/page/contact
https://example.com/page/privacy-policy</code></pre>

  <h3>5.2 在应用中链接到页面</h3>
  <pre><code>import Link from 'next/link';
import { buildPageUrl } from '../utils/url-builder';

// 在导航菜单中使用
function Navigation() {
  return (
    <nav>
      <Link href={buildPageUrl('about-us')}>关于我们</Link>
      <Link href={buildPageUrl('contact')}>联系我们</Link>
    </nav>
  );
}</code></pre>

  <h3>5.3 动态获取并展示页面列表</h3>
  <pre><code>import { usePages } from '../hooks/usePages';
import Link from 'next/link';
import { buildPageUrl } from '../utils/url-builder';

function PagesList() {
  const { pages, loading, error } = usePages({ first: 10 });
  
  if (loading) return <p>加载中...</p>;
  if (error) return <p>加载失败: {error.message}</p>;
  
  return (
    <div>
      <h2>网站页面</h2>
      <ul>
        {pages.map(page => (
          <li key={page.id}>
            <Link href={buildPageUrl(page.slug)}>{page.title}</Link>
          </li>
        ))}
      </ul>
    </div>
  );
}</code></pre>

  <h2>6. 主要变更摘要</h2>
  
  <table>
    <tr>
      <th>组件/文件</th>
      <th>变更内容</th>
    </tr>
    <tr>
      <td>路由</td>
      <td>新增 <code>/page/[slug]</code> 动态路由</td>
    </tr>
    <tr>
      <td>URL构建器</td>
      <td>更新 <code>buildPageUrl</code> 函数使用新的路径格式</td>
    </tr>
    <tr>
      <td>中间件</td>
      <td>添加页面路由处理逻辑</td>
    </tr>
    <tr>
      <td>UI组件</td>
      <td>实现 <code>Loading</code> 和 <code>ErrorMessage</code> 通用组件</td>
    </tr>
    <tr>
      <td>页面组件</td>
      <td>实现页面内容展示，支持标题、特色图片和HTML内容</td>
    </tr>
  </table>

  <h2>7. 后续优化方向</h2>
  
  <ul>
    <li>增加页面内容的SEO优化，包括动态Meta标签</li>
    <li>实现页面内容的缓存策略，减少重复请求</li>
    <li>添加页面过渡动画，提升用户体验</li>
    <li>支持页面内容的懒加载，优化长页面性能</li>
    <li>实现页面访问权限控制，支持私有页面</li>
  </ul>

  <div class="note">
    <p><strong>开发注意事项</strong></p>
    <p>在创建页面内容时，请确保提供有效的slug，这将直接影响URL的可读性和SEO效果。特色图片建议使用宽高比为16:9的图片以获得最佳显示效果。</p>
  </div>

  <div class="success">
    <p>此文档由前端开发团队维护，最后更新：2023-12-15</p>
  </div>
</body>
</html> 