<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V1.6.3 路由实现文档</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1100px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #1a73e8;
            border-bottom: 2px solid #1a73e8;
            padding-bottom: 10px;
        }
        h2 {
            color: #1a73e8;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        h3 {
            color: #333;
            margin-top: 25px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            font-family: 'Courier New', Courier, monospace;
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .version-badge {
            background-color: #1a73e8;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .file-path {
            font-family: 'Courier New', Courier, monospace;
            color: #555;
            margin-bottom: 5px;
        }
        .method {
            background-color: #f9f9f9;
            border-left: 4px solid #1a73e8;
            padding: 15px;
            margin: 15px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            text-align: left;
            padding: 12px;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f5f5f5;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .note {
            background-color: #fff8db;
            border-left: 4px solid #ffe066;
            padding: 15px;
            margin: 15px 0;
        }
        .important {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>V1.6.3 索引页、文章列表页和文章路由实现 <span class="version-badge">V1.6.3</span></h1>
    
    <p>本文档详细记录了V1.6.3版本中索引页、文章列表页和文章路由的实现细节，包括最近的路由修复和优化。</p>

    <h2>1. 路由结构概述</h2>
    
    <p>项目采用了以下路由结构：</p>

    <table>
        <tr>
            <th>页面类型</th>
            <th>路由格式</th>
            <th>示例</th>
        </tr>
        <tr>
            <td>分类索引页</td>
            <td>/category-index</td>
            <td>/category-index</td>
        </tr>
        <tr>
            <td>标签索引页</td>
            <td>/tag-index</td>
            <td>/tag-index</td>
        </tr>
        <tr>
            <td>分类文章列表页</td>
            <td>/[categoryPrefix]/[slug]</td>
            <td>/category/technology</td>
        </tr>
        <tr>
            <td>标签文章列表页</td>
            <td>/[tagPrefix]/[slug]</td>
            <td>/tag/wordpress</td>
        </tr>
        <tr>
            <td>文章详情页</td>
            <td>/[postPrefix]/[uuid]/[slug]</td>
            <td>/article/Abc12Def34/how-to-use-react</td>
        </tr>
    </table>

    <div class="note">
        <p><strong>注意：</strong> 前缀（categoryPrefix、tagPrefix、postPrefix）可通过GraphQL API的routePrefixes字段获取，并可在WordPress后台进行配置。</p>
    </div>

    <h2>2. 索引页实现</h2>

    <h3>2.1 分类索引页</h3>
    <p class="file-path">src/app/category-index/page.tsx</p>

    <p>分类索引页展示所有文章分类的列表，提供搜索过滤功能，按照分类名称排序。关键实现包括：</p>

    <pre><code>// 获取分类URL
const getCategoryUrl = (slug: string) => {
  return prefixes?.categoryPrefix 
    ? `/${prefixes.categoryPrefix}/${slug}`
    : `/category/${slug}`;
};</code></pre>

    <p>此函数根据系统设置的前缀来构建分类页面的URL。如果没有设置前缀，则使用默认的"/category/"前缀。</p>

    <div class="method">
        <h4>功能特点</h4>
        <ul>
            <li>搜索过滤功能：可通过名称快速查找分类</li>
            <li>按字母顺序排序：使用localeCompare支持中文排序</li>
            <li>显示文章数量：每个分类旁边显示包含的文章数量</li>
            <li>加载和错误状态处理：提供友好的加载和错误提示</li>
        </ul>
    </div>

    <h3>2.2 标签索引页</h3>
    <p class="file-path">src/app/tag-index/page.tsx</p>

    <p>标签索引页展示所有文章标签的列表，同样提供搜索过滤功能，按照标签名称排序。具有额外的标签大小和颜色设计，根据文章数量动态调整。关键实现包括：</p>

    <pre><code>// 获取标签URL
const getTagUrl = (slug: string) => {
  return prefixes?.tagPrefix 
    ? `/${prefixes.tagPrefix}/${slug}`
    : `/tag/${slug}`;
};</code></pre>

    <p>此函数根据系统设置的前缀来构建标签页面的URL。修复后使用了正确的tagPrefix属性。</p>

    <div class="method">
        <h4>功能特点</h4>
        <ul>
            <li>搜索过滤功能：可通过名称快速查找标签</li>
            <li>按字母顺序排序：使用localeCompare支持中文排序</li>
            <li>显示文章数量：每个标签旁边显示包含的文章数量</li>
            <li>标签大小和颜色：根据文章数量动态调整标签的显示样式</li>
            <li>加载和错误状态处理：提供友好的加载和错误提示</li>
        </ul>
    </div>

    <h2>3. 文章列表页实现</h2>

    <h3>3.1 分类文章列表页</h3>
    <p class="file-path">src/app/category/[slug]/page.tsx</p>

    <p>分类文章列表页展示特定分类下的所有文章，提供搜索过滤功能。关键实现包括：</p>

    <pre><code>// 获取文章URL
const getPostUrl = (postSlug: string, shortUuid: string) => {
  return prefixes?.postPrefix 
    ? `/${prefixes.postPrefix}/${shortUuid}/${postSlug}`
    : `/${shortUuid}/${postSlug}`;
};</code></pre>

    <p>此函数构建文章详情页的URL，使用了短UUID和slug组合，确保URL的唯一性和可读性。</p>

    <div class="method">
        <h4>功能特点</h4>
        <ul>
            <li>搜索过滤功能：可通过标题快速查找文章</li>
            <li>分类信息显示：显示分类名称、描述和文章数量</li>
            <li>文章摘要展示：显示每篇文章的标题、日期和摘要内容</li>
            <li>分页加载：支持加载更多文章</li>
            <li>加载和错误状态处理：提供友好的加载和错误提示</li>
        </ul>
    </div>

    <h3>3.2 标签文章列表页</h3>
    <p class="file-path">src/app/tag/[slug]/page.tsx</p>

    <p>标签文章列表页展示特定标签下的所有文章，提供搜索过滤功能。关键实现包括：</p>

    <pre><code>// 获取文章URL
const getPostUrl = (postSlug: string, shortUuid: string) => {
  return prefixes?.postPrefix 
    ? `/${prefixes.postPrefix}/${shortUuid}/${postSlug}`
    : `/${shortUuid}/${postSlug}`;
};</code></pre>

    <p>与分类文章列表页类似，此函数构建文章详情页的URL，使用了短UUID和slug组合。</p>

    <div class="method">
        <h4>功能特点</h4>
        <ul>
            <li>搜索过滤功能：可通过标题快速查找文章</li>
            <li>标签信息显示：显示标签名称和文章数量</li>
            <li>文章摘要展示：显示每篇文章的标题、日期和摘要内容</li>
            <li>分页加载：支持加载更多文章</li>
            <li>加载和错误状态处理：提供友好的加载和错误提示</li>
        </ul>
    </div>

    <h2>4. 文章路由实现</h2>

    <h3>4.1 文章详情页</h3>
    <p class="file-path">src/app/[prefix]/[uuid]/[slug]/page.tsx</p>

    <p>文章详情页使用动态路由，基于前缀、UUID和slug组合来唯一标识一篇文章。这种设计既保证了URL的唯一性，又提供了良好的可读性和SEO优化。</p>

    <pre><code>export default async function ArticlePage({ params }: { 
  params: { prefix: string; uuid: string; slug: string } 
}) {
  const { prefix, uuid, slug } = params;
  // ...
}</code></pre>

    <p>此组件从URL参数中提取前缀、UUID和slug，然后使用UUID查询文章详情。</p>

    <div class="important">
        <p><strong>重要：</strong> 文章路由格式为 /{prefix}/{uuid}/{slug}，其中：</p>
        <ul>
            <li><strong>prefix</strong>: 可配置的文章前缀，默认为"article"</li>
            <li><strong>uuid</strong>: 文章的短UUID，10位唯一标识符</li>
            <li><strong>slug</strong>: 文章的SEO友好URL，基于标题生成</li>
        </ul>
    </div>

    <h3>4.2 GraphQL查询</h3>
    <p>文章详情通过GraphQL API获取，主要使用以下两个查询：</p>

    <pre><code>// 通过UUID获取文章
const POST_BY_UUID_QUERY = gql`
  query GetPostByUuid($uuid: String!) {
    postByUuid(uuid: $uuid) {
      id
      databaseId
      title
      slug
      date
      excerpt
      content
      shortUuid
      // ... 其他字段
    }
  }
`;

// 备用查询：通过Meta查询UUID
const POST_BY_UUID_FALLBACK_QUERY = gql`
  query GetPostByUuidFallback($uuid: String!) {
    posts(where: {metaQuery: {metaArray: [{key: "_fd_short_uuid", value: $uuid, compareOperator: EQUAL_TO}]}}, first: 1) {
      nodes {
        // ... 文章字段
      }
    }
  }
`;</code></pre>

    <h2>5. 路由前缀配置</h2>

    <h3>5.1 前缀Hook</h3>
    <p class="file-path">src/hooks/useRoutePrefixes.ts</p>

    <p>项目使用自定义Hook获取路由前缀配置：</p>

    <pre><code>export const useRoutePrefixes = (): UseRoutePrefixesResult => {
  const { data, loading, error } = useQuery(GET_ROUTE_PREFIXES);
  
  // 默认值
  const defaultPrefixes: RoutePrefixes = {
    categoryPrefix: null,
    tagPrefix: 'topics',
    postPrefix: 'articles'
  };
  
  return {
    prefixes: data?.routePrefixes || defaultPrefixes,
    loading,
    error
  };
};</code></pre>

    <h3>5.2 GraphQL查询</h3>
    <p>路由前缀通过以下GraphQL查询获取：</p>

    <pre><code>// 获取路由前缀设置
export const GET_ROUTE_PREFIXES = gql`
  query GetRoutePrefixes {
    routePrefixes {
      categoryPrefix
      tagPrefix
      postPrefix
    }
  }
`;</code></pre>

    <h2>6. 最近修复和优化</h2>

    <h3>6.1 修复的问题</h3>
    <ul>
        <li>修复了Tag类型导入路径，从@/types改为@/types/post</li>
        <li>在Post接口定义中添加了shortUuid字段</li>
        <li>在GraphQL片段中添加了shortUuid字段</li>
        <li>修复了标签索引页中getTagUrl函数，正确使用tagPrefix</li>
        <li>修复了分类索引页中getCategoryUrl函数，正确使用categoryPrefix</li>
        <li>修复了分类和标签文章列表页中getPostUrl函数，加入shortUuid参数</li>
    </ul>

    <h3>6.2 改进的功能</h3>
    <ul>
        <li>统一了URL路由格式，确保一致的用户体验</li>
        <li>添加了前缀配置支持，增强了系统的可配置性</li>
        <li>优化了文章URL结构，同时兼顾SEO友好性和唯一性</li>
        <li>增强了类型安全，减少运行时错误</li>
    </ul>

    <div class="note">
        <p><strong>开发建议：</strong> 在开发新功能时，请确保遵循现有的路由结构和命名约定，使用useRoutePrefixes hook获取系统配置的前缀。所有文章链接都应使用shortUuid。</p>
    </div>

    <h2>7. 总结</h2>
    <p>V1.6.3版本完善了索引页、文章列表页和文章路由的实现，解决了多个路由相关的问题，提高了系统的稳定性和一致性。通过配置化的路由前缀和统一的URL格式，提供了更好的用户体验和更灵活的系统配置能力。</p>

    <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #eee; color: #777; font-size: 14px;">
        <p>文档版本：V1.6.3</p>
        <p>最后更新：2023年04月25日</p>
    </footer>
</body>
</html> 