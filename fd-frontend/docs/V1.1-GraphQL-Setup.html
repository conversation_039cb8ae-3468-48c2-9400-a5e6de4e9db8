<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V1.1 - GraphQL 环境设置</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #1a1a1a;
        }
        h1 {
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        h2 {
            margin-top: 30px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 5px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            font-family: '<PERSON><PERSON>', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
        }
        .note {
            background-color: #e6f7ff;
            border-left: 4px solid #1890ff;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .step {
            border-left: 3px solid #52c41a;
            padding-left: 15px;
            margin: 20px 0;
        }
        .warning {
            background-color: #fff7e6;
            border-left: 4px solid #fa8c16;
            padding: 10px 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>V1.1 - GraphQL 环境设置</h1>
    
    <h2>概述</h2>
    <p>本文档记录了将 Future Decade 项目从 REST API 迁移到 GraphQL 的第一阶段：环境设置。这一阶段主要包括前端项目的 GraphQL 客户端配置和基础组件的创建。</p>
    
    <div class="note">
        <p><strong>前提条件：</strong>WordPress 已安装并配置好必要的 GraphQL 插件，包括 WPGraphQL 和 WPGraphQL JWT Authentication。</p>
    </div>
    
    <h2>迁移策略</h2>
    <p>从 REST API 迁移到 GraphQL 的总体策略分为以下几个阶段：</p>
    <ol>
        <li><strong>环境设置</strong> - 安装必要的依赖和配置 GraphQL 客户端（本文档）</li>
        <li><strong>后端准备</strong> - 扩展 GraphQL Schema，映射自定义字段</li>
        <li><strong>前端改造</strong> - 将 REST API 调用替换为 GraphQL 查询</li>
        <li><strong>认证系统迁移</strong> - 实现基于 GraphQL 的认证机制</li>
        <li><strong>缓存与状态管理</strong> - 利用 Apollo Client 的缓存和状态管理功能</li>
        <li><strong>完成迁移</strong> - 清理旧代码，完全转换到 GraphQL</li>
    </ol>

    <h2>阶段一：环境设置详细步骤</h2>

    <div class="step">
        <h3>1. 安装 GraphQL 客户端库</h3>
        <p>在 fd-frontend 项目中安装必要的 GraphQL 客户端库：</p>
        <pre><code>// 修改 package.json 添加依赖
{
  "dependencies": {
    // 原有依赖...
    "@apollo/client": "^3.8.8",
    "graphql": "^16.8.1"
  }
}</code></pre>
    </div>

    <div class="step">
        <h3>2. 创建 Apollo Client 配置</h3>
        <p>创建 GraphQL 客户端配置文件，设置与 WordPress GraphQL 端点的连接：</p>
        <pre><code>// src/lib/apollo-client.ts
import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';

// GraphQL API的HTTP链接
const httpLink = createHttpLink({
  uri: process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost/graphql',
});

// 认证链接，用于添加JWT令牌到请求头
const authLink = setContext((_, { headers }) => {
  // 从localStorage获取令牌（仅在客户端）
  let token = null;
  if (typeof window !== 'undefined') {
    token = localStorage.getItem('authToken');
  }
  
  // 返回带有headers的上下文
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : "",
    }
  };
});

// 创建Apollo Client实例
const client = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'cache-and-network',
    },
  },
});

export default client;</code></pre>
    </div>

    <div class="step">
        <h3>3. 创建 Apollo Provider 组件</h3>
        <p>创建 Provider 组件，使 Apollo Client 在整个应用中可用：</p>
        <pre><code>// src/lib/apollo-provider.tsx
'use client';

import { ApolloProvider } from '@apollo/client';
import client from './apollo-client';

export default function ApolloClientProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return <ApolloProvider client={client}>{children}</ApolloProvider>;
}</code></pre>
    </div>

    <div class="step">
        <h3>4. 更新应用布局</h3>
        <p>修改布局文件，将 Apollo Provider 添加到应用中：</p>
        <pre><code>// src/app/layout.tsx
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import ApolloClientProvider from '../lib/apollo-provider'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Next.js应用',
  description: 'Next.js应用示例',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh">
      <body className={inter.className}>
        <ApolloClientProvider>
          {children}
        </ApolloClientProvider>
      </body>
    </html>
  )
}</code></pre>
    </div>

    <div class="step">
        <h3>5. 配置环境变量</h3>
        <p>更新环境变量配置，移除不再需要的 REST API 端点，设置 GraphQL 端点：</p>
        <pre><code>// .env.local
# 应用配置
NODE_ENV=production

# WordPress GraphQL API端点
NEXT_PUBLIC_WORDPRESS_API_URL=https://admin.futuredecade.com/graphql</code></pre>
    </div>

    <div class="step">
        <h3>6. 创建 GraphQL 查询</h3>
        <p>为常用数据创建 GraphQL 查询定义：</p>
        <pre><code>// src/lib/graphql/queries.ts
import { gql } from '@apollo/client';

// 获取最新文章列表
export const GET_LATEST_POSTS = gql`
  query GetLatestPosts($first: Int = 10) {
    posts(first: $first, where: { orderby: { field: DATE, order: DESC } }) {
      nodes {
        id
        title
        date
        slug
        excerpt
        featuredImage {
          node {
            sourceUrl
            altText
          }
        }
        categories {
          nodes {
            id
            name
            slug
          }
        }
      }
    }
  }
`;

// 获取单个文章详情
export const GET_POST_BY_SLUG = gql`
  query GetPostBySlug($slug: ID!) {
    post(id: $slug, idType: SLUG) {
      id
      title
      date
      content
      excerpt
      featuredImage {
        node {
          sourceUrl
          altText
        }
      }
      categories {
        nodes {
          id
          name
          slug
        }
      }
    }
  }
`;

// 获取所有分类
export const GET_CATEGORIES = gql`
  query GetCategories {
    categories {
      nodes {
        id
        name
        slug
        count
      }
    }
  }
`;</code></pre>
    </div>

    <div class="step">
        <h3>7. 创建示例组件</h3>
        <p>创建使用 GraphQL 查询的示例组件：</p>
        <pre><code>// src/components/PostList.tsx
'use client';

import { useQuery } from '@apollo/client';
import { GET_LATEST_POSTS } from '../lib/graphql/queries';
import Link from 'next/link';

export default function PostList() {
  const { loading, error, data } = useQuery(GET_LATEST_POSTS);

  if (loading) return <p>加载中...</p>;
  if (error) return <p>加载出错: {error.message}</p>;

  const posts = data?.posts?.nodes || [];

  return (
    <div className="post-list">
      <h2 className="text-2xl font-bold mb-4">最新文章</h2>
      {posts.length === 0 ? (
        <p>没有找到文章</p>
      ) : (
        <ul className="space-y-4">
          {posts.map((post: any) => (
            <li key={post.id} className="border-b pb-4">
              <Link href={`/post/${post.slug}`}>
                <h3 className="text-xl font-semibold hover:text-blue-600">
                  {post.title}
                </h3>
              </Link>
              <div className="text-sm text-gray-500 mt-1">
                {new Date(post.date).toLocaleDateString('zh-CN')}
              </div>
              <div 
                className="mt-2"
                dangerouslySetInnerHTML={{ __html: post.excerpt || '' }}
              />
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}</code></pre>
    </div>

    <div class="step">
        <h3>8. 更新首页</h3>
        <p>更新首页，使用新的 PostList 组件：</p>
        <pre><code>// src/app/page.tsx
import PostList from '../components/PostList';

export default function Home() {
  return (
    <main className="flex min-h-screen flex-col items-center p-8">
      <div className="w-full max-w-4xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Future Decade</h1>
          <p className="text-gray-600 mt-2">基于GraphQL的WordPress前端</p>
        </div>
        
        <PostList />
      </div>
    </main>
  )
}</code></pre>
    </div>

    <div class="step">
        <h3>9. 更新 Docker 配置</h3>
        <p>修正 Dockerfile 中的 ENV 指令格式：</p>
        <pre><code>// Dockerfile
# 原来的格式
ENV NODE_ENV production
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# 更新后的格式
ENV NODE_ENV=production
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"</code></pre>
    </div>

    <h2>TypeScript 类型问题</h2>
    <div class="warning">
        <p>在设置过程中，出现了一些 TypeScript 类型错误，主要是由于缺少类型定义引起的：</p>
        <ul>
            <li>找不到模块"@apollo/client"或其相应的类型声明</li>
            <li>找不到模块"next/link"或其相应的类型声明</li>
            <li>JSX 元素隐式具有类型 "any"</li>
            <li>找不到命名空间"React"</li>
        </ul>
        <p>这些错误不影响功能实现，但在实际开发中应该通过安装相应的类型定义包来解决。</p>
    </div>

    <h2>下一步工作</h2>
    <p>完成阶段一的环境设置后，下一步工作包括：</p>
    <ol>
        <li>在 WordPress 主题中扩展 GraphQL Schema，添加自定义字段和类型</li>
        <li>实现 GraphQL 认证机制，替换原有的基于 REST API 的认证</li>
        <li>逐步将各个组件从 REST API 迁移到 GraphQL 查询</li>
        <li>配置 Apollo Client 的缓存策略</li>
        <li>清理不再需要的 REST API 相关代码</li>
    </ol>

    <h2>总结</h2>
    <p>阶段一环境设置已经完成，主要成果包括：</p>
    <ul>
        <li>安装并配置了 Apollo Client</li>
        <li>创建了基本的 GraphQL 查询</li>
        <li>实现了一个示例组件，验证 GraphQL 查询功能</li>
        <li>更新了环境变量配置，指向 GraphQL 端点</li>
        <li>调整了项目布局，集成了 Apollo Provider</li>
    </ul>

    <div class="note">
        <p><strong>注意：</strong>当前实现仅是基础框架，后续需要根据具体业务需求进一步完善 GraphQL 查询和组件。</p>
    </div>
</body>
</html> 