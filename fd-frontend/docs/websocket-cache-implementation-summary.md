# WebSocket推送和缓存失效完整实现总结

## 概述

本文档总结了分类索引页、标签索引页、自定义类型条目索引页，以及分类页、标签页、自定义类型条目页的WebSocket推送和缓存失效、软刷新的完整实现。

## 实现架构

### 1. 后端推送 (fd-pusher.php)

当WordPress后台更新分类法条目时，会触发以下流程：

1. **WebSocket推送**：向fd-websocket服务器发送事件
2. **缓存失效**：向Next.js前端发送revalidate请求

#### 分类更新推送

```php
function fd_pusher_notify_on_category_update( $term_id, $tt_id, $taxonomy ) {
    // WebSocket推送
    $event_data = [
        'event' => 'category:updated',
        'target' => 'public',
        'data' => [
            'termId' => $term_id,
            'taxonomy' => $taxonomy,
            'slug' => $term->slug,
            'name' => $term->name,
        ],
    ];
    
    // 缓存失效
    // 1. 分类索引页
    wp_remote_post('http://frontend:3000/api/revalidate', [
        'body' => 'category-index-page'
    ]);
    
    // 2a. 单个分类页数据缓存
    wp_remote_post('http://frontend:3000/api/revalidate', [
        'body' => 'category:' . $term->slug
    ]);

    // 2b. 单个分类页HTML缓存
    wp_remote_post('http://frontend:3000/api/revalidate-path', [
        'body' => '/category/' . $term->slug
    ]);
}
```

#### 标签更新推送

```php
function fd_pusher_notify_on_tag_update( $term_id, $tt_id, $taxonomy ) {
    // WebSocket推送
    $event_data = [
        'event' => 'tag:updated',
        'target' => 'public',
        'data' => [
            'termId' => $term_id,
            'taxonomy' => $taxonomy,
            'slug' => $term->slug,
            'name' => $term->name,
        ],
    ];
    
    // 缓存失效
    // 1. 标签索引页
    wp_remote_post('http://frontend:3000/api/revalidate', [
        'body' => 'tag-index-page'
    ]);
    
    // 2a. 单个标签页数据缓存
    wp_remote_post('http://frontend:3000/api/revalidate', [
        'body' => 'tag:' . $term->slug
    ]);

    // 2b. 单个标签页HTML缓存
    wp_remote_post('http://frontend:3000/api/revalidate-path', [
        'body' => '/tag/' . $term->slug
    ]);
}
```

#### 自定义分类法更新推送

```php
function fd_pusher_notify_on_taxonomy_update( $term_id, $tt_id, $taxonomy ) {
    // WebSocket推送
    $event_data = [
        'event' => 'taxonomy:updated',
        'target' => 'public',
        'data' => [
            'termId' => $term_id,
            'taxonomy' => $taxonomy,
            'slug' => $term->slug,
            'name' => $term->name,
        ],
    ];
    
    // 缓存失效
    // 1. 分类法索引页
    wp_remote_post('http://frontend:3000/api/revalidate', [
        'body' => 'taxonomy:' . $taxonomy
    ]);
    
    // 2a. 单个条目页数据缓存
    wp_remote_post('http://frontend:3000/api/revalidate', [
        'body' => 'taxonomy-term:' . $taxonomy . ':' . $term->slug
    ]);

    // 2b. 单个条目页HTML缓存
    wp_remote_post('http://frontend:3000/api/revalidate-path', [
        'body' => '/taxonomy/' . $taxonomy . '/' . $term->slug
    ]);
}
```

### 2. 前端缓存标签

#### 索引页缓存标签

- **分类索引页** (`/category-index/page.tsx`): `['category-index-page']`
- **标签索引页** (`/tag-index/page.tsx`): `['tag-index-page']`
- **自定义类型索引页** (`/taxonomy/[taxonomy]/page.tsx`): `['taxonomy:${taxonomy}']`

#### 单页缓存标签

- **分类页** (`/category/[slug]/page.tsx`): `['category:${slug}']`
- **标签页** (`/tag/[slug]/page.tsx`): `['tag:${slug}']`
- **自定义类型条目页** (`/taxonomy/[taxonomy]/[slug]/page.tsx`): `['taxonomy-term:${taxonomy}:${slug}']`

### 3. WebSocket事件处理 (WebSocketEventHub.tsx)

前端通过WebSocketEventHub监听后端推送的事件，并根据当前路径决定是否刷新页面：

#### 分类事件处理

```typescript
const categoryHandler = (payload: CategoryUpdatedPayload) => {
  // 分类索引页刷新
  const categoryIndexPaths = ['/intelligence', '/category-index'];
  if (categoryIndexPaths.some(path => pathname === path)) {
    router.refresh();
    return;
  }
  
  // 单个分类页刷新
  if (payload.slug && pathname.includes(`/category/${payload.slug}`)) {
    router.refresh();
    return;
  }
};
```

#### 标签事件处理

```typescript
const tagHandler = (payload: TagUpdatedPayload) => {
  // 标签索引页刷新
  const tagIndexPaths = ['/innovation', '/tag-index', '/topics'];
  if (tagIndexPaths.some(path => pathname === path)) {
    router.refresh();
    return;
  }
  
  // 单个标签页刷新
  if (payload.slug) {
    const tagPaths = [`/tag/${payload.slug}`, `/topics/${payload.slug}`];
    if (tagPaths.some(path => pathname.includes(path))) {
      router.refresh();
      return;
    }
  }
};
```

#### 自定义分类法事件处理

```typescript
const taxonomyHandler = (payload: TaxonomyUpdatedPayload) => {
  // 分类法索引页刷新
  if (payload.taxonomy) {
    const taxonomyIndexPath = `/taxonomy/${payload.taxonomy}`;
    const directIndexPath = `/${payload.taxonomy}`;
    if (pathname === taxonomyIndexPath || pathname === directIndexPath) {
      router.refresh();
      return;
    }
  }
  
  // 单个条目页刷新
  if (payload.taxonomy && payload.slug) {
    const taxonomyTermPath = `/taxonomy/${payload.taxonomy}/${payload.slug}`;
    const directTermPath = `/${payload.taxonomy}/${payload.slug}`;
    if (pathname === taxonomyTermPath || pathname === directTermPath) {
      router.refresh();
      return;
    }
  }
};
```

## 完整事件流程

1. **管理员更新**：在WordPress后台更新分类/标签/自定义分类法条目
2. **后端推送**：fd-pusher插件捕获更新事件，发送WebSocket推送和缓存失效请求
3. **缓存清除**：Next.js接收revalidate请求，清除相关页面的缓存
4. **前端刷新**：浏览器接收WebSocket事件，WebSocketEventHub根据当前路径决定是否刷新
5. **数据更新**：页面刷新后重新获取数据，由于缓存已清除，获取到最新数据

## 支持的页面类型

### 索引页
- ✅ 分类索引页 (`/category-index`)
- ✅ 标签索引页 (`/tag-index`)  
- ✅ 自定义类型条目索引页 (`/taxonomy/[taxonomy]`)

### 单页
- ✅ 分类页 (`/category/[slug]`)
- ✅ 标签页 (`/tag/[slug]`)
- ✅ 自定义类型条目页 (`/taxonomy/[taxonomy]/[slug]`)

## 技术特点

1. **实时性**：通过WebSocket实现实时推送
2. **精确性**：通过缓存标签实现精确的缓存失效
3. **高效性**：只刷新相关页面，避免全局刷新
4. **可靠性**：结合ISR缓存和按需重新验证，确保数据一致性

## 重要修复说明

### 🚨 根据 `taxonomy-index-websocket-cache-guide.html` 文档修复的问题：

1. **缓存标签格式统一**：
   - 索引页：`taxonomy:${taxonomy}` ✅
   - 条目页：`taxonomy-term:${taxonomy}:${slug}` ✅（避免与索引页冲突）

2. **双重失效机制**：
   - 数据缓存失效：通过 `/api/revalidate` + 缓存标签
   - HTML缓存失效：通过 `/api/revalidate-path` + 路径

3. **避免的常见错误**：
   - ❌ 缓存标签不匹配导致失效无效
   - ❌ 只失效数据缓存，忽略HTML缓存
   - ❌ 请求格式错误（JSON vs 纯文本）
   - ❌ 路径与中间件重写冲突

### 📋 完整的失效请求示例：

```php
// 分类更新时
wp_remote_post('http://frontend:3000/api/revalidate', [
    'headers' => ['x-revalidate-secret' => $secret],
    'body' => 'category:tesla' // 纯文本，不是JSON
]);

wp_remote_post('http://frontend:3000/api/revalidate-path', [
    'headers' => ['x-revalidate-secret' => $secret],
    'body' => '/category/tesla' // 路径失效
]);
```
