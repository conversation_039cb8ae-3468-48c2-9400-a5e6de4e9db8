# Future Decade GraphQL Hooks 目录

这份文档提供了一个完整的GraphQL Hooks目录，帮助你快速找到所需的功能。

## 内容相关Hooks

### 文章Hooks

| Hook名称 | 文件路径 | 用途 |
|----------|----------|------|
| `usePosts` | hooks/usePosts.ts | 获取文章列表，支持分页和按分类/标签筛选 |
| `usePost` | hooks/usePost.ts | 获取单篇文章详情，支持通过ID或slug获取 |

### 分类Hooks

| Hook名称 | 文件路径 | 用途 |
|----------|----------|------|
| `useCategories` | hooks/useCategory.ts | 获取所有分类列表 |
| `useCategory` | hooks/useCategory.ts | 获取单个分类详情，通过slug获取 |

### 标签Hooks

| Hook名称 | 文件路径 | 用途 |
|----------|----------|------|
| `useTags` | hooks/useTags.ts | 获取所有标签列表 |
| `useTag` | hooks/useTags.ts | 获取单个标签详情，通过slug获取 |

### 自定义内容类型Hooks

| Hook名称 | 文件路径 | 用途 |
|----------|----------|------|
| `useCustomPosts` | hooks/useCustomPost.ts | 获取自定义内容类型列表，支持分页 |
| `useCustomPost` | hooks/useCustomPost.ts | 获取单个自定义内容详情，支持通过ID或slug获取 |

### 分类法Hooks

| Hook名称 | 文件路径 | 用途 |
|----------|----------|------|
| `useTaxonomies` | hooks/useTaxonomy.ts | 获取所有分类法列表 |
| `useTaxonomy` | hooks/useTaxonomy.ts | 获取单个分类法详情，通过name获取 |
| `useTaxonomyTerms` | hooks/useTaxonomyTerms.ts | 获取指定分类法下的所有条目列表 |
| `useTaxonomyTerm` | hooks/useTaxonomyTerm.ts | 获取单个分类法条目详情及相关文章，支持通过slug获取 |

### 菜单Hooks

| Hook名称 | 文件路径 | 用途 |
|----------|----------|------|
| `useMenu` | hooks/useMenu.ts | 获取指定名称的菜单（如"顶部菜单"、"底部菜单"） |

## 用户相关Hooks

| Hook名称 | 文件路径 | 用途 |
|----------|----------|------|
| `useUser` | hooks/useUser.ts | 获取单个用户信息，通过ID获取 |
| `useUsers` | hooks/useUser.ts | 获取用户列表 |

## 设置相关Hooks

| Hook名称 | 文件路径 | 用途 |
|----------|----------|------|
| `useAllSettings` | hooks/useSettings.ts | 获取所有WordPress设置 |
| `useGeneralSettings` | hooks/useSettings.ts | 获取WordPress常规设置 |
| `useReadingSettings` | hooks/useSettings.ts | 获取WordPress阅读设置 |
| `useDiscussionSettings` | hooks/useSettings.ts | 获取WordPress讨论设置 |
| `useWritingSettings` | hooks/useSettings.ts | 获取WordPress写作设置 |

## 评论相关Hooks

| Hook名称 | 文件路径 | 用途 |
|----------|----------|------|
| `useComments` | hooks/useComment.ts | 获取文章评论列表 |
| `useComment` | hooks/useComment.ts | 获取单个评论详情 |
| `useCreateComment` | hooks/useComment.ts | 创建评论，返回创建函数和状态 |
| `useUpdateComment` | hooks/useComment.ts | 更新评论，返回更新函数和状态 |
| `useDeleteComment` | hooks/useComment.ts | 删除评论，返回删除函数和状态 |

## 搜索相关Hooks

| Hook名称 | 文件路径 | 用途 |
|----------|----------|------|
| `useSearchPosts` | hooks/useSearch.ts | 搜索文章 |
| `useSearchContent` | hooks/useSearch.ts | 高级搜索，支持多种内容类型，返回标准化结果 |

## 路由与URL相关Hooks

| Hook名称 | 文件路径 | 用途 |
|----------|----------|------|
| `useRoutePrefixes` | hooks/useRoutePrefixes.ts | 获取站点路由前缀配置，如分类、标签页面的URL前缀 |
| `useSlugMappingTable` | hooks/useSlugMappingTable.ts | 获取slug映射表，用于处理自定义URL结构和重定向 |

## 统一API导出

所有的hooks都通过`api/useGraphQL.ts`文件统一导出，可以这样导入：

```tsx
import { usePost, useCategories, useMenu } from '@/api/useGraphQL';
```

## 示例页面

- `src/app/hooks-test/page.tsx`: 简单的hooks测试页面，展示部分hooks的基本用法
- `src/app/hooks-showcase/page.tsx`: 完整的hooks展示页面，分类展示所有hooks的使用方法和效果

## 错误处理

所有hooks都返回统一的error对象，可以这样处理错误：

```tsx
const { data, loading, error } = usePost({ slug: 'hello-world' });

if (error) {
  if (error.networkError) {
    console.error('网络错误:', error.networkError);
  } else if (error.graphQLErrors) {
    error.graphQLErrors.forEach(err => {
      console.error('GraphQL错误:', err.message);
    });
  }
}
```

## 缓存处理

所有hooks都利用Apollo Client的缓存机制。可以使用`refetch`函数强制重新获取数据：

```tsx
const { data, refetch } = usePosts();

// 强制刷新数据
const refreshData = () => {
  refetch();
};
```

## 最近更新 (v1.5.1)

### useMenu Hook 改进
- 从使用菜单位置改为使用菜单名称
- 使用示例：

```tsx
// 获取顶部菜单
const { menuItems } = useMenu('顶部菜单');

// 获取底部菜单
const { menuItems } = useMenu('底部菜单');
```

### 新增分类法相关Hooks
- 增加了对自定义分类法和术语的完整支持
- 使用示例：

```tsx
// 获取产品分类列表
const { terms } = useTaxonomyTerms('product_category');

// 获取特定产品分类详情及其文章
const { term, posts } = useTaxonomyTerm('product_category', 'electronics');
```

### 路由与URL相关Hooks
- 增加了路由前缀和slug映射相关功能
- 使用示例：

```tsx
// 获取路由前缀配置
const { categoryPrefix, tagPrefix } = useRoutePrefixes();

// 从slug映射表中查找相关信息
const { mappings } = useSlugMappingTable();
const pageInfo = mappings.find(item => item.slug === 'about-us');
``` 