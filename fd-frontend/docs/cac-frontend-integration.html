<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <title>内容访问控制 – 前端集成实现文档</title>
  <style>
    body{font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Helvetica,Arial,sans-serif;line-height:1.6;color:#333;max-width:1024px;margin:20px auto;padding:0 20px}
    h1,h2,h3{color:#222;border-bottom:1px solid #eaecef;padding-bottom:.3em}
    h1{font-size:1.9em}h2{font-size:1.5em}h3{font-size:1.25em}
    pre{background:#f6f8fa;padding:16px;border-radius:6px;overflow:auto}
    code{font-family:"SFMono-Regular",Consolas,"Liberation Mono",Menlo,Courier,monospace;background:#f6f8fa;padding:.2em .4em;border-radius:3px}
    .file{color:#0366d6;font-weight:600}
  </style>
</head>
<body>
<h1>内容访问控制 – 前端集成实现文档</h1>
<p>本文档记录了在 <code>fd-frontend</code> 中，为了支持 <strong>会员内容访问控制</strong>（CAC）而完成的<strong>跨子域 JWT Cookie + SSR 注入 Authorization</strong> 方案的完整落地过程。</p>

<h2>1. 背景与目标</h2>
<ul>
  <li>后端（<span class="file">fd-member</span> 插件）已实现基于 JWT 的内容访问控制。</li>
  <li>问题：用户即使登录也会在首屏看到付费墙，因为 <em>Server Components</em> 请求未携带 Token。</li>
  <li>目标：让浏览器与 SSR/RSC 两侧的 GraphQL 请求都自动携带有效 JWT，从而首屏就展示完整内容。</li>
</ul>

<h2>2. 方案概览</h2>
<ol>
  <li>登录成功后，将 <code>authToken</code> / <code>refreshToken</code> 同时写入
    <ul>
      <li>本地 <code>localStorage</code>（供浏览器端 Apollo <code>authLink</code> 使用）</li>
      <li>跨子域 <strong>HTTP-Only Cookie</strong>（<code>domain=.futuredecade.com</code>，供 SSR）</li>
    </ul>
  </li>
  <li>Next.js 服务端通过 <code>next/headers</code> 读取 Cookie，创建按请求实例化的 ApolloClient，并把 <code>Authorization: Bearer xxx</code> 注入。</li>
  <li>刷新 Token 时同步更新 Cookie；登出时清除 Cookie。</li>
</ol>

<h2>3. 关键代码变更</h2>
<h3>3.1 API 路由 – 写入 / 清除 Cookie</h3>
<p class="file">src/app/api/auth/set-token/route.ts</p>
<pre><code>// 接收 { authToken, refreshToken } 并写入 HTTP-Only Cookie
res.cookies.set('fd_auth_token', authToken, {
  httpOnly: true,
  secure: true,
  sameSite: 'lax',
  domain: '.futuredecade.com',
  path: '/',
  maxAge: 60 * 60 * 24 * 7,
});
</code></pre>
<p class="file">src/app/api/auth/logout/route.ts</p>
<pre><code>// 清除 Cookie
res.cookies.set('fd_auth_token', '', { maxAge: 0, ...commonOpts });
</code></pre>

<h3>3.2 服务端 Apollo Client</h3>
<p class="file">src/lib/server-apollo-client.ts</p>
<pre><code>const token = cookies().get('fd_auth_token')?.value;
const httpLink = createHttpLink({
  uri: process.env.NEXT_PUBLIC_WORDPRESS_API_URL,
  headers: token ? { Authorization: `Bearer ${token}` } : {},
  fetch, // Node18 全局 fetch
});
</code></pre>

<h3>3.3 数据层修改</h3>
<ul>
  <li><strong>getPostByUuid()</strong>（<span class="file">src/lib/api.ts</span>）在服务端使用 <code>getServerApollo()</code>。</li>
  <li><strong>getCustomPostByUuid()</strong> 在服务端调用 <code>fetch</code> 时自动附加 <code>Authorization</code> 头。</li>
</ul>

<h3>3.4 认证流程同步 Cookie</h3>
<p class="file">src/contexts/AuthContext.tsx</p>
<pre><code>// 登录成功后
await fetch('/api/auth/set-token', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ authToken, refreshToken }),
});

// 登出时
await fetch('/api/auth/logout', { method: 'POST' });
</code></pre>
<p class="file">src/lib/apollo-client.ts</p>
<pre><code>// 刷新令牌成功后，同步 Cookie
await fetch('/api/auth/set-token', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ authToken: newToken, refreshToken }),
});
</code></pre>

<h3>3.5 付费墙自动恢复完整内容</h3>
<p class="file">src/components/post/PostContentSmart.tsx</p>
<pre><code>'use client';
// 根据登录状态和初始内容是否包含 paywall，再次查询完整正文
</code></pre>

<h3>3.6 跳转回原文章 – callbackUrl</h3>
<p>为了让登录 / 注册 / 升级会员完成后自动回到之前的受限文章页面，前端做了两处处理：</p>
<ol>
  <li><strong>PostContentSmart.tsx</strong> 渲染付费墙 HTML 后，遍历其中所有按钮链接，为其追加<br><code>?callbackUrl=&lt;当前页面 URL&gt;</code></li>
  <li><strong>ProtectedRoute.tsx</strong> + 表单组件：<br>
    • 登录 / 注册表单提交成功后读取 <code>callbackUrl</code>，<code>router.push(callbackUrl)</code><br>
    • 若用户已登录却误入登录页，<code>ProtectedRoute</code> 也会立即检查参数并重定向。</li>
</ol>
<p>这样不论用户在哪种入口进入认证流程，最终都会跳回原本的文章详情页，体验更加流畅。</p>

<h2>4. 安全与跨域配置</h2>
<ul>
  <li>Cookie 使用 <code>Secure + HttpOnly + SameSite=Lax + domain=.futuredecade.com</code>，仅同站请求携带，杜绝 XSS、CSRF。</li>
  <li>后端 Nginx / Apache 需允许：
    <pre><code>Access-Control-Allow-Origin: https://www.futuredecade.com
Access-Control-Allow-Credentials: true
Access-Control-Allow-Headers: Authorization,Content-Type
</code></pre>
  </li>
</ul>

<h2>5. 构建与运行</h2>
<ol>
  <li><code>npm install</code>（确保 <code>@apollo/client</code> 等依赖已存在）</li>
  <li><code>npm run build</code> → 通过</li>
  <li>启动后：
    <ul>
      <li>游客访问受限文章 → 显示预览 + 付费墙</li>
      <li>登录会员访问 → 首屏即显示完整正文</li>
    </ul>
  </li>
</ol>

<h2>6. 后续优化方向</h2>
<ul>
  <li>去除 <code>// @ts-nocheck</code>，补充类型声明。</li>
  <li>封装统一 <code>requestGraphQL()</code>，避免多处重复注入 Header。</li>
  <li>Edge Middleware 方案：在边缘层自动同步 Cookie → Header，加速并简化代码。</li>
</ul>

</body>
</html> 