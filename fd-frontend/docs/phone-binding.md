# 手机号绑定功能前端实现文档

本文档详细描述了网站手机号绑定、换绑和解绑功能的前端实现。

## 目录

1. [功能概述](#功能概述)
2. [组件结构](#组件结构)
3. [数据流程](#数据流程)
4. [API接口](#API接口)
5. [页面和组件详解](#页面和组件详解)
6. [使用示例](#使用示例)

## 功能概述

手机号绑定功能允许用户：
- 绑定手机号到自己的账户
- 更换已绑定的手机号
- 解绑当前绑定的手机号

整个流程基于token验证机制，提供了更高的安全性。

## 组件结构

```
fd-frontend/
├── src/
│   ├── components/
│   │   └── profile/
│   │       ├── BindPhoneSection.tsx   # 手机绑定组件
│   │       └── ProfileInfo.tsx        # 个人资料页面显示组件
│   ├── app/
│   │   └── auth/
│   │       └── profile/
│   │           └── bind-phone/
│   │               └── page.tsx       # 手机绑定页面
│   ├── contexts/
│   │   └── AuthContext.tsx           # 认证上下文，包含绑定相关API
│   ├── lib/
│   │   └── graphql/
│   │       ├── mutations.ts          # GraphQL绑定相关mutation
│   │       └── fragments.ts          # 包含用户片段(含phone字段)
│   └── types/
│       └── user-types.ts             # 包含BindPhoneInput类型定义
```

## 数据流程

手机绑定流程使用基于token的三步验证：

1. **发送验证码**:
   - 用户输入手机号
   - 系统向该手机发送验证码
   
2. **验证验证码并获取token**:
   - 用户输入收到的验证码
   - 系统验证该验证码，并生成一个一次性token
   
3. **使用token绑定手机号**:
   - 系统使用token完成手机号绑定
   - 此token只能使用一次且有时间限制

## API接口

### 绑定手机号

```typescript
// 在AuthContext中
interface BindPhoneInput {
  phone: string;
  token: string;
  nationCode?: string;
}

bindPhone(input: BindPhoneInput): Promise<PhoneAuthResponse>
```

### 获取绑定token

```typescript
// 在AuthContext中
verifyPhoneCodeForBindingAndGetToken(
  phone: string, 
  code: string, 
  nationCode?: string
): Promise<{ success: boolean; message: string; token?: string }>
```

### 解绑手机号

```typescript
// 在AuthContext中
unbindPhone(): Promise<PhoneAuthResponse>
```

## 页面和组件详解

### 1. BindPhoneSection组件

手机绑定流程的主要组件，负责：
- 显示手机号输入表单
- 发送验证码
- 验证验证码并获取token
- 使用token绑定手机号

组件使用了步骤状态机设计，有三个主要状态：
- `initial`: 初始状态，输入手机号并发送验证码
- `verify`: 验证状态，输入验证码获取token
- `bind`: 绑定状态，使用token完成绑定

### 2. ProfileInfo组件

个人资料页面上展示手机绑定状态的组件，提供：
- 显示当前绑定的手机号
- 提供"绑定手机号"按钮（未绑定时）
- 提供"更换手机号"和"解绑手机号"按钮（已绑定时）

解绑手机号功能直接在组件内通过按钮点击事件实现，使用`useAuth`钩子中的`unbindPhone`方法。

### 3. 绑定手机页面

专门用于绑定手机的页面，包含：
- 标题和描述
- BindPhoneSection组件
- 返回个人资料的链接

## 使用示例

### 绑定手机号

```tsx
import { useAuth } from '../../hooks/useAuth';

// 在组件内
const { verifyPhoneCodeForBindingAndGetToken, bindPhone } = useAuth();

// 步骤1：验证验证码并获取token
const result = await verifyPhoneCodeForBindingAndGetToken(phone, code, nationCode);
if (result.success && result.token) {
  setToken(result.token);
}

// 步骤2：使用token绑定手机
const bindResult = await bindPhone({
  phone,
  token,
  nationCode
});
if (bindResult.success) {
  // 绑定成功
}
```

### 解绑手机号

```tsx
import { useAuth } from '../../hooks/useAuth';

// 在组件内
const { unbindPhone } = useAuth();

// 处理解绑按钮点击
const handleUnbindPhone = async () => {
  if (confirm('确定要解绑手机号吗？')) {
    const result = await unbindPhone();
    if (result.success) {
      // 解绑成功
    }
  }
};
```

## 注意事项

1. **安全性考虑**:
   - 使用一次性token增强安全性
   - token有时间限制，过期失效
   - 绑定操作需要先验证手机所有权
   
2. **用户体验**:
   - 验证码倒计时功能防止频繁请求
   - 绑定成功后自动刷新用户数据
   - 操作结果有明确的成功/失败提示

3. **数据刷新**:
   - 绑定或解绑成功后，立即调用`fetchCurrentUser()`刷新用户数据
   - 确保UI立即反映最新状态 