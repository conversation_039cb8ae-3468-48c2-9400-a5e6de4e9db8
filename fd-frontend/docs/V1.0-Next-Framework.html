<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V1.0 - Next.js 应用框架</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #1a1a1a;
        }
        h1 {
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        h2 {
            margin-top: 30px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 5px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
        }
        .note {
            background-color: #e6f7ff;
            border-left: 4px solid #1890ff;
            padding: 10px 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>V1.0 - Next.js 应用框架</h1>
    
    <h2>项目概述</h2>
    <p>本文档描述了 Future Decade 项目的前端基础框架。该项目采用 Next.js 构建，作为 WordPress Headless CMS 的前端展示层。</p>
    
    <h2>技术栈</h2>
    <table>
        <tr>
            <th>技术</th>
            <th>版本</th>
            <th>用途</th>
        </tr>
        <tr>
            <td>Next.js</td>
            <td>14.0.4</td>
            <td>React 框架，提供服务端渲染、静态生成等功能</td>
        </tr>
        <tr>
            <td>React</td>
            <td>18.x</td>
            <td>用户界面库</td>
        </tr>
        <tr>
            <td>TypeScript</td>
            <td>5.x</td>
            <td>静态类型检查</td>
        </tr>
        <tr>
            <td>Tailwind CSS</td>
            <td>3.3.0</td>
            <td>原子化 CSS 框架</td>
        </tr>
    </table>

    <h2>目录结构</h2>
    <pre><code>fd-frontend/
├── public/          # 静态资源
├── src/             # 源代码
│   ├── app/         # App Router 页面和布局
│   ├── components/  # React 组件
│   └── lib/         # 工具库和服务
├── .env.local       # 环境变量
├── next.config.js   # Next.js 配置
├── package.json     # 依赖管理
├── tsconfig.json    # TypeScript 配置
├── tailwind.config.js # Tailwind 配置
└── postcss.config.js  # PostCSS 配置</code></pre>

    <h2>关键文件说明</h2>
    
    <h3>应用入口</h3>
    <p>项目使用 Next.js 14 的 App Router 架构，主要入口文件包括：</p>
    <ul>
        <li><code>src/app/layout.tsx</code> - 全局布局组件</li>
        <li><code>src/app/page.tsx</code> - 首页组件</li>
    </ul>

    <pre><code>// src/app/layout.tsx
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Next.js应用',
  description: 'Next.js应用示例',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    &lt;html lang="zh"&gt;
      &lt;body className={inter.className}&gt;{children}&lt;/body&gt;
    &lt;/html&gt;
  )
}</code></pre>

    <pre><code>// src/app/page.tsx
export default function Home() {
  return (
    &lt;main className="flex min-h-screen flex-col items-center justify-between p-24"&gt;
      &lt;div className="z-10 w-full max-w-5xl items-center justify-between font-mono text-sm lg:flex"&gt;
        &lt;p className="fixed left-0 top-0 flex w-full justify-center border-b border-gray-300 bg-gradient-to-b from-zinc-200 pb-6 pt-8 backdrop-blur-2xl dark:border-neutral-800 dark:bg-zinc-800/30 dark:from-inherit lg:static lg:w-auto lg:rounded-xl lg:border lg:bg-gray-200 lg:p-4 lg:dark:bg-zinc-800/30"&gt;
          Next.js 应用已成功运行！
        &lt;/p&gt;
      &lt;/div&gt;

      &lt;div className="flex flex-col items-center justify-center"&gt;
        &lt;h1 className="text-4xl font-bold mb-8"&gt;欢迎使用 Next.js！&lt;/h1&gt;
        &lt;p className="text-xl"&gt;这是一个基于 App Router 架构的测试页面&lt;/p&gt;
      &lt;/div&gt;
    &lt;/main&gt;
  )
}</code></pre>

    <h3>配置文件</h3>
    
    <h4>环境变量</h4>
    <p>项目使用 <code>.env.local</code> 来管理环境变量：</p>
    <pre><code># 应用配置
NODE_ENV=production

# WordPress基础设置
NEXT_PUBLIC_API_URL=https://admin.futuredecade.com/wp-json</code></pre>

    <h4>Next.js 配置</h4>
    <pre><code>// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
}

module.exports = nextConfig</code></pre>

    <h2>Docker 支持</h2>
    <p>项目包含 Docker 相关配置，支持容器化部署：</p>
    <ul>
        <li>Dockerfile - 定义构建和运行容器的指令</li>
        <li>docker-compose.yml - 定义开发环境的服务</li>
        <li>.dockerignore - 排除不需要包含在 Docker 镜像中的文件</li>
    </ul>

    <pre><code>// Dockerfile 关键配置
# 多阶段构建
FROM node:18-alpine AS base

# ...构建过程...

ENV NODE_ENV=production
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

CMD ["node", "server.js"]</code></pre>

    <h2>依赖管理</h2>
    <p>项目的主要依赖包括：</p>
    <pre><code>{
  "dependencies": {
    "next": "14.0.4",
    "react": "^18",
    "react-dom": "^18",
    "axios": "^1.6.2",
    "swr": "^2.2.4"
  },
  "devDependencies": {
    "typescript": "^5",
    "@types/node": "^20",
    "@types/react": "^18",
    "@types/react-dom": "^18",
    "autoprefixer": "^10.0.1",
    "postcss": "^8",
    "tailwindcss": "^3.3.0",
    "eslint": "^8",
    "eslint-config-next": "14.0.4"
  }
}</code></pre>

    <h2>总结</h2>
    <p>该项目基于 Next.js 14 的 App Router 架构建立，为迁移到 GraphQL 做好了准备。基础框架提供了：</p>
    <ul>
        <li>现代的 React 开发体验</li>
        <li>TypeScript 的类型安全</li>
        <li>Tailwind CSS 的样式解决方案</li>
        <li>Docker 容器化支持</li>
        <li>ESLint 代码质量检查</li>
    </ul>

    <div class="note">
        <p><strong>注意：</strong>该文档描述了项目的初始状态，作为迁移到 GraphQL 之前的基准。</p>
    </div>
</body>
</html> 