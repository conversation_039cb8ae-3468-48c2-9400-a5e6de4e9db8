<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>V2.0.0 核心UI组件实现文档</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      border-bottom: 2px solid #f0f0f0;
      padding-bottom: 10px;
      margin-top: 0;
    }
    h2 {
      margin-top: 30px;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 5px;
    }
    h3 {
      margin-top: 25px;
    }
    code {
      font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
      background-color: #f6f8fa;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 0.9em;
    }
    pre {
      background-color: #f6f8fa;
      padding: 12px;
      border-radius: 5px;
      overflow-x: auto;
    }
    pre code {
      background-color: transparent;
      padding: 0;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 20px 0;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px 12px;
      text-align: left;
    }
    th {
      background-color: #f6f8fa;
    }
    .note {
      background-color: #e7f3fe;
      border-left: 4px solid #2196F3;
      padding: 12px;
      margin: 15px 0;
    }
    .warning {
      background-color: #fff8e1;
      border-left: 4px solid #ffc107;
      padding: 12px;
      margin: 15px 0;
    }
  </style>
</head>
<body>
  <h1>Future Decade 核心UI组件实现文档</h1>
  <p>本文档详细描述了 Future Decade 网站前端的核心UI组件实现，这些组件是在 V2.0.0 版本中添加的，旨在提供一致的用户体验和简化开发流程。</p>

  <h2>目录</h2>
  <ol>
    <li><a href="#overview">概述</a></li>
    <li><a href="#modal">模态框组件</a></li>
    <li><a href="#dialog">对话框组件</a></li>
    <li><a href="#toast">Toast通知组件</a></li>
    <li><a href="#dropdown">下拉菜单组件</a></li>
    <li><a href="#providers">上下文提供程序</a></li>
    <li><a href="#usage">使用示例</a></li>
    <li><a href="#future">未来计划</a></li>
  </ol>

  <h2 id="overview">1. 概述</h2>
  <p>在本次更新中，我们实现了以下核心UI组件，这些组件旨在解决用户交互中的常见需求：</p>
  <ul>
    <li><strong>Modal (模态框)</strong>：用于显示需要用户关注的内容，支持自定义头部、内容和底部</li>
    <li><strong>Dialog (对话框)</strong>：基于模态框的特化组件，用于确认/取消操作</li>
    <li><strong>Toast (通知提示)</strong>：用于显示临时消息通知</li>
    <li><strong>Dropdown (下拉菜单)</strong>：提供多种选项的下拉列表</li>
  </ul>
  <p>所有组件都支持亮色/暗色主题，并且与现有的VITheme主题系统集成，确保UI的一致性。</p>

  <h2 id="modal">2. 模态框组件</h2>
  <h3>2.1 特性</h3>
  <ul>
    <li>支持不同尺寸（sm, md, lg, xl, full）</li>
    <li>自定义头部、内容和底部</li>
    <li>点击外部区域关闭</li>
    <li>按ESC键关闭</li>
    <li>关闭按钮可定制</li>
    <li>使用React Portal渲染，避免z-index问题</li>
    <li>自动禁用背景滚动</li>
  </ul>

  <h3>2.2 使用方式</h3>
  <pre><code>import Modal from '@/components/ui/Modal';

// 直接使用
const MyComponent = () => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    &lt;>
      &lt;button onClick={() => setIsOpen(true)}>打开模态框&lt;/button>
      
      &lt;Modal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        title="模态框标题"
        footer={&lt;button onClick={() => setIsOpen(false)}>关闭&lt;/button>}
        size="md"
      >
        这里是模态框内容
      &lt;/Modal>
    &lt;/>
  );
};

// 或者使用上下文
import { useModal } from '@/providers/UIProvider';

const MyComponent = () => {
  const { openModal } = useModal();
  
  const handleOpenModal = () => {
    openModal({
      title: '模态框标题',
      content: &lt;div>这里是模态框内容&lt;/div>,
      footer: &lt;button>关闭&lt;/button>
    });
  };
  
  return &lt;button onClick={handleOpenModal}>打开模态框&lt;/button>;
};</code></pre>

  <h2 id="dialog">3. 对话框组件</h2>
  <h3>3.1 特性</h3>
  <ul>
    <li>基于模态框构建的特化组件</li>
    <li>内置确认/取消按钮</li>
    <li>支持不同类型（info, success, warning, danger）</li>
    <li>每种类型都有对应的图标和颜色</li>
    <li>支持异步操作（加载状态）</li>
    <li>支持Promise式API</li>
  </ul>

  <h3>3.2 使用方式</h3>
  <pre><code>import Dialog from '@/components/ui/Dialog';

// 直接使用
const MyComponent = () => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    &lt;>
      &lt;button onClick={() => setIsOpen(true)}>打开对话框&lt;/button>
      
      &lt;Dialog
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        title="确认操作"
        message="您确定要执行此操作吗？"
        onConfirm={() => {
          console.log('用户确认');
          setIsOpen(false);
        }}
        type="warning"
      />
    &lt;/>
  );
};

// 或者使用上下文
import { useDialog } from '@/providers/UIProvider';

const MyComponent = () => {
  const { openDialog, confirmDialog } = useDialog();
  
  // 普通方式
  const handleOpenDialog = () => {
    openDialog({
      title: '确认操作',
      message: '您确定要执行此操作吗？',
      onConfirm: () => console.log('用户确认'),
      type: 'warning'
    });
  };
  
  // Promise方式
  const handleConfirm = async () => {
    const confirmed = await confirmDialog({
      title: '确认删除',
      message: '此操作不可撤销，确定继续吗？',
      type: 'danger'
    });
    
    if (confirmed) {
      console.log('用户确认删除');
    }
  };
  
  return (
    &lt;div>
      &lt;button onClick={handleOpenDialog}>打开对话框&lt;/button>
      &lt;button onClick={handleConfirm}>确认删除&lt;/button>
    &lt;/div>
  );
};</code></pre>

  <h2 id="toast">4. Toast通知组件</h2>
  <h3>4.1 特性</h3>
  <ul>
    <li>支持不同类型（info, success, warning, error）</li>
    <li>自动关闭功能（可配置持续时间）</li>
    <li>支持不同位置（top-right, top-left, bottom-right, bottom-left, top-center, bottom-center）</li>
    <li>支持标题和消息内容</li>
    <li>限制最大显示数量，避免堆积</li>
    <li>平滑的淡入淡出动画</li>
  </ul>

  <h3>4.2 使用方式</h3>
  <pre><code>import { useToast } from '@/providers/UIProvider';

const MyComponent = () => {
  const toast = useToast();
  
  const showSuccessToast = () => {
    toast.success('操作成功完成！');
  };
  
  const showCustomToast = () => {
    toast.addToast({
      type: 'info',
      title: '自定义标题',
      message: '这是一条自定义消息',
      duration: 8000, // 8秒后自动关闭
      position: 'top-center'
    });
  };
  
  return (
    &lt;div>
      &lt;button onClick={showSuccessToast}>显示成功提示&lt;/button>
      &lt;button onClick={showCustomToast}>显示自定义提示&lt;/button>
      &lt;button onClick={() => toast.error('操作失败！')}>显示错误提示&lt;/button>
      &lt;button onClick={() => toast.warning('请注意！')}>显示警告提示&lt;/button>
      &lt;button onClick={() => toast.info('这是一条提示信息')}>显示信息提示&lt;/button>
      &lt;button onClick={() => toast.clear()}>清除所有提示&lt;/button>
    &lt;/div>
  );
};</code></pre>

  <h2 id="dropdown">5. 下拉菜单组件</h2>
  <h3>5.1 特性</h3>
  <ul>
    <li>支持不同触发方式（点击、悬停）</li>
    <li>支持不同位置（bottom-left, bottom-right, top-left, top-right）</li>
    <li>支持图标</li>
    <li>支持禁用项</li>
    <li>支持分割线</li>
    <li>支持危险操作样式</li>
    <li>支持链接项</li>
    <li>点击外部自动关闭</li>
  </ul>

  <h3>5.2 使用方式</h3>
  <pre><code>import Dropdown from '@/components/ui/Dropdown';
import Button from '@/components/ui/Button';

const MyComponent = () => {
  const dropdownItems = [
    { id: 'edit', label: '编辑', onClick: () => console.log('编辑') },
    { id: 'duplicate', label: '复制', onClick: () => console.log('复制') },
    { id: 'archive', label: '归档', onClick: () => console.log('归档') },
    { id: 'divider', divider: true },
    { id: 'delete', label: '删除', danger: true, onClick: () => console.log('删除') },
  ];
  
  return (
    &lt;div>
      &lt;Dropdown
        trigger={&lt;Button>操作&lt;/Button>}
        items={dropdownItems}
        placement="bottom-left"
        triggerType="click"
      />
      
      &lt;Dropdown
        trigger={&lt;span>更多选项&lt;/span>}
        items={dropdownItems}
        placement="bottom-right"
        triggerType="hover"
      />
    &lt;/div>
  );
};</code></pre>

  <h2 id="providers">6. 上下文提供程序</h2>
  <p>为了方便使用这些UI组件，我们实现了以下上下文提供程序：</p>
  <ul>
    <li><strong>ModalContext</strong>：提供模态框相关功能</li>
    <li><strong>DialogContext</strong>：提供对话框相关功能</li>
    <li><strong>ToastContext</strong>：提供通知提示相关功能</li>
  </ul>
  <p>这些上下文提供程序被整合到了<code>UIProvider</code>中，应用程序只需要包装在这个提供程序中即可使用所有功能。</p>

  <pre><code>// 在应用程序入口点使用
import UIProvider from '@/providers/UIProvider';

function App({ children }) {
  return (
    &lt;UIProvider>
      {children}
    &lt;/UIProvider>
  );
}</code></pre>

  <h2 id="usage">7. 使用示例</h2>
  <p>我们创建了一个示例页面，展示了所有组件的用法：<code>/ui-demo</code>。您可以访问此页面查看所有组件的实际效果和交互方式。</p>

  <div class="note">
    <p><strong>提示：</strong> 访问 <a href="/ui-demo">/ui-demo</a> 页面可以查看所有UI组件的演示和用法示例。</p>
  </div>

  <h2 id="future">8. 未来计划</h2>
  <p>在接下来的迭代中，我们计划添加以下UI组件：</p>
  <ul>
    <li><strong>Tabs (标签页)</strong>：用于在不同内容视图之间切换</li>
    <li><strong>Accordion (手风琴)</strong>：用于折叠/展开内容</li>
    <li><strong>Tooltip (工具提示)</strong>：用于显示元素的额外信息</li>
    <li><strong>Form controls (表单控件)</strong>：更丰富的表单组件</li>
    <li><strong>Pagination (分页)</strong>：用于分页导航</li>
  </ul>

  <div class="warning">
    <p><strong>注意：</strong> 当前实现的组件可能存在一些TypeScript类型错误，这是由于项目配置问题导致的。这些错误不影响组件的实际功能，我们将在后续迭代中修复这些问题。</p>
  </div>

  <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #eee; color: #666;">
    <p>文档生成日期：2023年11月26日</p>
    <p>Future Decade 团队</p>
  </footer>
</body>
</html> 