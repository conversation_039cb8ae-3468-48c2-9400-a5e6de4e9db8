# Future Decade GraphQL 数据查询和缓存标准

本文档定义了项目中GraphQL查询、缓存和错误处理的标准实践。

## 1. 查询标准

### 1.1 基本查询结构

所有GraphQL查询应遵循以下结构：

```graphql
query GetEntityName($param1: Type!, $param2: Type) {
  entityName(where: { param1: $param1, param2: $param2 }) {
    ...EntityNameFragment
  }
}
```

### 1.2 片段使用

- 每个实体类型都应定义对应的片段
- 片段命名使用大写蛇形（如`POST_FRAGMENT`）
- 片段应包含该实体类型的所有基本信息

```graphql
fragment POST_FRAGMENT on Post {
  id
  title
  slug
  date
  excerpt
  content
  featuredImage {
    node {
      sourceUrl
      altText
    }
  }
}
```

### 1.3 命名规范

| 查询类型 | 命名规范 | 示例 |
|---------|----------|------|
| 获取列表 | GET_{ENTITY_NAME}S | GET_POSTS |
| 获取单个 | GET_{ENTITY_NAME}_BY_{FIELD} | GET_POST_BY_SLUG |
| 创建 | CREATE_{ENTITY_NAME} | CREATE_COMMENT |
| 更新 | UPDATE_{ENTITY_NAME} | UPDATE_USER |
| 删除 | DELETE_{ENTITY_NAME} | DELETE_COMMENT |

## 2. 缓存标准

### 2.1 Apollo缓存配置

当前项目使用Apollo Client的`InMemoryCache`进行缓存，主要配置：

```typescript
const cache = new InMemoryCache({
  typePolicies: {
    Query: {
      fields: {
        posts: {
          keyArgs: ['where'],
          merge(existing = { nodes: [] }, incoming) {
            return {
              ...incoming,
              nodes: [...existing.nodes, ...incoming.nodes],
            };
          },
        },
        // 其他实体的策略...
      },
    },
  },
});
```

### 2.2 缓存标识符

- 每个实体都应有唯一标识符
- 标识符构建格式：`${__typename}:${id}`
- 例如：`Post:1234`或`User:5678`

### 2.3 缓存策略

| 操作类型 | 推荐策略 | 场景 |
|---------|----------|------|
| 列表查询 | cache-and-network | 首次加载和下拉刷新 |
| 详情查询 | cache-first | 详情页面 |
| 搜索操作 | network-only | 搜索结果 |
| 变更操作 | no-cache | 提交表单 |

### 2.4 缓存更新

变更操作后的缓存更新应遵循以下方法之一：

1. **自动更新**：使用`refetchQueries`或`update`回调
2. **手动更新**：使用`cache.modify`或`cache.writeFragment`
3. **字段级无效化**：使用`cache.evict`

示例：
```typescript
const [addComment] = useMutation(ADD_COMMENT, {
  update(cache, { data: { createComment } }) {
    // 更新缓存
    cache.modify({
      id: cache.identify({ __typename: 'Post', id: postId }),
      fields: {
        comments(existingRefs = []) {
          const newCommentRef = cache.writeFragment({
            data: createComment.comment,
            fragment: COMMENT_FRAGMENT
          });
          return [...existingRefs, newCommentRef];
        }
      }
    });
  }
});
```

## 3. 错误处理标准

### 3.1 错误类型分类

| 错误类型 | 代码 | 处理方式 |
|---------|------|----------|
| 认证错误 | UNAUTHENTICATED | 清除令牌并重定向到登录页 |
| 授权错误 | FORBIDDEN | 显示无权限消息 |
| 请求格式错误 | BAD_USER_INPUT | 显示表单验证错误 |
| 资源不存在 | NOT_FOUND | 显示404页面或提示 |
| 服务器错误 | INTERNAL_SERVER_ERROR | 显示通用错误消息 |
| 网络错误 | NETWORK_ERROR | 显示网络连接错误 |

### 3.2 错误处理流程

1. **捕获错误**：在Apollo hooks中捕获错误
2. **错误分类**：使用`error-handler.ts`分类错误类型
3. **错误日志**：记录错误信息
4. **用户反馈**：显示友好的错误消息
5. **恢复策略**：提供重试或回退机制

### 3.3 错误展示

- 表单错误：在对应字段下方显示
- 页面级错误：在页面顶部显示通知条
- 应用级错误：使用全局通知组件显示

### 3.4 使用示例

```tsx
const { data, loading, error } = useQuery(GET_POST, {
  variables: { slug }
});

// 使用错误处理钩子
const errorMessages = useErrorHandler(error, (messages) => {
  // 显示错误通知
  showNotification({ type: 'error', messages });
});

// 在UI中显示错误
if (error) {
  return (
    <ErrorDisplay 
      messages={errorMessages}
      onRetry={() => refetch()}
    />
  );
}
```

## 4. 性能优化

### 4.1 查询优化

- 只请求需要的字段
- 使用分页和懒加载
- 合理设置fetchPolicy

### 4.2 缓存优化

- 适当配置缓存合并策略
- 定期使用`cache.gc()`回收缓存
- 更新实体时使用`cache.modify`而不是整个替换

### 4.3 批处理查询

对于需要同时获取多个资源的场景，使用批处理：

```typescript
// 使用 batchQueries 工具函数
const [postData, userInfo] = await batchQueries([
  { query: GET_POST, variables: { id: postId } },
  { query: GET_USER, variables: { id: userId } }
]);
```

## 5. 使用工具函数

项目中提供了多个工具函数，简化常见操作：

| 文件 | 功能 |
|------|------|
| apollo-client.ts | Apollo客户端配置，包括错误处理和日志 |
| error-handler.ts | 标准化错误处理功能 |
| cache-utils.ts | 缓存操作辅助函数 |
| hooks-utils.ts | 通用React hooks，包括错误处理 |
| query-utils.ts | 查询和变更的辅助函数 |

使用这些工具可以确保项目中的GraphQL操作遵循统一的最佳实践。 