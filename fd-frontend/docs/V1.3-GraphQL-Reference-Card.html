<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GraphQL快速参考卡片 - Future Decade</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.5;
      color: #000;
      margin: 0;
      padding: 0;
      font-size: 10pt;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 900px;
      margin: 0 auto;
      padding: 20px;
      background-color: white;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    h1 {
      font-size: 16pt;
      margin-bottom: 10px;
      border-bottom: 2px solid #0066cc;
      padding-bottom: 5px;
      color: #0066cc;
    }
    h2 {
      font-size: 12pt;
      margin-top: 20px;
      margin-bottom: 10px;
      color: #0066cc;
      border-bottom: 1px solid #ddd;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 10px 0;
      font-size: 9pt;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 4px 6px;
      text-align: left;
    }
    th {
      background-color: #f0f0f0;
      font-weight: bold;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .code {
      font-family: monospace;
      color: #333;
      background-color: #f5f5f5;
      padding: 1px 3px;
      border-radius: 3px;
      font-size: 9pt;
    }
    .card {
      break-inside: avoid;
      page-break-inside: avoid;
    }
    .print-btn {
      position: fixed;
      top: 10px;
      right: 10px;
      background-color: #0066cc;
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 3px;
      cursor: pointer;
    }
    @media print {
      body {
        background-color: white;
      }
      .container {
        box-shadow: none;
        padding: 0;
      }
      .print-btn {
        display: none;
      }
      @page {
        size: A4;
        margin: 1cm;
      }
    }
  </style>
</head>
<body>
  <button class="print-btn" onclick="window.print()">打印</button>
  <div class="container">
    <h1>Future Decade GraphQL快速参考卡片</h1>
    
    <div class="card">
      <h2>片段列表</h2>
      <table>
        <tr>
          <th width="30%">名称</th>
          <th>描述</th>
          <th width="25%">主要字段</th>
        </tr>
        <tr>
          <td><span class="code">POST_FRAGMENT</span></td>
          <td>文章基本信息</td>
          <td>id, title, slug, content, excerpt</td>
        </tr>
        <tr>
          <td><span class="code">POST_DETAIL_FRAGMENT</span></td>
          <td>文章详细信息</td>
          <td>扩展POST_FRAGMENT</td>
        </tr>
        <tr>
          <td><span class="code">CATEGORY_FRAGMENT</span></td>
          <td>分类信息</td>
          <td>id, name, slug, description</td>
        </tr>
        <tr>
          <td><span class="code">TAG_FRAGMENT</span></td>
          <td>标签信息</td>
          <td>id, name, slug, count</td>
        </tr>
        <tr>
          <td><span class="code">USER_FRAGMENT</span></td>
          <td>用户信息</td>
          <td>id, name, avatar, description</td>
        </tr>
        <tr>
          <td><span class="code">MEDIA_FRAGMENT</span></td>
          <td>媒体文件信息</td>
          <td>id, title, mediaItemUrl, srcSet</td>
        </tr>
        <tr>
          <td><span class="code">MENU_ITEM_FRAGMENT</span></td>
          <td>菜单项信息</td>
          <td>id, title, url, childItems</td>
        </tr>
        <tr>
          <td><span class="code">CUSTOM_POST_FRAGMENT</span></td>
          <td>自定义内容类型信息</td>
          <td>id, title, slug, content</td>
        </tr>
        <tr>
          <td><span class="code">COMMENT_FRAGMENT</span></td>
          <td>评论信息</td>
          <td>id, content, date, author</td>
        </tr>
      </table>
    </div>
    
    <div class="card">
      <h2>文章查询</h2>
      <table>
        <tr>
          <th width="30%">名称</th>
          <th>用途</th>
          <th width="25%">主要参数</th>
        </tr>
        <tr>
          <td><span class="code">GET_LATEST_POSTS</span></td>
          <td>获取最新文章列表</td>
          <td>$first: Int</td>
        </tr>
        <tr>
          <td><span class="code">GET_POST_BY_SLUG</span></td>
          <td>通过slug获取单个文章</td>
          <td>$slug: ID!</td>
        </tr>
        <tr>
          <td><span class="code">GET_POST_BY_ID</span></td>
          <td>通过ID获取单个文章</td>
          <td>$id: ID!</td>
        </tr>
        <tr>
          <td><span class="code">SEARCH_POSTS</span></td>
          <td>搜索文章</td>
          <td>$search: String!, $first: Int</td>
        </tr>
      </table>
    </div>
    
    <div class="card">
      <h2>页面查询</h2>
      <table>
        <tr>
          <th width="30%">名称</th>
          <th>用途</th>
          <th width="25%">主要参数</th>
        </tr>
        <tr>
          <td><span class="code">GET_PAGES</span></td>
          <td>获取所有页面</td>
          <td>$first: Int</td>
        </tr>
        <tr>
          <td><span class="code">GET_PAGE_BY_SLUG</span></td>
          <td>通过slug获取单个页面</td>
          <td>$slug: ID!</td>
        </tr>
      </table>
    </div>
    
    <div class="card">
      <h2>分类法查询</h2>
      <table>
        <tr>
          <th width="30%">名称</th>
          <th>用途</th>
          <th width="25%">主要参数</th>
        </tr>
        <tr>
          <td><span class="code">GET_CATEGORIES</span></td>
          <td>获取所有分类</td>
          <td>无</td>
        </tr>
        <tr>
          <td><span class="code">GET_TAGS</span></td>
          <td>获取所有标签</td>
          <td>无</td>
        </tr>
        <tr>
          <td><span class="code">GET_TAXONOMY_TERMS</span></td>
          <td>获取指定分类法下的所有项</td>
          <td>$taxonomy: TaxonomyEnum!</td>
        </tr>
        <tr>
          <td><span class="code">GET_POSTS_BY_TAXONOMY</span></td>
          <td>获取某个分类法项下的文章</td>
          <td>$taxonomyId: ID!, $taxonomyType: TaxonomyEnum!</td>
        </tr>
      </table>
    </div>
    
    <div class="card">
      <h2>自定义内容类型查询</h2>
      <table>
        <tr>
          <th width="30%">名称</th>
          <th>用途</th>
          <th width="25%">主要参数</th>
        </tr>
        <tr>
          <td><span class="code">GET_CUSTOM_POSTS</span></td>
          <td>获取指定自定义内容类型的列表</td>
          <td>$type: ContentTypeEnum!, $first: Int</td>
        </tr>
        <tr>
          <td><span class="code">GET_CUSTOM_POST_BY_SLUG</span></td>
          <td>通过slug获取自定义内容类型</td>
          <td>$type: ContentTypeEnum!, $slug: String!</td>
        </tr>
        <tr>
          <td><span class="code">GET_CUSTOM_POST_BY_ID</span></td>
          <td>通过ID获取自定义内容类型</td>
          <td>$type: ContentTypeEnum!, $id: ID!</td>
        </tr>
      </table>
    </div>
    
    <div class="card">
      <h2>用户查询</h2>
      <table>
        <tr>
          <th width="30%">名称</th>
          <th>用途</th>
          <th width="25%">主要参数</th>
        </tr>
        <tr>
          <td><span class="code">GET_USER</span></td>
          <td>获取用户信息</td>
          <td>$id: ID!</td>
        </tr>
        <tr>
          <td><span class="code">GET_USERS</span></td>
          <td>获取用户列表</td>
          <td>$first: Int</td>
        </tr>
      </table>
    </div>
    
    <div class="card">
      <h2>菜单查询</h2>
      <table>
        <tr>
          <th width="30%">名称</th>
          <th>用途</th>
          <th width="25%">主要参数</th>
        </tr>
        <tr>
          <td><span class="code">GET_MENU_BY_LOCATION</span></td>
          <td>获取指定位置的菜单</td>
          <td>$location: MenuLocationEnum!</td>
        </tr>
        <tr>
          <td><span class="code">GET_MENU_BY_NAME</span></td>
          <td>通过名称获取菜单</td>
          <td>$id: ID!</td>
        </tr>
      </table>
    </div>
    
    <div class="card">
      <h2>评论查询</h2>
      <table>
        <tr>
          <th width="30%">名称</th>
          <th>用途</th>
          <th width="25%">主要参数</th>
        </tr>
        <tr>
          <td><span class="code">GET_POST_COMMENTS</span></td>
          <td>获取文章的评论列表</td>
          <td>$postId: ID!, $first: Int</td>
        </tr>
        <tr>
          <td><span class="code">GET_COMMENT</span></td>
          <td>通过ID获取评论</td>
          <td>$id: ID!</td>
        </tr>
      </table>
    </div>
    
    <div class="card">
      <h2>常用参数类型</h2>
      <table>
        <tr>
          <th width="30%">类型</th>
          <th>说明</th>
          <th width="25%">示例</th>
        </tr>
        <tr>
          <td><span class="code">ContentTypeEnum</span></td>
          <td>内容类型枚举</td>
          <td>POST, PAGE, PRODUCT等</td>
        </tr>
        <tr>
          <td><span class="code">TaxonomyEnum</span></td>
          <td>分类法枚举</td>
          <td>CATEGORY, TAG等</td>
        </tr>
        <tr>
          <td><span class="code">MenuLocationEnum</span></td>
          <td>菜单位置枚举</td>
          <td>PRIMARY, FOOTER等</td>
        </tr>
        <tr>
          <td><span class="code">OrderEnum</span></td>
          <td>排序枚举</td>
          <td>ASC, DESC</td>
        </tr>
      </table>
    </div>
    
    <div class="card">
      <h2>使用示例</h2>
      <pre style="font-size:9pt;background:#f5f5f5;padding:10px;border-radius:3px;overflow-x:auto;">
import { gql, useQuery } from '@apollo/client';
import { POST_FRAGMENT } from '@/lib/graphql/fragments';

const GET_POST = gql`
  query GetPost($slug: ID!) {
    post(id: $slug, idType: SLUG) {
      ...PostFragment
    }
  }
  ${POST_FRAGMENT}
`;

// 在组件中使用
const { loading, error, data } = useQuery(GET_POST, {
  variables: { slug: 'hello-world' }
});
      </pre>
    </div>
    
  </div>
</body>
</html> 