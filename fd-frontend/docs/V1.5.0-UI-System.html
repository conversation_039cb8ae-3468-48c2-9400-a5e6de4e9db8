<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Future Decade UI系统 V1.5.0</title>
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4338ca;
            --gray-900: #1f2937;
            --gray-800: #374151;
            --gray-700: #4b5563;
            --gray-600: #6b7280;
            --gray-500: #9ca3af;
            --gray-400: #d1d5db;
            --gray-300: #e5e7eb;
            --gray-200: #f3f4f6;
            --gray-100: #f9fafb;
            --gray-50: #f9fafb;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: var(--gray-900);
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        h1, h2, h3, h4 {
            color: var(--gray-900);
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }

        h1 {
            font-size: 2.25rem;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
        }

        h2 {
            font-size: 1.75rem;
            border-bottom: 1px solid var(--gray-300);
            padding-bottom: 0.3rem;
        }

        h3 {
            font-size: 1.5rem;
        }

        h4 {
            font-size: 1.25rem;
        }

        p, ul, ol {
            margin-bottom: 1rem;
        }

        code {
            font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
            background-color: var(--gray-100);
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 0.9em;
        }

        pre {
            background-color: var(--gray-100);
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
            margin-bottom: 1.5rem;
        }

        pre code {
            background-color: transparent;
            padding: 0;
        }

        .toc {
            background-color: var(--gray-50);
            border: 1px solid var(--gray-300);
            border-radius: 5px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .toc h2 {
            margin-top: 0;
            border-bottom: 1px solid var(--gray-300);
            padding-bottom: 0.5rem;
        }

        .toc ul {
            list-style-type: none;
            padding-left: 1rem;
        }

        .toc ul ul {
            padding-left: 1.5rem;
        }

        .toc a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .toc a:hover {
            text-decoration: underline;
        }

        .example {
            background-color: var(--gray-50);
            border: 1px solid var(--gray-300);
            border-radius: 5px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .example h4 {
            margin-top: 0;
            margin-bottom: 0.5rem;
        }

        .component {
            border: 1px solid var(--gray-300);
            border-radius: 5px;
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .component-header {
            background-color: var(--gray-100);
            padding: 1rem;
            border-bottom: 1px solid var(--gray-300);
        }

        .component-body {
            padding: 1rem;
        }

        .color-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .color-item {
            border: 1px solid var(--gray-300);
            border-radius: 5px;
            overflow: hidden;
        }

        .color-preview {
            height: 100px;
        }

        .color-info {
            padding: 0.5rem;
            background-color: white;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1.5rem;
        }

        th, td {
            padding: 0.75rem;
            border: 1px solid var(--gray-300);
        }

        th {
            background-color: var(--gray-100);
            text-align: left;
        }

        .print-button {
            display: block;
            margin: 20px 0;
            padding: 10px 15px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
        }

        .print-button:hover {
            background-color: var(--primary-dark);
        }

        @media print {
            .print-button {
                display: none;
            }

            body {
                padding: 0;
                font-size: 11pt;
            }

            h1 {
                font-size: 18pt;
            }

            h2 {
                font-size: 16pt;
            }

            h3 {
                font-size: 14pt;
            }

            h4 {
                font-size: 12pt;
            }

            pre, code {
                font-size: 9pt;
            }
        }
    </style>
</head>
<body>
    <button class="print-button" onclick="window.print()">打印文档</button>
    
    <h1>Future Decade UI系统 V1.5.0</h1>
    <p>Future Decade 前端UI系统文档，提供布局框架、主题系统和组件的详细说明。</p>
    
    <div class="toc">
        <h2>目录</h2>
        <ul>
            <li><a href="#introduction">1. 简介</a></li>
            <li>
                <a href="#layout-framework">2. 布局框架</a>
                <ul>
                    <li><a href="#main-layout">2.1 主布局</a></li>
                    <li><a href="#header">2.2 页头</a></li>
                    <li><a href="#footer">2.3 页脚</a></li>
                    <li><a href="#responsive">2.4 响应式设计</a></li>
                </ul>
            </li>
            <li>
                <a href="#theme-system">3. 主题系统</a>
                <ul>
                    <li><a href="#design-tokens">3.1 设计令牌</a></li>
                    <li><a href="#color-system">3.2 颜色系统</a></li>
                    <li><a href="#typography">3.3 排版系统</a></li>
                    <li><a href="#spacing">3.4 间距系统</a></li>
                    <li><a href="#dark-mode">3.5 暗色模式</a></li>
                </ul>
            </li>
            <li>
                <a href="#components">4. 组件库</a>
                <ul>
                    <li><a href="#theme-toggle">4.1 主题切换按钮</a></li>
                    <li><a href="#future-components">4.2 即将开发的组件</a></li>
                </ul>
            </li>
            <li>
                <a href="#usage">5. 使用指南</a>
                <ul>
                    <li><a href="#layout-usage">5.1 布局使用</a></li>
                    <li><a href="#theme-usage">5.2 主题使用</a></li>
                </ul>
            </li>
            <li><a href="#roadmap">6. 开发路线</a></li>
        </ul>
    </div>

    <section id="introduction">
        <h2>1. 简介</h2>
        <p>Future Decade UI系统是一个基于React和Next.js的前端界面框架，提供了一套完整的布局、主题和组件系统，用于构建现代化、响应式的Web应用程序。本系统遵循组件化、主题化和响应式设计原则，确保应用程序在各种设备上都能提供一致的用户体验。</p>
        
        <p>主要特点：</p>
        <ul>
            <li>自适应的布局框架，支持桌面和移动设备</li>
            <li>完整的主题系统，包括亮色和暗色模式</li>
            <li>基于设计令牌的设计系统，确保视觉一致性</li>
            <li>组件库，提供常用UI组件</li>
            <li>与Tailwind CSS集成，支持快速开发</li>
        </ul>
    </section>

    <section id="layout-framework">
        <h2>2. 布局框架</h2>
        <p>布局框架是UI系统的基础，提供了页面的基本结构和组织方式。Future Decade采用了上中下三部分结构，由Header、Main和Footer组成，确保所有页面保持一致的布局。</p>

        <h3 id="main-layout">2.1 主布局</h3>
        <p>MainLayout组件是所有页面的外层容器，负责组织页面的基本结构。它包含以下特点：</p>
        <ul>
            <li>灵活的配置选项，可以控制是否显示页头和页脚</li>
            <li>自动适应不同屏幕尺寸</li>
            <li>支持亮色和暗色模式</li>
            <li>使用Flexbox布局，确保页面内容能够填充整个视口高度</li>
        </ul>

        <div class="example">
            <h4>使用示例：</h4>
            <pre><code>import MainLayout from '../components/layouts/MainLayout';

export default function HomePage() {
  return (
    &lt;MainLayout&gt;
      &lt;div&gt;页面内容&lt;/div&gt;
    &lt;/MainLayout&gt;
  );
}</code></pre>
        </div>

        <p>主布局的主要代码：</p>
        <pre><code>const MainLayout: React.FC&lt;MainLayoutProps&gt; = ({ 
  children, 
  showHeader = true, 
  showFooter = true 
}) => {
  return (
    &lt;div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-950"&gt;
      {showHeader && &lt;Header /&gt;}
      
      &lt;main className="flex-grow container mx-auto px-4 py-8"&gt;
        {children}
      &lt;/main&gt;
      
      {showFooter && &lt;Footer /&gt;}
    &lt;/div&gt;
  );
};</code></pre>

        <h3 id="header">2.2 页头</h3>
        <p>Header组件是页面的顶部区域，包含网站的标识、主导航和主题切换按钮。它的主要特点有：</p>
        <ul>
            <li>响应式设计，在移动设备上采用垂直堆叠布局</li>
            <li>固定在页面顶部（sticky定位）</li>
            <li>包含主导航链接和主题切换按钮</li>
            <li>当前页面链接高亮显示</li>
        </ul>

        <h3 id="footer">2.3 页脚</h3>
        <p>Footer组件是页面的底部区域，包含辅助信息、链接和版权信息。它的主要特点有：</p>
        <ul>
            <li>响应式设计，在不同设备上调整列数</li>
            <li>包含关于信息、快速链接和社交媒体链接</li>
            <li>自动显示当前年份的版权信息</li>
        </ul>

        <h3 id="responsive">2.4 响应式设计</h3>
        <p>布局框架采用了mobile-first的响应式设计方法，确保在各种设备上都能提供良好的用户体验。主要的响应式断点有：</p>
        <table>
            <thead>
                <tr>
                    <th>断点名称</th>
                    <th>宽度</th>
                    <th>描述</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>xs</td>
                    <td>0px</td>
                    <td>超小屏幕（移动设备）</td>
                </tr>
                <tr>
                    <td>sm</td>
                    <td>640px</td>
                    <td>小屏幕（平板设备）</td>
                </tr>
                <tr>
                    <td>md</td>
                    <td>768px</td>
                    <td>中等屏幕（平板设备横向）</td>
                </tr>
                <tr>
                    <td>lg</td>
                    <td>1024px</td>
                    <td>大屏幕（笔记本电脑）</td>
                </tr>
                <tr>
                    <td>xl</td>
                    <td>1280px</td>
                    <td>超大屏幕（桌面电脑）</td>
                </tr>
                <tr>
                    <td>2xl</td>
                    <td>1536px</td>
                    <td>特大屏幕（大显示器）</td>
                </tr>
            </tbody>
        </table>
    </section>

    <section id="theme-system">
        <h2>3. 主题系统</h2>
        <p>主题系统是UI系统的核心，它定义了应用程序的视觉风格和用户体验。Future Decade的主题系统基于设计令牌（Design Tokens），提供了一套完整的颜色、排版、间距和边框半径等设计元素。</p>

        <h3 id="design-tokens">3.1 设计令牌</h3>
        <p>设计令牌是UI系统中最小的设计单元，它们定义了所有视觉元素的基本属性。通过使用设计令牌，可以确保整个应用程序的视觉一致性，并且可以轻松地进行主题切换。</p>
        <p>主要的设计令牌类别包括：</p>
        <ul>
            <li>颜色令牌</li>
            <li>排版令牌</li>
            <li>间距令牌</li>
            <li>圆角令牌</li>
            <li>阴影令牌</li>
            <li>过渡令牌</li>
        </ul>

        <h3 id="color-system">3.2 颜色系统</h3>
        <p>颜色系统定义了应用程序中使用的所有颜色，包括品牌色、中性色和辅助色。每种颜色都有多个色阶，以适应不同的使用场景。</p>
        
        <h4>品牌色：</h4>
        <div class="color-grid">
            <div class="color-item">
                <div class="color-preview" style="background-color: #eef2ff;"></div>
                <div class="color-info">
                    <p><strong>primary-50</strong></p>
                    <p>#eef2ff</p>
                </div>
            </div>
            <div class="color-item">
                <div class="color-preview" style="background-color: #e0e7ff;"></div>
                <div class="color-info">
                    <p><strong>primary-100</strong></p>
                    <p>#e0e7ff</p>
                </div>
            </div>
            <div class="color-item">
                <div class="color-preview" style="background-color: #c7d2fe;"></div>
                <div class="color-info">
                    <p><strong>primary-200</strong></p>
                    <p>#c7d2fe</p>
                </div>
            </div>
            <div class="color-item">
                <div class="color-preview" style="background-color: #a5b4fc;"></div>
                <div class="color-info">
                    <p><strong>primary-300</strong></p>
                    <p>#a5b4fc</p>
                </div>
            </div>
            <div class="color-item">
                <div class="color-preview" style="background-color: #818cf8;"></div>
                <div class="color-info">
                    <p><strong>primary-400</strong></p>
                    <p>#818cf8</p>
                </div>
            </div>
            <div class="color-item">
                <div class="color-preview" style="background-color: #6366f1;"></div>
                <div class="color-info">
                    <p><strong>primary-500</strong></p>
                    <p>#6366f1</p>
                </div>
            </div>
            <div class="color-item">
                <div class="color-preview" style="background-color: #4f46e5;"></div>
                <div class="color-info">
                    <p><strong>primary-600</strong></p>
                    <p>#4f46e5</p>
                </div>
            </div>
            <div class="color-item">
                <div class="color-preview" style="background-color: #4338ca;"></div>
                <div class="color-info">
                    <p><strong>primary-700</strong></p>
                    <p>#4338ca</p>
                </div>
            </div>
        </div>

        <h4>辅助色：</h4>
        <div class="color-grid">
            <div class="color-item">
                <div class="color-preview" style="background-color: #10b981;"></div>
                <div class="color-info">
                    <p><strong>success-main</strong></p>
                    <p>#10b981</p>
                </div>
            </div>
            <div class="color-item">
                <div class="color-preview" style="background-color: #f59e0b;"></div>
                <div class="color-info">
                    <p><strong>warning-main</strong></p>
                    <p>#f59e0b</p>
                </div>
            </div>
            <div class="color-item">
                <div class="color-preview" style="background-color: #ef4444;"></div>
                <div class="color-info">
                    <p><strong>error-main</strong></p>
                    <p>#ef4444</p>
                </div>
            </div>
            <div class="color-item">
                <div class="color-preview" style="background-color: #0ea5e9;"></div>
                <div class="color-info">
                    <p><strong>info-main</strong></p>
                    <p>#0ea5e9</p>
                </div>
            </div>
        </div>

        <h3 id="typography">3.3 排版系统</h3>
        <p>排版系统定义了应用程序中使用的所有字体、字号、行高和字母间距。它确保文本在整个应用程序中的一致性和可读性。</p>
        
        <h4>字体系列：</h4>
        <ul>
            <li><strong>sans</strong>: Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif</li>
            <li><strong>serif</strong>: "Noto Serif SC", "Times New Roman", serif</li>
            <li><strong>mono</strong>: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace</li>
        </ul>

        <h4>字号：</h4>
        <table>
            <thead>
                <tr>
                    <th>名称</th>
                    <th>大小</th>
                    <th>像素</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>xs</td>
                    <td>0.75rem</td>
                    <td>12px</td>
                </tr>
                <tr>
                    <td>sm</td>
                    <td>0.875rem</td>
                    <td>14px</td>
                </tr>
                <tr>
                    <td>base</td>
                    <td>1rem</td>
                    <td>16px</td>
                </tr>
                <tr>
                    <td>lg</td>
                    <td>1.125rem</td>
                    <td>18px</td>
                </tr>
                <tr>
                    <td>xl</td>
                    <td>1.25rem</td>
                    <td>20px</td>
                </tr>
                <tr>
                    <td>2xl</td>
                    <td>1.5rem</td>
                    <td>24px</td>
                </tr>
                <tr>
                    <td>3xl</td>
                    <td>1.875rem</td>
                    <td>30px</td>
                </tr>
                <tr>
                    <td>4xl</td>
                    <td>2.25rem</td>
                    <td>36px</td>
                </tr>
            </tbody>
        </table>

        <h3 id="spacing">3.4 间距系统</h3>
        <p>间距系统定义了应用程序中使用的所有间距值，包括内边距、外边距和元素之间的间距。它确保所有元素之间的空间关系一致，提升整体视觉效果。</p>
        
        <p>间距系统采用了0.25rem（4px）为基本单位的设计方法，常用的间距值如下：</p>
        <table>
            <thead>
                <tr>
                    <th>名称</th>
                    <th>值</th>
                    <th>像素</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>0</td>
                    <td>0</td>
                    <td>0px</td>
                </tr>
                <tr>
                    <td>px</td>
                    <td>1px</td>
                    <td>1px</td>
                </tr>
                <tr>
                    <td>1</td>
                    <td>0.25rem</td>
                    <td>4px</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>0.5rem</td>
                    <td>8px</td>
                </tr>
                <tr>
                    <td>4</td>
                    <td>1rem</td>
                    <td>16px</td>
                </tr>
                <tr>
                    <td>6</td>
                    <td>1.5rem</td>
                    <td>24px</td>
                </tr>
                <tr>
                    <td>8</td>
                    <td>2rem</td>
                    <td>32px</td>
                </tr>
            </tbody>
        </table>

        <h3 id="dark-mode">3.5 暗色模式</h3>
        <p>Future Decade UI系统支持亮色和暗色两种主题模式，用户可以根据自己的喜好或环境选择适合的模式。暗色模式不仅可以减轻眼睛疲劳，还可以节省电量（特别是在OLED屏幕上）。</p>
        
        <p>暗色模式的主要特点：</p>
        <ul>
            <li>自动检测系统偏好设置，默认跟随系统主题</li>
            <li>支持用户手动切换主题模式</li>
            <li>主题设置会被保存到本地存储，下次访问时自动应用</li>
            <li>所有组件都支持亮色和暗色两种模式</li>
        </ul>

        <div class="example">
            <h4>主题上下文的使用：</h4>
            <pre><code>import { useTheme } from '@/contexts/ThemeContext';

function MyComponent() {
  const { themeMode, toggleTheme } = useTheme();
  
  return (
    &lt;div&gt;
      &lt;p&gt;当前主题模式: {themeMode}&lt;/p&gt;
      &lt;button onClick={toggleTheme}&gt;
        切换到{themeMode === 'light' ? '暗色' : '亮色'}模式
      &lt;/button&gt;
    &lt;/div&gt;
  );
}</code></pre>
        </div>
    </section>

    <section id="components">
        <h2>4. 组件库</h2>
        <p>Future Decade UI系统提供了一系列可重用的组件，用于构建用户界面。这些组件遵循一致的设计原则，确保整个应用程序的视觉一致性和用户体验。</p>

        <h3 id="theme-toggle">4.1 主题切换按钮</h3>
        <p>ThemeToggle组件是一个简单的按钮，用于切换亮色和暗色主题模式。它的主要特点有：</p>
        <ul>
            <li>根据当前主题模式显示不同的图标（太阳或月亮）</li>
            <li>带有过渡效果的hover状态</li>
            <li>支持键盘导航和辅助技术</li>
        </ul>

        <div class="example">
            <h4>组件代码：</h4>
            <pre><code>const ThemeToggle: React.FC = () => {
  const { themeMode, toggleTheme } = useTheme();
  
  return (
    &lt;button
      className="p-2 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
      onClick={toggleTheme}
      aria-label={themeMode === 'light' ? '切换到暗色模式' : '切换到亮色模式'}
    &gt;
      {themeMode === 'light' ? &lt;MoonIcon /&gt; : &lt;SunIcon /&gt;}
    &lt;/button&gt;
  );
};</code></pre>
        </div>

        <h3 id="future-components">4.2 即将开发的组件</h3>
        <p>以下是计划在未来版本中添加的组件：</p>
        <ul>
            <li><strong>按钮组件</strong>：包含不同尺寸、变体和状态的按钮</li>
            <li><strong>表单组件</strong>：包含输入框、单选框、复选框、选择框等表单元素</li>
            <li><strong>卡片组件</strong>：用于展示内容的卡片</li>
            <li><strong>导航组件</strong>：包含下拉菜单、面包屑导航等</li>
            <li><strong>对话框组件</strong>：用于显示模态对话框</li>
            <li><strong>提示组件</strong>：包含提示框、通知框等</li>
            <li><strong>文章组件</strong>：专门用于展示文章内容</li>
        </ul>
    </section>

    <section id="usage">
        <h2>5. 使用指南</h2>
        <p>本节提供了UI系统的使用指南，帮助开发者快速上手并正确使用布局和主题系统。</p>

        <h3 id="layout-usage">5.1 布局使用</h3>
        <p>布局是构建页面的基础，Future Decade提供了灵活的布局组件，可以轻松构建各种页面布局。</p>

        <div class="example">
            <h4>使用MainLayout组件：</h4>
            <pre><code>// 页面组件
import MainLayout from '@/components/layouts/MainLayout';

export default function AboutPage() {
  return (
    &lt;MainLayout&gt;
      &lt;div className="prose prose-indigo max-w-none dark:prose-invert"&gt;
        &lt;h1&gt;关于我们&lt;/h1&gt;
        &lt;p&gt;Future Decade是一个关注前沿科技和未来趋势的平台...&lt;/p&gt;
      &lt;/div&gt;
    &lt;/MainLayout&gt;
  );
}</code></pre>
        </div>

        <div class="example">
            <h4>自定义布局配置：</h4>
            <pre><code>// 无页脚的布局
import MainLayout from '@/components/layouts/MainLayout';

export default function LandingPage() {
  return (
    &lt;MainLayout showFooter={false}&gt;
      &lt;div className="text-center"&gt;
        &lt;h1 className="text-4xl font-bold"&gt;欢迎来到 Future Decade&lt;/h1&gt;
        // ...
      &lt;/div&gt;
    &lt;/MainLayout&gt;
  );
}</code></pre>
        </div>

        <h3 id="theme-usage">5.2 主题使用</h3>
        <p>主题系统是保持应用程序视觉一致性的关键。Future Decade提供了完整的主题系统，包括多种主题模式和设计令牌。</p>

        <div class="example">
            <h4>使用主题上下文：</h4>
            <pre><code>// 自定义组件
import { useTheme } from '@/contexts/ThemeContext';

export default function WelcomeMessage() {
  const { themeMode } = useTheme();
  
  return (
    &lt;div&gt;
      &lt;h2&gt;欢迎使用{themeMode === 'dark' ? '暗色' : '亮色'}模式&lt;/h2&gt;
      &lt;p&gt;您可以随时切换主题模式以获得最佳体验。&lt;/p&gt;
    &lt;/div&gt;
  );
}</code></pre>
        </div>

        <div class="example">
            <h4>使用Tailwind CSS的暗色模式类：</h4>
            <pre><code>&lt;div className="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"&gt;
  &lt;h3 className="text-xl font-bold text-gray-900 dark:text-white"&gt;特色内容&lt;/h3&gt;
  &lt;p className="text-gray-600 dark:text-gray-300"&gt;这里是一些内容描述...&lt;/p&gt;
&lt;/div&gt;</code></pre>
        </div>
    </section>

    <section id="roadmap">
        <h2>6. 开发路线</h2>
        <p>Future Decade UI系统的未来发展路线包括以下几个方面：</p>
        
        <h3>短期目标（V1.6）：</h3>
        <ul>
            <li>完善基础组件库，包括按钮、表单元素、卡片等</li>
            <li>添加文章相关组件，优化文章页面的显示效果</li>
            <li>增强主题系统，支持自定义主题</li>
            <li>添加动画和过渡效果</li>
        </ul>

        <h3>中期目标（V2.0）：</h3>
        <ul>
            <li>构建完整的组件文档系统</li>
            <li>增加更多页面模板</li>
            <li>提供组件测试和性能优化</li>
            <li>完善辅助功能，确保可访问性</li>
        </ul>

        <h3>长期目标：</h3>
        <ul>
            <li>支持插件系统，允许第三方开发者扩展UI系统</li>
            <li>提供更多主题和布局变体</li>
            <li>构建组件可视化编辑器</li>
            <li>支持更多国际化和本地化功能</li>
        </ul>
    </section>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加目录项的点击事件
            const tocLinks = document.querySelectorAll('.toc a');
            for (const link of tocLinks) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 20,
                            behavior: 'smooth'
                        });
                    }
                });
            }
        });
    </script>
</body>
</html> 