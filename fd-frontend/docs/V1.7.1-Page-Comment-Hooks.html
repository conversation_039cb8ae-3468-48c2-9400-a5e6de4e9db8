<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>V1.7.1 - 页面与评论Hooks实现 - 前端开发文档</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #2c3e50;
      border-bottom: 2px solid #eaecef;
      padding-bottom: 10px;
    }
    h2 {
      color: #1a73e8;
      margin-top: 30px;
      padding-bottom: 6px;
      border-bottom: 1px solid #eaecef;
    }
    h3 {
      color: #34495e;
      margin-top: 24px;
    }
    code {
      font-family: Consolas, Monaco, 'Andale Mono', monospace;
      background-color: #f5f7f9;
      border: 1px solid #e4e6e8;
      border-radius: 3px;
      padding: 2px 5px;
    }
    pre {
      background-color: #f5f7f9;
      border: 1px solid #e4e6e8;
      border-radius: 5px;
      padding: 15px;
      overflow-x: auto;
    }
    pre code {
      border: none;
      padding: 0;
      background: none;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 20px 0;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px 12px;
      text-align: left;
    }
    th {
      background-color: #f5f7f9;
    }
    .note {
      background-color: #fff8e1;
      border-left: 4px solid #ffc107;
      padding: 10px 15px;
      margin: 20px 0;
    }
    .warning {
      background-color: #fef2f2;
      border-left: 4px solid #ef4444;
      padding: 10px 15px;
      margin: 20px 0;
    }
    .success {
      background-color: #f0fdf4;
      border-left: 4px solid #10b981;
      padding: 10px 15px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <h1>V1.7.1 - 页面与评论Hooks实现</h1>
  
  <div class="success">
    <p><strong>版本: V1.7.1</strong></p>
    <p>发布日期: 2023-12-12</p>
    <p>此版本主要实现了页面查询相关hooks和评论系统相关hooks，以便前端可以高效查询和管理页面与评论内容。</p>
  </div>

  <h2>1. 页面查询Hooks</h2>
  
  <h3>1.1 usePage Hook</h3>
  <p>可通过ID或Slug查询单个页面的详细信息。该hook支持两种查询方式，根据提供的参数自动选择相应的GraphQL查询。</p>
  
  <pre><code>/**
 * 获取单个页面详情的Hook
 * @param options 查询选项，提供id或slug
 * @returns 页面数据、加载状态和错误信息
 */
export const usePage = (options: UsePageOptions = {}) => {
  const { id, slug } = options;
  
  // 如果没有提供有效参数，跳过查询而不是抛出错误
  const validParams = !!(id || slug);

  // 根据参数选择查询方式
  const queryInfo = id 
    ? { query: GET_PAGE_BY_ID, variables: { id } }
    : { query: GET_PAGE_BY_SLUG, variables: { slug } };

  const { data, loading, error, refetch } = useQuery<PageData>(
    queryInfo.query, 
    { 
      variables: queryInfo.variables,
      skip: !validParams
    }
  );

  return {
    page: data?.page,
    loading: validParams && loading,
    error,
    refetch,
  };
};</code></pre>

  <h4>使用示例:</h4>
  <pre><code>// 通过ID查询页面
const { page, loading, error } = usePage({ id: '123' });

// 或通过Slug查询页面
const { page, loading, error } = usePage({ slug: 'about-us' });
</code></pre>

  <h3>1.2 usePages Hook</h3>
  <p>用于获取页面列表，支持分页功能。</p>
  
  <pre><code>/**
 * 获取页面列表的Hook
 * @param options 查询选项
 * @returns 页面列表数据、加载状态和错误信息
 */
export const usePages = (options: UsePagesOptions = {}) => {
  const { first = 10, after } = options;

  const { data, loading, error, fetchMore, refetch } = useQuery<PagesData>(
    GET_PAGES,
    {
      variables: { first, after },
      notifyOnNetworkStatusChange: true,
    }
  );

  const loadMore = (afterCursor: string) => {
    return fetchMore({
      variables: {
        first,
        after: afterCursor,
      },
      updateQuery: (prev: PagesData, { fetchMoreResult }: { fetchMoreResult: PagesData | undefined }) => {
        if (!fetchMoreResult) return prev;
        return {
          pages: {
            ...fetchMoreResult.pages,
            nodes: [...prev.pages.nodes, ...fetchMoreResult.pages.nodes],
          },
        };
      },
    });
  };

  return {
    pages: data?.pages?.nodes || [],
    loading,
    error,
    loadMore,
    refetch,
  };
};</code></pre>

  <h4>使用示例:</h4>
  <pre><code>// 获取前10个页面
const { pages, loading, error, loadMore } = usePages({ first: 10 });

// 加载更多页面
const handleLoadMore = () => {
  if (pages.length > 0) {
    const lastPage = pages[pages.length - 1];
    loadMore(lastPage.cursor);
  }
};</code></pre>

  <h2>2. 评论系统Hooks</h2>

  <h3>2.1 评论状态枚举</h3>
  <p>WordPress评论系统支持多种状态，我们通过枚举定义了这些状态：</p>
  
  <pre><code>// 评论状态枚举
export enum CommentStatusEnum {
  APPROVE = 'APPROVE',
  HOLD = 'HOLD',
  SPAM = 'SPAM',
  TRASH = 'TRASH'
}</code></pre>

  <h3>2.2 useComments Hook</h3>
  <p>获取特定文章的评论列表。</p>
  
  <pre><code>/**
 * 获取文章评论列表的Hook
 * @param postId 文章ID
 * @param first 获取的评论数量，默认为20
 * @returns 评论列表数据、加载状态和错误信息
 */
export const useComments = (postId: string | number, first: number = 20) => {
  const { data, loading, error, refetch } = useQuery<CommentsData>(
    GET_POST_COMMENTS,
    {
      variables: { postId, first },
      skip: !postId
    }
  );

  return {
    comments: data?.post?.comments?.nodes || [],
    loading,
    error,
    refetch,
  };
};</code></pre>

  <h3>2.3 useComment Hook</h3>
  <p>获取单个评论的详细信息。</p>
  
  <pre><code>/**
 * 获取单个评论详情的Hook
 * @param id 评论ID
 * @returns 评论数据、加载状态和错误信息
 */
export const useComment = (id: string) => {
  const { data, loading, error, refetch } = useQuery<CommentData>(
    GET_COMMENT,
    {
      variables: { id },
      skip: !id
    }
  );

  return {
    comment: data?.comment,
    loading,
    error,
    refetch,
  };
};</code></pre>

  <h3>2.4 useCommentReplies Hook</h3>
  <p>获取特定评论的所有回复。</p>
  
  <pre><code>/**
 * 获取评论回复列表的Hook
 * @param id 评论ID
 * @param first 获取的回复数量，默认为50
 * @returns 评论回复列表数据、加载状态和错误信息
 */
export const useCommentReplies = (id: string, first: number = 50) => {
  const { data, loading, error, refetch } = useQuery<CommentRepliesData>(
    GET_COMMENT_REPLIES,
    {
      variables: { id, first },
      skip: !id
    }
  );

  return {
    parentComment: data?.comment,
    replies: data?.comment?.replies?.nodes || [],
    loading,
    error,
    refetch,
  };
};</code></pre>

  <h3>2.5 useCommentsByStatus Hook</h3>
  <p>按状态获取评论列表。</p>
  
  <pre><code>/**
 * 按状态获取评论列表的Hook
 * @param status 评论状态数组
 * @param first 获取的评论数量，默认为50
 * @returns 评论列表数据、加载状态和错误信息
 */
export const useCommentsByStatus = (status: CommentStatusEnum[], first: number = 50) => {
  const { data, loading, error, refetch } = useQuery<CommentsData>(
    GET_COMMENTS_BY_STATUS,
    {
      variables: { status, first },
      skip: !status || status.length === 0
    }
  );

  return {
    comments: data?.comments?.nodes || [],
    loading,
    error,
    refetch,
  };
};</code></pre>

  <h3>2.6 评论变更操作Hooks</h3>
  <p>支持评论的增删改操作：</p>
  
  <h4>2.6.1 useCreateComment</h4>
  <pre><code>/**
 * 创建评论的Hook
 * @returns 创建评论的变更函数、加载状态和错误信息
 */
export const useCreateComment = () => {
  // 实现详见代码
};</code></pre>

  <h4>2.6.2 useUpdateComment</h4>
  <pre><code>/**
 * 更新评论的Hook
 * @returns 更新评论的变更函数、加载状态和错误信息
 */
export const useUpdateComment = () => {
  // 实现详见代码
};</code></pre>

  <h4>2.6.3 useDeleteComment</h4>
  <pre><code>/**
 * 删除评论的Hook
 * @returns 删除评论的变更函数、加载状态和错误信息
 */
export const useDeleteComment = () => {
  // 实现详见代码
};</code></pre>

  <h2>3. 主要变更摘要</h2>
  
  <table>
    <tr>
      <th>功能组件</th>
      <th>说明</th>
    </tr>
    <tr>
      <td>usePage Hook</td>
      <td>通过ID或Slug查询单个页面详情，自动选择查询方式</td>
    </tr>
    <tr>
      <td>usePages Hook</td>
      <td>获取页面列表，支持分页和加载更多功能</td>
    </tr>
    <tr>
      <td>useComments Hook</td>
      <td>获取特定文章的评论列表</td>
    </tr>
    <tr>
      <td>useComment Hook</td>
      <td>获取单个评论详情</td>
    </tr>
    <tr>
      <td>useCommentReplies Hook</td>
      <td>获取评论的所有回复</td>
    </tr>
    <tr>
      <td>useCommentsByStatus Hook</td>
      <td>按状态筛选获取评论</td>
    </tr>
    <tr>
      <td>评论变更操作Hooks</td>
      <td>支持评论的创建、更新和删除操作</td>
    </tr>
  </table>

  <h2>4. 使用建议</h2>
  
  <div class="note">
    <p><strong>页面数据获取</strong></p>
    <p>在需要展示单个页面详情时，优先使用slug查询，因为它更符合用户友好的URL结构；在需要编辑或管理页面时，可以使用ID查询以确保精确定位。</p>
  </div>
  
  <div class="note">
    <p><strong>评论管理</strong></p>
    <p>在展示文章评论时，建议先使用useComments获取一级评论，再对特定评论使用useCommentReplies获取回复，以减轻服务器负担并提高页面加载速度。</p>
  </div>

  <h2>5. 后续优化方向</h2>
  
  <ul>
    <li>优化评论的缓存策略，减少不必要的网络请求</li>
    <li>添加评论分页功能，支持无限滚动加载更多评论</li>
    <li>实现评论实时更新功能，以提供更好的用户互动体验</li>
    <li>为评论添加更多元数据支持，如点赞数、举报状态等</li>
  </ul>

  <div class="success">
    <p>此文档由前端开发团队维护，最后更新：2023-12-12</p>
  </div>
</body>
</html> 