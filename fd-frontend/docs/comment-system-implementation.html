<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Future Decade - 评论功能实现文档</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3, h4 {
      color: #2c3e50;
    }
    h1 {
      border-bottom: 2px solid #3498db;
      padding-bottom: 10px;
    }
    h2 {
      border-bottom: 1px solid #ddd;
      padding-bottom: 5px;
      margin-top: 30px;
    }
    code {
      font-family: Consolas, Monaco, 'Andale Mono', monospace;
      background-color: #f5f5f5;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 0.9em;
    }
    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      overflow: auto;
      line-height: 1.4;
    }
    pre code {
      background: none;
      padding: 0;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 20px 0;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px 12px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
    }
    .warning {
      background-color: #fff3cd;
      border-left: 4px solid #ffc107;
      padding: 10px 15px;
      margin: 15px 0;
    }
    .info {
      background-color: #d1ecf1;
      border-left: 4px solid #17a2b8;
      padding: 10px 15px;
      margin: 15px 0;
    }
    .success {
      background-color: #d4edda;
      border-left: 4px solid #28a745;
      padding: 10px 15px;
      margin: 15px 0;
    }
    .component-tree {
      margin: 20px 0;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 5px;
    }
    .component-tree ul {
      list-style-type: none;
      padding-left: 20px;
    }
    .component-tree li {
      padding: 5px 0;
    }
  </style>
</head>
<body>
  <h1>Future Decade - 评论功能实现文档</h1>
  
  <div class="info">
    <p><strong>文档说明：</strong>本文档详细描述了Future Decade前端项目中评论功能的实现方案、组件结构、数据流和使用方法。</p>
  </div>

  <h2>1. 评论功能概述</h2>
  
  <p>评论功能是Headless WordPress站点的重要组成部分，允许用户在文章和自定义内容类型下发表评论、回复他人评论，以及查看评论列表。本实现基于GraphQL API，支持标准文章和自定义内容类型。</p>
  
  <h3>1.1 主要功能</h3>
  
  <ul>
    <li>显示评论列表，支持嵌套结构（父评论和回复）</li>
    <li>提交新评论</li>
    <li>回复已有评论</li>
    <li>显示评论作者信息（头像、名称、网站链接等）</li>
    <li>适应不同内容类型（标准文章、自定义类型）</li>
    <li>支持暗色模式</li>
    <li>响应式设计（移动端和桌面端）</li>
  </ul>

  <h3>1.2 技术栈</h3>
  
  <ul>
    <li>React + TypeScript</li>
    <li>Apollo Client (GraphQL 客户端)</li>
    <li>Tailwind CSS (样式系统)</li>
    <li>Next.js App Router</li>
  </ul>

  <h2>2. 组件结构</h2>
  
  <p>评论功能由以下组件组成，采用自上而下的组合方式：</p>
  
  <div class="component-tree">
    <ul>
      <li><strong>CommentSection</strong> - 评论区主组件
        <ul>
          <li><strong>CommentForm</strong> - 评论表单组件</li>
          <li><strong>CommentList</strong> - 评论列表组件
            <ul>
              <li><strong>CommentItem</strong> - 单个评论组件
                <ul>
                  <li>CommentForm (用于回复)</li>
                  <li>嵌套的 CommentItem (用于显示回复)</li>
                </ul>
              </li>
            </ul>
          </li>
        </ul>
      </li>
    </ul>
  </div>

  <h3>2.1 组件说明</h3>
  
  <h4>CommentSection</h4>
  <p>评论区的主容器组件，负责获取评论数据并渲染评论表单和评论列表。根据内容类型选择不同的数据获取方式。</p>
  
  <pre><code>interface CommentSectionProps {
  postId: number;
  commentStatus?: string; // 文章的评论状态
  isCustomType?: boolean; // 是否为自定义内容类型
}</code></pre>

  <h4>CommentForm</h4>
  <p>处理评论提交的表单组件，支持创建新评论和回复现有评论。</p>
  
  <pre><code>interface CommentFormProps {
  postId: number;
  parentId?: number;
  onCommentAdded: () => void;
}</code></pre>

  <h4>CommentList</h4>
  <p>展示评论列表，处理评论的嵌套结构（父评论和回复）。</p>
  
  <pre><code>interface CommentListProps {
  comments: Comment[];
  postId: number;
  onCommentAdded: () => void;
}</code></pre>

  <h4>CommentItem</h4>
  <p>单个评论的显示组件，包含评论内容、作者信息，以及回复功能。</p>
  
  <pre><code>interface CommentItemProps {
  comment: Comment;
  postId: number;
  onCommentAdded: () => void;
  replies?: Comment[];
}</code></pre>

  <h2>3. GraphQL 查询与变更</h2>
  
  <p>评论功能使用以下GraphQL查询和变更：</p>

  <h3>3.1 评论查询</h3>
  
  <h4>获取标准文章评论</h4>
  <pre><code>// 获取文章的评论
export const GET_POST_COMMENTS = gql`
  query GetPostComments($postId: ID!, $first: Int = 100) {
    post(id: $postId, idType: DATABASE_ID) {
      id
      title
      commentCount
      commentStatus
      comments(first: $first) {
        nodes {
          ...CommentFields
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  }
  ${COMMENT_FRAGMENT}
`;</code></pre>

  <h4>获取任意内容类型的评论</h4>
  <pre><code>// 根据内容节点ID获取评论（适用于任何内容类型）
export const GET_CONTENT_NODE_COMMENTS = gql`
  query GetContentNodeComments($contentNodeId: ID!, $first: Int = 100) {
    contentNode(id: $contentNodeId, idType: DATABASE_ID) {
      id
      ... on NodeWithComments {
        commentCount
        commentStatus
      }
      ... on Post {
        comments(first: $first) {
          nodes {
            ...CommentFields
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
      ... on Page {
        comments(first: $first) {
          nodes {
            ...CommentFields
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
      ... on MediaItem {
        comments(first: $first) {
          nodes {
            ...CommentFields
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
      ... on _note {
        comments(first: $first) {
          nodes {
            ...CommentFields
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
    }
  }
  ${COMMENT_FRAGMENT}
`;</code></pre>

  <h4>评论片段</h4>
  <pre><code>// 评论信息片段
export const COMMENT_FRAGMENT = gql`
  fragment CommentFields on Comment {
    id
    databaseId
    content
    date
    parentId
    parentDatabaseId
    status
    author {
      node {
        name
        url
        avatar {
          url
        }
      }
    }
  }
`;</code></pre>

  <h3>3.2 评论变更</h3>
  
  <pre><code>// 创建评论
export const CREATE_COMMENT = gql`
  mutation CreateComment($input: CreateCommentInput!) {
    createComment(input: $input) {
      success
      comment {
        ...CommentFields
      }
    }
  }
  ${COMMENT_FRAGMENT}
`;</code></pre>

  <h2>4. 自定义Hook</h2>
  
  <p>为了简化组件中的数据获取和状态管理，我们创建了以下自定义Hook：</p>

  <h3>4.1 基础Hook</h3>
  
  <h4>useComments</h4>
  <p>获取标准文章的评论列表。</p>
  <pre><code>export const useComments = (postId: string | number, first: number = 20) => {
  const { data, loading, error, refetch } = useQuery<CommentsData>(
    GET_POST_COMMENTS,
    {
      variables: { postId, first },
      skip: !postId
    }
  );

  return {
    comments: data?.post?.comments?.nodes || [],
    loading,
    error,
    refetch,
  };
};</code></pre>

  <h4>useContentNodeComments</h4>
  <p>获取任意内容类型的评论列表，适用于自定义内容类型。</p>
  <pre><code>export const useContentNodeComments = (contentNodeId: string | number, first: number = 20) => {
  const { data, loading, error, refetch } = useQuery<ContentNodeCommentsData>(
    GET_CONTENT_NODE_COMMENTS,
    {
      variables: { contentNodeId, first },
      skip: !contentNodeId
    }
  );

  // 从不同内容类型中获取评论
  let comments: Comment[] = [];
  let commentCount = 0;
  let commentStatus: string | undefined;
  
  if (data?.contentNode) {
    const node = data.contentNode;
    
    // 获取评论状态和评论数
    if ('commentStatus' in node) {
      commentStatus = node.commentStatus;
    }
    if ('commentCount' in node) {
      commentCount = node.commentCount || 0;
    }
    
    // 从各种可能的内容类型中获取评论
    const postComments = (node as any).comments?.nodes;
    const pageComments = (node as any).comments?.nodes;
    const mediaComments = (node as any).comments?.nodes;
    const noteComments = (node as any).comments?.nodes;
    
    if (Array.isArray(postComments)) comments = postComments;
    else if (Array.isArray(pageComments)) comments = pageComments;
    else if (Array.isArray(mediaComments)) comments = mediaComments;
    else if (Array.isArray(noteComments)) comments = noteComments;
  }

  return {
    comments,
    commentCount,
    commentStatus,
    loading,
    error,
    refetch,
  };
};</code></pre>

  <h4>useCreateComment</h4>
  <p>创建新评论的Hook。</p>
  <pre><code>export const useCreateComment = () => {
  const [createComment, { data, loading, error }] = useMutation<CreateCommentData>(
    CREATE_COMMENT
  );

  const handleCreateComment = async (input: CreateCommentInput) => {
    try {
      const response = await createComment({
        variables: { input }
      });
      return response?.data?.createComment;
    } catch (err) {
      console.error('创建评论失败:', err);
      throw err;
    }
  };

  return {
    createComment: handleCreateComment,
    newComment: data?.createComment?.comment,
    loading,
    error,
  };
};</code></pre>

  <h2>5. 使用方法</h2>
  
  <h3>5.1 在文章页面中使用</h3>
  
  <p>在标准文章页面中集成评论区：</p>
  
  <pre><code>// fd-frontend/src/app/post/[uuid]/[slug]/page.tsx
import CommentSection from '@/components/comments/CommentSection';

export default async function ArticlePage({ params }: { 
  params: { uuid: string; slug: string } 
}) {
  // ... 获取文章数据 ...
  
  return (
    &lt;article className="article-container max-w-4xl mx-auto py-10 px-4"&gt;
      {/* 文章内容 */}
      &lt;h1 className="text-3xl font-bold mb-6"&gt;{post.title}&lt;/h1&gt;
      {/* ... 其他文章内容 ... */}
      
      {/* 添加评论区 */}
      &lt;CommentSection 
        postId={post.databaseId || 0} 
        commentStatus={post.commentStatus} 
      /&gt;
    &lt;/article&gt;
  );
}</code></pre>

  <h3>5.2 在自定义内容类型页面中使用</h3>
  
  <p>在自定义内容类型页面中集成评论区：</p>
  
  <pre><code>// fd-frontend/src/app/post-type/[type]/[uuid]/[slug]/page.tsx
import CommentSection from '@/components/comments/CommentSection';

export default async function CustomPostTypePage({ params }: { 
  params: { type: string; uuid: string; slug: string } 
}) {
  // ... 获取自定义内容类型数据 ...
  
  return (
    &lt;article className="article-container max-w-4xl mx-auto py-10 px-4"&gt;
      {/* 内容 */}
      &lt;h1 className="text-3xl font-bold mb-6"&gt;{post.title}&lt;/h1&gt;
      {/* ... 其他内容 ... */}
      
      {/* 添加评论区 */}
      &lt;CommentSection 
        postId={post.databaseId || 0} 
        commentStatus={post.commentStatus || 'closed'} 
        isCustomType={true}
      /&gt;
    &lt;/article&gt;
  );
}</code></pre>

  <h2>6. 实现中的关键点</h2>
  
  <h3>6.1 自定义内容类型支持</h3>
  
  <p>为了支持自定义内容类型的评论功能，需要特别注意以下几点：</p>
  
  <div class="warning">
    <p><strong>注意：</strong> WordPress中不同内容类型以不同方式实现评论功能。自定义内容类型需要实现NodeWithComments接口才能支持评论。</p>
  </div>
  
  <ol>
    <li>使用正确的GraphQL片段指定内容类型（Post、Page、MediaItem、_note等）</li>
    <li>检查内容类型是否支持评论功能，有些自定义类型可能默认关闭评论</li>
    <li>在评论组件中提供isCustomType标志以使用正确的数据获取方法</li>
  </ol>
  
  <h3>6.2 评论嵌套结构处理</h3>
  
  <p>评论的嵌套结构（回复）通过以下方式实现：</p>
  
  <ol>
    <li>CommentList组件将评论组织为树形结构，识别父评论和子评论</li>
    <li>根据评论的parentId字段确定评论之间的关系</li>
    <li>使用递归方式渲染嵌套评论</li>
  </ol>
  
  <pre><code>// 将评论组织成树形结构（父评论和其回复）
const organizeComments = () => {
  const parentComments: Comment[] = [];
  const childComments: Record<string, Comment[]> = {};
  
  // 分类评论
  comments.forEach(comment => {
    if (!comment.parentId) {
      parentComments.push(comment);
    } else {
      if (!childComments[comment.parentId]) {
        childComments[comment.parentId] = [];
      }
      childComments[comment.parentId].push(comment);
    }
  });
  
  return { parentComments, childComments };
};</code></pre>

  <h3>6.3 错误处理与加载状态</h3>
  
  <p>评论功能包含全面的错误处理和加载状态显示：</p>
  
  <ul>
    <li>数据加载时显示加载指示器</li>
    <li>错误发生时显示友好的错误信息</li>
    <li>评论提交后显示成功消息</li>
    <li>表单验证错误处理</li>
  </ul>

  <h2>7. 问题解决</h2>
  
  <h3>7.1 GraphQL查询结构问题</h3>
  
  <p>在实现自定义内容类型评论功能时，遇到了GraphQL查询结构的问题。错误信息：</p>
  
  <pre><code>Cannot query field "comments" on type "NodeWithComments". 
Did you mean to use an inline fragment on "MediaItem", "_note", "Post", or "Page"?</code></pre>
  
  <p>解决方案：使用内联片段正确地查询不同内容类型的comments字段。</p>
  
  <div class="success">
    <p><strong>解决方法：</strong> 将comments字段查询放在具体内容类型的内联片段中，而不是直接放在NodeWithComments接口上。</p>
  </div>
  
  <h3>7.2 类型安全问题</h3>
  
  <p>在集成评论区组件时，遇到了TypeScript类型错误：</p>
  
  <pre><code>Type 'number | undefined' is not assignable to type 'number'.
  Type 'undefined' is not assignable to type 'number'.</code></pre>
  
  <p>解决方案：为可能为undefined的属性提供默认值，确保类型安全。</p>
  
  <div class="success">
    <p><strong>解决方法：</strong> 使用逻辑OR运算符提供默认值，如：<code>postId={post.databaseId || 0}</code></p>
  </div>

  <h2>8. 未来改进计划</h2>
  
  <ul>
    <li>增加评论分页功能，处理大量评论的情况</li>
    <li>添加评论编辑功能（已登录用户）</li>
    <li>实现评论点赞/投票功能</li>
    <li>添加评论内容过滤和格式化</li>
    <li>集成评论通知系统</li>
  </ul>

  <h2>9. 总结</h2>
  
  <p>本文档详细描述了Future Decade前端项目中评论功能的实现。该实现基于GraphQL API，能够支持标准WordPress文章和自定义内容类型的评论功能，包括评论列表显示、发表评论和回复评论等核心功能。</p>
  
  <p>实现过程中解决了GraphQL查询结构、类型安全和数据处理等方面的挑战，为用户提供了完善的评论体验。</p>
  
  <div class="info">
    <p><strong>文档更新日期：</strong> 2024年4月5日</p>
  </div>
</body>
</html> 