# 分类页面分页问题修复

## 问题描述

在测试某个有62篇文章的分类时发现，当分页下拉到底，显示只有22篇文章，就已经提示显示了全部文章了。

## 问题诊断

通过代码分析，发现了以下几个关键问题：

### 1. 分页大小不一致
- **服务器组件**：使用固定的 `POSTS_PER_PAGE = 10`
- **客户端组件**：使用动态的 `postsPerPage`（默认12）
- 这种不一致导致分页逻辑混乱和数据缺失

### 2. 重复查询问题
- 客户端组件的 `usePosts` hook 会立即执行查询
- 传入 `after: initialPageInfo?.endCursor` 导致立即加载下一页
- 这与预期的"用户触发加载更多"行为不符

### 3. 分页状态管理混乱
- 初始分页信息和后续分页信息的管理不一致
- `hasNextPage` 状态可能被错误计算

## 修复方案

### 1. 统一分页大小

**服务器组件** (`src/app/category/[slug]/page.tsx`)：
- 添加 `fetchPostsPerPageSetting()` 函数动态获取分页设置
- 修改 `fetchCategoryPageData()` 接受 `postsPerPage` 参数
- 更新 `generateMetadata()` 和 `CategoryPage()` 使用动态分页设置

### 2. 重新设计客户端分页逻辑

**客户端组件** (`src/components/pages/CategoryClientPage.tsx`)：
- 移除 `usePosts` hook 的使用，避免立即查询
- 实现手动的 `loadMorePosts()` 函数
- 只在用户触发"加载更多"时才执行查询

### 3. 改进状态管理
- 使用 `currentPageInfo` 状态跟踪分页信息
- 正确合并新加载的文章到现有列表
- 添加详细的调试日志便于问题诊断

## 关键代码修改

### 服务器组件修改
```typescript
// 动态获取分页设置
async function fetchPostsPerPageSetting(): Promise<number> {
  // ... GraphQL查询获取设置
}

// 使用动态分页大小
async function fetchCategoryPageData(slug: string, postsPerPage: number) {
  // ... 使用postsPerPage而不是固定值
}
```

### 客户端组件修改
```typescript
// 移除usePosts hook，使用手动加载
const loadMorePosts = useCallback(async (afterCursor: string) => {
  // 动态获取分页设置
  // 执行GraphQL查询
  // 返回结果
}, [category?.databaseId]);

// 简化的加载更多逻辑
const handleLoadMore = useCallback(async () => {
  if (!currentPageInfo?.hasNextPage || loadingMore) return;
  
  const result = await loadMorePosts(currentPageInfo.endCursor);
  // 合并数据并更新状态
}, [currentPageInfo, loadingMore, loadMorePosts]);
```

## 修复效果

1. **统一分页大小**：服务器端和客户端现在使用相同的分页设置
2. **正确的分页行为**：只在用户滚动到底部时才加载更多数据
3. **准确的状态管理**：`hasNextPage` 状态正确反映是否还有更多数据
4. **调试友好**：添加了详细的控制台日志便于问题诊断

## 测试建议

1. 在服务器环境中部署修复后的代码
2. 测试有大量文章的分类页面（如62篇文章的分类）
3. 检查控制台日志，验证分页逻辑是否正常工作
4. 确认能够加载所有文章，直到真正没有更多数据

## 注意事项

- 修复后的代码包含详细的调试日志，生产环境可考虑移除
- 确保WordPress后端的分页设置与前端一致
- 如果仍有问题，可通过控制台日志进一步诊断
