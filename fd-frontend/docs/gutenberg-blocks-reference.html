<h1>Gutenberg区块前端渲染快速参考</h1>

<h2>Block Renderer用法</h2>

<pre><code class="language-tsx">// 在页面文件中使用BlockRenderer
import { BlockRenderer } from '@/components/blocks';

// 在页面组件内部
return (
  &lt;div className="content-area"&gt;
    {page.blocks && page.blocks.length > 0 ? (
      &lt;BlockRenderer blocks={page.blocks} /&gt;
    ) : (
      &lt;div dangerouslySetInnerHTML={{ __html: page.content || '' }} /&gt;
    )}
  &lt;/div&gt;
);
</code></pre>

<h2>核心组件列表和开发状态</h2>

<table>
  <thead>
    <tr>
      <th>区块名称</th>
      <th>实现状态</th>
      <th>优先级</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>core/paragraph</code></td>
      <td>✅ 已实现</td>
      <td>P0</td>
    </tr>
    <tr>
      <td><code>core/heading</code></td>
      <td>✅ 已实现</td>
      <td>P0</td>
    </tr>
    <tr>
      <td><code>core/image</code></td>
      <td>✅ 已实现</td>
      <td>P0</td>
    </tr>
    <tr>
      <td><code>core/list</code></td>
      <td>✅ 已实现</td>
      <td>P0</td>
    </tr>
    <tr>
      <td><code>core/quote</code></td>
      <td>✅ 已实现</td>
      <td>P0</td>
    </tr>
    <tr>
      <td><code>core/gallery</code></td>
      <td>⏳ 待实现</td>
      <td>P1</td>
    </tr>
    <tr>
      <td><code>core/table</code></td>
      <td>⏳ 待实现</td>
      <td>P1</td>
    </tr>
    <tr>
      <td><code>core/columns</code></td>
      <td>⏳ 待实现</td>
      <td>P1</td>
    </tr>
    <tr>
      <td><code>core/button</code></td>
      <td>⏳ 待实现</td>
      <td>P1</td>
    </tr>
    <tr>
      <td><code>core/embed</code></td>
      <td>⏳ 待实现</td>
      <td>P2</td>
    </tr>
  </tbody>
</table>

<h2>常见问题解决</h2>

<ol>
  <li>
    <p><strong>区块显示为空白</strong>：检查attributesJSON是否正确解析</p>
    <pre><code class="language-tsx">// BlockRenderer.tsx中添加调试信息
console.log("区块属性:", getBlockAttributes(block));</code></pre>
  </li>
  
  <li>
    <p><strong>嵌套区块不显示</strong>：确保处理innerBlocks</p>
    <pre><code class="language-tsx">// 递归渲染innerBlocks
{block.innerBlocks && block.innerBlocks.length > 0 && (
  &lt;BlockRenderer blocks={block.innerBlocks} /&gt;
)}</code></pre>
  </li>
  
  <li>
    <p><strong>样式不正确</strong>：检查Tailwind类名和内联样式</p>
    <pre><code class="language-tsx">// 样式处理示例
const className = [
  'base-class',
  align && `align-${align}`,
  otherProp && 'conditional-class'
].filter(Boolean).join(' ');</code></pre>
  </li>
</ol>

<h2>添加新区块组件的步骤</h2>

<ol>
  <li>
    <p><strong>创建组件文件</strong>：</p>
    <pre><code class="language-bash">touch src/components/blocks/NewBlock.tsx</code></pre>
  </li>
  
  <li>
    <p><strong>实现组件</strong>：</p>
    <pre><code class="language-tsx">// src/components/blocks/NewBlock.tsx
import React from 'react';

interface NewBlockProps {
  attributes: {
    // 定义属性类型
    property1: string;
    property2?: number;
  };
}

const NewBlock: React.FC&lt;NewBlockProps&gt; = ({ attributes }) => {
  const { property1, property2 } = attributes;
  
  return (
    &lt;div className="new-block"&gt;
      {/* 渲染区块内容 */}
    &lt;/div&gt;
  );
};

export default NewBlock;</code></pre>
  </li>
  
  <li>
    <p><strong>更新索引文件</strong>：</p>
    <pre><code class="language-tsx">// src/components/blocks/index.ts
export { default as NewBlock } from './NewBlock';</code></pre>
  </li>
  
  <li>
    <p><strong>注册到BlockRenderer</strong>：</p>
    <pre><code class="language-tsx">// src/components/blocks/BlockRenderer.tsx
import NewBlock from './NewBlock';

// 在switch语句中添加
case 'CoreNewBlock':
  return &lt;NewBlock key={index} attributes={attributes} /&gt;;</code></pre>
  </li>
</ol>

<h2>调试技巧</h2>

<ol>
  <li>
    <p><strong>查看区块结构</strong>：</p>
    <pre><code class="language-tsx">useEffect(() => {
  console.log('页面区块结构:', page.blocks);
}, [page.blocks]);</code></pre>
  </li>
  
  <li>
    <p><strong>调试特定区块</strong>：</p>
    <pre><code class="language-tsx">if (__typename === 'CoreProblemBlock') {
  console.log('问题区块:', block);
}</code></pre>
  </li>
  
  <li>
    <p><strong>临时显示原始HTML</strong>：</p>
    <pre><code class="language-tsx">// 当区块组件出问题时添加此回退
return (
  &lt;&gt;
    &lt;div style={{color: 'red'}}&gt;调试区块: {__typename}&lt;/div&gt;
    &lt;div dangerouslySetInnerHTML={{__html: saveContent}} /&gt;
  &lt;/&gt;
);</code></pre>
  </li>
</ol> 