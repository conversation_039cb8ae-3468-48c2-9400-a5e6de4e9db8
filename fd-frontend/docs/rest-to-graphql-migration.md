# REST API 到 GraphQL 迁移指南

本文档提供了将WordPress REST API调用转换为GraphQL查询的指南。

## 目录

1. [迁移概述](#迁移概述)
2. [REST API与GraphQL对比](#rest-api与graphql对比)
3. [REST API端点映射](#rest-api端点映射)
4. [查询参数转换](#查询参数转换)
5. [常见REST API调用转换示例](#常见rest-api调用转换示例)
6. [使用片段优化查询](#使用片段优化查询)
7. [错误处理](#错误处理)
8. [GraphQL变更操作](#graphql变更操作)
9. [性能考虑](#性能考虑)

## 迁移概述

将REST API调用转换为GraphQL查询需要理解两种技术的差异：

- REST API：使用多个端点，每个端点返回预定义的数据结构
- GraphQL：使用单个端点，客户端指定需要的精确数据

迁移流程：
1. 识别旧版应用中的REST API调用点
2. 分析每个API调用获取的数据
3. 创建等效的GraphQL查询
4. 使用GraphQL片段组织代码
5. 更新客户端代码使用新的GraphQL查询

## REST API与GraphQL对比

| REST API | GraphQL |
|----------|---------|
| 多个端点 | 单个端点 |
| 服务器决定返回数据结构 | 客户端指定需要的数据 |
| 可能会过度获取或获取不足 | 精确获取所需数据 |
| 使用HTTP方法表示操作 | 使用查询和变更表示操作 |
| 使用嵌套资源URL表示关系 | 使用嵌套字段表示关系 |
| 使用查询参数进行过滤和排序 | 使用查询变量进行过滤和排序 |

## REST API端点映射

### 基础端点映射

| REST API端点 | GraphQL查询/变更 |
|-------------|----------------|
| GET /wp/v2/posts | query { posts { nodes { ... } } } |
| GET /wp/v2/posts/{id} | query { post(id: ID, idType: DATABASE_ID) { ... } } |
| GET /wp/v2/posts?slug={slug} | query { post(id: "slug", idType: SLUG) { ... } } |
| GET /wp/v2/categories | query { categories { nodes { ... } } } |
| GET /wp/v2/tags | query { tags { nodes { ... } } } |
| GET /wp/v2/users/{id} | query { user(id: ID, idType: DATABASE_ID) { ... } } |
| GET /wp/v2/media/{id} | query { mediaItem(id: ID, idType: DATABASE_ID) { ... } } |
| GET /wp/v2/search?search={term} | query { posts(where: { search: "term" }) { ... } } |
| GET /menus/v1/locations/{location} | query { menus(where: { location: LOCATION }) { ... } } |

## 查询参数转换

### 常见REST查询参数转换为GraphQL

| REST 参数 | GraphQL 等效 |
|----------|-------------|
| per_page=10 | first: 10 |
| page=2 | after: "..." (游标分页) |
| _embed=true | 在GraphQL中自动支持嵌入关系 |
| orderby=date&order=desc | where: { orderby: { field: DATE, order: DESC } } |
| categories=5 | where: { categoryId: 5 } |
| tags=12 | where: { tagId: 12 } |
| author=2 | where: { authorId: 2 } |
| search=关键词 | where: { search: "关键词" } |

## 常见REST API调用转换示例

### 获取文章列表

#### REST API
```javascript
// 旧版API
const posts = await fetchAPI<WPPost[]>('/wp/v2/posts', {
  params: { 
    _embed: true,
    per_page: 10,
    page: 1,
    orderby: 'date',
    order: 'desc'
  }
});
```

#### GraphQL
```javascript
// 新版GraphQL
const { data } = await client.query({
  query: GET_LATEST_POSTS,
  variables: { first: 10 }
});
const posts = data.posts.nodes;
```

### 获取单篇文章

#### REST API
```javascript
// 通过ID获取
const post = await fetchAPI<WPPost>(`/wp/v2/posts/${id}?_embed=true`);

// 通过slug获取
const posts = await fetchAPI<WPPost[]>(`/wp/v2/posts?slug=${slug}&_embed=true`);
const post = posts[0];
```

#### GraphQL
```javascript
// 通过ID获取
const { data } = await client.query({
  query: GET_POST_BY_ID,
  variables: { id }
});
const post = data.post;

// 通过slug获取
const { data } = await client.query({
  query: GET_POST_BY_SLUG,
  variables: { slug }
});
const post = data.post;
```

### 获取分类下的文章

#### REST API
```javascript
const posts = await fetchAPI<WPPost[]>('/wp/v2/posts', {
  params: {
    categories: categoryId,
    _embed: true,
    per_page: 10,
    page: 1
  }
});
```

#### GraphQL
```javascript
const { data } = await client.query({
  query: GET_POSTS_BY_CATEGORY,
  variables: { 
    categoryId: parseInt(categoryId),
    first: 10
  }
});
const posts = data.posts.nodes;
```

### 搜索文章

#### REST API
```javascript
const results = await fetchAPI<WPPost[]>('/wp/v2/posts', {
  params: { 
    search: searchTerm,
    _embed: true,
    per_page: 10 
  }
});
```

#### GraphQL
```javascript
const { data } = await client.query({
  query: SEARCH_POSTS,
  variables: { 
    search: searchTerm,
    first: 10
  }
});
const results = data.posts.nodes;
```

### 获取菜单

#### REST API
```javascript
const menu = await fetchAPI<{items: WPMenuItem[]}>(`/menus/v1/locations/${location}`);
```

#### GraphQL
```javascript
const { data } = await client.query({
  query: GET_MENU_BY_LOCATION,
  variables: { location: location.toUpperCase() }
});
const menu = data.menus.nodes[0];
```

## 使用片段优化查询

GraphQL片段允许重用查询的部分，使代码更简洁、更易维护。

```javascript
// 定义片段
export const POST_FRAGMENT = gql`
  fragment PostFields on Post {
    id
    title
    date
    slug
    excerpt
    featuredImage {
      node {
        sourceUrl
        altText
      }
    }
    categories {
      nodes {
        id
        name
        slug
      }
    }
  }
`;

// 在查询中使用片段
export const GET_LATEST_POSTS = gql`
  query GetLatestPosts($first: Int = 10) {
    posts(first: $first, where: { orderby: { field: DATE, order: DESC } }) {
      nodes {
        ...PostFields
      }
    }
  }
  ${POST_FRAGMENT}
`;
```

## 错误处理

### REST API错误处理
```javascript
try {
  const posts = await fetchAPI<WPPost[]>('/wp/v2/posts');
  // 处理成功响应
} catch (error) {
  // 处理错误
  console.error('获取文章失败:', error);
}
```

### GraphQL错误处理
```javascript
try {
  const { data, errors } = await client.query({
    query: GET_LATEST_POSTS
  });
  
  if (errors) {
    // 处理GraphQL错误
    console.error('GraphQL错误:', errors);
    return;
  }
  
  // 处理成功响应
  const posts = data.posts.nodes;
} catch (error) {
  // 处理网络错误
  console.error('查询执行失败:', error);
}
```

## GraphQL变更操作

GraphQL使用变更(mutations)来执行数据修改操作。

### 用户登录

#### REST API
```javascript
const loginData = await fetchAPI('/jwt-auth/v1/token', {
  method: 'POST',
  data: { username, password }
});
```

#### GraphQL
```javascript
const { data } = await client.mutate({
  mutation: LOGIN_USER,
  variables: { username, password }
});
const { authToken, user } = data.login;
```

### 创建评论

#### REST API
```javascript
const newComment = await fetchAPI('/wp/v2/comments', {
  method: 'POST',
  data: { 
    post: postId,
    content,
    author_name: name,
    author_email: email
  }
});
```

#### GraphQL
```javascript
const { data } = await client.mutate({
  mutation: CREATE_COMMENT,
  variables: { 
    postId,
    content,
    authorName: name,
    authorEmail: email
  }
});
const success = data.createComment.success;
const comment = data.createComment.comment;
```

## 性能考虑

使用GraphQL时的性能优化：

1. **请求仅需要的数据**：只查询组件真正需要的字段
2. **批量查询**：合并多个请求为单个查询
3. **使用片段**：重用查询部分
4. **分页处理**：使用`first`和`after`参数实现有效分页
5. **Apollo缓存**：利用Apollo Client的缓存功能
6. **预取数据**：在用户可能需要数据之前预先加载
7. **避免过度获取**：避免请求过多嵌套数据 