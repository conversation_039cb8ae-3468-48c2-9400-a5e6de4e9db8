<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文章点赞功能实现文档</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3, h4 {
            color: #1a1a1a;
        }
        h1 {
            text-align: center;
            border-bottom: 2px solid #eaeaea;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }
        h2 {
            border-bottom: 1px solid #eaeaea;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        h3 {
            margin-top: 30px;
        }
        pre {
            background-color: #f6f8fa;
            border-radius: 5px;
            padding: 15px;
            overflow: auto;
            font-family: 'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        code {
            font-family: 'SFMono-Regular', <PERSON>sol<PERSON>, 'Liberation Mono', Menlo, monospace;
            background-color: #f6f8fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 0.9em;
        }
        .container {
            display: flex;
            margin-top: 30px;
        }
        .col {
            flex: 1;
            padding: 0 15px;
        }
        .flow-diagram {
            text-align: center;
            margin: 30px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
        }
        th {
            background-color: #f6f8fa;
        }
        .highlight {
            background-color: #fffde7;
            padding: 2px;
        }
        .note {
            background-color: #e8f4fd;
            padding: 15px;
            border-left: 4px solid #2196f3;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>文章点赞功能实现文档</h1>
    
    <div class="note">
        本文档详细描述了基于 WordPress Headless CMS 的文章点赞功能的前后端实现方案。前端使用 Next.js + Apollo Client，后端使用 WordPress + WPGraphQL。
    </div>

    <h2>1. 功能概述</h2>
    <p>文章点赞功能允许已登录用户对文章进行点赞和取消点赞操作，同时显示文章的总点赞数。主要包括：</p>
    <ul>
        <li>点赞/取消点赞操作</li>
        <li>点赞状态实时更新</li>
        <li>点赞数量统计与显示</li>
        <li>用户登录状态检查</li>
    </ul>

    <div class="flow-diagram">
        <img src="https://mermaid.ink/img/pako:eNqNVMtu2zAQ_BVip7ZAH5ZTpEFzqNMGQYMCRdFDLgZXXEtEKFIlKdtK4H8vKdGSY8d1c-DOcGd2uXwgkXJCYrJTJWcbwRQvYa0ULKlWVELGtCrBcCWBwY5JDnvQHLRgZQUVE7BjZQEHLmEDVAJnBRdMQsYUh5xJWDFZgVYGNGzZRsGKFhVoZjbKkJhcTcYTuL4eTa6vJuPpJWTMcKYMWJmDgaKCHHZQMrGBB6ZLMJRDLtdgCvvYBRVaGbCv8Ky4NhvI2YZXsGG6hJxJoYzVbcCKCXBwmYMxXALXoKmAjYGCGvdIwZTKQVMJK1pSoYGVoFkOjGpYUl3RnL5tOVWwZmILGdNLXtHSQMmM3XDYcQMPVBjYsNK-6qLvwRjYM7WlZQGaCVhSbcA4GQPLd9QYKLmCJS2ZgJJKJpbw8_nLw-Tj_dPz7PF59vjp8-zLbP7wMJvPSUzG0-vJ5GYcxRfDOL4YDuPBIL4YDQbD4fDyKo7-Rg1GUXQZxVEcRYM4HkTDKBpG8TAaRMPBcBDFg2gYXTj3g1E0GkXxIIrjaDAYDIZxPIxG_xzGJCZKlzRnW-eLZcWlq7vSFdvZzDXLmS7ZVrhGu2Jb7rrsiuVMCNdnV2ynXJNdsa1wLXbFdso12RX7dM4_Lk4Ozo7Ojw9Pjk9P_wNJXYg8" alt="点赞功能流程图">
    </div>

    <h2>2. 数据结构设计</h2>
    
    <h3>2.1 数据库表设计</h3>
    <p>在 WordPress 数据库中创建自定义表 <code>wp_likes</code> 存储点赞数据：</p>
    <pre>
CREATE TABLE wp_likes (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    user_id bigint(20) NOT NULL,
    post_id bigint(20) NOT NULL,
    status tinyint(1) NOT NULL DEFAULT 1,
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY user_post (user_id, post_id),
    KEY post_id (post_id),
    KEY user_id (user_id)
);</pre>

    <h3>2.2 缓存设计</h3>
    <p>为提高性能，使用 WordPress 文章元数据缓存点赞数：</p>
    <ul>
        <li>使用 <code>_fd_likes_count</code> 文章元数据存储点赞总数</li>
        <li>设置定期任务更新点赞缓存</li>
        <li>在点赞状态变更时触发缓存更新</li>
    </ul>

    <h2>3. 后端实现</h2>

    <h3>3.1 核心功能实现</h3>
    <p>在 <code>like-core.php</code> 中实现核心功能：</p>
    <pre>
/**
 * 添加/更新用户点赞
 * 
 * @param int $user_id 用户ID
 * @param int $post_id 文章ID
 * @param int $status 点赞状态: 1 点赞, 0 取消点赞
 * @return bool|int 成功返回记录ID，失败返回false
 */
function fd_member_set_like_status($user_id, $post_id, $status = 1) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'likes';
    
    // 参数验证
    $user_id = absint($user_id);
    $post_id = absint($post_id);
    $status = absint($status);
    
    if (!$user_id || !$post_id) {
        return false;
    }
    
    // 检查用户和文章是否存在
    if (!get_user_by('id', $user_id) || !get_post($post_id)) {
        return false;
    }
    
    // 查询是否已存在记录
    $existing = $wpdb->get_row(
        $wpdb->prepare(
            "SELECT * FROM $table_name WHERE user_id = %d AND post_id = %d",
            $user_id,
            $post_id
        )
    );
    
    if ($existing) {
        // 如果状态相同，直接返回ID
        if ((int)$existing->status === $status) {
            return $existing->id;
        }
        
        // 更新状态
        $result = $wpdb->update(
            $table_name,
            ['status' => $status],
            ['id' => $existing->id],
            ['%d'],
            ['%d']
        );
        
        if ($result !== false) {
            do_action('fd_member_like_updated', $user_id, $post_id, $status);
            return $existing->id;
        }
        
        return false;
    } else {
        // 插入新记录
        $result = $wpdb->insert(
            $table_name,
            [
                'user_id' => $user_id,
                'post_id' => $post_id,
                'status' => $status
            ],
            ['%d', '%d', '%d']
        );
        
        if ($result) {
            $like_id = $wpdb->insert_id;
            do_action('fd_member_like_added', $user_id, $post_id, $status);
            return $like_id;
        }
        
        return false;
    }
}

/**
 * 添加点赞
 */
function fd_member_like_post($user_id, $post_id) {
    return fd_member_set_like_status($user_id, $post_id, 1);
}

/**
 * 取消点赞
 */
function fd_member_unlike_post($user_id, $post_id) {
    return fd_member_set_like_status($user_id, $post_id, 0);
}</pre>

    <h3>3.2 点赞统计与缓存</h3>
    <p>在 <code>like-stats.php</code> 中实现点赞统计与缓存：</p>
    <pre>
/**
 * 获取文章点赞数（优先使用缓存）
 * 
 * @param int $post_id 文章ID
 * @param bool $refresh 是否强制刷新缓存
 * @return int 点赞数
 */
function fd_member_get_post_likes_count($post_id, $refresh = false) {
    $post_id = absint($post_id);
    if (!$post_id) {
        return 0;
    }
    
    if ($refresh) {
        return fd_member_update_post_likes_count_cache($post_id);
    }
    
    $cached_count = get_post_meta($post_id, '_fd_likes_count', true);
    
    if ($cached_count === '') {
        // 如果缓存不存在，则更新缓存
        return fd_member_update_post_likes_count_cache($post_id);
    }
    
    return (int)$cached_count;
}

/**
 * 更新文章点赞数缓存
 */
function fd_member_update_post_likes_count_cache($post_id) {
    $post_id = absint($post_id);
    if (!$post_id) {
        return 0;
    }
    
    $likes_count = fd_member_calculate_likes_count($post_id);
    update_post_meta($post_id, '_fd_likes_count', $likes_count);
    
    return $likes_count;
}

// 添加点赞相关钩子
add_action('fd_member_like_added', 'fd_member_update_cache_on_like_change', 10, 3);
add_action('fd_member_like_updated', 'fd_member_update_cache_on_like_change', 10, 3);</pre>

    <h3>3.3 GraphQL API 实现</h3>
    <p>在 <code>like-graphql.php</code> 中实现 GraphQL API：</p>
    <pre>
// 注册点赞变更 (Mutation)
register_graphql_mutation('likePost', [
    'inputFields' => [
        'postId' => [
            'type' => ['non_null' => 'Int'],
            'description' => '要点赞的文章ID'
        ]
    ],
    'outputFields' => [
        'success' => [
            'type' => 'Boolean',
            'description' => '操作是否成功'
        ],
        'message' => [
            'type' => 'String',
            'description' => '操作结果消息'
        ],
        'likeStatus' => [
            'type' => 'LikeStatus',
            'description' => '更新后的点赞状态',
            'resolve' => function($payload) {
                if (!isset($payload['post_id'])) {
                    return null;
                }
                
                $post_id = $payload['post_id'];
                $user_id = get_current_user_id();
                
                return [
                    'postId' => $post_id,
                    'likesCount' => fd_member_get_post_likes_count($post_id),
                    'userHasLiked' => $user_id ? fd_member_user_has_liked($user_id, $post_id) : false
                ];
            }
        ]
    ],
    'mutateAndGetPayload' => function($input) {
        if (!is_user_logged_in()) {
            throw new \GraphQL\Error\UserError('请先登录后再点赞');
        }
        
        $user_id = get_current_user_id();
        $post_id = absint($input['postId']);
        
        $post = get_post($post_id);
        if (!$post) {
            throw new \GraphQL\Error\UserError('文章不存在');
        }
        
        $result = fd_member_like_post($user_id, $post_id);
        
        if (!$result) {
            return [
                'success' => false,
                'message' => '点赞失败，请稍后重试',
                'post_id' => $post_id
            ];
        }
        
        return [
            'success' => true,
            'message' => '点赞成功',
            'post_id' => $post_id
        ];
    }
]);</pre>

    <h2>4. 前端实现</h2>

    <h3>4.1 GraphQL 查询与变更</h3>
    <p>在 <code>mutations.ts</code> 中定义 GraphQL 变更：</p>
    <pre>
// 点赞文章
export const LIKE_POST = gql`
  mutation LikePost($postId: Int!) {
    likePost(input: { postId: $postId }) {
      success
      likeStatus {
        likesCount
        userHasLiked
      }
    }
  }
`;

// 取消点赞文章
export const UNLIKE_POST = gql`
  mutation UnlikePost($postId: Int!) {
    unlikePost(input: { postId: $postId }) {
      success
      likeStatus {
        likesCount
        userHasLiked
      }
    }
  }
`;</pre>

    <h3>4.2 自定义 Hook 实现</h3>
    <p>在 <code>useLikeAction.ts</code> 中实现点赞 Hook：</p>
    <pre>
import { useMutation, gql, ApolloCache, ApolloError } from '@apollo/client';
import React, { useContext } from 'react';
import { LIKE_POST, UNLIKE_POST } from '../lib/graphql/mutations';
import { AuthContext } from '../contexts/AuthContext';
import { Post } from '../types/post';

export const useLikeAction = ({ id, postId, currentIsLiked, currentLikesCount }: UseLikeActionOptions) => {
  const authContext = useContext(AuthContext);

  if (!authContext) {
    throw new Error('useLikeAction must be used within an AuthProvider');
  }

  const { isAuthenticated } = authContext;

  const [likePost, { loading: likeLoading }] = useMutation<LikePostPayload>(LIKE_POST, {
    update: (cache: ApolloCache<LikePostPayload>, { data }) => {
      console.log('[useLikeAction] Like mutation update function called.', data);
      if (data?.likePost?.success) {
        const { likesCount, userHasLiked } = data.likePost.likeStatus;
        cache.writeFragment({
          id: `Post:${id}`,
          fragment: gql`
            fragment UpdateLikeStatus on Post {
              likesCount
              userHasLiked
            }
          `,
          data: {
            likesCount: likesCount,
            userHasLiked: userHasLiked,
          },
        });
      }
    }
  });

  const [unlikePost, { loading: unlikeLoading }] = useMutation<UnlikePostPayload>(UNLIKE_POST, {
    update: (cache: ApolloCache<UnlikePostPayload>, { data }) => {
      if (data?.unlikePost?.success) {
        const { likesCount, userHasLiked } = data.unlikePost.likeStatus;
        cache.writeFragment({
          id: `Post:${id}`,
          fragment: gql`
            fragment UpdateUnlikeStatus on Post {
              likesCount
              userHasLiked
            }
          `,
          data: {
            likesCount: likesCount,
            userHasLiked: userHasLiked,
          },
        });
      }
    }
  });

  const handleLike = () => {
    if (!isAuthenticated) {
      return;
    }
    likePost({ 
      variables: { postId },
      optimisticResponse: {
        likePost: {
          __typename: 'LikePostPayload',
          success: true,
          likeStatus: {
            likesCount: currentLikesCount + 1,
            userHasLiked: true,
          }
        },
      }
    });
  };

  const handleUnlike = () => {
    if (!isAuthenticated) {
      return;
    }
    unlikePost({ 
      variables: { postId },
      optimisticResponse: {
        unlikePost: {
          __typename: 'UnlikePostPayload',
          success: true,
          likeStatus: {
            likesCount: currentLikesCount - 1,
            userHasLiked: false,
          }
        },
      }
    });
  };
  
  return {
    handleLike,
    handleUnlike,
    loading: likeLoading || unlikeLoading,
  };
};</pre>

    <h3>4.3 点赞按钮组件</h3>
    <p>在 <code>LikeButton.tsx</code> 中实现点赞按钮组件：</p>
    <pre>
'use client';

import React, { useContext } from 'react';
import { useQuery, gql } from '@apollo/client';
import { useLikeAction } from '@/hooks/useLikeAction';
import { AuthContext } from '@/contexts/AuthContext';
import { ThumbsUp } from 'lucide-react';

interface LikeButtonProps {
  id: string;
  postId: number;
  initialLikesCount: number;
  initialIsLiked: boolean;
}

const LIKE_BUTTON_QUERY = gql`
  query PostLikeStatus($postId: ID!) {
    post(id: $postId, idType: DATABASE_ID) {
      id
      databaseId
      likesCount
      userHasLiked
    }
  }
`;

export const LikeButton: React.FC<LikeButtonProps> = ({ id, postId, initialLikesCount, initialIsLiked }) => {
  const authContext = useContext(AuthContext);

  // 使用 useQuery 来订阅缓存变化
  const { data } = useQuery(LIKE_BUTTON_QUERY, {
    variables: { postId: String(postId) },
    skip: !postId
  });
  
  const isLiked = data?.post?.userHasLiked ?? initialIsLiked;
  const likesCount = data?.post?.likesCount ?? initialLikesCount;

  const { handleLike, handleUnlike, loading } = useLikeAction({
    id,
    postId,
    currentIsLiked: isLiked,
    currentLikesCount: likesCount,
  });
  
  const handleClick = () => {
    if (loading) {
      return;
    }

    if (!authContext?.isAuthenticated) {
      alert('请先登录再点赞');
      return;
    }

    if (isLiked) {
      handleUnlike();
    } else {
      handleLike();
    }
  };

  return (
    <button
      onClick={handleClick}
      disabled={loading || !authContext?.isAuthenticated}
      className={`flex items-center space-x-2 text-gray-600 hover:text-red-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed`}
      aria-label={isLiked ? '取消点赞' : '点赞'}
    >
      <ThumbsUp
        className={`w-5 h-5 mr-1 ${isLiked ? 'text-blue-600' : 'text-gray-500'}`}
        fill={isLiked ? 'currentColor' : 'none'}
      />
      <span className="font-semibold text-lg">{likesCount}</span>
    </button>
  );
};</pre>

    <h3>4.4 在文章模板中使用</h3>
    <p>在 <code>StandardTemplate.tsx</code> 中使用点赞按钮：</p>
    <pre>
// 点赞按钮
&lt;div className="like-section mt-6 pt-4 border-t"&gt;
  &lt;LikeButton
    id={post.id}
    postId={post.databaseId}
    initialIsLiked={post.userHasLiked}
    initialLikesCount={post.likesCount}
  /&gt;
&lt;/div&gt;</pre>

    <h2>5. 实时更新机制</h2>
    
    <h3>5.1 Apollo 缓存管理</h3>
    <p>点赞状态的实时更新主要通过 Apollo Client 的缓存管理实现：</p>
    <ul>
        <li>使用 <code>cache.writeFragment</code> 更新缓存中的点赞状态</li>
        <li>组件通过 <code>useQuery</code> 订阅缓存变化</li>
        <li>缓存更新时组件自动重新渲染</li>
    </ul>

    <h3>5.2 乐观更新</h3>
    <p>为提升用户体验，实现了乐观更新（Optimistic UI）：</p>
    <ul>
        <li>用户点击按钮后立即更新 UI，不等待服务器响应</li>
        <li>通过 <code>optimisticResponse</code> 提供预期的响应数据</li>
        <li>如果服务器返回错误，Apollo Client 会自动回滚 UI 状态</li>
    </ul>

    <h2>6. 性能优化</h2>
    
    <h3>6.1 后端优化</h3>
    <ul>
        <li>使用文章元数据缓存点赞数，减少数据库查询</li>
        <li>设置唯一索引，提高查询效率</li>
        <li>使用计划任务批量更新缓存，分散数据库压力</li>
    </ul>

    <h3>6.2 前端优化</h3>
    <ul>
        <li>使用 Apollo 缓存减少网络请求</li>
        <li>实现乐观更新提升用户体验</li>
        <li>组件按需重新渲染，避免不必要的渲染</li>
    </ul>

    <h2>7. 安全性考虑</h2>
    <ul>
        <li>严格的用户身份验证</li>
        <li>使用 WordPress 内置函数进行数据清理和验证</li>
        <li>防止 SQL 注入攻击</li>
        <li>限制未登录用户的操作</li>
    </ul>

    <h2>8. 总结</h2>
    <p>文章点赞功能通过前后端分离架构实现，后端提供 GraphQL API，前端通过 Apollo Client 消费 API。整体设计注重性能、用户体验和数据一致性，实现了高效、可靠的点赞功能。</p>
    
    <div class="note">
        <p><strong>主要特点：</strong></p>
        <ul>
            <li>高性能设计：缓存机制、批量更新、乐观 UI</li>
            <li>良好的用户体验：实时反馈、状态同步</li>
            <li>数据一致性：唯一索引、钩子机制、清理机制</li>
            <li>完整的前后端分离架构：GraphQL API、Apollo Client</li>
        </ul>
    </div>
</body>
</html>
