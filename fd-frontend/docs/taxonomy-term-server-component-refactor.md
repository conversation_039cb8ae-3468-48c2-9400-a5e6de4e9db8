# 自定义分类法术语页面服务器组件改造与SEO集成

## 背景与目标

参照分类页面和标签页面的成功改造，对自定义分类法术语页面进行同样的服务器组件+客户端组件架构改造，并集成完整的SEO支持。

### 改造前的问题
- **性能问题**：完全依赖客户端Hook获取数据，首屏加载慢
- **SEO缺失**：无服务器端渲染的SEO元数据
- **分页问题**：可能存在与其他页面类似的分页逻辑问题
- **搜索功能冗余**：客户端搜索功能与整体架构不符
- **复杂的Hook依赖**：依赖多个Hook进行数据获取

### 改造目标
1. **首屏性能优化**：服务器端获取首屏数据
2. **完整SEO支持**：AI生成的SEO元数据、Open Graph、Twitter Card、JSON-LD
3. **统一分页逻辑**：与分类和标签页面保持一致的分页实现
4. **简化客户端逻辑**：移除搜索功能，专注于文章展示和分页
5. **支持多种分类法**：兼容Category、Tag和其他自定义分类法

## 改造方案

### 架构设计

| 模块 | 职责 | 文件位置 |
|------|------|----------|
| **服务器组件** | 1. 动态获取分页设置<br/>2. 获取分类法信息<br/>3. GraphQL获取术语数据和首屏文章<br/>4. generateMetadata输出SEO<br/>5. JSON-LD结构化数据注入 | `src/app/taxonomy/[taxonomy]/[slug]/page.tsx` |
| **客户端组件** | 1. 渲染页面UI<br/>2. 智能分页加载更多<br/>3. 视图模式切换<br/>4. 本地偏好存储 | `src/components/pages/TaxonomyTermClientPage.tsx` |

## 实施步骤

### 1. 更新GraphQL查询

首先为自定义分类法术语添加AI SEO字段支持：

```typescript
// fd-frontend/src/lib/graphql/queries.ts
export const GET_TAXONOMY_TERM = gql`
  query GetTaxonomyTerm($taxonomy: TaxonomyEnum!, $slug: [String]!) {
    terms(where: { taxonomies: [$taxonomy], slug: $slug }) {
      nodes {
        __typename
        id
        databaseId
        name
        slug
        description
        uri
        # AI SEO 字段
        aiSeoTitle
        aiSeoDescription
        aiSeoJsonLd
        ... on Category {
          count
          children { nodes { id name slug uri } }
        }
        ... on Tag {
          count
        }
      }
    }
  }
`;

// 新增专门的术语详情查询，包含文章数据
export const GET_TAXONOMY_TERM_DETAIL = gql`
  query GetTaxonomyTermDetail($taxonomy: TaxonomyEnum!, $slug: [String]!, $first: Int = 10) {
    terms(where: { taxonomies: [$taxonomy], slug: $slug }) {
      nodes {
        # 完整的术语字段和文章数据
        posts(first: $first) {
          nodes { ...PostFields }
          pageInfo { hasNextPage endCursor }
        }
      }
    }
  }
`;
```

### 2. 创建服务器组件

完全重写 `src/app/taxonomy/[taxonomy]/[slug]/page.tsx`：

```typescript
import { notFound } from 'next/navigation';
import TaxonomyTermClientPage from '@/components/pages/TaxonomyTermClientPage';

// 获取分页设置
async function fetchPostsPerPageSetting(): Promise<number> {
  // 动态获取分页设置，确保与其他页面一致
}

// 获取分类法信息
async function fetchTaxonomyInfo(taxonomyName: string) {
  const query = `
    query GetTaxonomy($name: ID!) {
      taxonomy(id: $name, idType: NAME) {
        id name label description hierarchical restBase
      }
    }
  `;
  // GraphQL查询实现
}

// 获取自定义分类法术语页面数据
async function fetchTaxonomyTermPageData(taxonomyName: string, termSlug: string, postsPerPage: number) {
  // 将分类法名称转换为大写（GraphQL枚举格式）
  const taxonomyEnum = taxonomyName.toUpperCase();
  
  const query = `
    query GetTaxonomyTermDetail($taxonomy: TaxonomyEnum!, $slug: [String]!, $first: Int) {
      terms(where: { taxonomies: [$taxonomy], slug: $slug }) {
        nodes {
          __typename id databaseId name slug description uri
          aiSeoTitle aiSeoDescription aiSeoJsonLd
          ... on Category {
            count
            posts(first: $first) { nodes { /* 完整文章字段 */ } pageInfo { hasNextPage endCursor } }
            children { nodes { id name slug uri } }
          }
          ... on Tag {
            count
            posts(first: $first) { nodes { /* 完整文章字段 */ } pageInfo { hasNextPage endCursor } }
          }
        }
      }
    }
  `;
  // GraphQL查询实现
}

// SEO元数据生成
export async function generateMetadata({ params }) {
  const postsPerPage = await fetchPostsPerPageSetting();
  const taxonomyInfo = await fetchTaxonomyInfo(params.taxonomy);
  const term = await fetchTaxonomyTermPageData(params.taxonomy, params.slug, postsPerPage);
  
  if (!term || !taxonomyInfo) {
    return { title: '条目未找到 - Future Decade' };
  }

  const metaTitle = term.aiSeoTitle || `${term.name} - ${taxonomyInfo.label}`;
  const metaDescription = term.aiSeoDescription || 
    (term.description ? term.description.replace(/<[^>]*>/g, '').substring(0, 160) : 
     `${term.name}相关内容 - Future Decade`);
  
  return {
    title: `${metaTitle} - Future Decade`,
    description: metaDescription,
    alternates: { canonical: `https://www.futuredecade.com/taxonomy/${params.taxonomy}/${term.slug}` },
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      url: `https://www.futuredecade.com/taxonomy/${params.taxonomy}/${term.slug}`,
      siteName: 'Future Decade',
      type: 'website',
      images: [{ url: defaultImage, width: 1200, height: 630 }],
      locale: 'zh_CN',
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: [defaultImage],
      site: '@FutureDecade',
    },
  };
}

// 主组件
export default async function TaxonomyTermPage({ params }) {
  const postsPerPage = await fetchPostsPerPageSetting();
  const taxonomyInfo = await fetchTaxonomyInfo(params.taxonomy);
  const term = await fetchTaxonomyTermPageData(params.taxonomy, params.slug, postsPerPage);

  if (!term || !taxonomyInfo) return notFound();

  // JSON-LD结构化数据处理
  let jsonLd = null;
  if (term.aiSeoJsonLd) {
    try {
      jsonLd = JSON.parse(term.aiSeoJsonLd);
      jsonLd.url = `https://www.futuredecade.com/taxonomy/${params.taxonomy}/${term.slug}`;
    } catch (error) {
      console.error('Failed to parse JSON-LD:', error);
    }
  }

  return (
    <>
      {jsonLd && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
      )}
      <TaxonomyTermClientPage
        initialTaxonomy={taxonomyInfo}
        initialTerm={term}
        initialPosts={term.posts.nodes}
        initialPageInfo={term.posts.pageInfo}
      />
    </>
  );
}
```

### 3. 创建客户端组件

新建 `src/components/pages/TaxonomyTermClientPage.tsx`：

```typescript
'use client';

export default function TaxonomyTermClientPage({
  initialTaxonomy: taxonomy,
  initialTerm: term,
  initialPosts,
  initialPageInfo,
}) {
  // 状态管理
  const [allPosts, setAllPosts] = useState(initialPosts);
  const [currentPageInfo, setCurrentPageInfo] = useState(initialPageInfo || null);
  const [loadingMore, setLoadingMore] = useState(false);
  const [viewMode, setViewMode] = useState('list');

  // 智能分页加载（根据术语类型选择不同查询）
  const loadMorePosts = useCallback(async (afterCursor: string) => {
    const isCategory = term.__typename === 'Category';
    const isTag = term.__typename === 'Tag';
    
    let query = '';
    let variables = { first: postsPerPage, after: afterCursor };
    
    if (isCategory) {
      query = `query GetCategoryPosts($categoryId: Int!, $first: Int, $after: String) { ... }`;
      variables.categoryId = term.databaseId;
    } else if (isTag) {
      query = `query GetTagPosts($tagId: String!, $first: Int, $after: String) { ... }`;
      variables.tagId = term.databaseId.toString();
    } else {
      // 其他自定义分类法的通用查询
      query = `query GetTaxonomyTermPosts($taxonomy: TaxonomyEnum!, $termId: ID!, $first: Int, $after: String) { ... }`;
      variables.taxonomy = taxonomy.name.toUpperCase();
      variables.termId = term.id;
    }
    
    // 执行GraphQL查询并返回结果
  }, [taxonomy.name, term.__typename, term.databaseId, term.id]);

  // 加载更多处理
  const handleLoadMore = useCallback(async () => {
    if (!currentPageInfo?.hasNextPage || loadingMore) return;
    
    try {
      setLoadingMore(true);
      const result = await loadMorePosts(currentPageInfo.endCursor);
      
      if (result) {
        // 合并新文章
        setAllPosts((prev) => {
          const map = new Map();
          prev.forEach((p) => map.set(p.id, p));
          result.nodes.forEach((p) => map.set(p.id, p));
          return Array.from(map.values());
        });
        
        // 更新分页信息
        setCurrentPageInfo(result.pageInfo);
      }
    } catch (error) {
      console.error('加载更多文章出错:', error);
    } finally {
      setLoadingMore(false);
    }
  }, [currentPageInfo, loadingMore, loadMorePosts]);

  return (
    <MainLayout>
      <div className="container mx-auto py-8 px-4">
        {/* 面包屑导航 */}
        <div className="flex items-center gap-2 text-sm text-gray-500 mb-2">
          <Link href={`/taxonomy/${taxonomy.name}`}>{taxonomy.label}</Link>
          <span>›</span>
          <span>{term.name}</span>
        </div>
        
        {/* 术语标题和描述 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">{term.name}</h1>
          {term.description && <div dangerouslySetInnerHTML={{ __html: term.description }} />}
          <div className="text-sm text-gray-500">共 {term.count} 篇文章</div>
        </div>

        {/* 视图切换器 */}
        <ViewModeSwitcher currentMode={viewMode} onChange={handleViewModeChange} />

        {/* 文章列表 */}
        <InfiniteScroll
          hasMore={currentPageInfo?.hasNextPage ?? false}
          loading={loadingMore}
          onLoadMore={handleLoadMore}
          totalCount={term.count}
        >
          <ArticleListView articles={allPosts} mode={viewMode} />
        </InfiniteScroll>
      </div>
    </MainLayout>
  );
}
```

## 关键改进

### 1. 移除的功能
- **客户端搜索**：移除了搜索框和过滤逻辑
- **复杂的Hook依赖**：不再依赖 `useTaxonomy` 和 `useTaxonomyTerm`
- **多重状态管理**：简化了状态管理逻辑

### 2. 新增的功能
- **服务器端渲染**：首屏数据在服务器端获取
- **完整SEO支持**：AI生成的元数据、社交媒体标签、结构化数据
- **WPGraphQL Tax Query集成**：使用专业插件进行自定义分类法查询
- **智能分页逻辑**：根据术语类型选择最优的GraphQL查询策略
- **多分类法支持**：兼容Category、Tag和其他自定义分类法

### 3. 技术亮点
- **AI内容优先**：优先使用AI生成的SEO标题和描述
- **智能图片选择**：自动使用术语第一篇文章的特色图片
- **URL一致性**：JSON-LD中的URL自动更新为前端地址
- **分类法枚举转换**：自动将分类法名称转换为GraphQL枚举格式
- **TaxQuery优化**：使用WPGraphQL Tax Query插件进行高效查询
- **分离查询策略**：术语信息和文章数据分别获取，提高缓存效率

## 预期效果

### 性能提升
- **首屏加载速度**：服务器端渲染消除加载状态
- **SEO友好**：搜索引擎可直接抓取完整内容
- **缓存优化**：ISR缓存提升后续访问速度

### SEO优化
- **完整元数据**：title、description、canonical URL
- **社交分享**：Open Graph和Twitter Card支持
- **结构化数据**：JSON-LD帮助搜索引擎理解内容
- **AI内容**：使用AI生成的高质量SEO内容

### 用户体验
- **无缝分页**：统一的无限滚动体验
- **视图切换**：保持用户偏好设置
- **面包屑导航**：清晰的层级结构
- **多分类法支持**：统一的用户界面

## 验证方法

1. **功能测试**：访问不同类型的分类法术语页面，测试分页和视图切换
2. **SEO检查**：查看页面源码，验证所有SEO元素
3. **社交分享**：使用Facebook/Twitter调试工具验证
4. **性能测试**：对比改造前后的加载速度
5. **多分类法测试**：验证Category、Tag和自定义分类法的兼容性

## WPGraphQL Tax Query 集成

### 插件说明
本项目使用 **WPGraphQL Tax Query** 插件来处理自定义分类法的文章查询。该插件提供了强大的 `taxQuery` 字段，支持复杂的分类法查询。

### 查询策略
根据分类法类型采用不同的查询策略：

1. **Category（分类）**：使用原生的 `categoryId` 查询
2. **Tag（标签）**：使用原生的 `tagId` 查询
3. **自定义分类法**：使用 `taxQuery` 进行查询

### TaxQuery 语法示例
```graphql
query GetPostsByTaxQuerySlug($taxonomy: TaxonomyEnum!, $slugs: [String!], $first: Int, $after: String) {
  posts(
    first: $first,
    after: $after,
    where: {
      taxQuery: {
        relation: AND,
        taxArray: [
          {
            taxonomy: $taxonomy,
            operator: IN,
            terms: $slugs,
            field: SLUG
          }
        ]
      }
    }
  ) {
    pageInfo { hasNextPage endCursor }
    nodes { /* 文章字段 */ }
  }
}
```

### 分离查询优势
1. **术语信息查询**：获取分类法术语的基本信息和AI SEO数据
2. **文章数据查询**：使用TaxQuery获取相关文章
3. **缓存优化**：不同类型的数据可以设置不同的缓存策略
4. **性能提升**：避免单个复杂查询，提高响应速度

改造完成后，自定义分类法术语页面将具备与分类和标签页面相同的高性能和完整SEO支持，同时支持多种分类法类型。
