<?php
/**
 * 为分类添加Banner图片字段
 * 
 * 这个文件需要添加到WordPress后端主题或插件中
 * 建议添加到 fd-theme/inc/ 目录下
 */

class FD_Category_Banner_Fields {
    
    public function __construct() {
        // 添加分类表单字段
        add_action('category_add_form_fields', [$this, 'add_category_banner_field']);
        add_action('category_edit_form_fields', [$this, 'edit_category_banner_field']);
        
        // 保存分类字段
        add_action('edited_category', [$this, 'save_category_banner_field']);
        add_action('create_category', [$this, 'save_category_banner_field']);
        
        // 添加管理列表列
        add_filter('manage_edit-category_columns', [$this, 'add_category_columns']);
        add_filter('manage_category_custom_column', [$this, 'add_category_column_content'], 10, 3);
        
        // 加载媒体库脚本
        add_action('admin_enqueue_scripts', [$this, 'enqueue_media_scripts']);
    }
    
    /**
     * 添加分类Banner字段 - 新建分类时
     */
    public function add_category_banner_field() {
        ?>
        <div class="form-field term-banner-wrap">
            <label for="category-banner"><?php _e('Banner图片', 'fd-theme'); ?></label>
            <div class="fd-banner-upload">
                <input type="hidden" id="category-banner" name="category_banner" value="" />
                <div class="fd-banner-preview" style="display: none;">
                    <img src="" alt="Banner预览" style="max-width: 300px; height: auto; border: 1px solid #ddd; padding: 5px;" />
                    <p>
                        <button type="button" class="button fd-remove-banner"><?php _e('移除Banner', 'fd-theme'); ?></button>
                    </p>
                </div>
                <p>
                    <button type="button" class="button fd-upload-banner"><?php _e('选择Banner图片', 'fd-theme'); ?></button>
                </p>
            </div>
            <p class="description">
                <?php _e('为此分类选择一个Banner图片，建议尺寸：1200x400像素。将用于分类页面和分类索引页面的背景展示。', 'fd-theme'); ?>
            </p>
        </div>
        <?php
    }
    
    /**
     * 编辑分类Banner字段 - 编辑分类时
     */
    public function edit_category_banner_field($term) {
        $banner_id = get_term_meta($term->term_id, 'category_banner', true);
        $banner_url = '';
        
        if ($banner_id) {
            $banner_url = wp_get_attachment_image_url($banner_id, 'medium');
        }
        ?>
        <tr class="form-field term-banner-wrap">
            <th scope="row">
                <label for="category-banner"><?php _e('Banner图片', 'fd-theme'); ?></label>
            </th>
            <td>
                <div class="fd-banner-upload">
                    <input type="hidden" id="category-banner" name="category_banner" value="<?php echo esc_attr($banner_id); ?>" />
                    <div class="fd-banner-preview" <?php echo $banner_url ? '' : 'style="display: none;"'; ?>>
                        <img src="<?php echo esc_url($banner_url); ?>" alt="Banner预览" style="max-width: 300px; height: auto; border: 1px solid #ddd; padding: 5px;" />
                        <p>
                            <button type="button" class="button fd-remove-banner"><?php _e('移除Banner', 'fd-theme'); ?></button>
                        </p>
                    </div>
                    <p>
                        <button type="button" class="button fd-upload-banner"><?php _e($banner_url ? '更换Banner图片' : '选择Banner图片', 'fd-theme'); ?></button>
                    </p>
                </div>
                <p class="description">
                    <?php _e('为此分类选择一个Banner图片，建议尺寸：1200x400像素。将用于分类页面和分类索引页面的背景展示。', 'fd-theme'); ?>
                </p>
            </td>
        </tr>
        <?php
    }
    
    /**
     * 保存分类Banner字段
     */
    public function save_category_banner_field($term_id) {
        if (isset($_POST['category_banner'])) {
            $banner_id = intval($_POST['category_banner']);
            if ($banner_id > 0) {
                update_term_meta($term_id, 'category_banner', $banner_id);
            } else {
                delete_term_meta($term_id, 'category_banner');
            }
        }
    }
    
    /**
     * 添加管理列表列
     */
    public function add_category_columns($columns) {
        $new_columns = [];
        foreach ($columns as $key => $value) {
            $new_columns[$key] = $value;
            if ($key === 'name') {
                $new_columns['banner'] = __('Banner', 'fd-theme');
            }
        }
        return $new_columns;
    }
    
    /**
     * 显示管理列表列内容
     */
    public function add_category_column_content($content, $column_name, $term_id) {
        if ($column_name === 'banner') {
            $banner_id = get_term_meta($term_id, 'category_banner', true);
            if ($banner_id) {
                $banner_url = wp_get_attachment_image_url($banner_id, 'thumbnail');
                if ($banner_url) {
                    $content = '<img src="' . esc_url($banner_url) . '" alt="Banner" style="width: 50px; height: auto;" />';
                } else {
                    $content = '<span class="dashicons dashicons-format-image" style="color: #ccc;"></span>';
                }
            } else {
                $content = '<span class="dashicons dashicons-minus" style="color: #ccc;"></span>';
            }
        }
        return $content;
    }
    
    /**
     * 加载媒体库脚本
     */
    public function enqueue_media_scripts($hook) {
        if ($hook === 'edit-tags.php' || $hook === 'term.php') {
            wp_enqueue_media();
            wp_enqueue_script(
                'fd-category-banner',
                get_template_directory_uri() . '/assets/js/admin/category-banner.js',
                ['jquery'],
                '1.0.0',
                true
            );
        }
    }
}

// 初始化
new FD_Category_Banner_Fields();

/**
 * 获取分类Banner图片的辅助函数
 */
function fd_get_category_banner($category_id, $size = 'full') {
    $banner_id = get_term_meta($category_id, 'category_banner', true);
    if ($banner_id) {
        return wp_get_attachment_image_url($banner_id, $size);
    }
    return false;
}

/**
 * 获取分类Banner图片完整信息的辅助函数
 */
function fd_get_category_banner_data($category_id, $size = 'full') {
    $banner_id = get_term_meta($category_id, 'category_banner', true);
    if ($banner_id) {
        $attachment = wp_get_attachment_image_src($banner_id, $size);
        if ($attachment) {
            return [
                'id' => $banner_id,
                'url' => $attachment[0],
                'width' => $attachment[1],
                'height' => $attachment[2],
                'alt' => get_post_meta($banner_id, '_wp_attachment_image_alt', true),
                'title' => get_the_title($banner_id)
            ];
        }
    }
    return false;
}
?>
