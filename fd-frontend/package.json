{"name": "headless-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.8.8", "@emoji-mart/data": "^1.1.2", "@emoji-mart/react": "^1.1.1", "axios": "^1.6.2", "cheerio": "^0.22.0", "class-variance-authority": "^0.7.1", "graphql": "^16.8.1", "html2canvas": "^1.4.1", "next": "14.0.4", "qrcode.react": "^4.2.0", "react": "^18", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18", "react-icons": "^5.5.0", "swr": "^2.2.4", "tailwind-merge": "^2.2.0", "uuid": "^9.0.1", "react-hot-toast": "^2.4.1", "lucide-react": "^0.303.0", "lodash": "^4.17.21", "socket.io-client": "^4.7.5", "@tiptap/react": "^2.4.0", "@tiptap/starter-kit": "^2.4.0", "@tiptap/extension-image": "^2.4.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/html2canvas": "^1.0.0", "@types/lodash": "^4.17.0", "@types/uuid": "^9.0.8", "@types/cheerio": "^0.22.35", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5", "@tailwindcss/typography": "^0.5.10", "@next/swc-linux-x64-musl": "14.0.4"}}