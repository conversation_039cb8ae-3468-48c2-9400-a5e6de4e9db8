services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - .env
    environment:
      - NODE_ENV=production
    networks:
      - frontend-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://127.0.0.1:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s

  websocket:
    build:
      context: ../fd-websocket
      dockerfile: Dockerfile
    container_name: websocket
    restart: unless-stopped
    ports:
      - "8082:8080"
    env_file:
      - ../fd-websocket/.env
    environment:
      - NODE_ENV=production
    networks:
      - frontend-network
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s

# 定义网络
networks:
  frontend-network:
    driver: bridge