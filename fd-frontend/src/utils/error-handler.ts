import { ApolloError } from '@apollo/client';

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  BAD_REQUEST = 'BAD_REQUEST',
  SERVER = 'SERVER',
  UNKNOWN = 'UNKNOWN'
}

/**
 * 标准化错误接口
 */
export interface StandardError {
  type: ErrorType;
  message: string;
  originalError?: any;
  code?: string;
}

/**
 * 处理Apollo GraphQL错误
 * @param error ApolloError对象
 * @returns 标准化错误对象数组
 */
export function handleGraphQLError(error: ApolloError | undefined): StandardError[] {
  if (!error) return [];

  const errors: StandardError[] = [];

  // 处理GraphQL错误
  if (error.graphQLErrors && error.graphQLErrors.length > 0) {
    error.graphQLErrors.forEach(graphQLError => {
      const { message, extensions } = graphQLError;
      const code = extensions?.code as string;

      // 根据错误代码分类
      let type = ErrorType.UNKNOWN;
      if (code === 'UNAUTHENTICATED') {
        type = ErrorType.AUTHENTICATION;
      } else if (code === 'FORBIDDEN') {
        type = ErrorType.AUTHORIZATION;
      } else if (code === 'BAD_USER_INPUT' || code === 'VALIDATION_ERROR') {
        type = ErrorType.BAD_REQUEST;
      } else if (code === 'NOT_FOUND') {
        type = ErrorType.NOT_FOUND;
      } else if (code === 'INTERNAL_SERVER_ERROR') {
        type = ErrorType.SERVER;
      }

      errors.push({
        type,
        message,
        originalError: graphQLError,
        code
      });
    });
  }

  // 处理网络错误
  if (error.networkError) {
    errors.push({
      type: ErrorType.NETWORK,
      message: error.networkError.message || '网络连接失败',
      originalError: error.networkError
    });
  }

  // 如果没有识别到特定错误，添加一个通用错误
  if (errors.length === 0) {
    errors.push({
      type: ErrorType.UNKNOWN,
      message: error.message || '发生未知错误',
      originalError: error
    });
  }

  return errors;
}

/**
 * 获取用户友好的错误消息
 * @param error 标准化错误对象
 * @returns 用户友好的错误消息
 */
export function getFriendlyErrorMessage(error: StandardError): string {
  switch (error.type) {
    case ErrorType.AUTHENTICATION:
      return '请先登录后再试';
    case ErrorType.AUTHORIZATION:
      return '您没有权限执行此操作';
    case ErrorType.NOT_FOUND:
      return '未找到请求的资源';
    case ErrorType.BAD_REQUEST:
      return error.message || '请求包含无效参数';
    case ErrorType.NETWORK:
      return '网络连接失败，请检查您的网络连接';
    case ErrorType.SERVER:
      return '服务器遇到问题，请稍后再试';
    case ErrorType.UNKNOWN:
    default:
      return '发生错误，请稍后再试';
  }
}

/**
 * 处理错误并返回用户友好的消息
 * @param error ApolloError对象
 * @returns 用户友好的错误消息数组
 */
export function handleError(error: ApolloError | undefined): string[] {
  if (!error) return [];
  
  const standardErrors = handleGraphQLError(error);
  return standardErrors.map(getFriendlyErrorMessage);
}

/**
 * 判断是否为认证错误
 * @param error ApolloError对象
 * @returns 是否为认证错误
 */
export function isAuthenticationError(error: ApolloError | undefined): boolean {
  if (!error) return false;
  
  const standardErrors = handleGraphQLError(error);
  return standardErrors.some(err => err.type === ErrorType.AUTHENTICATION);
}

/**
 * 在控制台记录错误
 * @param error 错误对象
 * @param context 错误上下文
 */
export function logError(error: any, context?: string): void {
  if (process.env.NODE_ENV !== 'production') {
    console.error(`[Error]${context ? ` [${context}]` : ''}:`, error);
  }
  
  // 在这里可以添加将错误发送到监控系统的逻辑
}

export default {
  handleGraphQLError,
  getFriendlyErrorMessage,
  handleError,
  isAuthenticationError,
  logError
}; 