import { DocumentNode, OperationVariables } from '@apollo/client';
import client from '@/lib/apollo-client';

/**
 * 执行标准GraphQL查询的选项
 */
export interface QueryOptions<TVariables extends OperationVariables = OperationVariables> {
  query: DocumentNode;
  variables?: TVariables;
  fetchPolicy?: 'cache-first' | 'network-only' | 'cache-only' | 'no-cache';
}

/**
 * 执行标准GraphQL查询
 * @param options 查询选项
 * @returns Promise，解析为查询结果
 */
export async function executeQuery<TData = any, TVariables extends OperationVariables = OperationVariables>(
  options: QueryOptions<TVariables>
): Promise<TData> {
  try {
    const { data } = await client.query<TData>({
      query: options.query,
      variables: options.variables,
      fetchPolicy: options.fetchPolicy || 'cache-first'
    });
    return data;
  } catch (error) {
    console.error('执行GraphQL查询时出错:', error);
    throw error;
  }
}

/**
 * 执行标准GraphQL变更的选项
 */
export interface MutationOptions<TVariables extends OperationVariables = OperationVariables> {
  mutation: DocumentNode;
  variables?: TVariables;
  refetchQueries?: Array<string | { query: DocumentNode, variables?: OperationVariables }>;
}

/**
 * 执行标准GraphQL变更
 * @param options 变更选项
 * @returns Promise，解析为变更结果
 */
export async function executeMutation<TData = any, TVariables extends OperationVariables = OperationVariables>(
  options: MutationOptions<TVariables>
): Promise<TData> {
  try {
    const { data } = await client.mutate<TData>({
      mutation: options.mutation,
      variables: options.variables,
      refetchQueries: options.refetchQueries
    });
    
    if (!data) {
      throw new Error('GraphQL变更未返回数据');
    }
    
    return data;
  } catch (error) {
    console.error('执行GraphQL变更时出错:', error);
    throw error;
  }
}

/**
 * 标准化GraphQL查询结果的数据结构
 * 处理各种嵌套结构，提取有用的数据
 * @param data 原始GraphQL响应数据
 * @param path 要提取的数据路径
 * @returns 标准化后的数据
 */
export function normalizeQueryResult<T = any>(data: any, path?: string): T | null {
  if (!data) return null;
  
  // 如果没有提供路径，返回整个数据对象
  if (!path) return data as T;
  
  // 处理嵌套路径，例如 "posts.nodes"
  const pathParts = path.split('.');
  let result = data;
  
  for (const part of pathParts) {
    result = result[part];
    if (result === undefined || result === null) {
      return null;
    }
  }
  
  return result as T;
}

/**
 * 清理GraphQL变量，移除值为undefined或null的字段
 * @param variables 原始变量对象
 * @returns 清理后的变量对象
 */
export function cleanVariables<T extends Record<string, any>>(variables: T): Partial<T> {
  if (!variables) return {};
  
  const cleaned: Partial<T> = {};
  
  Object.entries(variables).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      cleaned[key as keyof T] = value;
    }
  });
  
  return cleaned;
}

/**
 * 批量查询多个GraphQL查询
 * @param queries 查询数组
 * @returns Promise，解析为查询结果数组
 */
export async function batchQueries<TData extends any[] = any[]>(
  queries: QueryOptions[]
): Promise<TData> {
  try {
    const results = await Promise.all(
      queries.map(queryOptions => 
        client.query({
          query: queryOptions.query,
          variables: queryOptions.variables,
          fetchPolicy: queryOptions.fetchPolicy || 'cache-first'
        })
      )
    );
    
    // 使用any[]作为中间类型来避免类型不兼容问题
    const dataArray: any[] = results.map(result => result.data);
    return dataArray as TData;
  } catch (error) {
    console.error('执行批量GraphQL查询时出错:', error);
    throw error;
  }
}

export default {
  executeQuery,
  executeMutation,
  normalizeQueryResult,
  cleanVariables,
  batchQueries
}; 