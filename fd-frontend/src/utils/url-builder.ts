import { RoutePrefixes } from '../types/routes';

/**
 * URL构建工具
 * 根据配置的路由前缀构建各类URL
 */

/**
 * 构建分类详情页URL
 * @param slug 分类别名
 * @param prefixes 路由前缀配置
 */
export const buildCategoryUrl = (slug: string, prefixes: RoutePrefixes): string => {
  const sanitizedSlug = encodeURIComponent(slug);
  return `/${prefixes.categoryPrefix}/${sanitizedSlug}`;
};

/**
 * 构建标签详情页URL
 * @param slug 标签别名
 * @param prefixes 路由前缀配置
 */
export const buildTagUrl = (slug: string, prefixes: RoutePrefixes): string => {
  const sanitizedSlug = encodeURIComponent(slug);
  return `/${prefixes.tagPrefix}/${sanitizedSlug}`;
};

/**
 * 构建文章详情页URL - 用于外部显示的URL
 * 使用后台设置的文章前缀
 * 
 * @param uuid 文章UUID（格式：YYMMDD-123456）
 * @param slug 文章别名
 * @param prefixes 路由前缀配置
 * @returns 用户友好的URL路径
 */
export const buildPostUrl = (uuid: string, slug: string, prefixes: RoutePrefixes): string => {
  // 验证UUID格式（YYMMDD-123456）
  const isValidUuid = uuid && /^\d{6}-\d{6}$/.test(uuid);
  
  if (!isValidUuid) {
    console.warn(`警告: 无效的UUID格式 "${uuid}"，应为YYMMDD-123456格式`);
  }
  
  const sanitizedSlug = slug ? encodeURIComponent(slug) : '';
  return `/${prefixes.postPrefix}/${uuid}/${sanitizedSlug}`;
};

/**
 * 构建自定义类型文章详情页URL
 * 使用后台设置的自定义类型前缀
 * 
 * @param type 自定义类型
 * @param uuid 文章UUID（格式：YYMMDD-123456）或数字ID
 * @param slug 文章别名
 * @param prefixes 路由前缀配置
 * @returns 用户友好的URL路径
 */
export const buildCustomPostUrl = (type: string, uuid: string, slug: string, prefixes: RoutePrefixes): string => {
  // 验证UUID格式（YYMMDD-123456）
  const isValidUuid = uuid && /^\d{6}-\d{6}$/.test(uuid);
  
  const sanitizedSlug = slug ? encodeURIComponent(slug) : '';
  // 如果 customTypePrefix 不存在或为空，则不添加任何前缀，直接使用类型
  const prefix = prefixes.customTypePrefix ? `/${prefixes.customTypePrefix}` : '';

  // 修正后的逻辑：如果前缀为空，则不添加开头的斜杠
  if (prefix) {
      return `${prefix}/${type}/${uuid}/${sanitizedSlug}`;
  }
  // 如果前缀为空，则直接从类型开始，避免出现 /note/... 的情况
  return `/${type}/${uuid}/${sanitizedSlug}`;
};

/**
 * 构建文章详情页内部路由URL
 * 注意: 这个函数仅用于内部路由目的，不应在链接中使用
 * 
 * @param uuid 文章UUID
 * @param slug 文章别名
 * @returns 内部路由URL
 */
export const buildInternalPostUrl = (uuid: string, slug: string): string => {
  return `/post/${uuid}/${slug}`;
};

/**
 * 构建分类索引页URL - 用于Next.js内部路由
 * 
 * 注意：在页面跳转时，应该使用buildPublicCategoryIndexUrl函数
 * 这个函数仅用于特殊场景下需要直接访问内部路由的情况
 * 
 * @returns 始终返回标准的内部路由路径
 */
export const buildCategoryIndexUrl = (prefixes: RoutePrefixes): string => {
  return `/${prefixes.categoryIndexRoute}`;
};

/**
 * 构建标签索引页URL - 用于Next.js内部路由
 * 
 * 注意：在页面跳转时，应该使用buildPublicTagIndexUrl函数
 * 这个函数仅用于特殊场景下需要直接访问内部路由的情况
 * 
 * @returns 始终返回标准的内部路由路径
 */
export const buildTagIndexUrl = (prefixes: RoutePrefixes): string => {
  return `/${prefixes.tagIndexRoute}`;
};

/**
 * 构建用于展示给用户的分类索引页URL
 * 所有链接和导航都应使用此函数生成分类索引页URL
 * 
 * @param prefixes 路由前缀配置
 * @returns 自定义的用户友好URL路径
 */
export const buildPublicCategoryIndexUrl = (prefixes: RoutePrefixes): string => {
  return `/${prefixes.categoryIndexRoute}`;
};

/**
 * 构建用于展示给用户的标签索引页URL
 * 所有链接和导航都应使用此函数生成标签索引页URL
 * 
 * @param prefixes 路由前缀配置
 * @returns 自定义的用户友好URL路径
 */
export const buildPublicTagIndexUrl = (prefixes: RoutePrefixes): string => {
  return `/${prefixes.tagIndexRoute}`;
};

/**
 * 根据分类法构建URL
 * @param taxonomy 分类法（category, tag等）
 * @param slug 分类法项slug
 * @param prefixes 路由前缀配置
 * @returns 分类URL
 */
export const buildTaxonomyUrl = (taxonomy: string, slug: string, prefixes: RoutePrefixes): string => {
  const sanitizedSlug = slug ? encodeURIComponent(slug) : '';
  
  if (taxonomy === 'category') {
    return buildCategoryUrl(slug, prefixes);
  } else if (taxonomy === 'tag') {
    return buildTagUrl(slug, prefixes);
  }
  
  return `/${taxonomy}/${sanitizedSlug}`;
};

/**
 * 构建搜索URL
 * @param keyword 搜索关键词
 * @returns 搜索页URL
 */
export const buildSearchUrl = (keyword: string): string => {
  const sanitizedKeyword = keyword ? encodeURIComponent(keyword) : '';
  return `/search?q=${sanitizedKeyword}`;
};

/**
 * 构建作者URL
 * @param slug 作者slug
 * @param prefixes 路由前缀配置
 * @returns 作者页URL
 */
export const buildAuthorUrl = (slug: string, prefixes: RoutePrefixes): string => {
  const sanitizedSlug = slug ? encodeURIComponent(slug) : '';
  return `/author/${sanitizedSlug}`;
};

/**
 * 构建简单页面URL
 * @param slug 页面slug
 * @returns 页面URL
 */
export function buildPageUrl(slug: string): string {
  const sanitizedSlug = slug ? encodeURIComponent(slug) : '';
  return `/page/${sanitizedSlug}`;
}

/**
 * 构建文章URL（带完整域名）
 * @param uuid 文章UUID（格式：YYMMDD-123456）
 * @param slug 文章slug
 * @param prefixes 路由前缀配置
 * @param baseDomain 基础域名
 * @returns 完整文章URL
 */
export const buildFullPostUrl = (uuid: string, slug: string, prefixes: RoutePrefixes, baseDomain?: string): string => {
  const url = buildPostUrl(uuid, slug, prefixes);
  if (baseDomain) {
    return `${baseDomain}${url}`;
  }
  return url;
}; 