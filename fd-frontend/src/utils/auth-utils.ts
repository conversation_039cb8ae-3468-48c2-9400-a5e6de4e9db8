/**
 * 认证工具函数
 * 管理认证令牌的存储、获取和清除
 */

// 本地存储键名
const AUTH_TOKEN_KEY = 'fd_auth_token';
const REFRESH_TOKEN_KEY = 'fd_refresh_token';
const USER_DATA_KEY = 'fd_user_data';

/**
 * 存储认证令牌到本地存储
 * @param token 认证令牌
 */
export const setAuthToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(AUTH_TOKEN_KEY, token);
  }
};

/**
 * 存储刷新令牌到本地存储
 * @param token 刷新令牌
 */
export const setRefreshToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(REFRESH_TOKEN_KEY, token);
  }
};

/**
 * 从本地存储获取认证令牌
 * @returns 认证令牌或null
 */
export const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem(AUTH_TOKEN_KEY);
  }
  return null;
};

/**
 * 从本地存储获取刷新令牌
 * @returns 刷新令牌或null
 */
export const getRefreshToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem(REFRESH_TOKEN_KEY);
  }
  return null;
};

/**
 * 从本地存储清除认证令牌
 */
export const clearAuthToken = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(AUTH_TOKEN_KEY);
  }
};

/**
 * 从本地存储清除刷新令牌
 */
export const clearRefreshToken = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(REFRESH_TOKEN_KEY);
  }
};

/**
 * 存储用户数据到本地存储
 * @param userData 用户数据对象
 */
export const setUserData = (userData: any): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(USER_DATA_KEY, JSON.stringify(userData));
  }
};

/**
 * 从本地存储获取用户数据
 * @returns 用户数据对象或null
 */
export const getUserData = (): any => {
  if (typeof window !== 'undefined') {
    const userData = localStorage.getItem(USER_DATA_KEY);
    if (userData) {
      try {
        return JSON.parse(userData);
      } catch (error) {
        console.error('Failed to parse user data:', error);
        return null;
      }
    }
  }
  return null;
};

/**
 * 从本地存储清除用户数据
 */
export const clearUserData = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(USER_DATA_KEY);
  }
};

/**
 * 清除所有认证相关数据
 */
export const clearAuthData = (): void => {
  clearAuthToken();
  clearRefreshToken();
  clearUserData();
};

/**
 * 保存完整的认证信息（包含auth token和refresh token）
 * @param authToken 认证令牌
 * @param refreshToken 刷新令牌
 * @param userData 用户数据
 */
export const saveAuthData = (authToken: string, refreshToken: string | null, userData: any): void => {
  setAuthToken(authToken);
  if (refreshToken) {
    setRefreshToken(refreshToken);
  }
  if (userData) {
    setUserData(userData);
  }
};

/**
 * 检查用户是否已认证
 * @returns 布尔值，表示用户是否已认证
 */
export const isAuthenticated = (): boolean => {
  return !!getAuthToken();
};

/**
 * 创建带认证头的HTTP请求头对象
 * @returns 包含认证头的对象
 */
export const getAuthHeaders = (): Record<string, string> => {
  const token = getAuthToken();
  if (token) {
    return {
      Authorization: `Bearer ${token}`
    };
  }
  return {};
}; 