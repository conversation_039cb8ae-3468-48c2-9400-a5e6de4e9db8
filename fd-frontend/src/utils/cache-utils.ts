import { InMemoryCache, Reference, StoreObject } from '@apollo/client';

/**
 * 缓存标识符类型
 */
export interface CacheIdentifier {
  __typename: string;
  id: string | number;
}

/**
 * 缓存查询结果类型
 */
export interface QueryResult {
  [key: string]: {
    nodes: Array<any>;
    [key: string]: any;
  };
}

/**
 * 获取标准化的缓存ID
 * @param typename 类型名
 * @param id ID
 * @returns 标准化的缓存ID
 */
export function getCacheId(typename: string, id: string | number): string {
  return `${typename}:${id}`;
}

/**
 * 从Apollo缓存中读取指定的对象
 * @param cache Apollo缓存
 * @param typename 对象类型
 * @param id 对象ID
 * @returns 缓存的对象或undefined
 */
export function readFromCache<T extends StoreObject>(
  cache: InMemoryCache,
  typename: string,
  id: string | number
): any {
  const cacheId = getCacheId(typename, id);
  return cache.readFragment<T>({
    id: cacheId,
    fragment: { kind: 'Document' } as any, // 这里是为了类型检查而添加的
    fragmentName: typename
  });
}

/**
 * 更新缓存中的单个对象
 * @param cache Apollo缓存
 * @param typename 对象类型
 * @param id 对象ID
 * @param data 更新的数据
 */
export function updateCacheObject<T extends StoreObject>(
  cache: InMemoryCache,
  typename: string,
  id: string | number,
  data: Partial<T>
): void {
  const cacheId = getCacheId(typename, id);
  
  // 读取缓存的当前值
  const cachedObject = cache.readFragment<T>({
    id: cacheId,
    fragment: { kind: 'Document' } as any,
    fragmentName: typename
  });

  if (cachedObject) {
    // 创建合并数据对象
    const mergedData = {
      ...cachedObject,
      ...data
    };
    
    // 更新缓存对象
    cache.writeFragment({
      id: cacheId,
      fragment: { kind: 'Document' } as any,
      fragmentName: typename,
      data: mergedData as any
    });
  }
}

/**
 * 从列表缓存中添加一个项目
 * @param cache Apollo缓存
 * @param queryName 查询名称（例如，'posts'）
 * @param newItem 要添加的新项目
 * @param variables 查询变量
 */
export function addItemToCache<T extends CacheIdentifier>(
  cache: InMemoryCache,
  queryName: string,
  newItem: T,
  variables?: Record<string, any>
): void {
  try {
    // 尝试读取当前缓存的查询结果
    const queryData = cache.readQuery({
      query: { kind: 'Document' } as any,
      variables
    }) as Record<string, any>;

    if (queryData && queryData[queryName]?.nodes) {
      // 检查项目是否已存在
      const exists = queryData[queryName].nodes.some(
        (item: CacheIdentifier) => item.id === newItem.id
      );

      if (!exists) {
        // 准备新的缓存数据
        const newData = {
          ...queryData,
          [queryName]: {
            ...queryData[queryName],
            nodes: [newItem, ...queryData[queryName].nodes]
          }
        };
        
        // 向缓存添加新项目
        cache.writeQuery({
          query: { kind: 'Document' } as any,
          variables,
          data: newData as any
        });
      }
    }
  } catch (e) {
    console.error('添加项目到缓存时出错:', e);
  }
}

/**
 * 从列表缓存中删除一个项目
 * @param cache Apollo缓存
 * @param queryName 查询名称
 * @param itemId 要删除的项目ID
 * @param variables 查询变量
 */
export function removeItemFromCache(
  cache: InMemoryCache,
  queryName: string,
  itemId: string | number,
  variables?: Record<string, any>
): void {
  try {
    // 尝试读取当前缓存的查询结果
    const queryData = cache.readQuery({
      query: { kind: 'Document' } as any,
      variables
    }) as Record<string, any>;

    if (queryData && queryData[queryName]?.nodes) {
      // 过滤掉要删除的项目
      const filteredNodes = queryData[queryName].nodes.filter(
        (item: CacheIdentifier) => item.id !== itemId
      );

      // 准备新的缓存数据
      const newData = {
        ...queryData,
        [queryName]: {
          ...queryData[queryName],
          nodes: filteredNodes
        }
      };
      
      // 更新缓存
      cache.writeQuery({
        query: { kind: 'Document' } as any,
        variables,
        data: newData as any
      });
    }
  } catch (e) {
    console.error('从缓存删除项目时出错:', e);
  }
}

/**
 * 无效化缓存中的查询
 * @param cache Apollo缓存
 * @param queryName 要无效化的查询名称
 * @param variables 查询变量
 */
export function invalidateQuery(
  cache: InMemoryCache,
  queryName: string,
  variables?: Record<string, any>
): void {
  try {
    cache.evict({ 
      fieldName: queryName,
      args: variables
    });
    cache.gc();
  } catch (e) {
    console.error('无效化查询时出错:', e);
  }
}

export default {
  getCacheId,
  readFromCache,
  updateCacheObject,
  addItemToCache,
  removeItemFromCache,
  invalidateQuery
}; 