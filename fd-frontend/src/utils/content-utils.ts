/**
 * 根据内容类型获取显示名称
 * @param type 内容类型标识
 * @returns 用户友好的内容类型名称
 */
export function getContentTypeLabel(type: string | undefined): string {
  if (!type) return '未知类型';
  
  // 移除GraphQL类型前缀下划线
  let displayType = type.replace(/^_/, '');
  
  // 特殊类型映射
  const typeMapping: Record<string, string> = {
    'Post': '文章',
    'Page': '页面',
    'post': '文章',
    'page': '页面',
    'note': '笔记',
    'resources': '资源',
    'event': '活动',
    'product': '产品',
    'case': '案例',
    'news': '新闻',
    'faq': '问答'
  };
  
  return typeMapping[displayType] || displayType;
}

/**
 * 获取文章标题，处理各种可能的标题格式
 * @param title 标题对象或字符串
 * @returns 处理后的标题文本
 */
export function getTitle(title: any): string {
  if (!title) return '无标题';
  if (typeof title === 'string') return title;
  if (typeof title === 'object' && title.rendered) return title.rendered;
  return '无标题';
}

/**
 * 截断文本到指定长度，添加省略号
 * @param text 要截断的文本
 * @param length 最大长度
 * @returns 截断后的文本
 */
export function truncateText(text: string, length: number): string {
  if (!text) return '';
  if (text.length <= length) return text;
  
  return text.substring(0, length) + '...';
} 