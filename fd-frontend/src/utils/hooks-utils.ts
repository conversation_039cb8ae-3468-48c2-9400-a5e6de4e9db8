import { useEffect, useRef } from 'react';
import { ApolloError } from '@apollo/client';
import ErrorHandler from './error-handler';

/**
 * 跟踪错误并执行回调的钩子
 * @param error 当前的Apollo错误
 * @param callback 当错误变化时执行的回调函数
 */
export function useErrorHandler(
  error: ApolloError | undefined,
  callback?: (friendlyMessages: string[]) => void
) {
  const previousError = useRef<ApolloError | undefined>();

  useEffect(() => {
    // 如果错误发生变化且不为空
    if (error && error !== previousError.current) {
      // 记录错误
      ErrorHandler.logError(error, 'Apollo GraphQL');

      // 获取友好的错误消息
      const friendlyMessages = ErrorHandler.handleError(error);

      // 如果有回调函数，执行它
      if (callback && friendlyMessages.length > 0) {
        callback(friendlyMessages);
      }

      // 特殊处理认证错误
      if (ErrorHandler.isAuthenticationError(error) && typeof window !== 'undefined') {
        // 清除认证信息
        localStorage.removeItem('authToken');
        
        // 可以在这里添加重定向到登录页面的逻辑
        // window.location.href = '/login';
      }
    }

    // 更新前一个错误引用
    previousError.current = error;
  }, [error, callback]);

  // 返回标准化处理后的错误消息
  return error ? ErrorHandler.handleError(error) : [];
}

/**
 * 在挂载时执行一次函数
 * @param callback 要执行的回调函数
 */
export function useOnMount(callback: () => void | (() => void)) {
  useEffect(() => {
    const cleanup = callback();
    return cleanup;
  }, []);
}

/**
 * 带有防抖功能的状态变化监听钩子
 * @param value 要监听的值
 * @param onChange 值变化时的回调
 * @param delay 防抖延迟时间（毫秒）
 */
export function useDebounceEffect<T>(
  value: T,
  onChange: (value: T) => void,
  delay = 500
) {
  const timeoutRef = useRef<NodeJS.Timeout>();
  const previousValue = useRef<T>(value);

  useEffect(() => {
    // 如果值没有变化，不执行任何操作
    if (value === previousValue.current) {
      return;
    }

    // 更新前一个值
    previousValue.current = value;

    // 清除先前的超时
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // 设置新的超时
    timeoutRef.current = setTimeout(() => {
      onChange(value);
    }, delay);

    // 清理函数
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [value, onChange, delay]);
}

/**
 * 监听缓存加载状态的钩子
 * @param loading Apollo查询的加载状态
 * @param data 查询的数据
 * @returns 组合的加载状态
 */
export function useCombinedLoadingState<T>(
  loading: boolean,
  data: T | undefined | null
): boolean {
  // 返回true如果正在加载或数据还没准备好
  return loading || data === undefined || data === null;
}

export default {
  useErrorHandler,
  useOnMount,
  useDebounceEffect,
  useCombinedLoadingState
}; 