import { RoutePrefixes } from '../types/routes';

/**
 * 默认路由前缀设置
 */
export const DEFAULT_ROUTE_PREFIXES: RoutePrefixes = {
  categoryPrefix: null,
  tagPrefix: 'topics',
  postPrefix: 'articles',
  categoryIndexRoute: 'category-index',
  tagIndexRoute: 'tag-index',
  customTypePrefix: 'post-type'
};

/**
 * 全局路由前缀配置
 * 初始化为默认值，后续会通过GraphQL查询更新
 */
let routePrefixes: RoutePrefixes = { ...DEFAULT_ROUTE_PREFIXES };

/**
 * 更新路由前缀配置
 * @param prefixes 新的路由前缀配置
 */
export const updateRoutePrefixes = (prefixes: Partial<RoutePrefixes>): void => {
  routePrefixes = {
    ...routePrefixes,
    ...prefixes
  };
};

/**
 * 获取当前路由前缀配置
 * @returns 当前的路由前缀配置
 */
export const getRoutePrefixes = (): RoutePrefixes => {
  return { ...routePrefixes };
};

/**
 * 服务器端路由前缀初始化
 * 用于在Next.js的getServerSideProps或getStaticProps中调用
 * @param prefixes 从GraphQL获取的路由前缀
 */
export const initServerRoutePrefixes = (prefixes: RoutePrefixes): void => {
  updateRoutePrefixes(prefixes);
};

/**
 * 将路由前缀序列化为JSON字符串
 * 用于服务器端向客户端传递数据
 */
export const serializeRoutePrefixes = (): string => {
  return JSON.stringify(routePrefixes);
};

/**
 * 从JSON字符串反序列化路由前缀配置
 * @param json 序列化的JSON字符串
 */
export const deserializeRoutePrefixes = (json: string): RoutePrefixes => {
  try {
    return JSON.parse(json) as RoutePrefixes;
  } catch (e) {
    console.error('Error deserializing route prefixes:', e);
    return { ...DEFAULT_ROUTE_PREFIXES };
  }
}; 