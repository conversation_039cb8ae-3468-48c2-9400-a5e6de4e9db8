/**
 * 格式化日期为中文格式
 * @param dateString 日期字符串
 * @returns 格式化后的日期字符串
 */
export function formatDate(dateString: string | null | undefined): string {
  if (!dateString) return '未知日期';

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '未知日期';

    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
  } catch (e) {
    return '未知日期';
  }
}

/**
 * 格式化时间为友好格式
 * @param dateString 日期字符串
 * @returns 格式化后的友好时间字符串
 */
export function formatRelativeTime(dateString: string | null | undefined): string {
  if (!dateString) return '未知时间';

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '未知时间';
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    const diffMonth = Math.floor(diffDay / 30);
    const diffYear = Math.floor(diffDay / 365);
    
    if (diffSec < 60) return '刚刚';
    if (diffMin < 60) return `${diffMin}分钟前`;
    if (diffHour < 24) return `${diffHour}小时前`;
    if (diffDay < 30) return `${diffDay}天前`;
    if (diffMonth < 12) return `${diffMonth}个月前`;
    return `${diffYear}年前`;
  } catch (e) {
    return '未知时间';
  }
} 