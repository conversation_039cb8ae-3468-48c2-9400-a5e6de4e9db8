import { useQuery } from '@apollo/client';
import { GET_CUSTOM_POSTS, GET_CUSTOM_POST_BY_ID, GET_CUSTOM_POST_BY_SLUG } from '../lib/graphql/queries';
import { CustomPost } from '../types/post';

interface CustomPostsData {
  contentNodes: {
    nodes: CustomPost[];
  };
}

interface CustomPostData {
  contentNode: CustomPost;
}

interface UseCustomPostsOptions {
  type: string;
  first?: number;
  after?: string;
}

/**
 * 获取自定义内容类型列表的Hook
 * @param options 查询选项
 * @returns 自定义内容列表数据、加载状态和错误信息
 */
export const useCustomPosts = (options: UseCustomPostsOptions) => {
  const { type, first = 10, after } = options;

  const { data, loading, error, fetchMore, refetch } = useQuery<CustomPostsData>(
    GET_CUSTOM_POSTS,
    {
      variables: { type, first, after },
      skip: !type,
      notifyOnNetworkStatusChange: true,
    }
  );

  const loadMore = (afterCursor: string) => {
    return fetchMore({
      variables: {
        type,
        first,
        after: afterCursor,
      },
      updateQuery: (prev: any, { fetchMoreResult }: any) => {
        if (!fetchMoreResult) return prev;
        return {
          contentNodes: {
            ...fetchMoreResult.contentNodes,
            nodes: [...prev.contentNodes.nodes, ...fetchMoreResult.contentNodes.nodes],
          },
        };
      },
    });
  };

  return {
    posts: data?.contentNodes?.nodes || [],
    loading,
    error,
    loadMore,
    refetch,
  };
};

interface UseCustomPostOptions {
  type: string;
  id?: string;
  slug?: string;
}

/**
 * 获取单个自定义内容的Hook
 * @param options 查询选项，需提供类型和id或slug
 * @returns 自定义内容数据、加载状态和错误信息
 */
export const useCustomPost = (options: UseCustomPostOptions) => {
  const { type, id, slug } = options;

  if (!type || (!id && !slug)) {
    throw new Error('useCustomPost hook需要提供type和id或slug参数');
  }

  // 根据参数选择查询方式
  const queryInfo = id
    ? { query: GET_CUSTOM_POST_BY_ID, variables: { type, id } }
    : { query: GET_CUSTOM_POST_BY_SLUG, variables: { type, slug } };

  const { data, loading, error, refetch } = useQuery<CustomPostData>(
    queryInfo.query,
    {
      variables: queryInfo.variables,
      skip: !type || (!id && !slug),
    }
  );

  return {
    post: data?.contentNode,
    loading,
    error,
    refetch,
  };
}; 