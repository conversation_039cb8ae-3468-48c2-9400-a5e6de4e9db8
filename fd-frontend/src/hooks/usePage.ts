import { useQuery } from '@apollo/client';
import { GET_PAGE_BY_ID, GET_PAGE_BY_SLUG } from '../lib/graphql/queries';
import { Page } from '../types/page';

interface PageData {
  page: Page;
}

interface UsePageOptions {
  id?: string;
  slug?: string;
}

/**
 * 获取单个页面详情的Hook
 * @param options 查询选项，提供id或slug
 * @returns 页面数据、加载状态和错误信息
 */
export const usePage = (options: UsePageOptions = {}) => {
  const { id, slug } = options;
  
  // 如果没有提供有效参数，跳过查询而不是抛出错误
  const validParams = !!(id || slug);

  // 根据参数选择查询方式
  const queryInfo = id 
    ? { query: GET_PAGE_BY_ID, variables: { id } }
    : { query: GET_PAGE_BY_SLUG, variables: { slug } };

  const { data, loading, error, refetch } = useQuery<PageData>(
    queryInfo.query, 
    { 
      variables: queryInfo.variables,
      skip: !validParams
    }
  );

  return {
    page: data?.page,
    loading: validParams && loading,
    error,
    refetch,
  };
}; 