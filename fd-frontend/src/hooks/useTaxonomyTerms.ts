import { useQuery } from '@apollo/client';
import { GET_TAXONOMY_TERMS } from '../lib/graphql/queries';

interface TaxonomyTerm {
  __typename: string;
  id: string;
  name: string;
  slug: string;
  uri: string;
  count?: number;
  description?: string;
}

interface TaxonomyTermsData {
  terms: {
    nodes: TaxonomyTerm[];
  };
}

/**
 * 获取分类法条目的Hook
 * @param taxonomy 分类法名称
 * @returns 分类法条目列表数据、加载状态和错误信息
 */
export const useTaxonomyTerms = (taxonomy: string) => {
  // 将分类法名称转为大写，用于GraphQL枚举值
  const formatTaxonomy = (tax: string) => {
    return tax.toUpperCase();
  };

  const { data, loading, error, refetch } = useQuery<TaxonomyTermsData>(
    GET_TAXONOMY_TERMS,
    {
      variables: { taxonomy: formatTaxonomy(taxonomy) },
      skip: !taxonomy,
    }
  );

  return {
    terms: data?.terms?.nodes || [],
    loading,
    error,
    refetch,
  };
}; 