import { useQuery } from '@apollo/client';
import { GET_SLUG_MAPPING_TABLE } from '@/lib/graphql/queries';

// 定义类型
export interface SlugMappingItem {
  slug: string;
  type: string;  // 'page' | 'category' | 'tag' | 'taxonomy_archive' | 'post_type'
  id: string;
  taxonomy?: string;
}

interface UseSlugMappingTableResult {
  mappings: SlugMappingItem[];
  loading: boolean;
  error: Error | null;
  refetch: () => void;
}

/**
 * 获取完整的 slug 映射表的钩子
 * 
 * @returns {UseSlugMappingTableResult} 包含映射表数据、加载状态和错误信息的对象
 */
export function useSlugMappingTable(): UseSlugMappingTableResult {
  // 使用 Apollo Client 执行查询
  const { data, loading, error, refetch } = useQuery(GET_SLUG_MAPPING_TABLE, {
    fetchPolicy: 'cache-and-network',
  });

  // 处理并返回数据
  return {
    mappings: data?.slugMappingTable || [],
    loading,
    error: error || null,
    refetch,
  };
} 