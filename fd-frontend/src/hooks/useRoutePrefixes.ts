import { useQuery } from '@apollo/client';
import { GET_ROUTE_PREFIXES } from '../lib/graphql/queries';
import { RoutePrefixes } from '../types/routes';
import { 
  DEFAULT_ROUTE_PREFIXES, 
  updateRoutePrefixes,
  getRoutePrefixes
} from '../utils/route-config';
import { useEffect } from 'react';

interface UseRoutePrefixesResult {
  prefixes: RoutePrefixes;
  loading: boolean;
  error: any;
}

/**
 * 获取路由前缀设置的Hook
 * @returns 路由前缀设置、加载状态和错误信息
 */
export const useRoutePrefixes = (): UseRoutePrefixesResult => {
  const { data, loading, error } = useQuery(GET_ROUTE_PREFIXES);
  
  // 当获取到数据时更新全局配置
  useEffect(() => {
    if (data?.routePrefixes) {
      updateRoutePrefixes(data.routePrefixes);
    }
  }, [data]);
  
  return {
    prefixes: data?.routePrefixes || getRoutePrefixes(),
    loading,
    error
  };
}; 