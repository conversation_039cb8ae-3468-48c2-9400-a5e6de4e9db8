import { useQuery } from '@apollo/client';
import { GET_POST_BY_ID, GET_POST_BY_SLUG } from '../lib/graphql/queries';
import { Post } from '../types/post';

interface PostData {
  post: Post;
}

interface UsePostOptions {
  id?: string;
  slug?: string;
}

/**
 * 获取单篇文章详情的Hook
 * @param options 查询选项，提供id或slug
 * @returns 文章数据、加载状态和错误信息
 */
export const usePost = (options: UsePostOptions) => {
  const { id, slug } = options;

  if (!id && !slug) {
    throw new Error('usePost hook需要提供id或slug参数');
  }

  // 根据参数选择查询方式
  const queryInfo = id 
    ? { query: GET_POST_BY_ID, variables: { id } }
    : { query: GET_POST_BY_SLUG, variables: { slug } };

  const { data, loading, error, refetch } = useQuery<PostData>(
    queryInfo.query, 
    { 
      variables: queryInfo.variables,
      skip: !id && !slug
    }
  );

  return {
    post: data?.post,
    loading,
    error,
    refetch,
  };
}; 