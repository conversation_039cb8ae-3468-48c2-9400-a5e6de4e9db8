import { useQuery, useMutation, gql, QueryHookOptions } from '@apollo/client'
import {
  GET_MY_CONVERSATIONS,
  GET_CONVERSATION_MESSAGES,
  GET_UNREAD_MESSAGE_COUNT
} from '@/lib/graphql/queries'
import {
  SEND_PRIVATE_MESSAGE,
  MARK_CONVERSATION_AS_READ,
  DELETE_CONVERSATION
} from '@/lib/graphql/mutations'
import type {
  ViewerPrivateConversations,
  PrivateConversationData,
  ViewerUnreadMessageCount,
  SendPrivateMessagePayload,
  MarkConversationAsReadPayload,
  DeleteConversationPayload
} from '@/types/messages'

/**
 * Hook to fetch the current user's conversation list.
 */
export function useMyConversations(variables: {
  first?: number
  after?: string
}) {
  return useQuery<ViewerPrivateConversations>(GET_MY_CONVERSATIONS, {
    variables,
    fetchPolicy: 'cache-and-network'
  })
}

/**
 * Hook to fetch messages for a single conversation.
 */
export function useConversationMessages(variables: {
  id: string
  first?: number
  after?: string
}) {
  return useQuery<PrivateConversationData>(GET_CONVERSATION_MESSAGES, {
    variables,
    fetchPolicy: 'cache-and-network'
  })
}

/**
 * Hook to get the total number of unread messages for the current user.
 */
export function useUnreadMessageCount() {
  return useQuery<ViewerUnreadMessageCount>(GET_UNREAD_MESSAGE_COUNT, {
    fetchPolicy: 'cache-and-network'
  })
}

/**
 * Hook to send a private message.
 * Includes logic to update the cache optimistically or on completion.
 */
export function useSendPrivateMessage() {
  const [sendMessage, result] = useMutation<SendPrivateMessagePayload>(
    SEND_PRIVATE_MESSAGE
  )
  // Note: Cache update logic will be added in a later step.
  return { sendMessage, ...result }
}

/**
 * Hook to mark a conversation as read.
 */
export function useMarkConversationAsRead() {
  const [markAsRead, result] = useMutation<MarkConversationAsReadPayload>(
    MARK_CONVERSATION_AS_READ,
    {
      refetchQueries: [
        { query: GET_MY_CONVERSATIONS, variables: { first: 10 } }
      ]
    }
  )
  return { markAsRead, ...result }
}

/**
 * Hook to delete a conversation.
 */
export function useDeleteConversation() {
  const [deleteConversation, result] = useMutation<DeleteConversationPayload>(
    DELETE_CONVERSATION,
    {
      refetchQueries: [
        { query: GET_MY_CONVERSATIONS, variables: { first: 10 } }
      ]
    }
  )
  return { deleteConversation, ...result }
} 