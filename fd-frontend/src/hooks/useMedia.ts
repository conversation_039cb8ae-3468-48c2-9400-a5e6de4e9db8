import { useQuery } from '@apollo/client';
import { GET_MEDIA } from '../lib/graphql/queries';

// 媒体项类型定义
interface MediaItem {
  id: string;
  title: string;
  altText: string;
  sourceUrl: string;
  mediaItemUrl: string;
  mediaType: string;
  mimeType: string;
}

interface MediaData {
  mediaItem: MediaItem;
}

/**
 * 获取媒体详情的Hook
 * @param id 媒体项ID
 * @returns 媒体数据、加载状态和错误信息
 */
export const useMedia = (id: string) => {
  const { data, loading, error, refetch } = useQuery<MediaData>(
    GET_MEDIA,
    {
      variables: { id },
      skip: !id,
    }
  );

  return {
    media: data?.mediaItem,
    loading,
    error,
    refetch,
  };
}; 