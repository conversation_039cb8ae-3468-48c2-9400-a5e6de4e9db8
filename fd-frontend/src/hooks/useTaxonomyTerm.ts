import { useQuery } from '@apollo/client';
import { useMemo } from 'react';
import { GET_TAXONOMY_TERM, GET_POSTS_BY_TAX_QUERY_SLUG } from '../lib/graphql/queries';
import { Post } from '@/types/post';
import { useSettings } from '@/contexts/SettingsContext';

interface TaxonomyTermDetail {
  __typename: string;
  id: string;
  name: string;
  slug: string;
  description?: string;
  uri: string;
  count?: number;
  children?: {
    nodes: {
      id: string;
      name: string;
      slug: string;
      uri: string;
    }[];
  };
}

interface TaxonomyTermData {
  terms: {
    nodes: TaxonomyTermDetail[];
  };
}

interface PostsData {
  posts: {
    nodes: Post[];
    pageInfo?: {
      hasNextPage: boolean;
      endCursor: string;
    };
  };
}

interface UseTaxonomyTermOptions {
  first?: number;
}

/**
 * 获取分类法条目详情的Hook
 * @param taxonomy 分类法名称
 * @param slug 条目别名
 * @param options 可选参数，如first
 * @returns 分类法条目详情数据、相关文章、加载状态和错误信息
 */
export const useTaxonomyTerm = (taxonomy: string, slug: string, options: UseTaxonomyTermOptions = {}) => {
  const { postsPerPage } = useSettings();
  const { first: firstOption } = options;
  const first = firstOption ?? postsPerPage;
  // 将分类法名称转为大写，用于GraphQL枚举值
  const formatTaxonomy = (tax: string) => {
    return tax.toUpperCase();
  };

  // 获取分类法条目详情
  const { 
    data: termData, 
    loading: termLoading, 
    error: termError 
  } = useQuery<TaxonomyTermData>(
    GET_TAXONOMY_TERM,
    {
      variables: { 
        taxonomy: formatTaxonomy(taxonomy),
        slug: [slug]
      },
      skip: !taxonomy || !slug,
    }
  );

  // 获取该分类法条目下的文章
  const { 
    data: postsData, 
    loading: postsLoading, 
    error: postsError,
    fetchMore 
  } = useQuery<PostsData>(
    GET_POSTS_BY_TAX_QUERY_SLUG,
    {
      variables: { 
        taxonomy: formatTaxonomy(taxonomy),
        slugs: [slug],
        first: first
      },
      skip: !taxonomy || !slug,
      notifyOnNetworkStatusChange: true, // 允许区分加载状态
    }
  );

  // 对获取的文章数据进行二次去重
  const uniquePosts = useMemo(() => {
    if (!postsData?.posts?.nodes) return [];
    
    const postsMap = new Map();
    postsData.posts.nodes.forEach((post: Post) => {
      if (!postsMap.has(post.id)) {
        postsMap.set(post.id, post);
      }
    });
    
    return Array.from(postsMap.values());
  }, [postsData?.posts?.nodes]);

  // 加载更多文章
  const loadMorePosts = (afterCursor: string) => {
    return fetchMore({
      variables: {
        first: first,
        after: afterCursor
      },
      updateQuery: (prev: PostsData, { fetchMoreResult }: { fetchMoreResult?: PostsData }) => {
        if (!fetchMoreResult || !fetchMoreResult.posts) return prev;
        
        // 检查重复项
        const prevIds = new Set(prev.posts.nodes.map((post: Post) => post.id));
        const uniqueNewPosts = fetchMoreResult.posts.nodes.filter((post: Post) => !prevIds.has(post.id));
        
        return {
          posts: {
            ...fetchMoreResult.posts,
            nodes: [
              ...prev.posts.nodes,
              ...uniqueNewPosts // 合并时只添加唯一的新文章
            ]
          }
        };
      }
    });
  };

  // 获取第一个匹配的条目（如果存在）
  const term = termData?.terms?.nodes && termData.terms.nodes.length > 0 ? termData.terms.nodes[0] : undefined;

  return {
    term,
    posts: uniquePosts, // 返回去重后的文章
    pageInfo: postsData?.posts?.pageInfo,
    loading: postsLoading, // 只返回文章的加载状态，term的加载状态可以单独处理
    error: termError || postsError,
    loadMorePosts,
    termLoading, // 单独返回term的加载状态
  };
}; 