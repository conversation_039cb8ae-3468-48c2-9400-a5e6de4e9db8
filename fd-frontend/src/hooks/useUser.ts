import { useQuery } from '@apollo/client';
import { GET_USER, GET_USERS } from '../lib/graphql/queries';
import { User } from '../types/post';

interface UserData {
  user: User;
}

interface UsersData {
  users: {
    nodes: User[];
  };
}

/**
 * 获取用户信息的Hook
 * @param id 用户ID
 * @returns 用户数据、加载状态和错误信息
 */
export const useUser = (id: string) => {
  const { data, loading, error, refetch } = useQuery<UserData>(
    GET_USER,
    {
      variables: { id },
      skip: !id
    }
  );

  return {
    user: data?.user,
    loading,
    error,
    refetch,
  };
};

/**
 * 获取用户列表的Hook
 * @param first 获取的用户数量，默认为10
 * @returns 用户列表数据、加载状态和错误信息
 */
export const useUsers = (first: number = 10) => {
  const { data, loading, error, refetch } = useQuery<UsersData>(
    GET_USERS,
    {
      variables: { first }
    }
  );

  return {
    users: data?.users?.nodes || [],
    loading,
    error,
    refetch,
  };
}; 