import { useQuery, useMutation } from '@apollo/client';
import {
  GET_USER_NOTIFICATIONS,
  GET_USER_NOTIFICATION,
  GET_UNREAD_NOTIFICATION_COUNT
} from '../lib/graphql/queries';
import {
  MARK_NOTIFICATION_READ,
  MARK_ALL_NOTIFICATIONS_READ,
  DELETE_NOTIFICATION
} from '../lib/graphql/mutations';
import {
  Notification,
  NotificationQueryParams,
  NotificationResponse,
  MarkAllNotificationsReadResponse,
  DeleteNotificationResponse
} from '../types/notification';

/**
 * 获取用户通知列表的Hook
 * @param params 查询参数
 * @returns 通知列表数据、加载状态和错误信息
 */
export const useNotifications = (params?: NotificationQueryParams) => {
  const { status, type, perPage = 10, page = 1 } = params || {};
  const { data, loading, error, refetch } = useQuery<{ userNotifications: Notification[] }>(
    GET_USER_NOTIFICATIONS,
    {
      variables: { status, type, perPage, page },
      fetchPolicy: 'cache-and-network'
    }
  );

  const forceRefetch = async () => {
    try {
      return await refetch();
    } catch (err) {
      console.error('刷新通知失败:', err);
      throw err;
    }
  };

  return {
    notifications: data?.userNotifications || [],
    loading,
    error,
    refetch: forceRefetch
  };
};

/**
 * 获取单个通知详情的Hook
 * @param id 通知ID
 * @returns 通知详情数据、加载状态和错误信息
 */
export const useNotification = (id: string) => {
  const { data, loading, error, refetch } = useQuery<{ userNotification: Notification }>(
    GET_USER_NOTIFICATION,
    {
      variables: { id },
      skip: !id
    }
  );

  return {
    notification: data?.userNotification,
    loading,
    error,
    refetch
  };
};

/**
 * 获取未读通知数量的Hook
 * @returns 未读通知数量、加载状态和错误信息
 */
export const useUnreadNotificationCount = () => {
  const { data, loading, error, refetch } = useQuery<{ unreadNotificationCount: number }>(
    GET_UNREAD_NOTIFICATION_COUNT,
    {
      fetchPolicy: 'cache-and-network'
    }
  );

  // 强制刷新数据的函数
  const forceRefetch = async () => {
    try {
      return await refetch();
    } catch (err) {
      console.error('刷新未读计数失败:', err);
      throw err;
    }
  };

  return {
    unreadCount: data?.unreadNotificationCount || 0,
    loading,
    error,
    refetch: forceRefetch
  };
};

/**
 * 标记通知已读的Hook
 * @returns 标记通知已读的函数和执行状态
 */
export const useMarkNotificationRead = () => {
  const [markRead, { data, loading, error }] = useMutation<{ markNotificationRead: NotificationResponse }>(
    MARK_NOTIFICATION_READ,
    {
      refetchQueries: [
        { query: GET_UNREAD_NOTIFICATION_COUNT }
      ]
    }
  );

  const markNotificationRead = async (id: string) => {
    try {
      const result = await markRead({ variables: { id } });
      return result.data?.markNotificationRead;
    } catch (err) {
      console.error('标记通知已读失败:', err);
      throw err;
    }
  };

  return {
    markNotificationRead,
    data: data?.markNotificationRead,
    loading,
    error
  };
};

/**
 * 标记所有通知已读的Hook
 * @returns 标记所有通知已读的函数和执行状态
 */
export const useMarkAllNotificationsRead = () => {
  const [markAllRead, { data, loading, error }] = useMutation<{ markAllNotificationsRead: MarkAllNotificationsReadResponse }>(
    MARK_ALL_NOTIFICATIONS_READ,
    {
      refetchQueries: [
        { query: GET_UNREAD_NOTIFICATION_COUNT },
        { query: GET_USER_NOTIFICATIONS }
      ]
    }
  );

  const markAllNotificationsRead = async () => {
    try {
      const result = await markAllRead();
      return result.data?.markAllNotificationsRead;
    } catch (err) {
      console.error('标记所有通知已读失败:', err);
      throw err;
    }
  };

  return {
    markAllNotificationsRead,
    data: data?.markAllNotificationsRead,
    loading,
    error
  };
};

/**
 * 删除通知的Hook
 * @returns 删除通知的函数和执行状态
 */
export const useDeleteNotification = () => {
  const [deleteNotificationMutation, { data, loading, error }] = useMutation<{ 
    deleteNotification: DeleteNotificationResponse 
  }>(
    DELETE_NOTIFICATION,
    {
      refetchQueries: [
        { query: GET_USER_NOTIFICATIONS },
        { query: GET_UNREAD_NOTIFICATION_COUNT }
      ]
    }
  );

  const deleteNotification = async (id: string) => {
    try {
      const result = await deleteNotificationMutation({ variables: { id } });
      return result.data?.deleteNotification;
    } catch (err) {
      console.error('删除通知失败:', err);
      throw err;
    }
  };

  return {
    deleteNotification,
    data: data?.deleteNotification,
    loading,
    error
  };
}; 