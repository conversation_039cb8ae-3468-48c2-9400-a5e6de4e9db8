import { useQuery } from '@apollo/client';
import { GET_MENUS } from '../lib/graphql/queries';

interface MenuItem {
  id: string;
  title: string;
  label: string;
  url: string;
  target?: string;
  parentId?: string;
  cssClasses?: string[];
  children?: MenuItem[];
}

interface Menu {
  id: string;
  name: string;
  menuItems?: {
    nodes: MenuItem[];
  };
}

interface MenusData {
  menus: {
    nodes: Menu[];
  };
}

/**
 * 将扁平菜单列表转换为层次结构
 */
const flatListToHierarchical = (
  data: MenuItem[] = [],
  {idKey = 'id', parentKey = 'parentId', childrenKey = 'children'} = {}
): MenuItem[] => {
  const tree: MenuItem[] = [];
  const childrenOf: { [key: string]: MenuItem[] } = {};
  
  data.forEach((item) => {
    const newItem = {...item} as any;
    const id = newItem[idKey];
    const parentId = newItem[parentKey] || 0;
    
    childrenOf[id] = childrenOf[id] || [];
    newItem[childrenKey] = childrenOf[id];
    
    parentId
      ? (
          childrenOf[parentId] = childrenOf[parentId] || []
        ).push(newItem)
      : tree.push(newItem);
  });
  
  return tree;
};

/**
 * 获取指定名称菜单的Hook
 * @param menuName 菜单名称，如"顶部菜单"或"底部菜单"
 * @returns 菜单数据、加载状态和错误信息
 */
export const useMenu = (menuName: string) => {
  const { data, loading, error, refetch } = useQuery<MenusData>(
    GET_MENUS,
    {
      skip: !menuName,
    }
  );

  // 从所有菜单中找到匹配名称的菜单
  const menu = data?.menus?.nodes?.find((menu: Menu) => menu.name === menuName);
  const flatMenuItems = menu?.menuItems?.nodes || [];
  
  // 将扁平菜单项转换为层次结构
  const hierarchicalMenuItems = flatListToHierarchical(flatMenuItems);

  return {
    menu,
    menuItems: flatMenuItems,
    hierarchicalMenuItems,
    loading,
    error,
    refetch,
  };
}; 