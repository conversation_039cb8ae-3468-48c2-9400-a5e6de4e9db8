import { useQuery } from '@apollo/client';
import { GET_HOME_DATA } from '../lib/graphql/queries';
import { Post } from '../types/post';
import { Category } from '../types/post';

interface HomeData {
  featuredPosts: {
    nodes: Post[];
  };
  recentPosts: {
    nodes: Post[];
  };
  categories: {
    nodes: Category[];
  };
}

interface UseHomeDataOptions {
  featuredPostsCount?: number;
  recentPostsCount?: number;
}

/**
 * 获取首页数据的Hook
 * @param options 查询选项，包括精选文章和最新文章的数量
 * @returns 首页数据、加载状态和错误信息
 */
export const useHomeData = (options: UseHomeDataOptions = {}) => {
  const { featuredPostsCount = 5, recentPostsCount = 10 } = options;

  const { data, loading, error, refetch } = useQuery<HomeData>(
    GET_HOME_DATA,
    {
      variables: { featuredPostsCount, recentPostsCount },
      notifyOnNetworkStatusChange: true,
    }
  );

  return {
    featuredPosts: data?.featuredPosts?.nodes || [],
    recentPosts: data?.recentPosts?.nodes || [],
    categories: data?.categories?.nodes || [],
    loading,
    error,
    refetch,
  };
}; 