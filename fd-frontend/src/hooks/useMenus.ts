import { useQuery } from '@apollo/client';
import { GET_MENUS } from '../lib/graphql/queries';

// 菜单项类型定义
interface MenuItem {
  id: string;
  title: string;
  label: string;
  url: string;
  target: string;
  parentId: string;
  cssClasses: string[];
}

// 菜单类型定义
interface Menu {
  id: string;
  name: string;
  menuItems: {
    nodes: MenuItem[];
  };
}

interface MenusData {
  menus: {
    nodes: Menu[];
  };
}

/**
 * 获取所有菜单的Hook
 * @returns 所有菜单数据、加载状态和错误信息
 */
export const useMenus = () => {
  const { data, loading, error, refetch } = useQuery<MenusData>(GET_MENUS);

  return {
    menus: data?.menus?.nodes || [],
    loading,
    error,
    refetch,
  };
}; 