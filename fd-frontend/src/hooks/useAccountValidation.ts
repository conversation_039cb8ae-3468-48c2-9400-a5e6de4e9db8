import { useState } from 'react';
import { useLazyQuery } from '@apollo/client';
import { CHECK_ACCOUNT_EXISTS } from '../lib/graphql/queries';

type ValidationType = 'email' | 'username' | 'phone';

interface ValidationResult {
  isValid: boolean | null;
  isChecking: boolean;
  message: string;
}

/**
 * 账号信息验证Hook
 * 用于检查邮箱、用户名、手机号是否已被使用
 */
export function useAccountValidation() {
  // 使用惰性查询，只在需要验证时才执行
  const [checkAccountExists, { loading }] = useLazyQuery(CHECK_ACCOUNT_EXISTS);
  
  // 存储各字段的验证状态
  const [validationState, setValidationState] = useState<Record<string, ValidationResult>>({
    email: { isValid: null, isChecking: false, message: '' },
    username: { isValid: null, isChecking: false, message: '' },
    phone: { isValid: null, isChecking: false, message: '' },
  });

  /**
   * 验证账号信息
   * @param type 验证类型（email/username/phone）
   * @param value 要验证的值
   * @returns Promise<ValidationResult>
   */
  const validateAccount = async (type: ValidationType, value: string): Promise<ValidationResult> => {
    // 空值检查
    if (!value || value.trim() === '') {
      const result = { isValid: null, isChecking: false, message: '' };
      setValidationState(prev => ({ ...prev, [type]: result }));
      return result;
    }
    
    // 基本格式验证
    let isFormatValid = true;
    let formatMessage = '';
    
    // 根据类型执行格式验证
    if (type === 'email') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      isFormatValid = emailRegex.test(value);
      formatMessage = isFormatValid ? '' : '邮箱格式不正确';
    } else if (type === 'username') {
      const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
      isFormatValid = usernameRegex.test(value);
      formatMessage = isFormatValid ? '' : '用户名只能包含字母、数字和下划线，长度3-20位';
    } else if (type === 'phone') {
      const phoneRegex = /^1[3-9]\d{9}$/;
      isFormatValid = phoneRegex.test(value);
      formatMessage = isFormatValid ? '' : '手机号格式不正确';
    }
    
    // 如果格式无效，直接返回结果
    if (!isFormatValid) {
      const result = { isValid: false, isChecking: false, message: formatMessage };
      setValidationState(prev => ({ ...prev, [type]: result }));
      return result;
    }
    
    // 更新验证中状态
    setValidationState(prev => ({
      ...prev,
      [type]: { isValid: null, isChecking: true, message: '验证中...' }
    }));
    
    try {
      // 调用GraphQL查询检查是否已存在
      const { data } = await checkAccountExists({
        variables: { type, value },
        fetchPolicy: 'no-cache' // 确保每次都从服务器获取最新数据
      });
      
      // 结果处理：如果存在，则无效；如果不存在，则有效
      const exists = data?.checkAccountExists === true;
      let message = '';
      
      if (exists) {
        switch (type) {
          case 'email':
            message = '该邮箱已被注册';
            break;
          case 'username':
            message = '该用户名已被使用';
            break;
          case 'phone':
            message = '该手机号已被注册';
            break;
        }
      }
      
      const result = { isValid: !exists, isChecking: false, message };
      setValidationState(prev => ({ ...prev, [type]: result }));
      return result;
    } catch (error) {
      console.error(`验证${type}时出错:`, error);
      
      // 验证出错时返回null，不阻止表单提交
      const result = { isValid: null, isChecking: false, message: '验证失败，请重试' };
      setValidationState(prev => ({ ...prev, [type]: result }));
      return result;
    }
  };
  
  /**
   * 重置验证状态
   * @param type 验证类型，不传则重置所有
   */
  const resetValidation = (type?: ValidationType) => {
    if (type) {
      setValidationState(prev => ({
        ...prev,
        [type]: { isValid: null, isChecking: false, message: '' }
      }));
    } else {
      setValidationState({
        email: { isValid: null, isChecking: false, message: '' },
        username: { isValid: null, isChecking: false, message: '' },
        phone: { isValid: null, isChecking: false, message: '' },
      });
    }
  };
  
  return {
    validateAccount,
    resetValidation,
    validationState,
    isValidating: loading
  };
} 