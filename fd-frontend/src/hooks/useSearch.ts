import { useQuery } from '@apollo/client';
import { SEARCH_POSTS, SEARCH_CONTENT } from '../lib/graphql/queries';
import { Post, CustomPost } from '../types/post';
import { useVITheme } from '@/contexts/VIThemeContext';
import { useState, useEffect } from 'react';

interface PostsSearchData {
  posts: {
    nodes: Post[];
  };
}

interface ContentSearchData {
  contentNodes: {
    nodes: (Post | CustomPost)[];
  };
}

interface ContentTypeCount {
  type: string;
  count: number;
}

export interface ContentSearchResult {
  id: string;
  title: string;
  uri?: string;
  slug: string;
  date: string;
  content?: string;
  excerpt?: string;
  contentType: string;
  featuredImage?: {
    node: {
      sourceUrl: string;
      altText?: string;
    }
  };
}

interface SearchOptions {
  types?: string[];
  first?: number;
}

/**
 * 搜索文章的Hook
 * @param search 搜索关键词
 * @param first 获取的数量，默认为10
 * @returns 搜索结果数据、加载状态和错误信息
 */
export const useSearchPosts = (search: string, first: number = 10) => {
  const { data, loading, error, refetch } = useQuery<PostsSearchData>(
    SEARCH_POSTS,
    {
      variables: { search, first },
      skip: !search
    }
  );

  return {
    posts: data?.posts?.nodes || [],
    loading,
    error,
    refetch,
  };
};

/**
 * 高级搜索Hook - 支持搜索多种内容类型
 * @param search 搜索关键词
 * @param types 要搜索的内容类型数组，如 ['post', 'page', 'product']
 * @param first 每种类型获取的数量，默认为10
 * @returns 搜索结果数据、加载状态和错误信息
 */
export const useSearchContent = (search: string, types: string[] = ['post'], first: number = 10) => {
  // 将内容类型转换为大写以匹配 GraphQL ContentTypeEnum 枚举值
  const upperCaseTypes = types.map(type => type.toUpperCase());
  
  console.log('搜索参数:', { search, types: upperCaseTypes, first });
  
  const { data, loading, error, refetch } = useQuery<ContentSearchData>(
    SEARCH_CONTENT,
    {
      variables: { search, types: upperCaseTypes, first },
      skip: !search || types.length === 0
    }
  );

  console.log('GraphQL查询结果:', data?.contentNodes?.nodes);
  
  if (error) {
    console.error('GraphQL查询错误:', error);
  }

  // 处理搜索结果，将不同内容类型的结果标准化
  const results: ContentSearchResult[] = data?.contentNodes?.nodes.map(node => {
    if (!node) {
      console.log('节点为空');
      return null;
    }
    
    console.log('处理节点详情:', {
      id: node.id,
      title: node.title,
      date: node.date,
      // 使用类型断言访问__typename，仅用于日志输出
      __typename: (node as any).__typename
    });
    
    try {
      // 确保title字段一定有值
      let title = '无标题';
      if (node.title && typeof node.title === 'string') {
        title = node.title;
      } else if (node.title && typeof node.title === 'object' && (node.title as any).rendered) {
        title = (node.title as any).rendered;
      }
      
      // 确保date字段正确格式化
      let dateStr = '';
      if (node.date) {
        dateStr = node.date;
      } else if ((node as any).modified) {
        dateStr = (node as any).modified;
      }
      
      return {
        id: node.id || '',
        title: title,
        slug: node.slug || '',
        date: dateStr,
        content: (node as any).content || '',
        excerpt: (node as any).excerpt || '',
        contentType: (node as any).__typename || 'Unknown',
        featuredImage: node.featuredImage,
        uri: (node as any).uri || '',
        shortUuid: (node as any).shortUuid || '',
      };
    } catch (err) {
      console.error('处理搜索结果节点错误:', err, node);
      return {
        id: node.id || '',
        title: '处理错误',
        slug: '',
        date: '',
        contentType: 'Error',
      };
    }
  }).filter(Boolean) as ContentSearchResult[] || [];

  // 将处理后的结果记录到控制台
  console.log('处理后的搜索结果:', results);

  // 计算每种类型的结果数量
  const typeCounts: ContentTypeCount[] = [];
  if (results.length > 0) {
    const counts: Record<string, number> = {};
    results.forEach(result => {
      counts[result.contentType] = (counts[result.contentType] || 0) + 1;
    });
    
    Object.entries(counts).forEach(([type, count]) => {
      typeCounts.push({ type, count });
    });
  }

  return {
    results,
    typeCounts,
    loading,
    error,
    refetch,
  };
};

/**
 * 使用Meilisearch进行搜索
 * @param search 搜索关键词
 * @param options 搜索选项
 * @returns 搜索结果数据、加载状态和错误信息
 */
export const useMeilisearchSearch = (search: string, options: SearchOptions = {}) => {
  const { types = ['post'], first = 10 } = options;
  const { settings } = useVITheme();
  const [results, setResults] = useState<ContentSearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [typeCounts, setTypeCounts] = useState<ContentTypeCount[]>([]);

  useEffect(() => {
    const fetchFromMeilisearch = async () => {
      if (!search) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`${settings.meilisearchApiUrl}/indexes/${settings.meilisearchIndexName}/search`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${settings.meilisearchApiKey}`
          },
          body: JSON.stringify({
            q: search,
            limit: first,
            filter: types.length > 0 ? `contentType IN [${types.map(t => `"${t}"`).join(',')}]` : undefined
          })
        });
        
        if (!response.ok) {
          throw new Error('搜索请求失败');
        }
        
        const data = await response.json();
        
        // 格式化结果
        const formattedResults: ContentSearchResult[] = data.hits.map((hit: any) => ({
          id: hit.id,
          title: hit.title,
          slug: hit.slug,
          date: hit.date,
          content: hit.content,
          excerpt: hit.excerpt,
          contentType: hit.contentType,
          featuredImage: hit.featuredImage,
          uri: hit.uri
        }));
        
        setResults(formattedResults);
        
        // 计算类型计数
        const counts: Record<string, number> = {};
        formattedResults.forEach(result => {
          counts[result.contentType] = (counts[result.contentType] || 0) + 1;
        });
        
        const newTypeCounts: ContentTypeCount[] = [];
        Object.entries(counts).forEach(([type, count]) => {
          newTypeCounts.push({ type, count });
        });
        
        setTypeCounts(newTypeCounts);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('搜索出错'));
      } finally {
        setLoading(false);
      }
    };
    
    fetchFromMeilisearch();
  }, [search, settings.meilisearchApiUrl, settings.meilisearchApiKey, settings.meilisearchIndexName, first, types]);
  
  return {
    results,
    typeCounts,
    loading,
    error,
    refetch: () => {
      // 手动触发重新搜索
      setLoading(true);
    }
  };
};

/**
 * 通用搜索Hook - 根据设置自动选择搜索引擎
 * @param search 搜索关键词
 * @param options 搜索选项
 * @returns 搜索结果数据、加载状态和错误信息
 */
export const useSearch = (search: string, options: SearchOptions = {}) => {
  const { settings } = useVITheme();
  
  // 使用GraphQL进行搜索
  const graphqlSearch = useSearchContent(
    search, 
    options.types || ['post'], 
    options.first || 10
  );
  
  // 使用Meilisearch进行搜索
  const meilisearchSearch = useMeilisearchSearch(search, options);
  
  // 由于Meilisearch连接被拒绝，临时强制使用GraphQL搜索
  // 后续可以根据settings.searchEngineType恢复配置
  // if (settings.searchEngineType === 'meilisearch') {
  //   return meilisearchSearch;
  // }
  
  // 强制使用GraphQL搜索
  console.log('当前使用GraphQL搜索引擎');
  return graphqlSearch;
}; 