import { useQuery } from '@apollo/client';
import { GET_PAGES } from '../lib/graphql/queries';
import { Page } from '../types/page';

interface PagesData {
  pages: {
    nodes: Page[];
  };
}

interface UsePagesOptions {
  first?: number;
  after?: string;
}

/**
 * 获取页面列表的Hook
 * @param options 查询选项
 * @returns 页面列表数据、加载状态和错误信息
 */
export const usePages = (options: UsePagesOptions = {}) => {
  const { first = 10, after } = options;

  const { data, loading, error, fetchMore, refetch } = useQuery<PagesData>(
    GET_PAGES,
    {
      variables: { first, after },
      notifyOnNetworkStatusChange: true,
    }
  );

  const loadMore = (afterCursor: string) => {
    return fetchMore({
      variables: {
        first,
        after: afterCursor,
      },
      updateQuery: (prev: PagesData, { fetchMoreResult }: { fetchMoreResult: PagesData | undefined }) => {
        if (!fetchMoreResult) return prev;
        return {
          pages: {
            ...fetchMoreResult.pages,
            nodes: [...prev.pages.nodes, ...fetchMoreResult.pages.nodes],
          },
        };
      },
    });
  };

  return {
    pages: data?.pages?.nodes || [],
    loading,
    error,
    loadMore,
    refetch,
  };
}; 