import { useQuery } from '@apollo/client';
import { GET_LATEST_POSTS, GET_POSTS_BY_CATEGORY, GET_POSTS_BY_TAG } from '../lib/graphql/queries';
import { Post } from '../types/post';
import { useMemo } from 'react';
import { useSettings } from '@/contexts/SettingsContext';

interface PostsData {
  posts: {
    nodes: Post[];
    pageInfo?: {
      hasNextPage: boolean;
      endCursor: string;
    };
  };
}

interface UsePostsOptions {
  first?: number;
  after?: string;
  categoryId?: number;
  tagId?: string;
}

interface CategoryQueryVariables {
  categoryId: number;
  first: number;
  after?: string;
}

interface TagQueryVariables {
  tagId: string;
  first: number;
  after?: string;
}

interface LatestQueryVariables {
  first: number;
  after?: string;
}

type QueryVariables = CategoryQueryVariables | TagQueryVariables | LatestQueryVariables;

/**
 * 获取文章列表的Hook
 * @param options 查询选项
 * @returns 文章列表数据、加载状态和错误信息
 */
export const usePosts = (options: UsePostsOptions = {}) => {
  const { postsPerPage } = useSettings();
  const { first: firstOption, after, categoryId, tagId } = options;
  const first = firstOption ?? postsPerPage;

  // 根据参数选择不同的查询
  const queryInfo = (() => {
    if (categoryId) {
      return {
        query: GET_POSTS_BY_CATEGORY,
        variables: { categoryId, first, after } as CategoryQueryVariables
      };
    } else if (tagId) {
      return {
        query: GET_POSTS_BY_TAG,
        variables: { tagId, first, after } as TagQueryVariables
      };
    } else {
      return {
        query: GET_LATEST_POSTS,
        variables: { first, after } as LatestQueryVariables
      };
    }
  })();

  const { data, loading, error, fetchMore, refetch } = useQuery<PostsData>(
    queryInfo.query,
    {
      variables: queryInfo.variables,
      notifyOnNetworkStatusChange: true,
    }
  );

  // 对获取的文章数据进行去重处理
  const uniquePosts = useMemo(() => {
    if (!data?.posts?.nodes) return [];
    
    // 使用Map去重，保留相同ID的第一个文章
    const postsMap = new Map();
    data.posts.nodes.forEach(post => {
      if (!postsMap.has(post.id)) {
        postsMap.set(post.id, post);
      }
    });
    
    const result = Array.from(postsMap.values());
    
    return result;
  }, [data?.posts?.nodes]);

  const loadMore = (afterCursor: string) => {
    // 创建一个新的变量对象，确保类型一致
    const newVariables = {
      ...queryInfo.variables,
      after: afterCursor,
    };

    console.log('[usePosts] loadMore called:', {
      afterCursor,
      newVariables,
      currentPostsCount: uniquePosts.length,
      currentPageInfo: data?.posts?.pageInfo
    });

    return fetchMore({
      variables: newVariables,
      updateQuery: (prev, { fetchMoreResult }) => {
        console.log('[usePosts] updateQuery called:', {
          prevPostsCount: prev.posts.nodes.length,
          fetchMoreResultExists: !!fetchMoreResult,
          fetchMorePostsCount: fetchMoreResult?.posts.nodes.length,
          fetchMorePageInfo: fetchMoreResult?.posts.pageInfo
        });

        if (!fetchMoreResult) return prev;

        // 检查重复项
        const prevIds = new Set(prev.posts.nodes.map(post => post.id));

        // 过滤掉已经存在的文章，防止重复
        const uniqueNewPosts = fetchMoreResult.posts.nodes.filter(post => !prevIds.has(post.id));

        console.log('[usePosts] after deduplication:', {
          prevCount: prev.posts.nodes.length,
          newCount: fetchMoreResult.posts.nodes.length,
          uniqueNewCount: uniqueNewPosts.length,
          finalCount: prev.posts.nodes.length + uniqueNewPosts.length,
          newPageInfo: fetchMoreResult.posts.pageInfo
        });

        return {
          posts: {
            ...fetchMoreResult.posts,
            nodes: [...prev.posts.nodes, ...uniqueNewPosts], // 合并时只添加唯一的新文章
          },
        };
      },
    });
  };

  return {
    posts: uniquePosts, // 返回去重后的文章列表
    pageInfo: data?.posts?.pageInfo,
    loading,
    error,
    loadMore,
    refetch,
  };
}; 