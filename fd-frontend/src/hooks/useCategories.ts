import { gql, useQuery } from '@apollo/client';

interface Category {
  id: string;
  name: string;
  slug: string;
  count: number;
}

interface CategoriesData {
  nodes: Category[];
}

/**
 * 动态获取自定义类型对应的分类法列表
 * @param postType 自定义文章类型 slug，例如 "note"
 * @param first    获取数量，默认 100
 */
export const useCategories = (postType: string, first: number = 100) => {
  // 生成 GraphQL 字段名，例如 note -> noteCategories
  const fieldName = `${postType}Categories`;
  // 构造查询字符串
  const QUERY_STRING = `query Get${postType.charAt(0).toUpperCase() + postType.slice(1)}Categories($first:Int){\n    ${fieldName}(first:$first){\n      nodes{\n        id\n        name\n        slug\n        count\n      }\n    }\n  }`;
  const GQL = gql`${QUERY_STRING}`;
  const { data, loading, error } = useQuery<{ [key: string]: CategoriesData }>(GQL, {
    variables: { first },
    skip: !postType,
    fetchPolicy: 'cache-first',
  });

  const categories: Category[] = data ? (data[fieldName]?.nodes || []) : [];
  return { categories, loading, error };
}; 