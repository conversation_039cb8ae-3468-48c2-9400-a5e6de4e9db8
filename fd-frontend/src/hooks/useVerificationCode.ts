import { useState } from 'react';
import { useMutation } from '@apollo/client';
import { 
  SEND_VERIFICATION_CODE, 
  VERIFY_VERIFICATION_CODE, 
  SEND_REGISTRATION_CODE,
  VERIFY_REGISTRATION_CODE,
  VERIFY_REGISTRATION_CODE_AND_GET_TOKEN
} from '../lib/graphql/mutations';
import { useAccountValidation } from './useAccountValidation';
import { useAuth } from './useAuth';

type TemplateData = {
  subject?: string;
  title?: string;
  intro?: string;
  expire_notice?: string;
  footer?: string;
};

interface UseVerificationCodeOptions {
  defaultType?: string;
  defaultEmail?: string;
}

// 发送验证码结果接口
export interface SendCodeResult {
  success: boolean;
  message: string;
}

// 验证结果接口
export interface VerifyCodeResult {
  success: boolean;
  isValid: boolean;
  message: string;
  token?: string;
  userId?: string;
}

export function useVerificationCode(options: UseVerificationCodeOptions = {}) {
  const { defaultType = 'default', defaultEmail = '' } = options;
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [countdown, setCountdown] = useState(0);
  
  const [sendVerificationCodeMutation] = useMutation(SEND_VERIFICATION_CODE);
  const [verifyVerificationCodeMutation] = useMutation(VERIFY_VERIFICATION_CODE);
  const [sendRegistrationCodeMutation] = useMutation(SEND_REGISTRATION_CODE);
  const [verifyRegistrationCodeMutation] = useMutation(VERIFY_REGISTRATION_CODE);
  const [verifyRegistrationCodeAndGetTokenMutation] = useMutation(VERIFY_REGISTRATION_CODE_AND_GET_TOKEN);
  
  // 添加账号验证hook
  const { validateAccount, validationState } = useAccountValidation();
  
  // 从Auth上下文获取发送手机验证码方法
  const { sendPhoneCode } = useAuth();
  
  // 发送验证码
  const sendVerificationCode = async (
    identifier: string,
    type: string = defaultType,
    templateData?: any
  ): Promise<SendCodeResult> => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log(`[useVerificationCode] 请求发送验证码: ${identifier}, 类型: ${type}`);
      
      // 如果是注册类型，先检查账号是否已存在
      if (type === 'registration' && identifier) {
        // 邮箱格式验证
        if (identifier.includes('@')) {
          // 验证邮箱是否已注册
          console.log('[useVerificationCode] 验证邮箱是否已注册:', identifier);
          const emailValidation = await validateAccount('email', identifier);
          if (emailValidation.isValid === false) {
            console.warn('[useVerificationCode] 邮箱已注册:', identifier);
            return {
              success: false,
              message: emailValidation.message || '该邮箱已被注册'
            };
          }
        } else if (/^1[3-9]\d{9}$/.test(identifier)) {
          // 验证手机号是否已注册
          console.log('[useVerificationCode] 验证手机号是否已注册:', identifier);
          const phoneValidation = await validateAccount('phone', identifier);
          if (phoneValidation.isValid === false) {
            console.warn('[useVerificationCode] 手机号已注册:', identifier);
            return {
              success: false,
              message: phoneValidation.message || '该手机号已被注册'
            };
          }
        }
      }
      
      // 根据标识符类型选择发送方式
      if (identifier.includes('@')) {
        // 使用邮箱发送
        console.log('[useVerificationCode] 通过邮箱发送验证码:', identifier);
        
        // 对于注册场景，使用专门的注册验证码mutation
        if (type === 'registration') {
          console.log('[useVerificationCode] 使用注册专用验证码API发送');
          try {
            const { data } = await sendRegistrationCodeMutation({
              variables: {
                email: identifier
              }
            });
            
            console.log('[useVerificationCode] 注册验证码API响应:', data);
            const result = data?.sendRegistrationCode;
            
            if (result?.success) {
              // 开始倒计时
              startCountdown();
              return {
                success: true,
                message: result.message || '验证码已发送'
              };
            } else {
              setError(result?.message || '发送验证码失败');
              return {
                success: false,
                message: result?.message || '发送验证码失败'
              };
            }
          } catch (err) {
            console.error('[useVerificationCode] 注册验证码API错误:', err);
            // 如果专用注册API失败，尝试使用通用API
            console.log('[useVerificationCode] 尝试使用通用验证码API');
          }
        }
        
        // 使用通用验证码mutation
        const { data } = await sendVerificationCodeMutation({
          variables: {
            email: identifier,
            type,
            templateData: templateData ? JSON.stringify(templateData) : undefined
          }
        });
        
        console.log('[useVerificationCode] 通用验证码API响应:', data);
        const result = data?.sendVerificationCode;
        
        if (result?.success) {
          // 开始倒计时
          startCountdown();
          return {
            success: true,
            message: result.message || '验证码已发送'
          };
        } else {
          setError(result?.message || '发送验证码失败');
          return {
            success: false,
            message: result?.message || '发送验证码失败'
          };
        }
      } else if (/^1[3-9]\d{9}$/.test(identifier)) {
        // 使用手机号发送
        console.log('[useVerificationCode] 通过手机号发送验证码:', identifier);
        const result = await sendPhoneCode({
          phone: identifier,
          nationCode: '86' // 默认使用中国区号
        });
        
        console.log('[useVerificationCode] 手机验证码API响应:', result);
        
        if (result.success) {
          // 开始倒计时
          startCountdown();
          return {
            success: true,
            message: result.message || '验证码已发送'
          };
        } else {
          setError(result.message || '发送验证码失败');
          return {
            success: false,
            message: result.message || '发送验证码失败'
          };
        }
      } else {
        console.warn('[useVerificationCode] 无效的邮箱或手机号:', identifier);
        setError('无效的邮箱或手机号');
        return {
          success: false,
          message: '无效的邮箱或手机号'
        };
      }
    } catch (err) {
      console.error('[useVerificationCode] 发送验证码出错:', err);
      const errorMessage = err instanceof Error ? err.message : '发送验证码失败';
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  };
  
  // 验证验证码
  const verifyVerificationCode = async (
    code: string,
    email: string = defaultEmail,
    type: string = defaultType,
    autoClear: boolean = true
  ) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // 对于注册场景，使用专门的验证mutation
      if (type === 'registration') {
        // 优先使用带令牌的验证（如果前端已更新，但为了兼容性保留旧方法）
        try {
          const { data } = await verifyRegistrationCodeAndGetTokenMutation({
            variables: {
              email,
              code
            }
          });
          
          return {
            success: data?.verifyRegistrationCodeAndGetToken?.success || false,
            isValid: data?.verifyRegistrationCodeAndGetToken?.success || false,
            message: data?.verifyRegistrationCodeAndGetToken?.message || '验证完成',
            token: data?.verifyRegistrationCodeAndGetToken?.token
          };
        } catch (tokenErr) {
          // 如果新的验证令牌API不可用，回退到旧的验证方法
          console.warn('验证令牌API不可用，回退到旧的验证方法', tokenErr);
          const { data } = await verifyRegistrationCodeMutation({
            variables: {
              email,
              code,
              autoClear
            }
          });
          
          return {
            success: data?.verifyRegistrationCode?.success || false,
            isValid: data?.verifyRegistrationCode?.isValid || false,
            message: data?.verifyRegistrationCode?.message || '验证完成'
          };
        }
      } else {
        // 使用通用验证码验证
        const { data } = await verifyVerificationCodeMutation({
          variables: {
            email,
            code,
            type,
            autoClear
          }
        });
        
        return {
          success: data?.verifyVerificationCode?.success || false,
          isValid: data?.verifyVerificationCode?.isValid || false,
          message: data?.verifyVerificationCode?.message || '验证完成',
          userId: data?.verifyVerificationCode?.userId
        };
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '验证码验证失败，请重试';
      setError(errorMessage);
      return {
        success: false,
        isValid: false,
        message: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  };
  
  // 启动倒计时
  const startCountdown = (seconds: number = 60) => {
    setCountdown(seconds);
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };
  
  // 清除错误
  const clearError = () => {
    setError(null);
  };
  
  return {
    sendVerificationCode,
    verifyVerificationCode,
    isLoading,
    error,
    countdown,
    clearError,
    validationState
  };
} 