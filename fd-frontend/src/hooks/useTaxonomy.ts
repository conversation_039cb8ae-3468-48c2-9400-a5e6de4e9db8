import { useQuery } from '@apollo/client';
import { GET_TAXONOMIES, GET_TAXONOMY } from '../lib/graphql/queries';

interface Taxonomy {
  id: string;
  name: string;
  label: string;
  description?: string;
  connectedContentTypes?: {
    nodes: {
      name: string;
      label: string;
    }[];
  };
  hierarchical: boolean;
  restBase: string;
}

interface TaxonomiesData {
  taxonomies: {
    nodes: Taxonomy[];
  };
}

interface TaxonomyData {
  taxonomy: Taxonomy;
}

/**
 * 获取所有自定义分类法的Hook
 * @returns 分类法列表数据、加载状态和错误信息
 */
export const useTaxonomies = () => {
  const { data, loading, error, refetch } = useQuery<TaxonomiesData>(GET_TAXONOMIES);

  return {
    taxonomies: data?.taxonomies?.nodes || [],
    loading,
    error,
    refetch,
  };
};

/**
 * 获取单个分类法详情的Hook
 * @param name 分类法名称
 * @returns 分类法数据、加载状态和错误信息
 */
export const useTaxonomy = (name: string) => {
  const { data, loading, error, refetch } = useQuery<TaxonomyData>(
    GET_TAXONOMY,
    {
      variables: { name },
      skip: !name,
    }
  );

  return {
    taxonomy: data?.taxonomy,
    loading,
    error,
    refetch,
  };
}; 