import { useQuery } from '@apollo/client';
import { GET_CATEGORIES, GET_CATEGORY_BY_SLUG } from '../lib/graphql/queries';
import { Category } from '../types/post';

interface CategoriesData {
  categories: {
    nodes: Category[];
  };
}

interface CategoryData {
  category: Category;
}

/**
 * 获取所有分类的Hook
 * @returns 分类列表数据、加载状态和错误信息
 */
export const useCategories = () => {
  const { data, loading, error, refetch } = useQuery<CategoriesData>(GET_CATEGORIES);

  return {
    categories: data?.categories?.nodes || [],
    loading,
    error,
    refetch,
  };
};

/**
 * 获取单个分类详情的Hook
 * @param slug 分类别名
 * @returns 分类数据、加载状态和错误信息
 */
export const useCategory = (slug: string) => {
  const { data, loading, error, refetch } = useQuery<CategoryData>(
    GET_CATEGORY_BY_SLUG,
    { 
      variables: { slug },
      skip: !slug
    }
  );

  return {
    category: data?.category,
    loading,
    error,
    refetch,
  };
}; 