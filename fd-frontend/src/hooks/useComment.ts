import { useQuery, useMutation, ApolloCache } from '@apollo/client';
import { 
  GET_POST_COMMENTS, 
  GET_COMMENT,
  GET_COMMENT_REPLIES,
  GET_COMMENTS_BY_STATUS,
  GET_CONTENT_NODE_COMMENTS
} from '../lib/graphql/queries';
import {
  CREATE_COMMENT,
  UPDATE_COMMENT,
  DELETE_COMMENT,
  RESTORE_COMMENT,
  UPDATE_COMMENT_STATUS
} from '../lib/graphql/mutations';
import { Comment } from '../types/post';
import { useEffect } from 'react';

interface CommentsData {
  post?: {
    comments: {
      nodes: Comment[];
    };
  };
  comments?: {
    nodes: Comment[];
  };
}

interface CommentData {
  comment: Comment;
}

interface CommentRepliesData {
  comment: Comment & {
    replies: {
      nodes: Comment[];
    }
  }
}

interface CreateCommentData {
  createComment: {
    __typename?: string;
    comment: Comment;
    success: boolean;
  };
}

interface UpdateCommentData {
  updateComment: {
    __typename?: string;
    comment: Comment;
    success: boolean;
  };
}

interface DeleteCommentData {
  deleteComment: {
    __typename?: string;
    comment: Comment;
    success: boolean;
  };
}

interface RestoreCommentData {
  restoreComment: {
    __typename?: string;
    comment: Comment;
  };
}

interface UpdateCommentStatusData {
  updateComment: {
    __typename?: string;
    comment: Comment;
  };
}

interface CreateCommentInput {
  commentOn: number; // 文章ID
  content: string;   // 评论内容
  author?: string;   // 评论者名称
  authorEmail?: string; // 评论者邮箱
  authorUrl?: string;   // 评论者网址
  parent?: number;   // 父评论ID
}

interface UpdateCommentInput {
  id: string;        // 评论ID
  content: string;   // 新评论内容
}

interface ContentNodeCommentsData {
  contentNode?: {
    commentCount?: number;
    commentStatus?: string;
    comments?: {
      nodes: Comment[];
      pageInfo?: {
        hasNextPage: boolean;
        endCursor: string;
      };
    };
  };
}

// 评论状态枚举
export enum CommentStatusEnum {
  APPROVE = 'APPROVE',
  HOLD = 'HOLD',
  SPAM = 'SPAM',
  TRASH = 'TRASH'
}

/**
 * 获取文章评论列表的Hook
 * @param postId 文章ID
 * @param first 获取的评论数量，默认为20
 * @returns 评论列表数据、加载状态和错误信息
 */
export const useComments = (
  postId: string | number,
  first: number = 10,
  skip: boolean = false,
  order: 'ASC' | 'DESC' = 'ASC',
  orderby: string = 'COMMENT_DATE'
) => {
  const threadsPerPage = orderby === 'HOTTEST' ? 10 : first;

  const { data, loading, error, refetch, fetchMore } = useQuery<CommentsData>(
    GET_POST_COMMENTS,
    {
      variables: {
        postId,
        first: threadsPerPage,
        after: null,
        order,
        orderby,
        offset: 0,
      },
      skip: skip || !postId,
      notifyOnNetworkStatusChange: true,
      fetchPolicy: 'cache-first',
      nextFetchPolicy: 'cache-first',
    }
  );

  // 当排序变化时，不再强制 refetch
  // useEffect(() => {
  //   refetch({ postId, first, after: null, order, orderby });
  // }, [order, orderby, refetch, postId, first]);

  const comments = data?.post?.comments?.nodes || [];
  const pageInfo = (data?.post as any)?.comments?.pageInfo;

  const loadMore = () => {
    if (!pageInfo?.hasNextPage) return;

    if (orderby === 'HOTTEST') {
      // '最热' 排序使用 offset 分页
      const topLevelCount = comments.filter((n:any)=>!n.parentId).length;
      return fetchMore({
        variables: {
          postId,
          first: threadsPerPage,
          order,
          orderby,
          offset: topLevelCount,
        },
        updateQuery: (prev: any, { fetchMoreResult }: { fetchMoreResult?: any }) => {
          if (!fetchMoreResult) return prev;
          
          const newNodes = fetchMoreResult.post?.comments?.nodes || [];
          const prevNodes = prev.post?.comments?.nodes || [];

          // 合并并去重
          const allNodes = [...prevNodes, ...newNodes];
          const uniqueNodes = allNodes.filter(
            (node, index, self) => index === self.findIndex((t) => t.id === node.id)
          );

          return {
            post: {
              ...prev.post!,
              comments: {
                ...fetchMoreResult.post!.comments,
                nodes: uniqueNodes,
              },
            },
          };
        },
      });
    }

    // 默认使用光标分页
    return fetchMore({
      variables: {
        postId,
        first: threadsPerPage,
        after: pageInfo.endCursor,
        order,
        orderby,
        offset: comments.filter((n:any)=>!n.parentId).length,
      },
      updateQuery: (prev: any, { fetchMoreResult }: { fetchMoreResult?: any }) => {
        if (!fetchMoreResult) return prev;

        const newNodes = fetchMoreResult.post?.comments?.nodes || [];
        const prevNodes = prev.post?.comments?.nodes || [];

        return {
          post: {
            ...prev.post!,
            comments: {
              ...fetchMoreResult.post!.comments,
              nodes: [...prevNodes, ...newNodes],
            },
          },
        } as CommentsData;
      },
    });
  };

  return {
    comments,
    loading,
    error,
    refetch,
    hasNextPage: pageInfo?.hasNextPage || false,
    pageInfo,
    loadMore,
  };
};

/**
 * 获取单个评论详情的Hook
 * @param id 评论ID
 * @returns 评论数据、加载状态和错误信息
 */
export const useComment = (id: string) => {
  const { data, loading, error, refetch } = useQuery<CommentData>(
    GET_COMMENT,
    {
      variables: { id },
      skip: !id
    }
  );

  return {
    comment: data?.comment,
    loading,
    error,
    refetch,
  };
};

/**
 * 获取评论回复列表的Hook
 * @param id 评论ID
 * @param first 获取的回复数量，默认为50
 * @returns 评论回复列表数据、加载状态和错误信息
 */
export const useCommentReplies = (id: string, first: number = 50) => {
  const { data, loading, error, refetch } = useQuery<CommentRepliesData>(
    GET_COMMENT_REPLIES,
    {
      variables: { id, first },
      skip: !id
    }
  );

  return {
    parentComment: data?.comment,
    replies: data?.comment?.replies?.nodes || [],
    loading,
    error,
    refetch,
  };
};

/**
 * 按状态获取评论列表的Hook
 * @param status 评论状态数组
 * @param first 获取的评论数量，默认为50
 * @returns 评论列表数据、加载状态和错误信息
 */
export const useCommentsByStatus = (status: CommentStatusEnum[], first: number = 50) => {
  const { data, loading, error, refetch } = useQuery<CommentsData>(
    GET_COMMENTS_BY_STATUS,
    {
      variables: { status, first },
      skip: !status || status.length === 0
    }
  );

  return {
    comments: data?.comments?.nodes || [],
    loading,
    error,
    refetch,
  };
};

/**
 * 创建评论的Hook
 * @returns 创建评论的变更函数、加载状态和错误信息
 */
export const useCreateComment = () => {
  const [createCommentMutation, { data, loading, error }] = useMutation<CreateCommentData>(
    CREATE_COMMENT
  );

  /**
   * 创建评论（支持乐观更新）
   * @param input WordPress GraphQL 的 CreateCommentInput
   * @param options 额外参数：postId(必需)、currentUser(可选)、skipOptimisticUpdate(可选)
   */
  const handleCreateComment = async (
    input: CreateCommentInput,
    options?: {
      postId: number;
      requiresApproval?: boolean;
      isCustomType?: boolean;
      skipOptimisticUpdate?: boolean; // 新增：是否跳过乐观更新
      order?: 'ASC' | 'DESC';
      orderby?: string;
      currentUser?: {
        name?: string;
        avatarUrl?: string;
      };
    }
  ) => {
    const { postId, skipOptimisticUpdate = false } = options || {} as any;
    if (!postId) throw new Error('postId is required for optimistic comment');

    console.log('[DEBUG_OPT_COMMENT] input:', input);
    console.log('[DEBUG_OPT_COMMENT] options:', options);
    console.log('[DEBUG_OPT_COMMENT] skipOptimisticUpdate:', skipOptimisticUpdate);

    // 如果需要跳过乐观更新（如需要审核），则使用简单的mutation
    if (skipOptimisticUpdate) {
      console.log('[DEBUG_OPT_COMMENT] Skipping optimistic update due to comment moderation');
      try {
        const response = await createCommentMutation({
          variables: { input },
          // 不提供 optimisticResponse 和 update 函数
        });

        console.log('[DEBUG_OPT_COMMENT] Response (no optimistic update):', response);

        // 检查响应中是否有错误
        if (response?.errors && response.errors.length > 0) {
          const graphQLError = response.errors[0];
          console.error('[DEBUG_OPT_COMMENT] GraphQL error in response:', graphQLError);

          // 创建一个包含 GraphQL 错误的 Error 对象
          const error = new Error(graphQLError.message) as any;
          error.graphQLErrors = response.errors;
          error.networkError = null;
          throw error;
        }

        return response?.data?.createComment;
      } catch (error: any) {
        console.error('[DEBUG_OPT_COMMENT] Mutation error (no optimistic update):', error);
        console.error('[DEBUG_OPT_COMMENT] Error details:', {
          message: error?.message,
          graphQLErrors: error?.graphQLErrors,
          networkError: error?.networkError,
          extraInfo: error?.extraInfo
        });
        throw error; // 重新抛出错误，让 CommentForm 能够捕获
      }
    }

    // 构造乐观评论对象（仅在不跳过乐观更新时）
    const tempId = `optimistic-${Date.now()}`;
    const optimisticComment: Comment = {
      __typename: 'Comment',
      id: tempId,
      databaseId: 0,
      content: input.content,
      date: new Date().toISOString(),
      parentId: input.parent ? String(input.parent) : null as any,
      status: options?.requiresApproval ? 'HOLD' : 'APPROVE',
      author: {
        __typename: 'CommentToCommenterConnectionEdge',
        node: {
          __typename: 'Commenter',
          name: options?.currentUser?.name || 'Me',
          url: '',
          avatar: {
            __typename: 'Avatar',
            url: options?.currentUser?.avatarUrl || ''
          }
        }
      }
    } as any;

    console.log('[DEBUG_OPT_COMMENT] optimistic comment:', optimisticComment);

    try {
      const response = await createCommentMutation({
        variables: { input },
        optimisticResponse: {
          createComment: {
            __typename: 'CreateCommentPayload',
            success: true,
            comment: optimisticComment,
          },
        },
        update: (cache: ApolloCache<any>, { data: mutationData }: { data?: CreateCommentData | null }) => {
          const newComment = mutationData?.createComment?.comment;
          if (!newComment) return;

          // Add a guard to ensure options is defined
          if (!options) {
            console.warn('[DEBUG_OPT_COMMENT] Options are undefined, skipping cache update.');
            return;
          }
          
          console.log('[DEBUG_OPT_COMMENT] update cache with', newComment);

          const query = options.isCustomType ? GET_CONTENT_NODE_COMMENTS : GET_POST_COMMENTS;
          const variables = {
            id: postId,
            postId: postId,
            first: 20,
            after: null,
            order: options.order || 'DESC',
            orderby: options.orderby || 'HOTTEST',
          };

          if (options.isCustomType) {
            delete (variables as any).postId;
          } else {
            delete (variables as any).id;
          }

          try {
            const existingData: any = cache.readQuery({ query, variables });
            if (!existingData) {
              console.warn('[DEBUG_OPT_COMMENT] No existing data found in cache for optimistic update.');
              return;
            }

            // Deep copy to avoid read-only errors
            const newData = JSON.parse(JSON.stringify(existingData));
            const root = options.isCustomType ? newData.contentNode : newData.post;

            if (newComment.parentId) {
              // --- Logic for nested comments (2nd/3rd level) ---
              let injected = false;
              const findAndInject = (comments: any[], parentId: string, newReply: Comment): boolean => {
                for (const comment of comments) {
                  if (comment.id === parentId || comment.databaseId === Number(parentId)) { // Support both string and numeric parentId
                    if (!comment.children) comment.children = [];
                    comment.children.unshift(newReply);
                    injected = true;
                    return true;
                  }
                  if (comment.children && comment.children.length > 0) {
                    if (findAndInject(comment.children, parentId, newReply)) return true;
                  }
                }
                return false;
              };

              findAndInject(root.comments.nodes, newComment.parentId, newComment);
              
              // Key Fix: Also add the new reply to the root list to ensure buildCommentTree sees it.
              root.comments.nodes.unshift(newComment);

              if (injected) {
                cache.writeQuery({ query, variables, data: newData });
              } else {
                 console.warn('[DEBUG_OPT_COMMENT] Parent comment not found in cache for optimistic update, but added to root list anyway.');
                 // Still write to cache even if parent is not visible (e.g., on another page)
                 cache.writeQuery({ query, variables, data: newData });
              }

            } else {
              // --- Logic for top-level comments ---
              root.comments.nodes.unshift(newComment);
              if (root.commentCount !== null && typeof root.commentCount !== 'undefined') {
                root.commentCount += 1;
              }
              cache.writeQuery({ query, variables, data: newData });
            }
          } catch (e) {
            console.warn('[DEBUG_OPT_COMMENT] Failed to update cache optimistically.', e);
          }
        },
      });

      console.log('[DEBUG_OPT_COMMENT] Response (with optimistic update):', response);

      // 检查响应中是否有错误
      if (response?.errors && response.errors.length > 0) {
        const graphQLError = response.errors[0];
        console.error('[DEBUG_OPT_COMMENT] GraphQL error in response:', graphQLError);

        // 创建一个包含 GraphQL 错误的 Error 对象
        const error = new Error(graphQLError.message) as any;
        error.graphQLErrors = response.errors;
        error.networkError = null;
        throw error;
      }

      return response?.data?.createComment;
    } catch (error: any) {
      console.error('[DEBUG_OPT_COMMENT] Mutation error (with optimistic update):', error);
      console.error('[DEBUG_OPT_COMMENT] Error details:', {
        message: error?.message,
        graphQLErrors: error?.graphQLErrors,
        networkError: error?.networkError,
        extraInfo: error?.extraInfo
      });
      throw error; // 重新抛出错误，让 CommentForm 能够捕获
    }
  };

  return {
    createComment: handleCreateComment,
    newComment: data?.createComment?.comment,
    loading,
    error,
  };
};

/**
 * 更新评论的Hook
 * @returns 更新评论的变更函数、加载状态和错误信息
 */
export const useUpdateComment = () => {
  const [updateComment, { data, loading, error }] = useMutation<UpdateCommentData>(
    UPDATE_COMMENT
  );

  const handleUpdateComment = async (input: UpdateCommentInput) => {
    try {
      const response = await updateComment({
        variables: { input }
      });
      return response?.data?.updateComment;
    } catch (err) {
      console.error('更新评论失败:', err);
      throw err;
    }
  };

  return {
    updateComment: handleUpdateComment,
    updatedComment: data?.updateComment?.comment,
    loading,
    error,
  };
};

/**
 * 删除评论的Hook
 * @returns 删除评论的变更函数、加载状态和错误信息
 */
export const useDeleteComment = () => {
  const [deleteComment, { data, loading, error }] = useMutation<DeleteCommentData>(
    DELETE_COMMENT
  );

  const handleDeleteComment = async (id: string) => {
    try {
      const response = await deleteComment({
        variables: { id }
      });
      return response?.data?.deleteComment;
    } catch (err) {
      console.error('删除评论失败:', err);
      throw err;
    }
  };

  return {
    deleteComment: handleDeleteComment,
    deletedComment: data?.deleteComment?.comment,
    loading,
    error,
  };
};

/**
 * 恢复评论的Hook
 * @returns 恢复评论的变更函数、加载状态和错误信息
 */
export const useRestoreComment = () => {
  const [restoreComment, { data, loading, error }] = useMutation<RestoreCommentData>(
    RESTORE_COMMENT
  );

  const handleRestoreComment = async (id: string) => {
    try {
      const response = await restoreComment({
        variables: {
          input: { id }
        }
      });
      return response?.data?.restoreComment;
    } catch (err) {
      console.error('恢复评论失败:', err);
      throw err;
    }
  };

  return {
    restoreComment: handleRestoreComment,
    restoredComment: data?.restoreComment?.comment,
    loading,
    error,
  };
};

/**
 * 更新评论状态的Hook
 * @returns 更新评论状态的变更函数、加载状态和错误信息
 */
export const useUpdateCommentStatus = () => {
  const [updateCommentStatus, { data, loading, error }] = useMutation<UpdateCommentStatusData>(
    UPDATE_COMMENT_STATUS
  );

  const handleUpdateCommentStatus = async (id: string, status: CommentStatusEnum) => {
    try {
      const response = await updateCommentStatus({
        variables: { id, status }
      });
      return response?.data?.updateComment;
    } catch (err) {
      console.error('更新评论状态失败:', err);
      throw err;
    }
  };

  return {
    updateCommentStatus: handleUpdateCommentStatus,
    updatedComment: data?.updateComment?.comment,
    loading,
    error,
  };
};

/**
 * 获取任何内容类型节点评论列表的Hook
 * @param contentNodeId 内容节点ID
 * @param first 获取的评论数量，默认为20
 * @returns 评论列表数据、加载状态和错误信息
 */
export const useContentNodeComments = (
  contentNodeId: string | number,
  first: number = 10,
  skip: boolean = false,
  order: 'ASC' | 'DESC' = 'ASC',
  orderby: string = 'COMMENT_DATE'
) => {
  const { data, loading, error, refetch, fetchMore } = useQuery<ContentNodeCommentsData>(
    GET_CONTENT_NODE_COMMENTS,
    {
      variables: {
        id: contentNodeId,
        first,
        after: null,
        order,
        orderby,
        offset: 0,
      },
      skip: skip || !contentNodeId,
      notifyOnNetworkStatusChange: true,
      fetchPolicy: 'cache-first',
      nextFetchPolicy: 'cache-first',
    }
  );

  // 当排序变化时，不再强制 refetch
  // useEffect(() => {
  //  refetch({ id: contentNodeId, first, after: null, order, orderby });
  // }, [order, orderby, refetch, contentNodeId, first]);

  // Helper to extract comments & pageInfo regardless of typename
  const extract = (node: any) => {
    const commentsConn = node?.comments;
    return {
      comments: commentsConn?.nodes || [],
      pageInfo: commentsConn?.pageInfo,
    };
  };

  let comments: Comment[] = [];
  let pageInfo: { hasNextPage: boolean; endCursor: string } | undefined;
  let commentCount = 0;
  let commentStatus: string | undefined;
  
  if (data?.contentNode) {
    const node: any = data.contentNode;
    const result = extract(node);
    comments = result.comments;
    pageInfo = result.pageInfo;

    if ('commentStatus' in node) commentStatus = node.commentStatus;
    if ('commentCount' in node) commentCount = node.commentCount || 0;
    }
    
  const loadMore = () => {
    if (!pageInfo?.hasNextPage) return;

    if (orderby === 'HOTTEST') {
      // '最热' 排序使用 offset 分页
      return fetchMore({
        variables: {
          id: contentNodeId,
          first,
          order,
          orderby,
          offset: comments.filter((n:any)=>!n.parentId).length,
        },
        updateQuery: (prev: any, { fetchMoreResult }: { fetchMoreResult?: any }) => {
          if (!fetchMoreResult || !fetchMoreResult.contentNode) return prev;

          const prevComments = extract(prev.contentNode).comments;
          const newComments = extract(fetchMoreResult.contentNode).comments;
          const newPageInfo = extract(fetchMoreResult.contentNode).pageInfo;
          
          // 合并并去重
          const allNodes = [...prevComments, ...newComments];
          const uniqueNodes = allNodes.filter(
            (node, index, self) => index === self.findIndex((t) => t.id === node.id)
          );

          return {
            contentNode: {
              ...fetchMoreResult.contentNode,
              comments: {
                ...fetchMoreResult.contentNode.comments,
                nodes: uniqueNodes,
                pageInfo: newPageInfo,
              },
            },
          };
        },
      });
    }

    // 默认使用光标分页
    return fetchMore({
      variables: {
        id: contentNodeId,
        first,
        after: pageInfo.endCursor,
        order,
        orderby,
        offset: comments.filter((n:any)=>!n.parentId).length,
      },
      updateQuery: (prev: any, { fetchMoreResult }: { fetchMoreResult?: any }) => {
        if (!fetchMoreResult || !fetchMoreResult.contentNode) return prev;

        const prevComments = extract(prev.contentNode).comments;
        const newComments = extract(fetchMoreResult.contentNode).comments;
        const newPageInfo = extract(fetchMoreResult.contentNode).pageInfo;

        return {
          contentNode: {
            ...fetchMoreResult.contentNode,
            comments: {
              ...fetchMoreResult.contentNode.comments,
              nodes: [...prevComments, ...newComments],
              pageInfo: newPageInfo,
            },
          },
        } as ContentNodeCommentsData;
      },
    });
  };

  return {
    comments,
    commentCount,
    commentStatus,
    loading,
    error,
    refetch,
    hasNextPage: pageInfo?.hasNextPage || false,
    pageInfo,
    loadMore,
  };
}; 