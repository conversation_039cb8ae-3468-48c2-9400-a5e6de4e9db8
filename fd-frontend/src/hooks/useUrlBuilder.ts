import { useRoutePrefixes } from './useRoutePrefixes';
import {
  buildPostUrl as originalBuildPostUrl,
  buildCategoryUrl as originalBuildCategoryUrl,
  buildTagUrl as originalBuildTagUrl,
  buildTaxonomyUrl as originalBuildTaxonomyUrl,
  buildCustomPostUrl as originalBuildCustomPostUrl,
  buildSearchUrl,
  buildAuthorUrl as originalBuildAuthorUrl,
  buildPageUrl,
  buildFullPostUrl as originalBuildFullPostUrl
} from '../utils/url-builder';

/**
 * 统一的URL构建Hook
 * 封装所有URL生成函数，自动应用路由前缀配置
 * 在所有组件中使用相同的方式生成URL
 */
export function useUrlBuilder() {
  const { prefixes } = useRoutePrefixes();
  
  /**
   * 构建文章详情页URL，自动应用路由前缀
   * @param uuid 文章UUID
   * @param slug 文章别名
   * @returns 文章详情页URL
   */
  const buildPostUrl = (uuid: string | undefined, slug: string): string => {
    // 更健壮的UUID验证逻辑，确保大小写不敏感
    const validUuid = (uuid && typeof uuid === 'string' && uuid.trim() !== '')
      ? uuid 
      : '000000-000000';
    
    return originalBuildPostUrl(validUuid, slug, prefixes);
  };
  
  /**
   * 构建分类页URL，自动应用路由前缀
   * @param slug 分类别名
   * @returns 分类页URL
   */
  const buildCategoryUrl = (slug: string): string => {
    return originalBuildCategoryUrl(slug, prefixes);
  };
  
  /**
   * 构建标签页URL，自动应用路由前缀
   * @param slug 标签别名
   * @returns 标签页URL
   */
  const buildTagUrl = (slug: string): string => {
    return originalBuildTagUrl(slug, prefixes);
  };
  
  /**
   * 构建分类法页URL，自动应用路由前缀
   * @param taxonomy 分类法名称
   * @param slug 分类法项别名
   * @returns 分类法页URL
   */
  const buildTaxonomyUrl = (taxonomy: string, slug: string): string => {
    return originalBuildTaxonomyUrl(taxonomy, slug, prefixes);
  };
  
  /**
   * 构建自定义类型文章URL，自动应用路由前缀
   * @param type 自定义类型
   * @param uuid 文章UUID
   * @param slug 文章别名
   * @returns 自定义类型文章URL
   */
  const buildCustomPostUrl = (type: string, uuid: string, slug: string): string => {
    // 更健壮的UUID验证逻辑，确保大小写不敏感
    const validUuid = (uuid && typeof uuid === 'string' && uuid.trim() !== '')
      ? uuid 
      : '000000-000000';
      
    return originalBuildCustomPostUrl(type, validUuid, slug, prefixes);
  };
  
  /**
   * 构建作者页URL，自动应用路由前缀
   * @param slug 作者别名
   * @returns 作者页URL
   */
  const buildAuthorUrl = (slug: string): string => {
    return originalBuildAuthorUrl(slug, prefixes);
  };
  
  /**
   * 构建完整的文章URL，带域名
   * @param uuid 文章UUID
   * @param slug 文章别名
   * @param baseDomain 可选的基础域名
   * @returns 完整的文章URL
   */
  const buildFullPostUrl = (uuid: string, slug: string, baseDomain?: string): string => {
    // 更健壮的UUID验证逻辑，确保大小写不敏感
    const validUuid = (uuid && typeof uuid === 'string' && uuid.trim() !== '')
      ? uuid 
      : '000000-000000';
      
    return originalBuildFullPostUrl(validUuid, slug, prefixes, baseDomain);
  };
  
  // 返回所有URL构建函数
  return {
    buildPostUrl,
    buildCategoryUrl,
    buildTagUrl,
    buildTaxonomyUrl,
    buildCustomPostUrl,
    buildSearchUrl, // 直接导出，因为不需要prefixes
    buildAuthorUrl,
    buildPageUrl,   // 直接导出，因为不需要prefixes
    buildFullPostUrl
  };
} 