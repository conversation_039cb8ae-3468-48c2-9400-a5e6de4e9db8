import { useQuery } from '@apollo/client';
import { GET_SHARE_SETTINGS } from '@/lib/graphql/share';

// 定义查询返回的数据结构类型
interface ShareSettingsData {
  fdShareSettings: {
    isEnabled: boolean;
    platforms?: string[];
    posterLogo: string | null;
    defaultThumb: string | null;
    wechatDesc: string | null;
  };
}

/**
 * 自定义Hook，用于获取全站的社交分享设置
 */
export const useShare = () => {
  const { data, loading, error } = useQuery<ShareSettingsData>(GET_SHARE_SETTINGS, {
    // 添加缓存策略：优先使用缓存，减少网络请求
    fetchPolicy: 'cache-first',
    // 设置缓存时间：30分钟
    nextFetchPolicy: 'cache-first',
    // 错误策略：如果网络请求失败，使用缓存数据
    errorPolicy: 'ignore',
  });

  return {
    settings: data?.fdShareSettings,
    isLoading: loading,
    error,
  };
};