import { useQuery } from '@apollo/client';
import { GET_TAGS, GET_TAG_BY_SLUG } from '../lib/graphql/queries';
import { Tag } from '../types/post';

interface TagsData {
  tags: {
    nodes: Tag[];
  };
}

interface TagData {
  tag: Tag;
}

/**
 * 获取所有标签的Hook
 * @returns 标签列表数据、加载状态和错误信息
 */
export const useTags = () => {
  const { data, loading, error, refetch } = useQuery<TagsData>(GET_TAGS);

  return {
    tags: data?.tags?.nodes || [],
    loading,
    error,
    refetch,
  };
};

/**
 * 获取单个标签详情的Hook
 * @param slug 标签别名
 * @returns 标签数据、加载状态和错误信息
 */
export const useTag = (slug: string) => {
  const { data, loading, error, refetch } = useQuery<TagData>(
    GET_TAG_BY_SLUG,
    { 
      variables: { slug },
      skip: !slug
    }
  );

  return {
    tag: data?.tag,
    loading,
    error,
    refetch,
  };
}; 