'use client';

import { useRouter } from 'next/navigation';
import { useCallback } from 'react';

/**
 * 菜单刷新Hook
 * 提供菜单数据刷新功能，用于WebSocket事件处理
 */
export const useMenuRefresh = () => {
  const router = useRouter();

  /**
   * 刷新菜单数据
   * 通过router.refresh()触发服务端重新获取菜单数据
   */
  const refreshMenus = useCallback(() => {
    console.log('[MenuRefresh] Refreshing menu data...');
    
    // 使用router.refresh()会触发服务端重新执行，
    // 包括根布局中的fetchMenuData()调用
    router.refresh();
    
    console.log('[MenuRefresh] Menu refresh triggered');
  }, [router]);

  /**
   * 带延迟的菜单刷新
   * 用于避免频繁刷新
   */
  const refreshMenusWithDelay = useCallback((delay: number = 1000) => {
    console.log(`[MenuRefresh] Scheduling menu refresh in ${delay}ms...`);
    
    setTimeout(() => {
      refreshMenus();
    }, delay);
  }, [refreshMenus]);

  return {
    refreshMenus,
    refreshMenusWithDelay
  };
};
