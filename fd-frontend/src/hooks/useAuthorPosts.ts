import { useQuery } from '@apollo/client';
import { GET_POSTS_BY_AUTHOR } from '../lib/graphql/queries';
import { Post } from '../types/post';
import { useSettings } from '@/contexts/SettingsContext';

interface AuthorPostsData {
  posts: {
    nodes: Post[];
    pageInfo: {
      endCursor: string | null;
      hasNextPage: boolean;
    };
  };
}

interface UseAuthorPostsOptions {
  authorId: number; // 作者ID需要是数字
  first?: number;
  after?: string;
}

/**
 * 获取指定作者文章的Hook
 * @param options 查询选项，包括作者ID和分页信息
 * @returns 作者文章列表、加载状态和错误信息
 */
export const useAuthorPosts = (options: UseAuthorPostsOptions) => {
  const { postsPerPage } = useSettings();
  const { authorId, first: firstOption, after } = options;
  const first = firstOption ?? postsPerPage;

  // 如果authorId暂未可用，将在skip参数控制下延迟执行

  const { data, loading, error, fetchMore, refetch } = useQuery<AuthorPostsData>(
    GET_POSTS_BY_AUTHOR,
    {
      variables: { authorId, first, after },
      notifyOnNetworkStatusChange: true,
      skip: !authorId,
    }
  );

  const loadMore = (afterCursor: string) => {
    return fetchMore({
      variables: {
        authorId,
        first,
        after: afterCursor,
      },
      updateQuery: (prev: AuthorPostsData, { fetchMoreResult }: { fetchMoreResult: AuthorPostsData | undefined }) => {
        if (!fetchMoreResult) return prev;
        // 合并并去重
        const combined = [...prev.posts.nodes, ...fetchMoreResult.posts.nodes];
        const deduped = Array.from(new Map(combined.map((p) => [p.id, p])).values());
        return {
          posts: {
            ...fetchMoreResult.posts,
            nodes: deduped,
          },
        };
      },
    });
  };

  return {
    posts: data?.posts?.nodes || [],
    endCursor: data?.posts?.pageInfo?.endCursor || null,
    hasNextPage: data?.posts?.pageInfo?.hasNextPage || false,
    loading,
    error,
    loadMore,
    refetch,
  };
}; 