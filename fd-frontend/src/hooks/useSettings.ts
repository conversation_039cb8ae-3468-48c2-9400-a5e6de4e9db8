import { useQuery } from '@apollo/client';
import { 
  GET_ALL_SETTINGS, 
  GET_GENERAL_SETTINGS, 
  GET_READING_SETTINGS,
  GET_DISCUSSION_SETTINGS,
  GET_WRITING_SETTINGS
} from '../lib/graphql/queries';

interface GeneralSettings {
  title: string;
  description: string;
  url: string;
  email: string;
  language: string;
  timezone: string;
  dateFormat: string;
  timeFormat: string;
}

interface ReadingSettings {
  postsPerPage: number;
  defaultCategory: string;
  showOnFront: string;
  pageForPosts: string;
  pageOnFront: string;
}

interface DiscussionSettings {
  defaultCommentStatus: string;
  defaultPingStatus: string;
  commentModeration?: boolean;
}

interface WritingSettings {
  defaultCategory: string;
  defaultPostFormat: string;
  useSmilies: boolean;
}

interface AllSettingsData {
  allSettings: {
    generalSettings: GeneralSettings;
    readingSettings: ReadingSettings;
    discussionSettings: DiscussionSettings;
    writingSettings: WritingSettings;
  };
}

interface GeneralSettingsData {
  generalSettings: GeneralSettings;
}

interface ReadingSettingsData {
  readingSettings: ReadingSettings;
}

interface DiscussionSettingsData {
  discussionSettings: DiscussionSettings;
}

interface WritingSettingsData {
  writingSettings: WritingSettings;
}

/**
 * 获取所有设置的Hook
 * @returns 所有设置数据、加载状态和错误信息
 */
export const useAllSettings = () => {
  const { data, loading, error, refetch } = useQuery<AllSettingsData>(GET_ALL_SETTINGS);

  return {
    settings: data?.allSettings,
    loading,
    error,
    refetch,
  };
};

/**
 * 获取常规设置的Hook
 * @returns 常规设置数据、加载状态和错误信息
 */
export const useGeneralSettings = () => {
  const { data, loading, error, refetch } = useQuery<GeneralSettingsData>(GET_GENERAL_SETTINGS);

  return {
    settings: data?.generalSettings,
    loading,
    error,
    refetch,
  };
};

/**
 * 获取阅读设置的Hook
 * @returns 阅读设置数据、加载状态和错误信息
 */
export const useReadingSettings = () => {
  const { data, loading, error, refetch } = useQuery<ReadingSettingsData>(GET_READING_SETTINGS);

  return {
    settings: data?.readingSettings,
    loading,
    error,
    refetch,
  };
};

/**
 * 获取讨论设置的Hook
 * @returns 讨论设置数据、加载状态和错误信息
 */
export const useDiscussionSettings = () => {
  const { data, loading, error, refetch } = useQuery<DiscussionSettingsData>(GET_DISCUSSION_SETTINGS);

  return {
    settings: data?.discussionSettings,
    loading,
    error,
    refetch,
  };
};

/**
 * 获取写作设置的Hook
 * @returns 写作设置数据、加载状态和错误信息
 */
export const useWritingSettings = () => {
  const { data, loading, error, refetch } = useQuery<WritingSettingsData>(GET_WRITING_SETTINGS);

  return {
    settings: data?.writingSettings,
    loading,
    error,
    refetch,
  };
}; 