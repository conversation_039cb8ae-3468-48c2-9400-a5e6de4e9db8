'use client';

import React, { useEffect, useRef } from 'react';
import { usePathname, useRouter } from 'next/navigation';

import { useWebSocket } from '@/contexts/WebSocketContext';
import { useApolloClient } from '@apollo/client';
import { useRoutePrefixes } from '@/hooks';
import { useMenuRefresh } from '@/hooks/useMenuRefresh';

// 定义事件的载荷类型
interface PostUpdatedPayload {
  postId?: number;
  postType?: string;
  shortUuid?: string; // YYMMDD-xxxxxx
  slug?: string;
  updatedAt?: string;
  [key: string]: any;
}

interface TagUpdatedPayload {
  termId?: number;
  taxonomy?: string;
  slug?: string;
  name?: string;
  [key: string]: any;
}

interface CategoryUpdatedPayload {
  termId?: number;
  taxonomy?: string;
  slug?: string;
  name?: string;
  [key: string]: any;
}

interface MenuUpdatedPayload {
  menuId?: number;
  menuName?: string;
  menuSlug?: string;
  action?: string;
  timestamp?: string;
  [key: string]: any;
}

interface TaxonomyUpdatedPayload {
  termId?: number;
  taxonomy?: string;
  slug?: string;
  name?: string;
  [key: string]: any;
}

interface PostUnlockedPayload {
  postId: number;
}

interface PostUpdatedForListsPayload {
  postId: number;
  postType: string;
  status: string;
  shortUuid: string;
  slug: string;
  title: string;
  excerpt: string;
  date: string;
  modified: string;
  categories: Array<{
    id: number;
    slug: string;
    name: string;
  }>;
  tags: Array<{
    id: number;
    slug: string;
    name: string;
  }>;
  customTaxonomies?: Record<string, Array<{
    id: number;
    slug: string;
    name: string;
  }>>;
}

interface PostInsertedPayload {
  postId: number;
  postType: string;
  status: string;
  shortUuid: string;
  slug: string;
  title: string;
  excerpt: string;
  date: string;
  modified: string;
}

interface PostDeletedPayload {
  postId: number;
  postType: string;
  status: string;
  shortUuid: string;
  slug: string;
  title: string;
}

interface PostStatusChangedPayload {
  postId: number;
  postType: string;
  oldStatus: string;
  newStatus: string;
  shortUuid: string;
  slug: string;
  title: string;
  excerpt: string;
  date: string;
  modified: string;
}

/**
 * WebSocketEventHub
 * --------------------------------------------------
 * 全局客户端组件，负责消费后端推送的业务事件。
 * 处理 "post:updated"、"post:updated-for-lists"、"tag:updated" 和 "category:updated" 事件，根据当前路径决定是否刷新页面。
 * 新增处理 "post:unlocked" 事件，用于实时更新文章解锁状态。
 * 新增处理 "post:updated-for-lists" 事件，用于实时更新文章列表页面的文章信息。
 */
const WebSocketEventHub: React.FC = () => {
  const { socket } = useWebSocket();
  const pathname = usePathname();
  const router = useRouter();
  const client = useApolloClient(); // 获取 Apollo Client 实例
  const { refreshMenus } = useMenuRefresh(); // 菜单刷新Hook

  // 读取动态路由前缀配置（categoryPrefix、tagPrefix 等）
  const { prefixes } = useRoutePrefixes();
  // 用于去抖同一文章的多次推送 - 分别处理详情页和列表页事件
  const lastHandledDetailRef = useRef<Record<string, number>>({});
  const lastHandledListRef = useRef<Record<string, number>>({});

  // 简化路径匹配，仅返回布尔值，移除调试输出
  const debugPathMatch = (testPath: string) => pathname === testPath || pathname.includes(testPath);

  // 检查当前列表页面是否需要因为文章更新而刷新
  const checkIfListPageNeedsRefresh = (payload: PostUpdatedForListsPayload): boolean => {
    console.log('[WS DEBUG] === checkIfListPageNeedsRefresh START ===');
    console.log('[WS DEBUG] Payload status:', payload.status);
    console.log('[WS DEBUG] Payload postType:', payload.postType);

    // 1. 首页 - 所有已发布的文章都会影响首页
    if (pathname === '/') {
      console.log('[WS DEBUG] Current page is homepage');
      const shouldRefresh = payload.status === 'publish';
      console.log('[WS DEBUG] Homepage refresh decision:', shouldRefresh, '(status === publish)');
      return shouldRefresh;
    }

    // 2. 自定义类型页面 - 检查文章类型是否匹配
    // 支持两种路由格式：/post-type/[type] 和 /[type]
    if (pathname.startsWith('/post-type/')) {
      console.log('[WS DEBUG] Current page is custom post type page (standard route)');
      const typeMatch = pathname.match(/^\/post-type\/([^\/]+)/);
      console.log('[WS DEBUG] Type match result:', typeMatch);

      if (typeMatch && typeMatch[1] === payload.postType) {
        console.log('[WS DEBUG] Post type matches current page:', typeMatch[1], '===', payload.postType);
        const shouldRefresh = payload.status === 'publish';
        console.log('[WS DEBUG] Custom type page refresh decision:', shouldRefresh, '(status === publish)');
        return shouldRefresh;
      } else {
        console.log('[WS DEBUG] Post type does not match current page:', typeMatch?.[1], '!==', payload.postType);
      }
    }

    // 2b. 自定义类型页面 - 直接路径格式（如 /note）
    // 检查当前路径是否直接匹配文章类型
    if (pathname === `/${payload.postType}`) {
      console.log('[WS DEBUG] Current page is custom post type page (direct route)');
      console.log('[WS DEBUG] Direct route match:', pathname, '===', `/${payload.postType}`);
      const shouldRefresh = payload.status === 'publish';
      console.log('[WS DEBUG] Custom type page (direct) refresh decision:', shouldRefresh, '(status === publish)');
      return shouldRefresh;
    }

    // 3. 分类页面 - 检查文章是否属于当前分类
    if (payload.categories && payload.categories.length > 0) {
      console.log('[WS DEBUG] Checking category pages, post has', payload.categories.length, 'categories');

      for (const category of payload.categories) {
        console.log('[WS DEBUG] Checking category:', category.name, '(slug:', category.slug + ')');

        const categoryPaths = [
          `/category/${category.slug}`, // 标准路由
        ];

        // 添加友好路由
        if (prefixes.categoryPrefix) {
          categoryPaths.push(`/${prefixes.categoryPrefix}/${category.slug}`);
        } else {
          // 当categoryPrefix为null时，分类页面直接使用根路径
          categoryPaths.push(`/${category.slug}`);
        }

        console.log('[WS DEBUG] Category paths to check:', categoryPaths);

        const pathMatches = categoryPaths.some(path => pathname === path || pathname.startsWith(path));
        console.log('[WS DEBUG] Path match result for category', category.slug + ':', pathMatches);

        if (pathMatches) {
          console.log('[WS DEBUG] Category page matches! Category:', category.slug);
          const shouldRefresh = payload.status === 'publish';
          console.log('[WS DEBUG] Category page refresh decision:', shouldRefresh, '(status === publish)');
          return shouldRefresh;
        }
      }
      console.log('[WS DEBUG] No category page matches found');
    } else {
      console.log('[WS DEBUG] Post has no categories');
    }

    // 4. 标签页面 - 检查文章是否属于当前标签
    if (payload.tags && payload.tags.length > 0) {
      console.log('[WS DEBUG] Checking tag pages, post has', payload.tags.length, 'tags');

      for (const tag of payload.tags) {
        console.log('[WS DEBUG] Checking tag:', tag.name, '(slug:', tag.slug + ')');

        const tagPaths = [
          `/tag/${tag.slug}`, // 标准路由
        ];

        // 添加友好路由
        if (prefixes.tagPrefix) {
          tagPaths.push(`/${prefixes.tagPrefix}/${tag.slug}`);
        } else {
          tagPaths.push(`/${tag.slug}`);
        }

        console.log('[WS DEBUG] Tag paths to check:', tagPaths);

        const pathMatches = tagPaths.some(path => pathname === path || pathname.startsWith(path));
        console.log('[WS DEBUG] Path match result for tag', tag.slug + ':', pathMatches);

        if (pathMatches) {
          console.log('[WS DEBUG] Tag page matches! Tag:', tag.slug);
          const shouldRefresh = payload.status === 'publish';
          console.log('[WS DEBUG] Tag page refresh decision:', shouldRefresh, '(status === publish)');
          return shouldRefresh;
        }
      }
      console.log('[WS DEBUG] No tag page matches found');
    } else {
      console.log('[WS DEBUG] Post has no tags');
    }

    // 5. 自定义分类法页面 - 检查文章是否属于当前分类法条目
    if (payload.customTaxonomies) {
      console.log('[WS DEBUG] Checking custom taxonomy pages, post has custom taxonomies:', Object.keys(payload.customTaxonomies));

      for (const [taxonomy, terms] of Object.entries(payload.customTaxonomies)) {
        console.log('[WS DEBUG] Checking taxonomy:', taxonomy, 'with', terms.length, 'terms');

        for (const term of terms) {
          console.log('[WS DEBUG] Checking taxonomy term:', term.name, '(slug:', term.slug + ')');

          const taxonomyPaths = [
            `/taxonomy/${taxonomy}/${term.slug}`, // 标准路由
            `/${taxonomy}/${term.slug}`, // 直接路由
          ];

          console.log('[WS DEBUG] Taxonomy paths to check:', taxonomyPaths);

          const pathMatches = taxonomyPaths.some(path => pathname === path || pathname.startsWith(path));
          console.log('[WS DEBUG] Path match result for taxonomy term', term.slug + ':', pathMatches);

          if (pathMatches) {
            console.log('[WS DEBUG] Taxonomy page matches! Taxonomy:', taxonomy, 'Term:', term.slug);
            const shouldRefresh = payload.status === 'publish';
            console.log('[WS DEBUG] Taxonomy page refresh decision:', shouldRefresh, '(status === publish)');
            return shouldRefresh;
          }
        }
      }
      console.log('[WS DEBUG] No custom taxonomy page matches found');
    } else {
      console.log('[WS DEBUG] Post has no custom taxonomies');
    }

    console.log('[WS DEBUG] === checkIfListPageNeedsRefresh END: false ===');
    return false;
  };



  // 检查文章生命周期事件（插入、删除、状态变化）是否影响当前页面
  const checkIfPostLifecycleAffectsCurrentPage = (payload: PostInsertedPayload | PostDeletedPayload | PostStatusChangedPayload): boolean => {
    console.log('[WS DEBUG] === checkIfPostLifecycleAffectsCurrentPage START ===');
    console.log('[WS DEBUG] Payload postType:', payload.postType);

    // 1. 首页 - 所有已发布的文章都会影响首页
    if (pathname === '/') {
      console.log('[WS DEBUG] Current page is homepage');
      const shouldRefresh = true; // 文章生命周期变化都会影响首页
      console.log('[WS DEBUG] Homepage refresh decision:', shouldRefresh);
      return shouldRefresh;
    }

    // 2. 自定义类型页面 - 检查文章类型是否匹配
    // 支持两种路由格式：/post-type/[type] 和 /[type]
    if (pathname.startsWith('/post-type/')) {
      console.log('[WS DEBUG] Current page is custom post type page (standard route)');
      const typeMatch = pathname.match(/^\/post-type\/([^\/]+)/);
      console.log('[WS DEBUG] Type match result:', typeMatch);

      if (typeMatch && typeMatch[1] === payload.postType) {
        console.log('[WS DEBUG] Post type matches current page:', typeMatch[1], '===', payload.postType);
        const shouldRefresh = true; // 匹配的自定义类型页面需要刷新
        console.log('[WS DEBUG] Custom type page refresh decision:', shouldRefresh);
        return shouldRefresh;
      } else {
        console.log('[WS DEBUG] Post type does not match current page:', typeMatch?.[1], '!==', payload.postType);
      }
    }

    // 2b. 自定义类型页面 - 直接路径格式（如 /note）
    if (pathname === `/${payload.postType}`) {
      console.log('[WS DEBUG] Current page is custom post type page (direct route)');
      console.log('[WS DEBUG] Direct route match:', pathname, '===', `/${payload.postType}`);
      const shouldRefresh = true; // 匹配的自定义类型页面需要刷新
      console.log('[WS DEBUG] Custom type page (direct) refresh decision:', shouldRefresh);
      return shouldRefresh;
    }

    // 3. 对于普通的post类型，检查分类法页面
    if (payload.postType === 'post') {
      console.log('[WS DEBUG] Checking if post status change affects taxonomy pages...');

      // 检查是否在分类页面
      const categoryPaths = [`/category/`];
      if (prefixes.categoryPrefix) {
        categoryPaths.push(`/${prefixes.categoryPrefix}/`);
      }

      const isCategoryPage = categoryPaths.some(prefix => pathname.startsWith(prefix));
      if (isCategoryPage) {
        console.log('[WS DEBUG] Current page is a category page, post status change may affect it');
        const shouldRefresh = true;
        console.log('[WS DEBUG] Category page refresh decision:', shouldRefresh);
        return shouldRefresh;
      }

      // 检查是否在标签页面
      const tagPaths = [`/tag/`];
      if (prefixes.tagPrefix) {
        tagPaths.push(`/${prefixes.tagPrefix}/`);
      }

      const isTagPage = tagPaths.some(prefix => pathname.startsWith(prefix));
      if (isTagPage) {
        console.log('[WS DEBUG] Current page is a tag page, post status change may affect it');
        const shouldRefresh = true;
        console.log('[WS DEBUG] Tag page refresh decision:', shouldRefresh);
        return shouldRefresh;
      }

      // 检查是否在自定义分类法页面
      if (pathname.startsWith('/taxonomy/')) {
        console.log('[WS DEBUG] Current page is a custom taxonomy page, post status change may affect it');
        const shouldRefresh = true;
        console.log('[WS DEBUG] Custom taxonomy page refresh decision:', shouldRefresh);
        return shouldRefresh;
      }

      // 检查是否在自定义分类法条目页面（如 /company/tesla）
      const pathSegments = pathname.split('/').filter(Boolean);
      if (pathSegments.length === 2) {
        console.log('[WS DEBUG] Checking if two-level path might be a custom taxonomy term page');
        console.log('[WS DEBUG] Current pathname:', pathname);
        console.log('[WS DEBUG] Path segments:', pathSegments);
        console.log('[WS DEBUG] This looks like a custom taxonomy term page, should refresh');
        const shouldRefresh = true; // 自定义分类法条目页面需要刷新
        console.log('[WS DEBUG] Custom taxonomy term page refresh decision:', shouldRefresh);
        return shouldRefresh;
      }

      // 特殊处理：当categoryPrefix为null时，分类页面可能是根级路径
      // 这是关键修复：对于 /startup_investment 这样的路径
      if (!prefixes.categoryPrefix && pathname !== '/') {
        // 检查是否是单级路径（如 /startup_investment，而不是 /path/subpath）
        if (pathSegments.length === 1) {
          console.log('[WS DEBUG] Checking if root-level path might be a category page (categoryPrefix is null)');
          console.log('[WS DEBUG] Current pathname:', pathname);
          console.log('[WS DEBUG] Path segments:', pathSegments);
          console.log('[WS DEBUG] This looks like a root-level category page, should refresh');
          const shouldRefresh = true; // 根级分类页面需要刷新
          console.log('[WS DEBUG] Root-level category page refresh decision:', shouldRefresh);
          return shouldRefresh;
        }
      }

      console.log('[WS DEBUG] Post type is "post" but current page is not a recognized taxonomy page');
    }

    console.log('[WS DEBUG] === checkIfPostLifecycleAffectsCurrentPage END: false ===');
    return false;
  };

  useEffect(() => {
    console.log('[WS DEBUG] ========== WebSocketEventHub useEffect START ==========');
    console.log('[WS DEBUG] Socket available:', !!socket);
    console.log('[WS DEBUG] Current pathname:', pathname);
    console.log('[WS DEBUG] Route prefixes:', prefixes);

    if (!socket) {
      console.log('[WS DEBUG] No socket available, returning early');
      return;
    }

    console.log('[WS DEBUG] Socket connection state:', socket.connected);
    console.log('[WS DEBUG] Socket ID:', socket.id);

    // 监听连接状态变化
    const handleConnect = () => {
      console.log('[WS DEBUG] ✅ WebSocket connected! Socket ID:', socket.id);
    };

    const handleDisconnect = (reason: string) => {
      console.log('[WS DEBUG] ❌ WebSocket disconnected. Reason:', reason);
    };

    const handleConnectError = (error: any) => {
      console.error('[WS DEBUG] WebSocket connection error:', error);
    };

    socket.on('connect', handleConnect);
    socket.on('disconnect', handleDisconnect);
    socket.on('connect_error', handleConnectError);

    // 初始化完成

    // --- post:updated 事件处理器 ---
    const handlePostUpdate = (payload: PostUpdatedPayload) => {
      try {
        console.log('[WS DEBUG] Received post:updated event (detail page):', payload);

        const idKey = String(payload.postId || payload.shortUuid || 'unknown');
        const now = Date.now();
        if (now - (lastHandledDetailRef.current[idKey] || 0) < 2000) {
          console.log('[WS DEBUG] post:updated skipped due to debounce');
          return;
        }
        lastHandledDetailRef.current[idKey] = now;

        // 1. 页面 (page) 匹配逻辑 —— 基于 slug
        if (payload.postType === 'page' && payload.slug) {
          console.log('[WS DEBUG] post:updated checking page match for slug:', payload.slug);
          const possiblePath1 = `/page/${payload.slug}`; // 内部标准路由
          const possiblePath2 = `/${payload.slug}`;       // 友好根路径
          console.log('[WS DEBUG] post:updated page paths to check:', [possiblePath1, possiblePath2]);

          if (pathname === possiblePath1 || pathname === possiblePath2) {
            console.log('[WS DEBUG] post:updated page match found, refreshing...');
            router.refresh();
            return;
          }
        }

        // 2. 文章及其他类型 —— 基于 shortUuid / postId
        if (payload.shortUuid && pathname.includes(payload.shortUuid)) {
          console.log('[WS DEBUG] post:updated shortUuid match found:', payload.shortUuid, 'in', pathname);
          router.refresh();
          return;
        }
        if (payload.postId && pathname.includes(String(payload.postId))) {
          console.log('[WS DEBUG] post:updated postId match found:', payload.postId, 'in', pathname);
          router.refresh();
          return;
        }

        console.log('[WS DEBUG] post:updated no match found for current path:', pathname);
        // 未来：可在此广播到其他 handler（列表刷新、Toast 等）
      } catch (e) {
        console.error('WebSocketEventHub handler error', e);
      }
    };

    // --- post:unlocked 事件处理器 ---
    const handlePostUnlock = (payload: PostUnlockedPayload) => {
        // Apollo cache updated
        if (!payload.postId) return;

        // 直接修改 Apollo Client 缓存
        try {
            const cacheId = client.cache.identify({ __typename: 'Post', id: payload.postId });
            client.cache.modify({
                id: cacheId,
                fields: {
                    isUnlockedByCurrentUser() {
                        return true;
                    },
                    content(existingContent: any) {
                        // 可选：如果推送事件能附带完整内容，可以在此直接更新，避免再次请求
                        // 但通常仅更新权限标志位，让组件自行重新获取数据是更通用的做法
                        return existingContent;
                    }
                },
                broadcast: true, // 确保所有相关的查询都更新
            });
            // Apollo cache updated
        } catch(e) {
            console.error('Failed to update Apollo Cache for post unlock:', e);
      }
    };

    // --- post:updated-for-lists 事件处理器 ---
    const handlePostUpdateForLists = (payload: PostUpdatedForListsPayload) => {
      try {
        console.log('[WS DEBUG] ========== POST:UPDATED-FOR-LISTS EVENT ==========');
        console.log('[WS DEBUG] Received event payload:', JSON.stringify(payload, null, 2));
        console.log('[WS DEBUG] Current pathname:', pathname);
        console.log('[WS DEBUG] Current prefixes:', prefixes);

        // 验证payload数据完整性
        if (!payload || !payload.postId) {
          console.error('[WS DEBUG] Invalid payload: missing postId');
          return;
        }

        // 去抖处理 - 使用独立的列表页去抖记录
        const idKey = String(payload.postId || 'unknown');
        const now = Date.now();
        const lastHandled = lastHandledListRef.current[idKey] || 0;
        const timeSinceLastHandled = now - lastHandled;

        console.log('[WS DEBUG] List event debounce check - Post ID:', idKey, 'Time since last handled:', timeSinceLastHandled + 'ms');

        if (timeSinceLastHandled < 2000) {
          console.log('[WS DEBUG] Skipping list event due to debounce (< 2000ms)');
          return;
        }

        lastHandledListRef.current[idKey] = now;

        // 检查当前页面是否是需要刷新的文章列表页面
        console.log('[WS DEBUG] Checking if current page needs refresh...');
        const shouldRefresh = checkIfListPageNeedsRefresh(payload);

        console.log('[WS DEBUG] Should refresh result:', shouldRefresh);

        if (shouldRefresh) {
          console.log('[WS DEBUG] ✅ Post update affects current list page, triggering refresh...');
          router.refresh();
          console.log('[WS DEBUG] router.refresh() called');
        } else {
          console.log('[WS DEBUG] ❌ Post update does not affect current page, no refresh needed');
        }

        console.log('[WS DEBUG] ========== END POST:UPDATED-FOR-LISTS EVENT ==========');
      } catch (e) {
        console.error('[WS DEBUG] WebSocketEventHub handlePostUpdateForLists error:', e);
      }
    };

    // --- post:inserted 事件处理器 ---
    const handlePostInserted = (payload: PostInsertedPayload) => {
      try {
        console.log('[WS DEBUG] ========== POST:INSERTED EVENT ==========');
        console.log('[WS DEBUG] Received event payload:', JSON.stringify(payload, null, 2));
        console.log('[WS DEBUG] Current pathname:', pathname);

        // 验证payload数据完整性
        if (!payload || !payload.postId) {
          console.error('[WS DEBUG] Invalid payload: missing postId');
          return;
        }

        // 去抖处理
        const idKey = String(payload.postId || 'unknown');
        const now = Date.now();
        const lastHandled = lastHandledListRef.current[idKey] || 0;
        const timeSinceLastHandled = now - lastHandled;

        console.log('[WS DEBUG] Post insert debounce check - Post ID:', idKey, 'Time since last handled:', timeSinceLastHandled + 'ms');

        if (timeSinceLastHandled < 2000) {
          console.log('[WS DEBUG] Skipping post insert due to debounce (< 2000ms)');
          return;
        }

        lastHandledListRef.current[idKey] = now;

        // 检查当前页面是否需要刷新
        console.log('[WS DEBUG] Checking if current page needs refresh for post insert...');
        const shouldRefresh = checkIfPostLifecycleAffectsCurrentPage(payload);

        console.log('[WS DEBUG] Should refresh result:', shouldRefresh);

        if (shouldRefresh) {
          console.log('[WS DEBUG] ✅ Post insert affects current page, triggering refresh...');
          router.refresh();
          console.log('[WS DEBUG] router.refresh() called for post insert');
        } else {
          console.log('[WS DEBUG] ❌ Post insert does not affect current page, no refresh needed');
        }

        console.log('[WS DEBUG] ========== END POST:INSERTED EVENT ==========');
      } catch (e) {
        console.error('[WS DEBUG] WebSocketEventHub handlePostInserted error:', e);
      }
    };

    // --- post:deleted 事件处理器 ---
    const handlePostDeleted = (payload: PostDeletedPayload) => {
      try {
        console.log('[WS DEBUG] ========== POST:DELETED EVENT ==========');
        console.log('[WS DEBUG] Received event payload:', JSON.stringify(payload, null, 2));
        console.log('[WS DEBUG] Current pathname:', pathname);

        // 验证payload数据完整性
        if (!payload || !payload.postId) {
          console.error('[WS DEBUG] Invalid payload: missing postId');
          return;
        }

        // 去抖处理
        const idKey = String(payload.postId || 'unknown');
        const now = Date.now();
        const lastHandled = lastHandledListRef.current[idKey] || 0;
        const timeSinceLastHandled = now - lastHandled;

        console.log('[WS DEBUG] Post delete debounce check - Post ID:', idKey, 'Time since last handled:', timeSinceLastHandled + 'ms');

        if (timeSinceLastHandled < 2000) {
          console.log('[WS DEBUG] Skipping post delete due to debounce (< 2000ms)');
          return;
        }

        lastHandledListRef.current[idKey] = now;

        // 检查当前页面是否需要刷新
        console.log('[WS DEBUG] Checking if current page needs refresh for post delete...');
        const shouldRefresh = checkIfPostLifecycleAffectsCurrentPage(payload);

        console.log('[WS DEBUG] Should refresh result:', shouldRefresh);

        if (shouldRefresh) {
          console.log('[WS DEBUG] ✅ Post delete affects current page, triggering refresh...');
          router.refresh();
          console.log('[WS DEBUG] router.refresh() called for post delete');
        } else {
          console.log('[WS DEBUG] ❌ Post delete does not affect current page, no refresh needed');
        }

        console.log('[WS DEBUG] ========== END POST:DELETED EVENT ==========');
      } catch (e) {
        console.error('[WS DEBUG] WebSocketEventHub handlePostDeleted error:', e);
      }
    };

    // --- post:status-published/unpublished 事件处理器 ---
    const handlePostStatusChanged = (payload: PostStatusChangedPayload) => {
      try {
        console.log('[WS DEBUG] ========== POST:STATUS-CHANGED EVENT ==========');
        console.log('[WS DEBUG] Received event payload:', JSON.stringify(payload, null, 2));
        console.log('[WS DEBUG] Current pathname:', pathname);

        // 验证payload数据完整性
        if (!payload || !payload.postId) {
          console.error('[WS DEBUG] Invalid payload: missing postId');
          return;
        }

        // 去抖处理
        const idKey = String(payload.postId || 'unknown');
        const now = Date.now();
        const lastHandled = lastHandledListRef.current[idKey] || 0;
        const timeSinceLastHandled = now - lastHandled;

        console.log('[WS DEBUG] Post status change debounce check - Post ID:', idKey, 'Time since last handled:', timeSinceLastHandled + 'ms');

        if (timeSinceLastHandled < 2000) {
          console.log('[WS DEBUG] Skipping post status change due to debounce (< 2000ms)');
          return;
        }

        lastHandledListRef.current[idKey] = now;

        // 检查当前页面是否需要刷新
        console.log('[WS DEBUG] Checking if current page needs refresh for post status change...');
        const shouldRefresh = checkIfPostLifecycleAffectsCurrentPage(payload);

        console.log('[WS DEBUG] Should refresh result:', shouldRefresh);

        if (shouldRefresh) {
          console.log('[WS DEBUG] ✅ Post status change affects current page, triggering refresh...');
          router.refresh();
          console.log('[WS DEBUG] router.refresh() called for post status change');
        } else {
          console.log('[WS DEBUG] ❌ Post status change does not affect current page, no refresh needed');
        }

        console.log('[WS DEBUG] ========== END POST:STATUS-CHANGED EVENT ==========');
      } catch (e) {
        console.error('[WS DEBUG] WebSocketEventHub handlePostStatusChanged error:', e);
      }
    };

    // 标签更新事件处理器
    const tagHandler = (payload: TagUpdatedPayload) => {
      // tag updated handler
      try {
        // 动态计算标签索引页可能的路径
        const tagIndexPaths: string[] = [];
        if (prefixes.tagIndexRoute) tagIndexPaths.push(`/${prefixes.tagIndexRoute}`);
        if (prefixes.tagPrefix) tagIndexPaths.push(`/${prefixes.tagPrefix}`);
        // 兼容旧路径及默认值
        tagIndexPaths.push('/tag-index');
        tagIndexPaths.push('/innovation');

        const isTagIndexPage = tagIndexPaths.some(path => pathname === path);

        if (isTagIndexPage) {
          // refresh tag index
          router.refresh();
          return;
        }

        // 如果当前在特定标签页，且该标签被更新，则刷新页面
        // 检查多种可能的标签页路径格式
        if (payload.slug) {
          // 动态计算具体标签页的可能路径
          const tagPaths: string[] = [`/tag/${payload.slug}`]; // Next.js 内部标准路由

          if (prefixes.tagPrefix) {
            tagPaths.push(`/${prefixes.tagPrefix}/${payload.slug}`); // 友好别名路由
          } else {
            // 当 tagPrefix 为空时，根级路径形如 /{slug}
            tagPaths.push(`/${payload.slug}`);
          }

          const isTagPage = tagPaths.some(path => {
            if (path === `/${payload.slug}`) {
              return pathname.includes(path) && pathname.split('/').length === 2;
            }
            return pathname.includes(path);
          });

          if (isTagPage) {
            // refresh tag page
            router.refresh();
            return;
          }
        }

        // no match
        // 未来：可在此添加其他标签相关页面的刷新逻辑
      } catch (e) {
        console.error('WebSocketEventHub tag handler error', e);
      }
    };

    // 分类更新事件处理器
    const categoryHandler = (payload: CategoryUpdatedPayload) => {
      // category updated handler
      try {
        // 动态计算分类索引页可能的路径
        const categoryIndexPaths: string[] = [];
        if (prefixes.categoryIndexRoute) categoryIndexPaths.push(`/${prefixes.categoryIndexRoute}`);
        // 兼容默认和旧路径
        categoryIndexPaths.push('/category-index');
        if (prefixes.categoryPrefix) categoryIndexPaths.push(`/${prefixes.categoryPrefix}`);

        const isCategoryIndexPage = categoryIndexPaths.some(path => pathname === path);

        if (isCategoryIndexPage) {
          // refresh category index
          router.refresh();
          return;
        }

        // 如果当前在特定分类页，且该分类被更新，则刷新页面
        if (payload.slug) {
          // 动态计算具体分类页的可能路径
          const categoryPaths: string[] = [`/category/${payload.slug}`]; // Next.js 内部标准路由

          if (prefixes.categoryPrefix) {
            categoryPaths.push(`/${prefixes.categoryPrefix}/${payload.slug}`); // 友好别名路由
          }

          if (!prefixes.categoryPrefix) {
            // 当 categoryPrefix 为空时，可选根级路径 /{slug}，需确保层级为 2
            categoryPaths.push(`/${payload.slug}`);
          }

          const isCategoryPage = categoryPaths.some(path => {
            if (path === `/${payload.slug}`) {
              return pathname === path || (pathname.includes(path) && pathname.split('/').length === 2);
            }
            return pathname.includes(path);
          });

          if (isCategoryPage) {
            router.refresh();
            return;
          }
        }

        // no match
        // 未来：可在此添加其他分类相关页面的刷新逻辑
      } catch (e) {
        console.error('WebSocketEventHub category handler error', e);
      }
    };

    // 自定义分类法更新事件处理器
    const taxonomyHandler = (payload: TaxonomyUpdatedPayload) => {
      // taxonomy updated handler
      try {
        // 检查是否在分类法索引页
        if (payload.taxonomy) {
          // 方法1：检查标准路径 /taxonomy/{taxonomy}
          const taxonomyIndexPath = `/taxonomy/${payload.taxonomy}`;
          const isStandardIndexMatch = debugPathMatch(taxonomyIndexPath);

          // 方法2：检查直接路径 /{taxonomy} (用于重写路由)
          const directIndexPath = `/${payload.taxonomy}`;
          const isDirectIndexMatch = debugPathMatch(directIndexPath);

          if (isStandardIndexMatch || isDirectIndexMatch) {
            // refresh taxonomy index page
            router.refresh();
            return;
          }
        }

        // 检查是否在特定分类法条目页
        if (payload.taxonomy && payload.slug) {
          // 方法1：检查标准路径 /taxonomy/{taxonomy}/{slug}
          const taxonomyTermPath = `/taxonomy/${payload.taxonomy}/${payload.slug}`;
          const isStandardTermMatch = debugPathMatch(taxonomyTermPath);

          // 方法2：检查直接路径 /{taxonomy}/{slug} (用于重写路由)
          const directTermPath = `/${payload.taxonomy}/${payload.slug}`;
          const isDirectTermMatch = debugPathMatch(directTermPath);

          if (isStandardTermMatch || isDirectTermMatch) {
            // refresh taxonomy page
              router.refresh();
            return;
          }
        }

        // no match
        // 未来：可在此添加其他分类法相关页面的刷新逻辑
      } catch (e) {
        console.error('WebSocketEventHub taxonomy handler error', e);
      }
    };

    // 分类法关系变化事件处理器（包括文章添加/移除到分类法）
    const termChangeHandler = (payload: any) => {
      try {
        console.log(`[WS] Received ${payload.event || 'term change'} event`, payload, ' current pathname: ', pathname);

        // 验证payload数据完整性
        if (!payload?.affectedTerms || !Array.isArray(payload.affectedTerms)) {
          console.log('[WS] Invalid payload: missing or invalid affectedTerms');
          return;
        }

        // 判断当前页面是否受影响
        const isRelevant = payload.affectedTerms.some((term: any) => {
          const slug = term.slug;
          const taxonomy = term.taxonomy;

          if (!slug || !taxonomy) return false;

          // 分类详情页
          if (taxonomy === 'category') {
            const paths = [
              `/category/${slug}`, // 内部标准路由
              `/${slug}`,          // 无前缀的根路径
            ];
            if (prefixes.categoryPrefix) {
              paths.push(`/${prefixes.categoryPrefix}/${slug}`); // 带前缀的友好路由
            }
            return paths.some(p => pathname === p);
          }

          // 标签详情页
          if (taxonomy === 'post_tag') {
            const paths = [
              `/tag/${slug}`,      // 内部标准路由
              `/${slug}`,          // 无前缀的根路径
            ];
            if (prefixes.tagPrefix) {
              paths.push(`/${prefixes.tagPrefix}/${slug}`); // 带前缀的友好路由
            }
            return paths.some(p => pathname === p);
          }

          // 自定义分类法条目页
          const customPaths = [
            `/taxonomy/${taxonomy}/${slug}`,
            `/${taxonomy}/${slug}`
          ];
          return customPaths.some(p => pathname === p || pathname.startsWith(p));
        });

        if (isRelevant) {
          router.refresh();
          return;
        }

        // 如果当前在某个索引页，也需要刷新总数
        const needRefreshIndex = payload.affectedTerms.some((term: any) => {
          if (term.taxonomy === 'category') {
            const indexPaths: string[] = [`/${prefixes.categoryIndexRoute}`, '/category-index'];
            if (prefixes.categoryPrefix) indexPaths.push(`/${prefixes.categoryPrefix}`);
            return indexPaths.includes(pathname);
          }
          if (term.taxonomy === 'post_tag') {
            const indexPaths: string[] = [`/${prefixes.tagIndexRoute}`, '/tag-index', '/innovation'];
            if (prefixes.tagPrefix) indexPaths.push(`/${prefixes.tagPrefix}`);
            return indexPaths.includes(pathname);
          }
          // 自定义分类法索引
          const indexPath1 = `/taxonomy/${term.taxonomy}`;
          const indexPath2 = `/${term.taxonomy}`;
          return pathname === indexPath1 || pathname === indexPath2;
        });

        if (needRefreshIndex) {
          router.refresh();
          return;
        }
      } catch (e) {
        console.error('WebSocketEventHub listNewPost handler error', e);
      }
    };

    // --- menu:* 事件处理器 ---
    const handleMenuUpdate = (payload: MenuUpdatedPayload) => {
      try {
        console.log('[WS DEBUG] Received menu update event:', payload);
        console.log('[WS DEBUG] Menu action:', payload.action);
        console.log('[WS DEBUG] Menu name:', payload.menuName);

        // 菜单更新影响所有页面，因为菜单是全局组件
        // 使用专门的菜单刷新Hook来刷新菜单数据
        console.log('[WS DEBUG] Menu updated, refreshing menu data...');
        refreshMenus();

        // 可选：显示通知给用户
        // 这里可以添加toast通知或其他用户反馈
        console.log('[WS DEBUG] Menu refresh triggered for action:', payload.action);

      } catch (e) {
        console.error('[WS DEBUG] Menu update handler error:', e);
      }
    };

    console.log('[WS DEBUG] Registering event listeners...');

    socket.on('post:updated', handlePostUpdate);
    socket.on('post:updated-for-lists', handlePostUpdateForLists);
    socket.on('post:inserted', handlePostInserted);
    socket.on('post:deleted', handlePostDeleted);
    socket.on('post:status-published', handlePostStatusChanged);
    socket.on('post:status-unpublished', handlePostStatusChanged);
    socket.on('post:unlocked', handlePostUnlock);
    socket.on('tag:updated', tagHandler);
    socket.on('category:updated', categoryHandler);
    socket.on('taxonomy:updated', taxonomyHandler);
    socket.on('list:item-added', termChangeHandler);
    socket.on('list:item-removed', termChangeHandler);

    // 菜单事件处理器
    socket.on('menu:updated', handleMenuUpdate);
    socket.on('menu:created', handleMenuUpdate);
    socket.on('menu:deleted', handleMenuUpdate);
    socket.on('menu:locations-updated', handleMenuUpdate);
    socket.on('menu:options-updated', handleMenuUpdate);

    // 为了避免重复处理，我们让termChangeHandler也处理普通post文章的状态变化
    // 这样可以统一处理分类法相关的所有变化
    const postStatusChangeForTermsHandler = (payload: PostStatusChangedPayload) => {
      try {
        console.log('[WS] Received post status change event for terms handler', payload);

        // 只处理普通post类型的文章（自定义类型由生命周期处理器处理）
        if (payload.postType !== 'post') {
          console.log('[WS] Skipping post status change for terms handler - not a post type:', payload.postType);
          return;
        }

        console.log('[WS] Post status change affects post type pages, triggering refresh for relevant pages...');

        // 检查是否影响首页
        if (pathname === '/') {
          console.log('[WS] Post status change affects homepage, refreshing...');
          router.refresh();
          return;
        }

        // 检查是否影响分类页面
        const categoryPaths = [`/category/`];
        if (prefixes.categoryPrefix) {
          categoryPaths.push(`/${prefixes.categoryPrefix}/`);
        }

        const isCategoryPage = categoryPaths.some(prefix => pathname.startsWith(prefix));
        if (isCategoryPage) {
          console.log('[WS] Post status change affects category page, refreshing...');
          router.refresh();
          return;
        }

        // 检查是否影响标签页面
        const tagPaths = [`/tag/`];
        if (prefixes.tagPrefix) {
          tagPaths.push(`/${prefixes.tagPrefix}/`);
        }

        const isTagPage = tagPaths.some(prefix => pathname.startsWith(prefix));
        if (isTagPage) {
          console.log('[WS] Post status change affects tag page, refreshing...');
          router.refresh();
          return;
        }

        // 检查是否影响自定义分类法页面
        if (pathname.startsWith('/taxonomy/')) {
          console.log('[WS] Post status change affects custom taxonomy page, refreshing...');
          router.refresh();
          return;
        }

        // 检查是否影响自定义分类法条目页面（如 /company/tesla）
        const pathSegments = pathname.split('/').filter(Boolean);
        if (pathSegments.length === 2) {
          console.log('[WS] Post status change may affect custom taxonomy term page, refreshing...');
          router.refresh();
          return;
        }

        // 特殊处理：当categoryPrefix为null时，分类页面可能是根级路径
        if (!prefixes.categoryPrefix && pathname !== '/') {
          // 检查是否是单级路径（如 /startup_investment，而不是 /path/subpath）
          if (pathSegments.length === 1) {
            console.log('[WS] Post status change may affect root-level category page, refreshing...');
            router.refresh();
            return;
          }
        }

        console.log('[WS] Post status change - no specific page match for pathname:', pathname);

      } catch (e) {
        console.error('[WS] postStatusChangeForTermsHandler error:', e);
      }
    };

    // 注册状态变化事件（仅用于处理普通post文章对首页的影响）
    socket.on('post:status-published', postStatusChangeForTermsHandler);
    socket.on('post:status-unpublished', postStatusChangeForTermsHandler);

    console.log('[WS DEBUG] Event listeners registered:', [
      'post:updated',
      'post:updated-for-lists',
      'post:inserted',
      'post:deleted',
      'post:status-published',
      'post:status-unpublished',
      'post:unlocked',
      'tag:updated',
      'category:updated',
      'taxonomy:updated',
      'list:item-added',
      'list:item-removed',
      'menu:updated',
      'menu:created',
      'menu:deleted',
      'menu:locations-updated',
      'menu:options-updated'
    ]);

    return () => {
      console.log('[WS DEBUG] Cleaning up event listeners...');

      socket.off('connect', handleConnect);
      socket.off('disconnect', handleDisconnect);
      socket.off('connect_error', handleConnectError);
      socket.off('post:updated', handlePostUpdate);
      socket.off('post:updated-for-lists', handlePostUpdateForLists);
      socket.off('post:inserted', handlePostInserted);
      socket.off('post:deleted', handlePostDeleted);
      socket.off('post:status-published', handlePostStatusChanged);
      socket.off('post:status-unpublished', handlePostStatusChanged);
      socket.off('post:unlocked', handlePostUnlock);
      socket.off('tag:updated', tagHandler);
      socket.off('category:updated', categoryHandler);
      socket.off('taxonomy:updated', taxonomyHandler);
      socket.off('list:item-added', termChangeHandler);
      socket.off('list:item-removed', termChangeHandler);

      // 清理菜单事件监听器
      socket.off('menu:updated', handleMenuUpdate);
      socket.off('menu:created', handleMenuUpdate);
      socket.off('menu:deleted', handleMenuUpdate);
      socket.off('menu:locations-updated', handleMenuUpdate);
      socket.off('menu:options-updated', handleMenuUpdate);
      socket.off('post:status-published', postStatusChangeForTermsHandler);
      socket.off('post:status-unpublished', postStatusChangeForTermsHandler);

      console.log('[WS DEBUG] Event listeners cleaned up');
      console.log('[WS DEBUG] ========== WebSocketEventHub useEffect END ==========');
    };
  }, [socket, pathname, router, client, prefixes]);

  return null; // 该组件不渲染任何 UI
};

export default WebSocketEventHub; 