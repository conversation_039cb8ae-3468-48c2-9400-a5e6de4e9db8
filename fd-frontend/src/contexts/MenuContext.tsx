'use client';

import React, { createContext, useContext, ReactNode } from 'react';

interface MenuItem {
  id: string;
  title: string;
  label: string;
  url: string;
  target?: string;
  children?: MenuItem[];
}

interface MenuContextType {
  topMenuItems: MenuItem[];
  footerMenuItems: MenuItem[];
  refreshMenus?: () => void; // 可选的刷新函数
}

const MenuContext = createContext<MenuContextType>({
  topMenuItems: [],
  footerMenuItems: [],
  refreshMenus: undefined
});

interface MenuProviderProps {
  children: ReactNode;
  topMenuItems: MenuItem[];
  footerMenuItems: MenuItem[];
}

export const MenuProvider: React.FC<MenuProviderProps> = ({
  children,
  topMenuItems,
  footerMenuItems
}) => {
  return (
    <MenuContext.Provider value={{ topMenuItems, footerMenuItems }}>
      {children}
    </MenuContext.Provider>
  );
};

export const useMenuContext = () => {
  return useContext(MenuContext);
};
