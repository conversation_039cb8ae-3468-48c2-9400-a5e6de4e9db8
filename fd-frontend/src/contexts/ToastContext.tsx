'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { createPortal } from 'react-dom';
import Toast, { ToastProps, ToastType } from '../components/ui/Toast';
import { v4 as uuidv4 } from 'uuid';

// 省略ToastProps中的id和onClose属性
type ToastOptions = Omit<ToastProps, 'id' | 'onClose'>;

// Toast上下文类型
interface ToastContextType {
  addToast: (options: ToastOptions) => string;
  removeToast: (id: string) => void;
  success: (message: string, options?: Partial<Omit<ToastOptions, 'message' | 'type'>>) => string;
  error: (message: string, options?: Partial<Omit<ToastOptions, 'message' | 'type'>>) => string;
  warning: (message: string, options?: Partial<Omit<ToastOptions, 'message' | 'type'>>) => string;
  info: (message: string, options?: Partial<Omit<ToastOptions, 'message' | 'type'>>) => string;
  clear: () => void;
}

// 创建上下文
const ToastContext = createContext<ToastContextType | undefined>(undefined);

// 提供一个自定义钩子来使用Toast上下文
export const useToast = () => {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

// Toast提供者组件
export const ToastProvider: React.FC<{ 
  children: ReactNode;
  position?: ToastProps['position'];
  maxToasts?: number;
}> = ({ 
  children, 
  position = 'top-right',
  maxToasts = 5
}) => {
  const [toasts, setToasts] = useState<ToastProps[]>([]);
  const [container, setContainer] = useState<HTMLElement | null>(null);

  // 创建DOM容器
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      const el = document.createElement('div');
      el.className = 'toast-container';
      document.body.appendChild(el);
      setContainer(el);

      return () => {
        document.body.removeChild(el);
      };
    }
  }, []);

  // 添加Toast
  const addToast = useCallback((options: ToastOptions): string => {
    const id = uuidv4();
    const toast: ToastProps = {
      id,
      ...options,
      onClose: (toastId) => removeToast(toastId),
      position: options.position || position,
    };

    setToasts((prevToasts) => {
      // 如果超过最大数量，移除最旧的
      const newToasts = [...prevToasts, toast];
      if (newToasts.length > maxToasts) {
        return newToasts.slice(1);
      }
      return newToasts;
    });

    return id;
  }, [maxToasts, position]);

  // 移除Toast
  const removeToast = useCallback((id: string) => {
    setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id));
  }, []);

  // 清除所有Toast
  const clear = useCallback(() => {
    setToasts([]);
  }, []);

  // 便捷方法 - 成功提示
  const success = useCallback((message: string, options: Partial<Omit<ToastOptions, 'message' | 'type'>> = {}) => {
    return addToast({ 
      type: 'success', 
      message,
      title: options.title || '成功',
      ...options 
    });
  }, [addToast]);

  // 便捷方法 - 错误提示
  const error = useCallback((message: string, options: Partial<Omit<ToastOptions, 'message' | 'type'>> = {}) => {
    return addToast({ 
      type: 'error', 
      message,
      title: options.title || '错误',
      ...options 
    });
  }, [addToast]);

  // 便捷方法 - 警告提示
  const warning = useCallback((message: string, options: Partial<Omit<ToastOptions, 'message' | 'type'>> = {}) => {
    return addToast({ 
      type: 'warning', 
      message,
      title: options.title || '警告',
      ...options 
    });
  }, [addToast]);

  // 便捷方法 - 信息提示
  const info = useCallback((message: string, options: Partial<Omit<ToastOptions, 'message' | 'type'>> = {}) => {
    return addToast({ 
      type: 'info', 
      message,
      title: options.title || '提示',
      ...options 
    });
  }, [addToast]);

  // 根据位置获取容器样式
  const getPositionStyle = () => {
    switch (position) {
      case 'top-left':
        return 'fixed top-4 left-4 z-50 flex flex-col items-start';
      case 'top-center':
        return 'fixed top-4 left-1/2 transform -translate-x-1/2 z-50 flex flex-col items-center';
      case 'bottom-right':
        return 'fixed bottom-4 right-4 z-50 flex flex-col-reverse items-end';
      case 'bottom-left':
        return 'fixed bottom-4 left-4 z-50 flex flex-col-reverse items-start';
      case 'bottom-center':
        return 'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 flex flex-col-reverse items-center';
      case 'top-right':
      default:
        return 'fixed top-4 right-4 z-50 flex flex-col items-end';
    }
  };

  return (
    <ToastContext.Provider 
      value={{ 
        addToast, 
        removeToast, 
        success, 
        error, 
        warning, 
        info, 
        clear 
      }}
    >
      {children}
      {container && createPortal(
        <div className={getPositionStyle()}>
          {toasts.map((toast) => (
            <Toast key={toast.id} {...toast} />
          ))}
        </div>,
        container
      )}
    </ToastContext.Provider>
  );
};

export default ToastProvider; 