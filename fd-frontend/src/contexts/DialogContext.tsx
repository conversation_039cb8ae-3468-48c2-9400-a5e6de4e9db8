'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import Dialog, { DialogProps } from '../components/ui/Dialog';

// 省略DialogProps中的isOpen和onClose属性
type DialogOptions = Omit<DialogProps, 'isOpen' | 'onClose'>;

// 对话框上下文类型
interface DialogContextType {
  openDialog: (options: DialogOptions) => void;
  closeDialog: () => void;
  confirmDialog: (options: Omit<DialogOptions, 'onConfirm' | 'onCancel'>) => Promise<boolean>;
}

// 创建上下文
const DialogContext = createContext<DialogContextType | undefined>(undefined);

// 提供一个自定义钩子来使用对话框上下文
export const useDialog = () => {
  const context = useContext(DialogContext);
  if (context === undefined) {
    throw new Error('useDialog must be used within a DialogProvider');
  }
  return context;
};

// 对话框提供者组件
export const DialogProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [dialogOptions, setDialogOptions] = useState<DialogOptions | null>(null);
  const [promiseResolve, setPromiseResolve] = useState<((value: boolean) => void) | null>(null);

  // 关闭对话框
  const closeDialog = useCallback(() => {
    setIsOpen(false);
    // 延迟清空内容，以便关闭动画完成
    setTimeout(() => {
      setDialogOptions(null);
    }, 300);
  }, []);

  // 打开对话框
  const openDialog = useCallback((options: DialogOptions) => {
    setDialogOptions(options);
    setIsOpen(true);
  }, []);

  // 显示确认对话框并返回Promise
  const confirmDialog = useCallback((options: Omit<DialogOptions, 'onConfirm' | 'onCancel'>): Promise<boolean> => {
    return new Promise((resolve) => {
      setPromiseResolve(() => resolve);
      setDialogOptions({
        ...options,
        onConfirm: () => {
          resolve(true);
          closeDialog();
        },
        onCancel: () => {
          resolve(false);
          closeDialog();
        },
      });
      setIsOpen(true);
    });
  }, [closeDialog]);

  return (
    <DialogContext.Provider value={{ openDialog, closeDialog, confirmDialog }}>
      {children}
      {dialogOptions && (
        <Dialog
          isOpen={isOpen}
          onClose={closeDialog}
          {...dialogOptions}
        />
      )}
    </DialogContext.Provider>
  );
};

export default DialogProvider; 