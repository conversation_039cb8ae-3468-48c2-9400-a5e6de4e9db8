"use client";

import React, { createContext, useState, useEffect, ReactNode } from 'react';
import { useApolloClient, useMutation, useQuery } from '@apollo/client';
import { 
  getAuthToken, 
  setAuthToken, 
  clearAuthData, 
  getUserData, 
  setUserData,
  saveAuthData,
  getRefreshToken,
  setRefreshToken
} from '../utils/auth-utils';
import { 
  User, 
  LoginUserInput, 
  RegisterUserInput, 
  ResetPasswordInput, 
  UpdateUserInput,
  SendPhoneCodeInput,
  PhoneLoginInput,
  PhoneRegisterInput,
  BindPhoneInput,
  PhoneAuthResponse,
  RegisterWithTokenInput,
  PhoneRegisterWithTokenInput,
  BindEmailInput
} from '../types/user-types';
import { 
  LOGIN_USER, 
  REGISTER_USER, 
  SEND_PASSWORD_RESET_EMAIL, 
  RESET_USER_PASSWORD,
  UPDATE_PROFILE,
  SEND_PASSWORD_RESET_CODE,
  RESET_PASSWORD_WITH_CODE,
  RESET_PASSWORD_WITH_PHONE_CODE,
  CHANGE_PASSWORD,
  VERIFY_RESET_CODE,
  SEND_PHONE_CODE,
  PHONE_LOGIN,
  PHONE_REGISTER,
  BIND_PHONE,
  UNBIND_PHONE,
  VERIFY_PHONE_CODE,
  VERIFY_REGISTRATION_CODE_AND_GET_TOKEN,
  REGISTER_WITH_TOKEN,
  VERIFY_PHONE_CODE_AND_GET_TOKEN,
  PHONE_REGISTER_WITH_TOKEN,
  VERIFY_PHONE_CODE_FOR_BINDING_AND_GET_TOKEN,
  SEND_EMAIL_BINDING_CODE,
  VERIFY_EMAIL_BINDING_CODE_AND_GET_TOKEN,
  BIND_EMAIL,
  PHONE_LOGIN_WITH_TOKEN
} from '../lib/graphql/mutations';
import { GET_CURRENT_USER } from '../lib/graphql/queries';
import { gql } from '@apollo/client';
import { ApolloError } from '@apollo/client';

/**
 * 认证上下文类型定义
 */
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (input: LoginUserInput) => Promise<void>;
  register: (input: RegisterUserInput) => Promise<void>;
  logout: () => void;
  sendPasswordResetEmail: (username: string) => Promise<void>;
  resetPassword: (input: ResetPasswordInput) => Promise<void>;
  updateUserProfile: (data: UpdateUserInput) => Promise<void>;
  sendPasswordResetCode: (email: string) => Promise<{ success: boolean; message: string }>;
  resetPasswordWithCode: (email: string, code: string, newPassword: string) => Promise<{ success: boolean; message: string }>;
  resetPasswordWithPhoneCode: (phone: string, code: string, newPassword: string, nationCode?: string) => Promise<{ success: boolean; message: string }>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  verifyResetCode: (email: string, code: string) => Promise<{ success: boolean; isValid: boolean; message: string }>;
  clearError: () => void;
  // 手机验证相关方法
  sendPhoneCode: (input: SendPhoneCodeInput) => Promise<PhoneAuthResponse>;
  phoneLogin: (input: PhoneLoginInput) => Promise<void>;
  phoneRegister: (input: PhoneRegisterInput) => Promise<void>;
  // 添加手机令牌登录方法
  phoneLoginWithToken: (input: { phone: string; token: string; nationCode?: string }) => Promise<void>;
  bindPhone: (input: BindPhoneInput) => Promise<PhoneAuthResponse>;
  unbindPhone: () => Promise<PhoneAuthResponse>;
  // 新增：验证邮箱验证码并获取令牌
  verifyEmailCodeAndGetToken: (email: string, code: string) => Promise<{ success: boolean; message: string; token?: string }>;
  // 新增：使用令牌进行注册
  registerWithToken: (input: RegisterWithTokenInput) => Promise<void>;
  // 新增：验证手机验证码并获取令牌
  verifyPhoneCodeAndGetToken: (phone: string, code: string, nationCode?: string) => Promise<{ success: boolean; message: string; token?: string }>;
  // 新增：使用令牌进行手机注册
  phoneRegisterWithToken: (input: PhoneRegisterWithTokenInput) => Promise<void>;
  // 新增：验证手机验证码并获取绑定用的令牌
  verifyPhoneCodeForBindingAndGetToken: (phone: string, code: string, nationCode?: string) => Promise<{ success: boolean; message: string; token?: string }>;
  // 新增：邮箱绑定相关方法
  sendEmailBindingCode: (email: string) => Promise<{ success: boolean; message: string }>;
  verifyEmailBindingCodeAndGetToken: (email: string, code: string) => Promise<{ success: boolean; message: string; token?: string }>;
  bindEmail: (input: BindEmailInput) => Promise<{ success: boolean; message: string; user?: User }>;
  // 新增：刷新用户信息
  refreshUser: () => Promise<User | null>;
}

// 创建认证上下文
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

/**
 * 认证上下文提供者组件
 */
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const client = useApolloClient();
  
  // GraphQL 变更钩子
  const [loginMutation] = useMutation(LOGIN_USER);
  const [registerMutation] = useMutation(REGISTER_USER);
  const [sendPasswordResetEmailMutation] = useMutation(SEND_PASSWORD_RESET_EMAIL);
  const [resetPasswordMutation] = useMutation(RESET_USER_PASSWORD);
  const [updateProfileMutation] = useMutation(UPDATE_PROFILE);
  const [sendPasswordResetCodeMutation] = useMutation(SEND_PASSWORD_RESET_CODE);
  const [resetPasswordWithCodeMutation] = useMutation(RESET_PASSWORD_WITH_CODE);
  const [resetPasswordWithPhoneCodeMutation] = useMutation(RESET_PASSWORD_WITH_PHONE_CODE);
  const [changePasswordMutation] = useMutation(CHANGE_PASSWORD);
  const [verifyResetCodeMutation] = useMutation(VERIFY_RESET_CODE);
  // 手机验证相关变更钩子
  const [sendPhoneCodeMutation] = useMutation(SEND_PHONE_CODE);
  const [phoneLoginMutation] = useMutation(PHONE_LOGIN);
  const [phoneRegisterMutation] = useMutation(PHONE_REGISTER);
  const [bindPhoneMutation] = useMutation(BIND_PHONE);
  const [unbindPhoneMutation] = useMutation(UNBIND_PHONE);
  // 新增变更钩子
  const [verifyRegistrationCodeAndGetTokenMutation] = useMutation(VERIFY_REGISTRATION_CODE_AND_GET_TOKEN);
  const [registerWithTokenMutation] = useMutation(REGISTER_WITH_TOKEN);
  const [verifyPhoneCodeAndGetTokenMutation] = useMutation(VERIFY_PHONE_CODE_AND_GET_TOKEN);
  const [phoneRegisterWithTokenMutation] = useMutation(PHONE_REGISTER_WITH_TOKEN);
  const [phoneLoginWithTokenMutation] = useMutation(PHONE_LOGIN_WITH_TOKEN);
  const [verifyPhoneCodeForBindingAndGetTokenMutation] = useMutation(VERIFY_PHONE_CODE_FOR_BINDING_AND_GET_TOKEN);
  // 新增邮箱绑定相关变更钩子
  const [sendEmailBindingCodeMutation] = useMutation(SEND_EMAIL_BINDING_CODE);
  const [verifyEmailBindingCodeAndGetTokenMutation] = useMutation(VERIFY_EMAIL_BINDING_CODE_AND_GET_TOKEN);
  const [bindEmailMutation] = useMutation(BIND_EMAIL);

  // 获取当前用户
  const fetchCurrentUser = async () => {
    try {
      const { data } = await client.query({
        query: GET_CURRENT_USER,
        fetchPolicy: 'network-only'
      });
      
      if (data?.viewer) {
        setUser(data.viewer);
        setUserData(data.viewer);
        return data.viewer;
      }
      return null;
    } catch (err) {
      console.error('获取当前用户失败:', err);
      return null;
    }
  };

  // 初始化时检查是否已登录
  useEffect(() => {
    const initAuth = async () => {
      const token = getAuthToken();
      const userData = getUserData();

      if (token) {
        console.log('初始化: 找到认证令牌');

        // 先设置缓存的用户数据，避免UI闪烁
        if (userData) {
          console.log('初始化: 使用缓存的用户数据');
          setUser(userData);
        }

        // 在后台验证令牌有效性，但不阻塞UI渲染
        try {
          console.log('初始化: 后台验证用户数据');
          const currentUser = await fetchCurrentUser();
          if (currentUser) {
            console.log('初始化: 验证成功，更新用户数据');
            // 只有当数据真正不同时才更新状态
            if (JSON.stringify(currentUser) !== JSON.stringify(userData)) {
              setUser(currentUser);
              setUserData(currentUser);
            }
          } else if (!userData) {
            // 只有在没有缓存数据且验证失败时才清除
            console.log('初始化: 验证失败，清除认证数据');
            clearAuthData();
            setUser(null);
          }
        } catch (err) {
          console.error('初始化验证失败:', err);
          // 如果有缓存数据，保持现状；否则清除
          if (!userData) {
            clearAuthData();
            setUser(null);
          }
        }
      } else {
        console.log('初始化: 未找到认证令牌');
        clearAuthData();
        setUser(null);
      }

      setIsLoading(false);
    };

    initAuth();
  }, []);

  // 监听本地存储的认证状态变化
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'fd_auth_token') {
        // 令牌发生变化时重新检查认证状态
        const token = getAuthToken();
        const userData = getUserData();
        
        if (token && userData) {
          setUser(userData);
        } else if (!token) {
          setUser(null);
        }
      }
    };
    
    // 为跨标签页通信添加监听器
    if (typeof window !== 'undefined') {
      window.addEventListener('storage', handleStorageChange);
    }
    
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('storage', handleStorageChange);
      }
    };
  }, []);

  /**
   * 用户登录
   * @param input 登录信息
   */
  const login = async (input: LoginUserInput): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // 登录获取令牌
      const { data } = await loginMutation({
        variables: { input },
      });
      
      if (data?.login) {
        const { authToken, refreshToken, user } = data.login;
        
        if (authToken) {
          console.log('登录成功，保存认证数据');
          // 保存认证相关数据到本地存储
          saveAuthData(authToken, refreshToken, user);

          // 同步至 HTTP-Only Cookie，供 SSR 使用
          try {
            await fetch('/api/auth/set-token', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ authToken, refreshToken }),
            });
          } catch (cookieErr) {
            console.error('写入 Cookie 失败', cookieErr);
          }

          // 先重置Apollo Client缓存，确保不使用旧数据
          await client.resetStore();
          console.log('登录: Apollo Client缓存已重置');
          
          // 主动获取最新的用户信息（包含完整的头像等信息）
          try {
            console.log('获取最新用户数据');
            const currentUser = await fetchCurrentUser();
            if (currentUser) {
              setUser(currentUser); // 使用最新获取的用户数据更新状态
              console.log('用户数据已更新，包含头像URL:', currentUser?.avatar?.url);
            } else {
              setUser(user); // 备选：使用登录返回的用户数据
              console.log('使用登录返回的用户数据');
            }
          } catch (fetchError) {
            console.error('获取用户详情失败，使用登录返回的基本数据:', fetchError);
            setUser(user); // 使用登录返回的信息作为备选
          }
          
        } else {
          throw new Error('登录失败：未返回认证令牌');
        }
      } else {
        throw new Error('登录失败：未返回数据');
      }
    } catch (err) {
      console.error('登录失败:', err);
      setError(err instanceof Error ? err.message : '登录过程中发生错误');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 用户注册
   * @param input 注册信息
   */
  const register = async (input: RegisterUserInput): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data } = await registerMutation({
        variables: {
          username: input.username,
          email: input.email,
          password: input.password
        }
      });
      
      if (data?.registerUser) {
        // 注册成功后自动登录
        await login({
          username: input.username,
          password: input.password
        });
      } else {
        throw new Error('注册失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '注册失败，请重试';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 用户登出
   */
  const logout = async (): Promise<void> => {
    console.log('执行登出操作');
    clearAuthData();
    setUser(null);

    // 通知后端清除 Cookie
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
    } catch (e) {
      console.error('清除 Cookie 失败', e);
    }

    // 重置Apollo Client缓存
    try {
      await client.resetStore();
      console.log('登出: Apollo Client缓存已重置');
    } catch (error) {
      console.error('登出时重置缓存失败:', error);
    }
  };

  /**
   * 发送密码重置邮件
   * @param username 用户名或邮箱
   */
  const sendPasswordResetEmail = async (username: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data } = await sendPasswordResetEmailMutation({
        variables: { username }
      });
      
      if (!data?.sendPasswordResetEmail?.success) {
        throw new Error('发送密码重置邮件失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '发送密码重置邮件失败，请重试';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 重置密码
   * @param input 重置密码信息
   */
  const resetPassword = async (input: ResetPasswordInput): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data } = await resetPasswordMutation({
        variables: {
          key: input.key,
          login: input.login,
          password: input.password
        }
      });
      
      if (!data?.resetUserPassword?.success) {
        throw new Error('重置密码失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '重置密码失败，请重试';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 更新用户资料
   * @param data 更新的用户资料
   */
  const updateUserProfile = async (data: UpdateUserInput): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    if (!user || !user.id) {
      setError('用户未登录或会话已过期');
      setIsLoading(false);
      throw new Error('用户未登录或会话已过期');
    }
    
    try {
      const { data: responseData } = await updateProfileMutation({
        variables: {
          id: user.id,
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          description: data.description,
          nickname: data.nickname
        }
      });
      
      if (responseData?.updateUser?.user) {
        const updatedUser = responseData.updateUser.user;
        setUser(updatedUser);
        setUserData(updatedUser);
      } else {
        throw new Error('更新用户资料失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '更新用户资料失败，请重试';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 发送密码重置验证码
   * @param email 用户邮箱
   */
  const sendPasswordResetCode = async (email: string): Promise<{ success: boolean; message: string }> => {
    setIsLoading(true);
    try {
      const { data } = await sendPasswordResetCodeMutation({
        variables: { email }
      });
      
      return {
        success: data?.sendPasswordResetCode?.success || false,
        message: data?.sendPasswordResetCode?.message || '发送验证码操作完成'
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '发送验证码失败，请重试';
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 使用验证码重置密码
   * @param email 用户邮箱
   * @param code 验证码
   * @param newPassword 新密码
   */
  const resetPasswordWithCode = async (
    email: string,
    code: string,
    newPassword: string
  ): Promise<{ success: boolean; message: string }> => {
    setIsLoading(true);
    try {
      const { data } = await resetPasswordWithCodeMutation({
        variables: { email, code, newPassword }
      });
      
      return {
        success: data?.resetPasswordWithCode?.success || false,
        message: data?.resetPasswordWithCode?.message || '密码重置操作完成'
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '密码重置失败，请重试';
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 使用手机验证码重置密码
   * @param phone 手机号
   * @param code 验证码
   * @param newPassword 新密码
   * @param nationCode 国际区号（可选）
   */
  const resetPasswordWithPhoneCode = async (
    phone: string,
    code: string,
    newPassword: string,
    nationCode: string = '86'
  ): Promise<{ success: boolean; message: string }> => {
    setIsLoading(true);
    try {
      const { data } = await resetPasswordWithPhoneCodeMutation({
        variables: { 
          phone, 
          code, 
          newPassword,
          nationCode
        }
      });
      
      return {
        success: data?.resetPasswordWithPhoneCode?.success || false,
        message: data?.resetPasswordWithPhoneCode?.message || '密码重置操作完成'
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '密码重置失败，请重试';
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 修改密码
   * @param currentPassword 当前密码
   * @param newPassword 新密码
   */
  const changePassword = async (currentPassword: string, newPassword: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data } = await changePasswordMutation({
        variables: {
          currentPassword,
          newPassword
        }
      });
      
      if (!data?.changePassword?.success) {
        throw new Error(data?.changePassword?.message || '修改密码失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '修改密码失败，请重试';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 验证重置密码验证码
   * @param email 用户邮箱
   * @param code 验证码
   */
  const verifyResetCode = async (email: string, code: string): Promise<{ success: boolean; isValid: boolean; message: string }> => {
    try {
      const { data } = await verifyResetCodeMutation({
        variables: { email, code }
      });
      
      return {
        success: data?.verifyResetCode?.success || false,
        isValid: data?.verifyResetCode?.isValid || false,
        message: data?.verifyResetCode?.message || '验证码验证完成'
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '验证码验证失败，请重试';
      setError(errorMessage);
      return {
        success: false,
        isValid: false,
        message: errorMessage
      };
    }
  };

  /**
   * 清除错误
   */
  const clearError = () => {
    setError(null);
  };

  /**
   * 发送手机验证码
   * @param input 手机号和国际区号
   */
  const sendPhoneCode = async (input: SendPhoneCodeInput): Promise<PhoneAuthResponse> => {
    setError(null);
    
    try {
      // 如果是验证验证码
      if (input.verify && input.code) {
        // 验证码格式检查
        if (!/^\d{6}$/.test(input.code)) {
          return {
            success: false,
            message: '验证码格式错误，请输入6位数字'
          };
        }
        
        // 调用真实的GraphQL验证接口来验证验证码
        try {
          const { data } = await client.mutate({
            mutation: VERIFY_PHONE_CODE,
            variables: {
              phone: input.phone,
              code: input.code,
              nationCode: input.nationCode || '86'
            }
          });
          
          if (data?.verifyPhoneCode) {
            return {
              success: data.verifyPhoneCode.success,
              message: data.verifyPhoneCode.message
            };
          } else {
            throw new Error('验证码验证失败');
          }
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : '验证码错误或已过期';
          return {
            success: false,
            message: errorMessage
          };
        }
      }
      
      // 常规发送验证码逻辑
      const { data } = await sendPhoneCodeMutation({
        variables: {
          phone: input.phone,
          nationCode: input.nationCode || '86'
        }
      });
      
      if (data?.sendPhoneCode) {
        return {
          success: data.sendPhoneCode.success,
          message: data.sendPhoneCode.message
        };
      } else {
        throw new Error('发送验证码失败');
      }
    } catch (err) {
      let errorMessage = err instanceof Error ? err.message : '发送验证码失败，请重试';
      setError(errorMessage);
      
      return {
        success: false,
        message: errorMessage
      };
    }
  };

  /**
   * 手机号登录
   * @param input 手机号、验证码和国际区号
   */
  const phoneLogin = async (input: PhoneLoginInput): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data } = await phoneLoginMutation({
        variables: {
          phone: input.phone,
          code: input.code,
          nationCode: input.nationCode || '86'
        }
      });
      
      if (data?.phoneLogin && data.phoneLogin.success) {
        const { authToken, user } = data.phoneLogin;
        
        // 设置用户数据和令牌
        console.log('手机登录成功，保存认证数据');
        
        // 保存认证相关数据到本地存储
        saveAuthData(authToken, null, user);
        
        // 重置Apollo Client缓存，确保不使用旧数据
        await client.resetStore();
        console.log('手机登录: Apollo Client缓存已重置');
        
        // 主动获取最新的用户信息
        try {
          console.log('获取最新用户数据');
          const currentUser = await fetchCurrentUser();
          if (currentUser) {
            setUser(currentUser); // 使用最新获取的用户数据更新状态
            console.log('用户数据已更新，包含头像URL:', currentUser?.avatar?.url);
          } else {
            setUser(user); // 备选：使用登录返回的用户数据
            console.log('使用登录返回的用户数据');
          }
        } catch (fetchErr) {
          console.error('获取最新用户数据失败:', fetchErr);
          setUser(user); // 使用登录返回的用户数据作为备选
        }
      } else {
        let errorMessage = data?.phoneLogin?.message || '手机号登录失败';
        throw new Error(errorMessage);
      }
    } catch (err) {
      let errorMessage = err instanceof Error ? err.message : '手机号登录失败，请重试';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 手机号注册
   * @param input 手机号、验证码、国际区号、用户名和显示名称
   */
  const phoneRegister = async (input: PhoneRegisterInput): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    // 如果没有提供用户名，则自动生成一个
    let username = input.username;
    if (!username) {
      // 生成一个基于手机号和随机字符的用户名
      const randomStr = Math.random().toString(36).substring(2, 8);
      username = `user_${input.phone.substring(input.phone.length - 4)}${randomStr}`;
      console.log('[调试-AuthContext] 自动生成用户名:', username);
    }
    
    // 如果没有提供密码，则自动生成一个8位的强密码
    let password = input.password;
    if (!password) {
      // 生成包含字母、数字和特殊字符的8位随机密码
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';
      password = Array(10).fill('').map(() => chars.charAt(Math.floor(Math.random() * chars.length))).join('');
      console.log('[调试-AuthContext] 自动生成密码 (为安全起见不显示完整密码):', password.substring(0, 2) + '********');
    }
    
    try {
      const { data } = await phoneRegisterMutation({
        variables: {
          phone: input.phone,
          code: input.code,
          nationCode: input.nationCode || '86',
          username: username,
          displayName: input.displayName || username,
          password: password
        }
      });
      
      if (data?.phoneRegister && data.phoneRegister.success) {
        const { authToken, user } = data.phoneRegister;
        
        // 如果用户名或密码是自动生成的，我们需要告知用户
        if (!input.username || !input.password) {
          // 将自动生成的凭据存储在sessionStorage中，这样就可以在页面间传递
          if (typeof window !== 'undefined' && window.sessionStorage) {
            window.sessionStorage.setItem('autoGeneratedCredentials', JSON.stringify({
              username: !input.username ? username : undefined,
              password: !input.password ? password : undefined
            }));
          }
        }
        
        // 保存认证相关数据到本地存储
        console.log('手机注册成功，保存认证数据');
        saveAuthData(authToken, null, user);
        
        // 重置Apollo Client缓存，确保不使用旧数据
        await client.resetStore();
        console.log('手机注册: Apollo Client缓存已重置');
        
        // 主动获取最新的用户信息
        try {
          console.log('获取最新用户数据');
          const currentUser = await fetchCurrentUser();
          if (currentUser) {
            setUser(currentUser); // 使用最新获取的用户数据更新状态
            console.log('用户数据已更新，包含头像URL:', currentUser?.avatar?.url);
          } else {
            setUser(user); // 备选：使用登录返回的用户数据
            console.log('使用登录返回的用户数据');
          }
        } catch (fetchErr) {
          console.error('获取最新用户数据失败:', fetchErr);
          setUser(user); // 使用登录返回的用户数据作为备选
        }
      } else {
        let errorMessage = data?.phoneRegister?.message || '手机号注册失败';
        throw new Error(errorMessage);
      }
    } catch (err) {
      let errorMessage = err instanceof Error ? err.message : '手机号注册失败，请重试';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 验证手机验证码并获取绑定用的令牌
   * @param phone 手机号码
   * @param code 验证码
   * @param nationCode 国际区号（可选）
   */
  const verifyPhoneCodeForBindingAndGetToken = async (phone: string, code: string, nationCode: string = '86'): Promise<{ success: boolean; message: string; token?: string }> => {
    setError(null);
    
    console.log('[调试-AuthContext] 开始验证绑定手机验证码:', { phone, code, nationCode });
    
    try {
      const { data, errors } = await verifyPhoneCodeForBindingAndGetTokenMutation({
        variables: { phone, code, nationCode },
        // 设置fetchPolicy以避免缓存
        fetchPolicy: 'no-cache'
      });
      
      console.log('[调试-AuthContext] verifyPhoneCodeForBindingAndGetToken GraphQL响应:', { data, errors });
      
      if (errors && errors.length > 0) {
        console.error('[调试-AuthContext] GraphQL错误:', errors);
        throw new Error(errors[0].message);
      }
      
      if (data?.verifyPhoneCodeForBindingAndGetToken) {
        console.log('[调试-AuthContext] 手机验证码验证结果:', data.verifyPhoneCodeForBindingAndGetToken);
        return {
          success: data.verifyPhoneCodeForBindingAndGetToken.success,
          message: data.verifyPhoneCodeForBindingAndGetToken.message,
          token: data.verifyPhoneCodeForBindingAndGetToken.token
        };
      } else {
        console.error('[调试-AuthContext] 无效响应:', data);
        throw new Error('验证码验证失败: 服务器响应无效');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '验证码验证失败，请重试';
      console.error('[调试-AuthContext] 验证码验证错误:', err);
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    }
  };

  /**
   * 使用验证令牌绑定手机号
   */
  const bindPhone = async (input: BindPhoneInput): Promise<PhoneAuthResponse> => {
    setIsLoading(true);
    setError(null);
    
    console.log('[调试-AuthContext] 开始绑定手机号:', {
      phone: input.phone,
      token: input.token ? `${input.token.substring(0, 10)}...` : '空',
      nationCode: input.nationCode
    });
    
    try {
      console.log('[调试-AuthContext] 调用 bindPhoneMutation...');
      const { data, errors } = await bindPhoneMutation({
        variables: {
          phone: input.phone,
          token: input.token,
          nationCode: input.nationCode || '86'
        },
        fetchPolicy: 'no-cache' // 避免缓存导致的问题
      });
      
      console.log('[调试-AuthContext] bindPhoneMutation 响应:', { data, errors });
      
      if (errors && errors.length > 0) {
        console.error('[调试-AuthContext] GraphQL 错误:', errors);
        throw new Error(errors[0].message);
      }
      
      if (data?.bindPhone) {
        // 如果绑定成功，更新用户数据
        if (data.bindPhone.success && data.bindPhone.user) {
          console.log('[调试-AuthContext] 绑定成功，更新用户数据:', data.bindPhone.user);
          
          // 设置用户数据并保存到缓存
          setUser(data.bindPhone.user);
          setUserData(data.bindPhone.user);
          
          // 重置Apollo Client缓存，确保使用新的用户数据
          await client.resetStore();
          console.log('[调试-AuthContext] 手机绑定: Apollo Client缓存已重置');
          
          // 刷新最新的用户信息
          fetchCurrentUser().catch(error => {
            console.error('刷新用户数据失败:', error);
          });
        } else {
          console.warn('[调试-AuthContext] 绑定未成功:', data.bindPhone);
        }
        
        return {
          success: data.bindPhone.success,
          message: data.bindPhone.message,
          user: data.bindPhone.user
        };
      } else {
        console.error('[调试-AuthContext] 无效响应:', data);
        throw new Error('绑定手机号失败: 服务器响应无效');
      }
    } catch (err) {
      console.error('[调试-AuthContext] 绑定手机号错误:', err);
      let errorMessage = '';
      
      // 增强错误处理
      if (err instanceof ApolloError) {
        console.error('[调试-AuthContext] Apollo错误:', {
          networkError: err.networkError,
          graphQLErrors: err.graphQLErrors
        });
        
        if (err.networkError) {
          errorMessage = '网络错误，请检查您的网络连接';
        } else if (err.graphQLErrors && err.graphQLErrors.length > 0) {
          errorMessage = err.graphQLErrors[0].message || '绑定手机号失败';
        }
      } else if (err instanceof Error) {
        errorMessage = err.message;
      } else {
        errorMessage = '绑定手机号失败，请重试';
      }
      
      setError(errorMessage);
      
      return {
        success: false,
        message: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 解绑当前账户的手机号
   */
  const unbindPhone = async (): Promise<PhoneAuthResponse> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data } = await unbindPhoneMutation({
        variables: {}
      });
      
      if (data?.unbindPhone) {
        // 如果解绑成功，更新用户数据
        if (data.unbindPhone.success && data.unbindPhone.user) {
          setUser(data.unbindPhone.user);
          setUserData(data.unbindPhone.user);
          
          // 刷新最新的用户信息
          fetchCurrentUser().catch(error => {
            console.error('刷新用户数据失败:', error);
          });
        }
        
        return {
          success: data.unbindPhone.success,
          message: data.unbindPhone.message,
          user: data.unbindPhone.user
        };
      } else {
        throw new Error('解绑手机号失败');
      }
    } catch (err) {
      let errorMessage = err instanceof Error ? err.message : '解绑手机号失败，请重试';
      setError(errorMessage);
      
      return {
        success: false,
        message: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 验证邮箱验证码并获取令牌
   * @param email 用户邮箱
   * @param code 验证码
   */
  const verifyEmailCodeAndGetToken = async (email: string, code: string): Promise<{ success: boolean; message: string; token?: string }> => {
    setError(null);
    try {
      console.log('开始验证邮箱验证码并获取令牌:', email, code);
      const { data } = await verifyRegistrationCodeAndGetTokenMutation({
        variables: { email, code },
        fetchPolicy: 'no-cache' // 避免缓存导致的问题
      });
      
      console.log('验证邮箱验证码响应:', data);
      
      const result = data?.verifyRegistrationCodeAndGetToken;
      
      if (!result) {
        console.error('验证邮箱验证码API返回异常，未返回结果');
        return {
          success: false,
          message: '验证失败，请重试'
        };
      }
      
      if (result.success && result.token) {
        console.log('邮箱验证成功，获取到令牌');
        return {
          success: true,
          message: result.message || '验证成功',
          token: result.token
        };
      } else {
        console.warn('邮箱验证失败:', result.message);
        return {
          success: false,
          message: result.message || '验证失败，请检查验证码是否正确'
        };
      }
    } catch (error) {
      console.error('验证邮箱验证码出错:', error);
      
      // 增强错误处理
      let errorMessage = '验证失败，请重试';
      if (error instanceof ApolloError) {
        if (error.networkError) {
          errorMessage = '网络错误，请检查您的网络连接';
        } else if (error.graphQLErrors && error.graphQLErrors.length > 0) {
          errorMessage = error.graphQLErrors[0].message || errorMessage;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    }
  };

  /**
   * 使用验证令牌进行注册
   * @param input 包含用户名、邮箱、密码和验证令牌的注册信息
   */
  const registerWithToken = async (input: RegisterWithTokenInput): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data } = await registerWithTokenMutation({
        variables: {
          username: input.username,
          email: input.email,
          password: input.password,
          token: input.token
        }
      });
      
      if (data?.registerWithToken && data.registerWithToken.success) {
        // 注册成功后自动登录
        await login({
          username: input.username,
          password: input.password
        });
      } else {
        throw new Error(data?.registerWithToken?.message || '注册失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '注册失败，请重试';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 验证手机验证码并获取令牌
   * @param phone 手机号码
   * @param code 验证码
   * @param nationCode 国际区号（可选）
   */
  const verifyPhoneCodeAndGetToken = async (phone: string, code: string, nationCode: string = '86'): Promise<{ success: boolean; message: string; token?: string }> => {
    setError(null);
    
    console.log('[调试-AuthContext] 开始验证手机验证码:', { phone, code, nationCode });
    
    try {
      const { data, errors } = await verifyPhoneCodeAndGetTokenMutation({
        variables: { phone, code, nationCode },
        // 设置fetchPolicy以避免缓存
        fetchPolicy: 'no-cache'
      });
      
      console.log('[调试-AuthContext] verifyPhoneCodeAndGetToken GraphQL响应:', { data, errors });
      
      if (errors && errors.length > 0) {
        console.error('[调试-AuthContext] GraphQL错误:', errors);
        throw new Error(errors[0].message);
      }
      
      if (data?.verifyPhoneCodeAndGetToken) {
        console.log('[调试-AuthContext] 手机验证码验证结果:', data.verifyPhoneCodeAndGetToken);
        return {
          success: data.verifyPhoneCodeAndGetToken.success,
          message: data.verifyPhoneCodeAndGetToken.message,
          token: data.verifyPhoneCodeAndGetToken.token
        };
      } else {
        console.error('[调试-AuthContext] 无效响应:', data);
        throw new Error('验证码验证失败: 服务器响应无效');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '验证码验证失败，请重试';
      console.error('[调试-AuthContext] 验证码验证错误:', err);
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    }
  };

  /**
   * 使用验证令牌进行手机注册
   * @param input 包含手机号、用户名、密码和验证令牌的注册信息
   */
  const phoneRegisterWithToken = async (input: PhoneRegisterWithTokenInput): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    // 如果没有提供用户名，则自动生成一个
    let username = input.username;
    if (!username) {
      // 生成一个基于手机号和随机字符的用户名
      const randomStr = Math.random().toString(36).substring(2, 8);
      username = `user_${input.phone.substring(input.phone.length - 4)}${randomStr}`;
      console.log('[调试-AuthContext] 自动生成用户名:', username);
    }
    
    // 如果没有提供密码，则自动生成一个8位的强密码
    let password = input.password;
    if (!password) {
      // 生成包含字母、数字和特殊字符的8位随机密码
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';
      password = Array(10).fill('').map(() => chars.charAt(Math.floor(Math.random() * chars.length))).join('');
      console.log('[调试-AuthContext] 自动生成密码 (为安全起见不显示完整密码):', password.substring(0, 2) + '********');
    }
    
    console.log('[调试-AuthContext] 开始手机令牌注册:', {
      phone: input.phone,
      username: username,
      token: input.token ? `${input.token.substring(0, 10)}...` : 'null',
      nationCode: input.nationCode,
      hasAutoUsername: !input.username,
      hasAutoPassword: !input.password
    });
    
    try {
      const { data, errors } = await phoneRegisterWithTokenMutation({
        variables: {
          phone: input.phone,
          username: username,
          password: password,
          displayName: input.displayName || username,
          token: input.token,
          nationCode: input.nationCode || '86'
        }
      });
      
      console.log('[调试-AuthContext] phoneRegisterWithToken GraphQL响应:', { data, errors });
      
      if (errors && errors.length > 0) {
        console.error('[调试-AuthContext] GraphQL错误:', errors);
        throw new Error(errors[0].message);
      }
      
      if (data?.phoneRegisterWithToken && data.phoneRegisterWithToken.success) {
        console.log('[调试-AuthContext] 手机注册成功, 现在尝试登录');
        
        // 如果用户名或密码是自动生成的，我们需要告知用户
        if (!input.username || !input.password) {
          // 将自动生成的凭据存储在sessionStorage中，这样就可以在页面间传递
          if (typeof window !== 'undefined' && window.sessionStorage) {
            window.sessionStorage.setItem('autoGeneratedCredentials', JSON.stringify({
              username: !input.username ? username : undefined,
              password: !input.password ? password : undefined
            }));
          }
        }
        
        // 注册成功后手动登录，与邮箱注册行为一致
        await login({
          username: username,
          password: password
        });
      } else {
        console.error('[调试-AuthContext] 注册失败:', data?.phoneRegisterWithToken);
        throw new Error(data?.phoneRegisterWithToken?.message || '注册失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '注册失败，请重试';
      console.error('[调试-AuthContext] 注册错误:', err);
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 发送邮箱绑定验证码
   * @param email 邮箱地址
   */
  const sendEmailBindingCode = async (email: string): Promise<{ success: boolean; message: string }> => {
    setError(null);
    
    try {
      console.log('[调试-邮箱绑定] 发送邮箱绑定验证码:', email);
      
      const { data } = await sendEmailBindingCodeMutation({
        variables: { email },
        fetchPolicy: 'no-cache'
      });
      
      console.log('[调试-邮箱绑定] 发送结果:', data);
      
      if (data?.sendEmailBindingCode) {
        return {
          success: data.sendEmailBindingCode.success,
          message: data.sendEmailBindingCode.message
        };
      } else {
        throw new Error('发送验证码失败');
      }
    } catch (err) {
      console.error('[调试-邮箱绑定] 发送验证码错误:', err);
      
      let errorMessage = '发送验证码失败，请重试';
      if (err instanceof Error) {
        errorMessage = err.message;
      }
      
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    }
  };
  
  /**
   * 验证邮箱绑定验证码并获取令牌
   * @param email 邮箱地址
   * @param code 验证码
   */
  const verifyEmailBindingCodeAndGetToken = async (email: string, code: string): Promise<{ success: boolean; message: string; token?: string }> => {
    setError(null);
    
    try {
      console.log('[调试-邮箱绑定] 验证邮箱验证码:', { email, code });
      
      const { data } = await verifyEmailBindingCodeAndGetTokenMutation({
        variables: { email, code },
        fetchPolicy: 'no-cache'
      });
      
      console.log('[调试-邮箱绑定] 验证结果:', data);
      
      if (data?.verifyEmailBindingCodeAndGetToken) {
        return {
          success: data.verifyEmailBindingCodeAndGetToken.success,
          message: data.verifyEmailBindingCodeAndGetToken.message,
          token: data.verifyEmailBindingCodeAndGetToken.token
        };
      } else {
        throw new Error('验证码验证失败');
      }
    } catch (err) {
      console.error('[调试-邮箱绑定] 验证码验证错误:', err);
      
      let errorMessage = '验证码验证失败，请重试';
      if (err instanceof Error) {
        errorMessage = err.message;
      }
      
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    }
  };
  
  /**
   * 绑定邮箱
   * @param input 包含邮箱和验证令牌的输入数据
   */
  const bindEmail = async (input: BindEmailInput): Promise<{ success: boolean; message: string; user?: User }> => {
    setError(null);
    setIsLoading(true);
    
    try {
      console.log('[调试-邮箱绑定] 绑定邮箱:', { 
        email: input.email, 
        token: input.token ? `${input.token.substring(0, 10)}...` : '空' 
      });
      
      const { data } = await bindEmailMutation({
        variables: {
          email: input.email,
          token: input.token
        }
      });
      
      console.log('[调试-邮箱绑定] 绑定结果:', data);
      
      if (data?.bindEmail) {
        if (data.bindEmail.success && data.bindEmail.user) {
          // 更新用户数据
          setUser(data.bindEmail.user);
          setUserData(data.bindEmail.user);
          
          // 邮箱绑定成功后，刷新用户认证令牌
          try {
            console.log('[调试-邮箱绑定] 开始刷新认证令牌...');
            
            // 重新查询当前用户信息，并更新令牌
            const { data: refreshData } = await client.query({
              query: GET_CURRENT_USER,
              fetchPolicy: 'network-only' // 禁用缓存，始终从网络获取最新数据
            });
            
            if (refreshData?.viewer) {
              console.log('[调试-邮箱绑定] 用户令牌已刷新');
              
              // 这里我们重新设置用户数据，确保前端状态与后端一致
              setUser(refreshData.viewer);
              setUserData(refreshData.viewer);
              
              // 通知客户端重置存储，确保 Apollo 缓存与新状态一致
              await client.resetStore();
              console.log('[调试-邮箱绑定] Apollo Client缓存已重置');
            } else {
              console.warn('[调试-邮箱绑定] 刷新用户数据成功，但未返回用户信息');
            }
          } catch (refreshErr) {
            console.error('[调试-邮箱绑定] 刷新令牌失败:', refreshErr);
            // 我们不希望令牌刷新错误影响主要的绑定流程，因此只记录错误
          }
          
          return {
            success: true,
            message: data.bindEmail.message || '邮箱绑定成功',
            user: data.bindEmail.user
          };
        } else {
          return {
            success: false,
            message: data.bindEmail.message || '邮箱绑定失败'
          };
        }
      } else {
        throw new Error('邮箱绑定失败');
      }
    } catch (err) {
      console.error('[调试-邮箱绑定] 绑定错误:', err);
      
      let errorMessage = '邮箱绑定失败，请重试';
      if (err instanceof Error) {
        errorMessage = err.message;
      }
      
      setError(errorMessage);
      return {
        success: false,
        message: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  };

  // 提供的上下文值
  const value = {
    user,
    isAuthenticated: !!user,
    isLoading,
    error,
    login,
    register,
    logout,
    sendPasswordResetEmail,
    resetPassword,
    updateUserProfile,
    sendPasswordResetCode,
    resetPasswordWithCode,
    resetPasswordWithPhoneCode,
    changePassword,
    verifyResetCode,
    clearError,
    // 手机验证相关方法
    sendPhoneCode,
    phoneLogin,
    phoneRegister,
    // 添加手机令牌登录方法
    phoneLoginWithToken: async (input: { phone: string; token: string; nationCode?: string }) => {
      setIsLoading(true);
      setError(null);
      
      try {
        const { data } = await phoneLoginWithTokenMutation({
          variables: {
            phone: input.phone,
            token: input.token,
            nationCode: input.nationCode || '86'
          }
        });
        
        if (data?.phoneLoginWithToken && data.phoneLoginWithToken.success) {
          const { authToken, user } = data.phoneLoginWithToken;
          
          // 设置用户数据和令牌
          console.log('手机令牌登录成功，保存认证数据');
          
          // 保存认证相关数据到本地存储
          saveAuthData(authToken, null, user);
          
          // 重置Apollo Client缓存，确保不使用旧数据
          await client.resetStore();
          console.log('手机令牌登录: Apollo Client缓存已重置');
          
          // 主动获取最新的用户信息
          try {
            console.log('获取最新用户数据');
            const currentUser = await fetchCurrentUser();
            if (currentUser) {
              setUser(currentUser); // 使用最新获取的用户数据更新状态
              console.log('用户数据已更新，包含头像URL:', currentUser?.avatar?.url);
            } else {
              setUser(user); // 备选：使用登录返回的用户数据
              console.log('使用登录返回的用户数据');
            }
          } catch (fetchErr) {
            console.error('获取最新用户数据失败:', fetchErr);
            setUser(user); // 使用登录返回的用户数据作为备选
          }
        } else {
          let errorMessage = data?.phoneLoginWithToken?.message || '手机号登录失败';
          throw new Error(errorMessage);
        }
      } catch (err) {
        let errorMessage = err instanceof Error ? err.message : '手机号登录失败，请重试';
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    },
    bindPhone,
    unbindPhone,
    // 邮箱验证方法
    verifyEmailCodeAndGetToken,
    registerWithToken,
    // 新增方法
    verifyPhoneCodeAndGetToken,
    phoneRegisterWithToken,
    verifyPhoneCodeForBindingAndGetToken,
    // 新增：邮箱绑定相关方法
    sendEmailBindingCode,
    verifyEmailBindingCodeAndGetToken,
    bindEmail,
    // 新增：刷新用户信息
    refreshUser: async (): Promise<User | null> => {
      setIsLoading(true);
      setError(null);
      
      try {
        const currentUser = await fetchCurrentUser();
        if (currentUser) {
          setUser(currentUser);
          setUserData(currentUser);
          
          // 重置Apollo Client缓存，确保使用最新的用户数据
          await client.resetStore();
          console.log('刷新用户信息: Apollo Client缓存已重置');
          
          return currentUser;
        } else {
          throw new Error('用户数据获取失败');
        }
      } catch (err) {
        console.error('刷新用户信息失败:', err);
        setError(err instanceof Error ? err.message : '刷新用户信息失败');
        return null;
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}; 