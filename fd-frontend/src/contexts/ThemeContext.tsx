'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { lightTheme, darkTheme } from '../styles/theme';

// 主题类型
type ThemeMode = 'light' | 'dark';

// 主题上下文接口
interface ThemeContextType {
  theme: typeof lightTheme;
  themeMode: ThemeMode;
  toggleTheme: () => void;
  setThemeMode: (mode: ThemeMode) => void;
}

// 创建主题上下文
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// 主题提供者属性接口
interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: ThemeMode;
}

// 本地存储键
const THEME_STORAGE_KEY = 'fd-theme-mode';

// 主题提供者组件
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ 
  children, 
  defaultTheme = 'light' 
}) => {
  // 状态
  const [themeMode, setThemeMode] = useState<ThemeMode>(defaultTheme);
  const [theme, setTheme] = useState(themeMode === 'light' ? lightTheme : darkTheme);

  // 初始化主题
  useEffect(() => {
    // 从本地存储中获取主题模式
    const savedTheme = localStorage.getItem(THEME_STORAGE_KEY) as ThemeMode | null;
    
    if (savedTheme) {
      setThemeMode(savedTheme);
    } else {
      // 检查系统偏好
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setThemeMode(prefersDark ? 'dark' : 'light');
    }
  }, []);

  // 当主题模式变化时更新主题
  useEffect(() => {
    setTheme(themeMode === 'light' ? lightTheme : darkTheme);
    
    // 更新文档根元素的类
    if (themeMode === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    
    // 保存到本地存储
    localStorage.setItem(THEME_STORAGE_KEY, themeMode);
  }, [themeMode]);

  // 切换主题模式
  const toggleTheme = () => {
    setThemeMode(prev => prev === 'light' ? 'dark' : 'light');
  };

  // 提供上下文值
  const contextValue: ThemeContextType = {
    theme,
    themeMode,
    toggleTheme,
    setThemeMode,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// 自定义钩子用于访问主题上下文
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  
  if (context === undefined) {
    throw new Error('useTheme必须在ThemeProvider内部使用');
  }
  
  return context;
};

export default ThemeContext; 