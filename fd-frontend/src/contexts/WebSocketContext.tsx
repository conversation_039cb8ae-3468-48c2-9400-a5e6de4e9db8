'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from '@/hooks/useAuth';
import { getAuthToken } from '@/utils/auth-utils';



interface WebSocketContextType {
  socket: Socket | null;
  isConnected: boolean;
}

const WebSocketContext = createContext<WebSocketContextType | null>(null);

// 全局 WebSocket 实例 (单例)
let socketInstance: Socket | null = null;

export const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const connectSocket = () => {
      // 如果已存在实例，先断开
      if (socketInstance) {
        socketInstance.disconnect();
        socketInstance = null;
      }

      const token = isAuthenticated ? getAuthToken() : null;
      const wsUrl = process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'http://localhost:8082';
      
      const options = {
        reconnectionAttempts: 5,
        reconnectionDelay: 5000,
        auth: token ? { token } : undefined,
      };

      // connect attempt (debug log removed)
      const newSocket = io(wsUrl, options);

      newSocket.on('connect', () => {
        // connected (debug log removed)
        socketInstance = newSocket;
        setIsConnected(true);
      });

      newSocket.on('disconnect', (reason: string) => {
        // disconnected (debug log removed)
        // 只有在非手动断开时才重置实例，允许重连
        if (reason !== 'io client disconnect') {
            socketInstance = null;
        }
        setIsConnected(false);
      });

      newSocket.on('connect_error', (err: Error) => {
        console.error('WebSocket connection error:', err.message);
      });
    };

    connectSocket();

    // 在组件卸载时断开连接
    return () => {
        if(socketInstance) {
            // unmount disconnect (debug log removed)
        socketInstance.disconnect();
        socketInstance = null;
        setIsConnected(false);
        }
    }
    
  }, [isAuthenticated]);

  return (
    <WebSocketContext.Provider value={{ socket: socketInstance, isConnected }}>
      {children}
    </WebSocketContext.Provider>
  );
};

export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (context === null) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
}; 