'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import Modal from '../components/ui/Modal';

// 模态框上下文类型
interface ModalContextType {
  openModal: (options: ModalOptions) => void;
  closeModal: () => void;
}

// 模态框选项类型
interface ModalOptions {
  title?: ReactNode;
  content: ReactNode;
  footer?: ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closeOnClickOutside?: boolean;
  closeOnEsc?: boolean;
  showCloseButton?: boolean;
  modalClassName?: string;
  headerClassName?: string;
  bodyClassName?: string;
  footerClassName?: string;
}

// 创建上下文
const ModalContext = createContext<ModalContextType | undefined>(undefined);

// 提供一个自定义钩子来使用模态框上下文
export const useModal = () => {
  const context = useContext(ModalContext);
  if (context === undefined) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};

// 模态框提供者组件
export const ModalProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [modalOptions, setModalOptions] = useState<ModalOptions | null>(null);

  // 打开模态框
  const openModal = useCallback((options: ModalOptions) => {
    setModalOptions(options);
    setIsOpen(true);
  }, []);

  // 关闭模态框
  const closeModal = useCallback(() => {
    setIsOpen(false);
    // 延迟清空内容，以便关闭动画完成
    setTimeout(() => {
      setModalOptions(null);
    }, 300);
  }, []);

  return (
    <ModalContext.Provider value={{ openModal, closeModal }}>
      {children}
      {modalOptions && (
        <Modal
          isOpen={isOpen}
          onClose={closeModal}
          title={modalOptions.title}
          footer={modalOptions.footer}
          size={modalOptions.size || 'md'}
          closeOnClickOutside={modalOptions.closeOnClickOutside}
          closeOnEsc={modalOptions.closeOnEsc}
          showCloseButton={modalOptions.showCloseButton !== false}
          modalClassName={modalOptions.modalClassName}
          headerClassName={modalOptions.headerClassName}
          bodyClassName={modalOptions.bodyClassName}
          footerClassName={modalOptions.footerClassName}
        >
          {modalOptions.content}
        </Modal>
      )}
    </ModalContext.Provider>
  );
};

export default ModalProvider; 