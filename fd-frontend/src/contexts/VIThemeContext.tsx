'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode, useMemo } from 'react';
import { useQuery } from '@apollo/client';
import { GET_VI_SETTINGS } from '@/lib/graphql/queries';

// VI主题设置接口
interface VISettings {
  // 基础设置
  logoUrl?: string;
  logoDarkUrl?: string;
  faviconUrl?: string;
  
  // 颜色设置
  primaryColor?: string;
  secondaryColor?: string;
  backgroundColor?: string;
  darkModeEnabled?: boolean;
  darkBackgroundColor?: string;
  amberColor?: string;
  roseColor?: string;
  successColor?: string;
  errorColor?: string;
  
  // 排版设置
  headingFont?: string;
  bodyFont?: string;
  baseFontSize?: string;
  lineHeight?: string;
  spacingUnit?: string;
  
  // UI设计令牌
  radiusSmall?: string;
  radiusMedium?: string;
  radiusLarge?: string;
  shadowSmall?: string;
  shadowMedium?: string;
  shadowLarge?: string;
  
  // 搜索设置
  searchEngineType?: 'graphql' | 'meilisearch';
  meilisearchApiUrl?: string;
  meilisearchApiKey?: string;
  meilisearchIndexName?: string;
}

// 默认VI设置
const defaultSettings: VISettings = {
  // 基础设置
  logoUrl: '',
  logoDarkUrl: '',
  faviconUrl: '',
  
  // 颜色设置
  primaryColor: '#1a73e8',
  secondaryColor: '#4285f4',
  backgroundColor: '#ffffff',
  darkModeEnabled: true,
  darkBackgroundColor: '#121212',
  amberColor: '#FFA000',
  roseColor: '#EC407A',
  successColor: '#4CAF50',
  errorColor: '#F44336',
  
  // 排版设置
  headingFont: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  bodyFont: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  baseFontSize: '16px',
  lineHeight: '1.5',
  spacingUnit: '4px',
  
  // UI设计令牌
  radiusSmall: '4px',
  radiusMedium: '8px',
  radiusLarge: '16px',
  shadowSmall: '0 2px 4px rgba(0,0,0,0.05)',
  shadowMedium: '0 4px 8px rgba(0,0,0,0.1)',
  shadowLarge: '0 8px 16px rgba(0,0,0,0.15)',
  
  // 搜索设置
  searchEngineType: 'graphql',
  meilisearchApiUrl: 'http://localhost:7700',
  meilisearchApiKey: '',
  meilisearchIndexName: 'posts',
};

// VI主题上下文接口
interface VIThemeContextType {
  settings: VISettings;
  loading: boolean;
  error: any;
  cssVariables: string;
  getLogoUrl: (isDark?: boolean) => string;
  getRadius: (size?: 'small' | 'medium' | 'large') => string;
  getShadow: (size?: 'small' | 'medium' | 'large') => string;
  getColor: (colorName: string) => string;
  getFontFamily: (type?: 'heading' | 'body') => string;
}

// 创建VI主题上下文
const VIThemeContext = createContext<VIThemeContextType | undefined>(undefined);

// VI主题提供者属性接口
interface VIThemeProviderProps {
  children: ReactNode;
}

// VI主题提供者组件
export const VIThemeProvider: React.FC<VIThemeProviderProps> = ({ children }) => {
  const { loading, error, data } = useQuery(GET_VI_SETTINGS);
  const [settings, setSettings] = useState<VISettings>(defaultSettings);
  const [cssVariables, setCssVariables] = useState<string>('');

  // 当数据加载完成时更新设置
  useEffect(() => {
    if (data?.viSettings) {
      setSettings({
        ...defaultSettings,
        ...data.viSettings,
      });
    }
  }, [data]);

  // 当设置变化时生成CSS变量
  useEffect(() => {
    // 创建CSS变量字符串
    const variables = `
      :root {
        /* 颜色设置 */
        --primary-color: ${settings.primaryColor};
        --secondary-color: ${settings.secondaryColor};
        --background-color: ${settings.backgroundColor};
        --dark-background-color: ${settings.darkBackgroundColor};
        --amber-color: ${settings.amberColor};
        --rose-color: ${settings.roseColor};
        --success-color: ${settings.successColor};
        --error-color: ${settings.errorColor};
        
        /* 排版设置 */
        --heading-font: ${settings.headingFont};
        --body-font: ${settings.bodyFont};
        --base-font-size: ${settings.baseFontSize};
        --line-height: ${settings.lineHeight};
        --spacing-unit: ${settings.spacingUnit};
        
        /* UI设计令牌 */
        --radius-small: ${settings.radiusSmall};
        --radius-medium: ${settings.radiusMedium};
        --radius-large: ${settings.radiusLarge};
        --shadow-small: ${settings.shadowSmall};
        --shadow-medium: ${settings.shadowMedium};
        --shadow-large: ${settings.shadowLarge};
      }
      
      .dark {
        --background-color: ${settings.darkBackgroundColor};
      }

      /* 应用字体到全局 */
      body {
        font-family: ${settings.bodyFont}, var(--font-sans);
        font-size: ${settings.baseFontSize};
        line-height: ${settings.lineHeight};
      }

      h1, h2, h3, h4, h5, h6 {
        font-family: ${settings.headingFont}, var(--font-sans);
      }

      /* Tailwind组件样式扩展 */
      .btn {
        border-radius: ${settings.radiusMedium};
      }
      
      .btn-primary {
        background-color: ${settings.primaryColor};
      }
      
      .btn-secondary {
        background-color: ${settings.secondaryColor};
      }
      
      .btn-success {
        background-color: ${settings.successColor};
      }
      
      .btn-error {
        background-color: ${settings.errorColor};
      }
      
      .card {
        border-radius: ${settings.radiusMedium};
        box-shadow: ${settings.shadowSmall};
      }
      
      .card-hover:hover {
        box-shadow: ${settings.shadowMedium};
      }
    `;
    
    setCssVariables(variables);
  }, [settings]);

  // 辅助函数，获取Logo URL
  const getLogoUrl = (isDark = false) => {
    if (isDark && settings.logoDarkUrl) {
      return settings.logoDarkUrl;
    }
    return settings.logoUrl || '';
  };

  // 辅助函数，获取圆角值
  const getRadius = (size = 'medium') => {
    switch (size) {
      case 'small': return settings.radiusSmall || '4px';
      case 'large': return settings.radiusLarge || '16px';
      default: return settings.radiusMedium || '8px';
    }
  };

  // 辅助函数，获取阴影值
  const getShadow = (size = 'medium') => {
    switch (size) {
      case 'small': return settings.shadowSmall || '0 2px 4px rgba(0,0,0,0.05)';
      case 'large': return settings.shadowLarge || '0 8px 16px rgba(0,0,0,0.15)';
      default: return settings.shadowMedium || '0 4px 8px rgba(0,0,0,0.1)';
    }
  };

  // 辅助函数，获取颜色值
  const getColor = (colorName: string) => {
    switch (colorName) {
      case 'primary': return settings.primaryColor || '#1a73e8';
      case 'secondary': return settings.secondaryColor || '#4285f4';
      case 'background': return settings.backgroundColor || '#ffffff';
      case 'darkBackground': return settings.darkBackgroundColor || '#121212';
      case 'amber': case 'warning': return settings.amberColor || '#FFA000';
      case 'rose': return settings.roseColor || '#EC407A';
      case 'success': return settings.successColor || '#4CAF50';
      case 'error': return settings.errorColor || '#F44336';
      default: return '#000000';
    }
  };

  // 辅助函数，获取字体
  const getFontFamily = (type = 'body') => {
    return type === 'heading' 
      ? settings.headingFont || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif' 
      : settings.bodyFont || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif';
  };

  // 提供上下文值
  const contextValue = useMemo<VIThemeContextType>(() => ({
    settings,
    loading,
    error,
    cssVariables,
    getLogoUrl,
    getRadius,
    getShadow,
    getColor,
    getFontFamily,
  }), [settings, loading, error, cssVariables]);

  return (
    <VIThemeContext.Provider value={contextValue}>
      <style jsx global>{`${cssVariables}`}</style>
      {children}
    </VIThemeContext.Provider>
  );
};

// 自定义钩子用于访问VI主题上下文
export const useVITheme = (): VIThemeContextType => {
  const context = useContext(VIThemeContext);
  
  if (context === undefined) {
    throw new Error('useVITheme必须在VIThemeProvider内部使用');
  }
  
  return context;
};

export default VIThemeContext; 