import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import * as cheerio from 'cheerio';

export async function POST(req: NextRequest) {
    try {
        const body = await req.json();
        const { url: weixinUrl } = body;

        // Forward cookies from the original request to WordPress
        const cookie = req.headers.get('cookie');

        if (!weixinUrl || typeof weixinUrl !== 'string' || !weixinUrl.includes('mp.weixin.qq.com')) {
            return NextResponse.json({ error: 'Invalid or missing WeChat article URL' }, { status: 400 });
        }

        const headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        };

        const response = await axios.get(weixinUrl, { headers });
        const html = response.data;

        const $ = cheerio.load(html);

        const title = $('meta[property="og:title"]').attr('content')?.trim() || '';
        const description = $('meta[property="og:description"]').attr('content')?.trim() || '';
        const contentElement = $('#js_content');
        
        const imagePromises = contentElement.find('img').map(async (index, element) => {
            const img = $(element);
            const weixinImgUrl = img.attr('data-src');

            if (weixinImgUrl) {
                try {
                    console.log("--- Sideloading Image ---");
                    console.log("WP_BASE_URL:", process.env.WP_BASE_URL);
                    console.log("WP_APP_USER:", process.env.WP_APP_USER);
                    // For security, don't log the password itself, just check if it exists
                    console.log("WP_APP_PASS exists:", !!process.env.WP_APP_PASS);

                    const basic = Buffer.from(`${process.env.WP_APP_USER}:${process.env.WP_APP_PASS}`).toString('base64');
                    const wpApiUrl = `${process.env.WP_BASE_URL}/wp-json/fd-grabweixin/v1/sideload-image`;

                    const apiRes = await fetch(wpApiUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Basic ${basic}`,
                        },
                        body: JSON.stringify({ imageUrl: weixinImgUrl }),
                    });

                    const result = await apiRes.json();
                    console.log(`Sideload API Response for ${weixinImgUrl}:`, result);
                    
                    if (result.success && result.url) {
                        img.attr('src', result.url);
                        img.removeAttr('data-src');
                    } else {
                        console.warn(`Sideload failed for ${weixinImgUrl}, keeping original data-src.`);
                    }
                } catch (e) {
                    console.error(`Failed to sideload image: ${weixinImgUrl}`, e);
                }
            }
        }).get();

        await Promise.all(imagePromises);

        const contentHtml = contentElement.html() || '';

        return NextResponse.json({
            success: true,
            title,
            description,
            contentHtml
        });

    } catch (error) {
        console.error('Error fetching WeChat article:', error);
        
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        return NextResponse.json({ error: 'Failed to fetch or parse article', details: errorMessage }, { status: 500 });
    }
} 