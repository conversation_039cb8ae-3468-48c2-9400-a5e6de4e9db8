// @ts-nocheck
import { NextResponse } from 'next/server';

// 该路由接收 { authToken:string, refreshToken?:string }，并把它们写入
// *.futuredecade.com 域的 HTTP-Only <PERSON><PERSON>，便于后端和 SSR 读取。
export async function POST(req: Request) {
  try {
    const { authToken, refreshToken } = await req.json();

    if (!authToken) {
      return NextResponse.json({ success: false, message: 'Missing authToken' }, { status: 400 });
    }

    const res = NextResponse.json({ success: true });

    const commonOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      domain: '.futuredecade.com', // 让 www 与 admin 子域共享
      path: '/',
    };

    // 访问令牌：较短有效期（7 天示例，可根据后端设置调整）
    res.cookies.set('fd_auth_token', authToken, {
      ...commonOptions,
      maxAge: 60 * 60 * 24 * 7, // 7 天
    });

    // 刷新令牌：更长有效期（30 天示例）
    if (refreshToken) {
      res.cookies.set('fd_refresh_token', refreshToken, {
        ...commonOptions,
        maxAge: 60 * 60 * 24 * 30, // 30 天
      });
    }

    return res;
  } catch (err) {
    console.error('set-token error', err);
    return NextResponse.json({ success: false, message: 'Invalid payload' }, { status: 400 });
  }
} 