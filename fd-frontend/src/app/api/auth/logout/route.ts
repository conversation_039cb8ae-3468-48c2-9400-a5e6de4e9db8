// @ts-nocheck
import { NextResponse } from 'next/server';

export async function POST() {
  const res = NextResponse.json({ success: true });

  const options = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax' as const,
    domain: '.futuredecade.com',
    path: '/',
    maxAge: 0,
  };

  res.cookies.set('fd_auth_token', '', options);
  res.cookies.set('fd_refresh_token', '', options);

  return res;
} 