import { ImageResponse } from 'next/og';

export const runtime = 'edge';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const categories = searchParams.get('categories') || '0';
    const posts = searchParams.get('posts') || '0';
    
    return new ImageResponse(
      (
        <div
          style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            position: 'relative',
          }}
        >
          {/* 背景装饰 */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%)',
            }}
          />
          
          {/* 主要内容 */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              textAlign: 'center',
              zIndex: 1,
            }}
          >
            {/* 标题 */}
            <div 
              style={{ 
                fontSize: 72, 
                fontWeight: 'bold', 
                marginBottom: 24,
                textShadow: '0 4px 8px rgba(0,0,0,0.3)',
              }}
            >
              分类索引
            </div>
            
            {/* 副标题 */}
            <div 
              style={{ 
                fontSize: 36, 
                opacity: 0.9, 
                marginBottom: 48,
                textShadow: '0 2px 4px rgba(0,0,0,0.3)',
              }}
            >
              探索所有文章分类 - Future Decade
            </div>
            
            {/* 统计数据 */}
            <div 
              style={{ 
                display: 'flex', 
                gap: 80,
                alignItems: 'center',
              }}
            >
              <div style={{ textAlign: 'center' }}>
                <div 
                  style={{ 
                    fontSize: 64, 
                    fontWeight: 'bold',
                    textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                  }}
                >
                  {categories}
                </div>
                <div 
                  style={{ 
                    fontSize: 28, 
                    opacity: 0.8,
                    marginTop: 8,
                  }}
                >
                  个分类
                </div>
              </div>
              
              <div 
                style={{
                  width: 2,
                  height: 80,
                  background: 'rgba(255,255,255,0.3)',
                }}
              />
              
              <div style={{ textAlign: 'center' }}>
                <div 
                  style={{ 
                    fontSize: 64, 
                    fontWeight: 'bold',
                    textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                  }}
                >
                  {posts}
                </div>
                <div 
                  style={{ 
                    fontSize: 28, 
                    opacity: 0.8,
                    marginTop: 8,
                  }}
                >
                  篇文章
                </div>
              </div>
            </div>
          </div>
          
          {/* 底部品牌标识 */}
          <div
            style={{
              position: 'absolute',
              bottom: 40,
              right: 40,
              fontSize: 24,
              opacity: 0.7,
              display: 'flex',
              alignItems: 'center',
              gap: 12,
            }}
          >
            <div
              style={{
                width: 32,
                height: 32,
                background: 'rgba(255,255,255,0.2)',
                borderRadius: 8,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: 18,
                fontWeight: 'bold',
              }}
            >
              FD
            </div>
            Future Decade
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
      }
    );
  } catch (e: any) {
    console.log(`Failed to generate OG image: ${e.message}`);
    return new Response(`Failed to generate the image`, {
      status: 500,
    });
  }
}
