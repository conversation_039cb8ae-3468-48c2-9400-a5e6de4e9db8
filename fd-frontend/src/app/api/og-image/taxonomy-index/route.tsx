import { ImageResponse } from 'next/og';
import { NextRequest } from 'next/server';

export const runtime = 'edge';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const taxonomy = searchParams.get('taxonomy') || 'taxonomy';
    const terms = searchParams.get('terms') || '0';
    const posts = searchParams.get('posts') || '0';

    // 获取分类法的中文名称
    const getTaxonomyLabel = (taxonomyName: string) => {
      const labels: { [key: string]: string } = {
        'company': '公司',
        'region': '区域',
        'industry': '行业',
        'category': '分类',
        'tag': '标签'
      };
      return labels[taxonomyName] || taxonomyName;
    };

    const taxonomyLabel = getTaxonomyLabel(taxonomy);

    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#1e40af',
            backgroundImage: 'linear-gradient(45deg, #1e40af 0%, #7c3aed 100%)',
            fontSize: 32,
            fontWeight: 600,
          }}
        >
          {/* 背景装饰 */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: 'radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%)',
            }}
          />
          
          {/* 主要内容 */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              textAlign: 'center',
              color: 'white',
              zIndex: 1,
            }}
          >
            {/* 标题 */}
            <div
              style={{
                fontSize: 64,
                fontWeight: 'bold',
                marginBottom: 20,
                textShadow: '0 4px 8px rgba(0,0,0,0.3)',
              }}
            >
              {taxonomyLabel}索引
            </div>
            
            {/* 副标题 */}
            <div
              style={{
                fontSize: 28,
                opacity: 0.9,
                marginBottom: 40,
              }}
            >
              Future Decade · 探索未来趋势
            </div>
            
            {/* 统计信息 */}
            <div
              style={{
                display: 'flex',
                gap: 40,
                fontSize: 24,
              }}
            >
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  padding: '20px 30px',
                  borderRadius: 16,
                  backdropFilter: 'blur(10px)',
                }}
              >
                <div style={{ fontSize: 36, fontWeight: 'bold' }}>{terms}</div>
                <div style={{ fontSize: 18, opacity: 0.8 }}>个{taxonomyLabel}</div>
              </div>
              
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  padding: '20px 30px',
                  borderRadius: 16,
                  backdropFilter: 'blur(10px)',
                }}
              >
                <div style={{ fontSize: 36, fontWeight: 'bold' }}>{posts}</div>
                <div style={{ fontSize: 18, opacity: 0.8 }}>篇文章</div>
              </div>
            </div>
          </div>
          
          {/* 底部装饰 */}
          <div
            style={{
              position: 'absolute',
              bottom: 30,
              right: 40,
              fontSize: 16,
              opacity: 0.7,
              color: 'white',
            }}
          >
            www.futuredecade.com
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
      }
    );
  } catch (error) {
    console.error('Error generating OG image:', error);
    
    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#1e40af',
            color: 'white',
            fontSize: 32,
            fontWeight: 600,
          }}
        >
          <div>分类法索引</div>
          <div style={{ fontSize: 24, marginTop: 20, opacity: 0.8 }}>
            Future Decade
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
      }
    );
  }
}
