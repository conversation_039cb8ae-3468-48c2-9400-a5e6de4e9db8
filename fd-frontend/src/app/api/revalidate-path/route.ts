import { NextRequest, NextResponse } from 'next/server';
import { revalidatePath } from 'next/cache';

/**
 * On-demand revalidate for a specific path (route).
 * Expects raw text body containing the path, e.g. "/company" or "/taxonomy/company".
 * Uses the same REVALIDATE_SECRET header as the tag revalidate route.
 */
export async function POST(request: NextRequest) {
  const secret = request.headers.get('x-revalidate-secret');
  const path = await request.text();

  console.log(`[Revalidate Path API] Received request for path: ${path}`);

  if (secret !== process.env.REVALIDATE_SECRET) {
    console.log('[Revalidate Path API] Invalid secret');
    return NextResponse.json({ message: 'Invalid secret' }, { status: 401 });
  }

  if (!path || !path.startsWith('/')) {
    return NextResponse.json({ message: 'Path is required and must start with /' }, { status: 400 });
  }

  try {
    revalidatePath(path);
    console.log(`[Revalidate Path API] Successfully revalidated path: ${path}`);
    return NextResponse.json({ revalidated: true, path, now: Date.now() });
  } catch (error: any) {
    console.error(`[Revalidate Path API] Error revalidating path ${path}:`, error);
    return NextResponse.json({ message: 'Error revalidating', error: error.message }, { status: 500 });
  }
} 