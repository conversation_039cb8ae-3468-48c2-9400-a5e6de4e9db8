import { NextRequest, NextResponse } from 'next/server';
import { revalidateTag } from 'next/cache';

/**
 * 按需重新验证 API 路由
 * 通过 tag 来精确清除 Next.js 的数据缓存。
 *
 * @param {NextRequest} request
 * @returns {NextResponse}
 */
export async function POST(request: NextRequest) {
  const secret = request.headers.get('x-revalidate-secret');
  const tag = await request.text();

  console.log(`[Revalidate API] Received request for tag: ${tag}`);

  // 1. 验证安全密钥
  if (secret !== process.env.REVALIDATE_SECRET) {
    console.log(`[Revalidate API] Invalid secret provided`);
    return NextResponse.json({ message: 'Invalid secret' }, { status: 401 });
  }

  // 2. 验证 tag 是否存在
  if (!tag) {
    console.log(`[Revalidate API] No tag provided`);
    return NextResponse.json({ message: 'Tag is required' }, { status: 400 });
  }

  // 3. 执行重新验证
  try {
    console.log(`[Revalidate API] Revalidating tag: ${tag}`);
    revalidateTag(tag);
    console.log(`[Revalidate API] Successfully revalidated tag: ${tag}`);
    return NextResponse.json({ revalidated: true, tag: tag, now: Date.now() });
  } catch (error: any) {
    console.error(`[Revalidate API] Error revalidating tag ${tag}:`, error);
    return NextResponse.json({ message: 'Error revalidating', error: error.message }, { status: 500 });
  }
}