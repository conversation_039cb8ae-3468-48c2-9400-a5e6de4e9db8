import React from 'react';
import MainLayout from '@/components/layouts/MainLayout';

// 示例页面 - 展示新的菜单系统使用方式
export default function ExamplePage() {
  return (
    <MainLayout>
      <div className="container mx-auto py-8 px-4">
        <h1 className="text-3xl font-bold mb-6">菜单系统示例页面</h1>
        
        <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6">
          <h2 className="text-lg font-semibold text-green-800 mb-2">✅ 菜单已全局优化</h2>
          <p className="text-green-700">
            菜单数据已在根布局中预获取，所有页面都可以直接使用MainLayout，
            菜单会立即显示，无需等待网络请求。
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-4">🚀 性能优势</h3>
            <ul className="space-y-2 text-gray-700">
              <li>• 服务端预获取菜单数据</li>
              <li>• ISR缓存策略 (10分钟)</li>
              <li>• Context全局共享</li>
              <li>• 零客户端延迟</li>
              <li>• 智能降级机制</li>
            </ul>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-xl font-semibold mb-4">📝 使用方式</h3>
            <div className="bg-gray-100 p-4 rounded text-sm">
              <pre>{`// 任何页面组件
export default function YourPage() {
  return (
    <MainLayout>
      <div>页面内容</div>
    </MainLayout>
  );
}`}</pre>
            </div>
          </div>
        </div>

        <div className="mt-8 bg-blue-50 p-6 rounded-lg">
          <h3 className="text-xl font-semibold mb-4 text-blue-800">📊 与Category页面性能对比</h3>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">对比项</th>
                  <th className="text-left py-2">Category页面</th>
                  <th className="text-left py-2">菜单组件</th>
                  <th className="text-left py-2">状态</th>
                </tr>
              </thead>
              <tbody className="text-gray-700">
                <tr className="border-b">
                  <td className="py-2">数据获取</td>
                  <td>服务端预获取</td>
                  <td>服务端预获取</td>
                  <td className="text-green-600">✅ 相同</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2">缓存策略</td>
                  <td>ISR 10分钟</td>
                  <td>ISR 10分钟</td>
                  <td className="text-green-600">✅ 相同</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2">首屏显示</td>
                  <td>立即显示</td>
                  <td>立即显示</td>
                  <td className="text-green-600">✅ 相同</td>
                </tr>
                <tr>
                  <td className="py-2">页面切换</td>
                  <td>缓存命中</td>
                  <td>Context共享</td>
                  <td className="text-green-600">✅ 更优</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
