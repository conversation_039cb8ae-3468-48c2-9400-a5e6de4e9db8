import React from 'react';
import { Metadata } from 'next';
import { getRoutePrefixes } from '@/lib/api';
import HomeClientPage from '@/components/pages/HomeClientPage';

// GraphQL端点
const GRAPHQL_ENDPOINT = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'https://admin.futuredecade.com/graphql';

// 定义SEO数据类型
interface HomeSeoData {
  aiSeoTitle?: string;
  aiSeoDescription?: string;
  aiSeoJsonLd?: string;
  enabled?: boolean;
  lastUpdated?: string;
}

// 定义分类类型
interface Category {
  id: string;
  name: string;
  slug: string;
}

/**
 * 获取首页SEO数据
 */
async function fetchHomeSeoData(): Promise<HomeSeoData | null> {
  try {
    const query = `
      query GetHomepageSeoData {
        homepageSeoData {
          aiSeoTitle
          aiSeoDescription
          aiSeoJsonLd
          enabled
          lastUpdated
        }
      }
    `;

    const response = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query }),
      next: {
        revalidate: 600, // ISR: 10分钟
        tags: ['homepage-seo'] // 缓存标签
      },
    } as RequestInit & { next: { revalidate: number; tags: string[] } });

    const json = await response.json();
    if (json.errors) {
      console.error('GraphQL errors:', json.errors);
      return null;
    }

    return json.data?.homepageSeoData || null;
  } catch (error) {
    console.error('Failed to fetch home SEO data:', error);
    return null;
  }
}

/**
 * 清理和解析JSON-LD数据
 */
function cleanAndParseJsonLd(rawJsonLd: string): any | null {
  if (!rawJsonLd) return null;

  try {
    let cleanJsonLd = rawJsonLd;

    // 移除markdown代码块标记
    cleanJsonLd = cleanJsonLd.replace(/```json\s*/, '').replace(/```\s*$/, '');

    // 处理转义字符
    cleanJsonLd = cleanJsonLd.replace(/\\\\\\"/g, '"'); // 处理三重转义
    cleanJsonLd = cleanJsonLd.replace(/\\"/g, '"');     // 处理双重转义
    cleanJsonLd = cleanJsonLd.replace(/\\\\/g, '\\');   // 处理反斜杠转义

    // 移除换行符和回车符
    cleanJsonLd = cleanJsonLd.replace(/\r\n/g, '').replace(/\r/g, '').replace(/\n/g, '');

    // 尝试解析JSON
    const jsonLd = JSON.parse(cleanJsonLd);

    // 更新URL为正确的前端URL
    if (jsonLd.url) {
      jsonLd.url = jsonLd.url.replace('admin.futuredecade.com', 'www.futuredecade.com');
      // 确保首页URL正确
      if (jsonLd.url.endsWith('/')) {
        jsonLd.url = 'https://www.futuredecade.com/';
      }
    }

    console.log('Successfully parsed JSON-LD for homepage:', jsonLd);
    return jsonLd;
  } catch (error) {
    console.error('Failed to parse JSON-LD for homepage:', error);
    return null;
  }
}

/**
 * 生成首页SEO元数据
 */
export async function generateMetadata(): Promise<Metadata> {
  const seoData = await fetchHomeSeoData();

  const metaTitle = seoData?.aiSeoTitle || '未来学人 | 全球视野，洞见未来趋势';
  const metaDescription = seoData?.aiSeoDescription || '未来学人，深度解读科技、创投、商业、汽车、AI等领域前沿趋势。全球视野，助您洞察未来。';
  const canonicalUrl = 'https://www.futuredecade.com/';

  return {
    title: metaTitle,
    description: metaDescription,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      url: canonicalUrl,
      siteName: '未来学人',
      type: 'website',
      locale: 'zh_CN',
      images: [
        {
          url: 'https://www.futuredecade.com/images/default-og-image.jpg',
          width: 1200,
          height: 630,
          alt: metaTitle,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: ['https://www.futuredecade.com/images/default-og-image.jpg'],
      site: '@FutureDecade',
      creator: '@FutureDecade',
    },
  };
}

// 设置ISR模式，10分钟重新验证
export const revalidate = 600;

/**
 * 首页服务端组件
 */
export default async function HomePage() {
  // 并行获取SEO数据和路由前缀
  const [seoData, routePrefixes] = await Promise.all([
    fetchHomeSeoData(),
    getRoutePrefixes()
  ]);

  // 准备JSON-LD结构化数据
  let jsonLd = null;
  if (seoData?.aiSeoJsonLd) {
    jsonLd = cleanAndParseJsonLd(seoData.aiSeoJsonLd);
  }

  return (
    <>
      {jsonLd && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
      )}
      <HomeClientPage routePrefixes={routePrefixes} />
    </>
  );
}