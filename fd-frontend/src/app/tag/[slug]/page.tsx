import { notFound } from 'next/navigation';
import TagClientPage from '@/components/pages/TagClientPage';

// 标签查询 & 文章列表查询
const GRAPHQL_ENDPOINT = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql';

// 获取分页设置的查询
const GET_POSTS_PER_PAGE_QUERY = `
  query GetPostsPerPageSetting {
    postsPerPageSetting
  }
`;

// 默认每页文章数量
const DEFAULT_POSTS_PER_PAGE = 12;

// 标签页面数据类型
interface TagPageData {
  tag: {
    id: string;
    databaseId: number;
    name: string;
    slug: string;
    count: number;
    description?: string;
    aiSeoTitle?: string;
    aiSeoDescription?: string;
    aiSeoJsonLd?: string;
    bannerImageUrl?: string;
    bannerImage?: {
      sourceUrl: string;
      altText?: string;
      mediaDetails?: {
        width: number;
        height: number;
      };
    };
    posts?: {
      nodes: any[];
      pageInfo: {
        hasNextPage: boolean;
        endCursor: string;
      };
    };
  };
}

// GraphQL 响应类型
interface GraphQLResponse<T> {
  data?: T;
  errors?: Array<{ message: string }>;
}

// 获取分页设置
async function fetchPostsPerPageSetting(): Promise<number> {
  try {
    const res = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: GET_POSTS_PER_PAGE_QUERY }),
      next: { revalidate: 3600 }, // 缓存1小时
    } as RequestInit & { next: { revalidate: number } });

    const json = await res.json();
    return json.data?.postsPerPageSetting || DEFAULT_POSTS_PER_PAGE;
  } catch (error) {
    console.error('Failed to fetch posts per page setting:', error);
    return DEFAULT_POSTS_PER_PAGE;
  }
}

async function fetchTagPageData(slug: string, postsPerPage: number): Promise<TagPageData['tag'] | null> {
  const query = `
    query GetTagPage($slug: ID!, $first: Int) {
      tag(id: $slug, idType: SLUG) {
        id
        databaseId
        name
        slug
        count
        description
        aiSeoTitle
        aiSeoDescription
        aiSeoJsonLd
        bannerImageUrl
        bannerImage {
          sourceUrl
          altText
          mediaDetails {
            width
            height
          }
        }
        posts(first: $first) {
          nodes {
            id
            title
            date
            slug
            shortUuid
            excerpt
            __typename
            featuredImage {
              node {
                sourceUrl
                altText
              }
            }
            author {
              node {
                name
                slug
                avatar {
                  url
                }
              }
            }
            categories {
              nodes {
                id
                name
                slug
              }
            }
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
    }
  `;

  const res = await fetch(GRAPHQL_ENDPOINT, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ query, variables: { slug, first: postsPerPage } }),
    next: {
      revalidate: 600, // ISR 10分钟
      tags: [`tag:${slug}`] // 添加缓存标签，用于按需重新验证
    },
  } as RequestInit & { next: { revalidate: number; tags: string[] } });

  const json: GraphQLResponse<TagPageData> = await res.json();
  if (json.errors) {
    console.error('GraphQL errors:', json.errors);
  }

  const tag = json.data?.tag || null;
  if (tag) {
    console.log('[TagPage] fetchTagPageData result:', {
      tagName: tag.name,
      tagCount: tag.count,
      initialPostsCount: tag.posts?.nodes.length,
      initialPageInfo: tag.posts?.pageInfo
    });
  }

  return tag;
}

export async function generateMetadata({ params }: { params: { slug: string } }) {
  const postsPerPage = await fetchPostsPerPageSetting();
  const tag = await fetchTagPageData(params.slug, postsPerPage);
  if (!tag) {
    return {
      title: '标签未找到 - Future Decade',
      description: 'Future Decade - AI驱动的新型科技媒体',
    };
  }

  const metaTitle = tag.aiSeoTitle || `#${tag.name}`;
  const metaDescription = tag.aiSeoDescription || `${tag.name}标签下的相关文章 - Future Decade`;
  const canonicalUrl = `https://www.futuredecade.com/tag/${tag.slug}`;

  // 获取标签的第一篇文章作为默认图片
  const defaultImage = tag.posts?.nodes?.[0]?.featuredImage?.node?.sourceUrl ||
                      'https://www.futuredecade.com/images/default-og-image.jpg';

  return {
    title: `${metaTitle} - Future Decade`,
    description: metaDescription,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      url: canonicalUrl,
      siteName: 'Future Decade',
      type: 'website',
      images: [
        {
          url: defaultImage,
          width: 1200,
          height: 630,
          alt: metaTitle,
        },
      ],
      locale: 'zh_CN',
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: [defaultImage],
      site: '@FutureDecade',
      creator: '@FutureDecade',
    },
  };
}

export default async function TagPage({ params }: { params: { slug: string } }) {
  const postsPerPage = await fetchPostsPerPageSetting();
  const tag = await fetchTagPageData(params.slug, postsPerPage);

  if (!tag) {
    return notFound();
  }

  const initialPosts = tag.posts?.nodes || [];
  const initialPageInfo = tag.posts?.pageInfo || null;

  console.log('[TagPage] Server component data:', {
    tagName: tag.name,
    tagCount: tag.count,
    postsPerPage,
    initialPostsCount: initialPosts.length,
    initialPageInfo
  });

  // 准备JSON-LD结构化数据
  let jsonLd = null;
  if (tag.aiSeoJsonLd) {
    try {
      jsonLd = JSON.parse(tag.aiSeoJsonLd);
      // 更新URL为正确的前端URL
      if (jsonLd.url) {
        jsonLd.url = `https://www.futuredecade.com/tag/${tag.slug}`;
      }
    } catch (error) {
      console.error('Failed to parse JSON-LD:', error);
    }
  }

  return (
    <>
      {jsonLd && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
      )}
      <TagClientPage
        initialTag={tag as any}
        initialPosts={initialPosts as any}
        initialPageInfo={initialPageInfo as any}
      />
    </>
  );
}