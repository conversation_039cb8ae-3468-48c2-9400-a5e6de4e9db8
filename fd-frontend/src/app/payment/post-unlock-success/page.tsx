'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import MainLayout from '@/components/layouts/MainLayout';
import PageTitle from '@/components/ui/PageTitle';
import Button from '@/components/ui/Button';

export default function PostUnlockSuccessPage() {
  const router = useRouter();
  const { refreshUser } = useAuth();
  const searchParams = useSearchParams();
  const returnUrl = searchParams?.get('returnUrl');

  // 倒计时状态
  const [countdown, setCountdown] = useState(5); // 5秒倒计时
  const [isCountdownActive, setIsCountdownActive] = useState(!!returnUrl); // 只有有返回URL时才启动倒计时

  // 支付成功后刷新用户信息，确保解锁状态更新
  useEffect(() => {
    refreshUser();
  }, [refreshUser]);

  // 倒计时逻辑
  useEffect(() => {
    if (!isCountdownActive || !returnUrl) return;

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          // 倒计时结束，自动跳转
          router.push(returnUrl);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isCountdownActive, returnUrl, router]);

  // 取消倒计时
  const cancelCountdown = () => {
    setIsCountdownActive(false);
  };

  // 立即返回
  const returnNow = () => {
    if (returnUrl) {
      router.push(returnUrl);
    }
  };
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <PageTitle title="解锁成功" description="您已成功解锁文章内容" />
        
        <div className="max-w-2xl mx-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
            {/* 成功图标 */}
            <div className="w-16 h-16 mx-auto mb-6 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            
            {/* 成功消息 */}
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              🎉 解锁成功！
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mb-8">
              您已成功解锁文章内容，现在可以阅读完整文章了。
            </p>
            
            {/* 倒计时显示 */}
            {returnUrl && isCountdownActive && (
              <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-700">
                <p className="text-blue-800 dark:text-blue-200 text-sm">
                  <span className="inline-flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    将在 
                    <span className="mx-1 font-semibold text-blue-600 dark:text-blue-400">
                      {countdown}
                    </span>
                    秒后自动返回文章页
                  </span>
                </p>
              </div>
            )}
            
            {/* 操作按钮 */}
            <div className="space-y-4">
              {returnUrl ? (
                <>
                  {/* 主要操作区域 */}
                  <div className="flex flex-col sm:flex-row justify-center gap-3">
                    <Button
                      variant="primary"
                      onClick={returnNow}
                      className="flex items-center justify-center"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                      </svg>
                      立即返回文章
                    </Button>
                    
                    {isCountdownActive && (
                      <Button
                        variant="outline"
                        onClick={cancelCountdown}
                        className="flex items-center justify-center"
                      >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        取消自动跳转
                      </Button>
                    )}
                  </div>

                  {/* 次要操作区域 */}
                  <div className="flex flex-col sm:flex-row justify-center gap-3 pt-2 border-t border-gray-200 dark:border-gray-700">
                    <Button
                      variant="ghost"
                      onClick={() => router.push('/auth/profile')}
                      className="text-sm"
                    >
                      查看个人资料
                    </Button>

                    <Button
                      variant="ghost"
                      onClick={() => router.push('/')}
                      className="text-sm"
                    >
                      返回首页
                    </Button>
                  </div>
                </>
              ) : (
                /* 没有返回URL时的默认按钮 */
                <div className="flex flex-col sm:flex-row justify-center gap-4">
                  <Button
                    variant="primary"
                    onClick={() => router.push('/auth/profile')}
                    className="flex items-center justify-center"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    查看个人资料
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => router.push('/')}
                    className="flex items-center justify-center"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    返回首页
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
