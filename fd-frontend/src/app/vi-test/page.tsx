'use client';

import { useQuery } from '@apollo/client';
import { GET_VI_SETTINGS } from '@/lib/graphql/queries';
import { useState } from 'react';
import Link from 'next/link';

export default function VITestPage() {
  const { loading, error, data } = useQuery(GET_VI_SETTINGS);
  const [activeTab, setActiveTab] = useState('basic');

  if (loading) return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">加载VI设置中...</h1>
      <div className="animate-pulse h-96 bg-gray-200 rounded"></div>
    </div>
  );

  if (error) return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">加载VI设置出错</h1>
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <p className="font-bold">错误</p>
        <p>{error.message}</p>
      </div>
      <div className="mt-4">
        <p>确保以下事项：</p>
        <ul className="list-disc ml-8">
          <li>WordPress站点可访问</li>
          <li>WPGraphQL插件已激活</li>
          <li>VI设置已正确注册到GraphQL API</li>
        </ul>
      </div>
    </div>
  );

  const viSettings = data?.viSettings || {};

  const tabs = [
    { id: 'basic', label: '基础设置' },
    { id: 'colors', label: '颜色设置' },
    { id: 'typography', label: '排版设置' },
    { id: 'tokens', label: '设计令牌' },
  ];

  return (
    <div className="container mx-auto p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">VI设置测试页面</h1>
        <Link href="/" className="text-blue-500 hover:underline">返回首页</Link>
      </div>
      
      <div className="mb-6">
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          <p className="font-bold">成功获取VI设置</p>
          <p>VI设置已成功从GraphQL API获取</p>
        </div>
      </div>

      {/* 导航标签 */}
      <div className="flex border-b mb-6">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`py-2 px-4 font-medium ${activeTab === tab.id
              ? 'border-b-2 border-blue-500 text-blue-500'
              : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* 基础设置 */}
      {activeTab === 'basic' && (
        <div>
          <h2 className="text-2xl font-bold mb-4">基础设置</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div className="border rounded p-4">
              <h3 className="text-lg font-semibold mb-2">Logo</h3>
              {viSettings.logoUrl ? (
                <div className="bg-gray-100 p-4 rounded flex justify-center">
                  <img src={viSettings.logoUrl} alt="网站Logo" className="max-h-24" />
                </div>
              ) : (
                <p className="text-gray-500">未设置Logo</p>
              )}
              <p className="mt-2 text-sm text-gray-600 break-all">URL: {viSettings.logoUrl || '无'}</p>
            </div>
            
            <div className="border rounded p-4">
              <h3 className="text-lg font-semibold mb-2">暗色模式Logo</h3>
              {viSettings.logoDarkUrl ? (
                <div className="bg-gray-800 p-4 rounded flex justify-center">
                  <img src={viSettings.logoDarkUrl} alt="暗色模式Logo" className="max-h-24" />
                </div>
              ) : (
                <p className="text-gray-500">未设置暗色模式Logo</p>
              )}
              <p className="mt-2 text-sm text-gray-600 break-all">URL: {viSettings.logoDarkUrl || '无'}</p>
            </div>
          </div>
          
          <div className="border rounded p-4">
            <h3 className="text-lg font-semibold mb-2">网站图标</h3>
            {viSettings.faviconUrl ? (
              <div className="flex items-center">
                <div className="bg-gray-100 p-4 rounded flex justify-center mr-4">
                  <img src={viSettings.faviconUrl} alt="网站图标" className="w-8 h-8" />
                </div>
                <p className="text-sm text-gray-600 break-all">URL: {viSettings.faviconUrl}</p>
              </div>
            ) : (
              <p className="text-gray-500">未设置网站图标</p>
            )}
          </div>
        </div>
      )}

      {/* 颜色设置 */}
      {activeTab === 'colors' && (
        <div>
          <h2 className="text-2xl font-bold mb-4">颜色设置</h2>
          
          <div className="flex flex-wrap gap-4 mb-6">
            <ColorSwatch label="主要颜色" color={viSettings.primaryColor} />
            <ColorSwatch label="次要颜色" color={viSettings.secondaryColor} />
            <ColorSwatch label="背景颜色" color={viSettings.backgroundColor} />
            <ColorSwatch label="提示色" color={viSettings.amberColor} />
            <ColorSwatch label="强调色" color={viSettings.roseColor} />
            <ColorSwatch label="成功色" color={viSettings.successColor} />
            <ColorSwatch label="错误色" color={viSettings.errorColor} />
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">暗色模式</h3>
            <div className="flex items-center gap-2 mb-4">
              <span>状态：</span>
              <span className={`px-2 py-1 rounded text-white ${viSettings.darkModeEnabled ? 'bg-green-500' : 'bg-red-500'}`}>
                {viSettings.darkModeEnabled ? '已启用' : '已禁用'}
              </span>
            </div>
            
            {viSettings.darkModeEnabled && (
              <div className="flex gap-4">
                <ColorSwatch label="暗色背景色" color={viSettings.darkBackgroundColor} />
              </div>
            )}
          </div>
        </div>
      )}

      {/* 排版设置 */}
      {activeTab === 'typography' && (
        <div>
          <h2 className="text-2xl font-bold mb-4">排版设置</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div className="border rounded p-4">
              <h3 className="text-lg font-semibold mb-2">字体设置</h3>
              <table className="w-full">
                <tbody>
                  <tr className="border-b">
                    <td className="py-2 font-medium">标题字体</td>
                    <td className="py-2" style={{ fontFamily: viSettings.headingFont || 'inherit' }}>
                      {viSettings.headingFont || '默认'}
                      <span className="ml-2 text-sm text-gray-500">
                        (示例文本)
                      </span>
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-2 font-medium">正文字体</td>
                    <td className="py-2" style={{ fontFamily: viSettings.bodyFont || 'inherit' }}>
                      {viSettings.bodyFont || '默认'}
                      <span className="ml-2 text-sm text-gray-500">
                        (示例文本)
                      </span>
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-2 font-medium">基础字体大小</td>
                    <td className="py-2">{viSettings.baseFontSize || '默认'}</td>
                  </tr>
                  <tr>
                    <td className="py-2 font-medium">行高</td>
                    <td className="py-2">{viSettings.lineHeight || '默认'}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <div className="border rounded p-4">
              <h3 className="text-lg font-semibold mb-2">间距设置</h3>
              <table className="w-full">
                <tbody>
                  <tr>
                    <td className="py-2 font-medium">基础间距单位</td>
                    <td className="py-2">{viSettings.spacingUnit || '默认'}</td>
                  </tr>
                </tbody>
              </table>
              
              <div className="mt-4">
                <h4 className="font-medium mb-2">间距示例：</h4>
                <div className="flex flex-col gap-2">
                  <div className="h-8 bg-gray-200 w-full"></div>
                  <div className="h-8 bg-gray-300 w-4/5"></div>
                  <div className="h-8 bg-gray-400 w-3/5"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 设计令牌 */}
      {activeTab === 'tokens' && (
        <div>
          <h2 className="text-2xl font-bold mb-4">UI设计令牌</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div className="border rounded p-4">
              <h3 className="text-lg font-semibold mb-2">圆角设置</h3>
              <table className="w-full mb-4">
                <tbody>
                  <tr className="border-b">
                    <td className="py-2 font-medium">小圆角</td>
                    <td className="py-2">{viSettings.radiusSmall || '默认'}</td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-2 font-medium">中圆角</td>
                    <td className="py-2">{viSettings.radiusMedium || '默认'}</td>
                  </tr>
                  <tr>
                    <td className="py-2 font-medium">大圆角</td>
                    <td className="py-2">{viSettings.radiusLarge || '默认'}</td>
                  </tr>
                </tbody>
              </table>
              
              <div className="flex flex-col gap-4">
                <div 
                  className="h-12 bg-blue-500 w-full flex items-center justify-center text-white"
                  style={{ borderRadius: viSettings.radiusSmall || '4px' }}
                >
                  小圆角: {viSettings.radiusSmall || '4px'}
                </div>
                <div 
                  className="h-12 bg-blue-500 w-full flex items-center justify-center text-white"
                  style={{ borderRadius: viSettings.radiusMedium || '8px' }}
                >
                  中圆角: {viSettings.radiusMedium || '8px'}
                </div>
                <div 
                  className="h-12 bg-blue-500 w-full flex items-center justify-center text-white"
                  style={{ borderRadius: viSettings.radiusLarge || '16px' }}
                >
                  大圆角: {viSettings.radiusLarge || '16px'}
                </div>
              </div>
            </div>
            
            <div className="border rounded p-4">
              <h3 className="text-lg font-semibold mb-2">阴影设置</h3>
              <table className="w-full mb-4">
                <tbody>
                  <tr className="border-b">
                    <td className="py-2 font-medium">小阴影</td>
                    <td className="py-2 text-sm">{viSettings.shadowSmall || '默认'}</td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-2 font-medium">中阴影</td>
                    <td className="py-2 text-sm">{viSettings.shadowMedium || '默认'}</td>
                  </tr>
                  <tr>
                    <td className="py-2 font-medium">大阴影</td>
                    <td className="py-2 text-sm">{viSettings.shadowLarge || '默认'}</td>
                  </tr>
                </tbody>
              </table>
              
              <div className="flex flex-col gap-4">
                <div 
                  className="h-12 bg-white w-full flex items-center justify-center"
                  style={{ boxShadow: viSettings.shadowSmall || '0 2px 4px rgba(0,0,0,0.05)' }}
                >
                  小阴影
                </div>
                <div 
                  className="h-12 bg-white w-full flex items-center justify-center"
                  style={{ boxShadow: viSettings.shadowMedium || '0 4px 8px rgba(0,0,0,0.1)' }}
                >
                  中阴影
                </div>
                <div 
                  className="h-12 bg-white w-full flex items-center justify-center"
                  style={{ boxShadow: viSettings.shadowLarge || '0 8px 16px rgba(0,0,0,0.15)' }}
                >
                  大阴影
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 原始数据查看 */}
      <div className="mt-8">
        <h2 className="text-xl font-bold mb-2">原始数据</h2>
        <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-96">
          {JSON.stringify(viSettings, null, 2)}
        </pre>
      </div>
    </div>
  );
}

// ColorSwatch组件的属性接口
interface ColorSwatchProps {
  label: string;
  color?: string;
}

function ColorSwatch({ label, color }: ColorSwatchProps) {
  return (
    <div className="border rounded p-3 w-36">
      <div 
        className="h-16 rounded mb-2" 
        style={{ backgroundColor: color || '#eee' }}
      ></div>
      <div className="text-sm font-medium">{label}</div>
      <div className="text-xs font-mono">{color || '未设置'}</div>
    </div>
  );
} 