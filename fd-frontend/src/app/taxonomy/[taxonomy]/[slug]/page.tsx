import { notFound } from 'next/navigation';
import TaxonomyTermClientPage from '@/components/pages/TaxonomyTermClientPage';

// 恢复ISR缓存，1小时重新验证
export const revalidate = 3600;

// 生成静态参数（可选，用于预渲染常见的分类法页面）
export async function generateStaticParams() {
  // 返回空数组，让Next.js在运行时生成页面
  // 这样可以支持所有可能的分类法和条目组合
  return [];
}

// GraphQL端点
const GRAPHQL_ENDPOINT = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql';

// 获取分页设置的查询
const GET_POSTS_PER_PAGE_QUERY = `
  query GetPostsPerPageSetting {
    postsPerPageSetting
  }
`;

// 默认每页文章数量
const DEFAULT_POSTS_PER_PAGE = 12;

// 自定义分类法术语页面数据类型
interface TaxonomyTermPageData {
  term: {
    __typename: string;
    id: string;
    databaseId: number;
    name: string;
    slug: string;
    description?: string;
    uri: string;
    aiSeoTitle?: string;
    aiSeoDescription?: string;
    aiSeoJsonLd?: string;
    bannerImageUrl?: string;
    bannerImage?: {
      sourceUrl: string;
      altText?: string;
      mediaDetails?: {
        width: number;
        height: number;
      };
    };
    count?: number;
    posts?: {
      nodes: any[];
      pageInfo: {
        hasNextPage: boolean;
        endCursor: string;
      };
    };
    children?: {
      nodes: any[];
    };
  };
  taxonomy: {
    id: string;
    name: string;
    label: string;
    description?: string;
    hierarchical: boolean;
    restBase: string;
  };
}

// GraphQL 响应类型
interface GraphQLResponse<T> {
  data?: T;
  errors?: Array<{ message: string }>;
}

// 获取分页设置
async function fetchPostsPerPageSetting(): Promise<number> {
  try {
    const res = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: GET_POSTS_PER_PAGE_QUERY }),
      next: { revalidate: 3600 }, // 缓存1小时
    } as RequestInit & { next: { revalidate: number } });

    const json = await res.json();
    return json.data?.postsPerPageSetting || DEFAULT_POSTS_PER_PAGE;
  } catch (error) {
    console.error('Failed to fetch posts per page setting:', error);
    return DEFAULT_POSTS_PER_PAGE;
  }
}

// 获取分类法信息
async function fetchTaxonomyInfo(taxonomyName: string): Promise<TaxonomyTermPageData['taxonomy'] | null> {
  const query = `
    query GetTaxonomy($name: ID!) {
      taxonomy(id: $name, idType: NAME) {
        id
        name
        label
        description
        hierarchical
        restBase
      }
    }
  `;

  try {
    const res = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query, variables: { name: taxonomyName } }),
      next: { revalidate: 3600 }, // 缓存1小时
    } as RequestInit & { next: { revalidate: number } });

    const json = await res.json();
    return json.data?.taxonomy || null;
  } catch (error) {
    console.error('Failed to fetch taxonomy info:', error);
    return null;
  }
}

// 获取自定义分类法术语信息（不包含文章）
async function fetchTaxonomyTermInfo(
  taxonomyName: string,
  termSlug: string
): Promise<TaxonomyTermPageData['term'] | null> {
  // 将分类法名称转换为大写（GraphQL枚举格式）
  const taxonomyEnum = taxonomyName.toUpperCase();

  const query = `
    query GetTaxonomyTermInfo($taxonomy: TaxonomyEnum!, $slug: [String]!) {
      terms(where: { taxonomies: [$taxonomy], slug: $slug }) {
        nodes {
          __typename
          id
          databaseId
          name
          slug
          description
          uri
          aiSeoTitle
          aiSeoDescription
          aiSeoJsonLd
          ... on Company {
            count
            bannerImageUrl
            bannerImage {
              sourceUrl
              altText
              mediaDetails {
                width
                height
              }
            }
          }
          ... on Region {
            count
            bannerImageUrl
            bannerImage {
              sourceUrl
              altText
              mediaDetails {
                width
                height
              }
            }
          }
          ... on Industry {
            count
            bannerImageUrl
            bannerImage {
              sourceUrl
              altText
              mediaDetails {
                width
                height
              }
            }
          }
          ... on Category {
            count
            bannerImageUrl
            bannerImage {
              sourceUrl
              altText
              mediaDetails {
                width
                height
              }
            }
            children {
              nodes {
                id
                name
                slug
                uri
              }
            }
          }
          ... on Tag {
            count
            bannerImageUrl
            bannerImage {
              sourceUrl
              altText
              mediaDetails {
                width
                height
              }
            }
          }
        }
      }
    }
  `;

  try {
    const res = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query,
        variables: {
          taxonomy: taxonomyEnum,
          slug: [termSlug]
        }
      }),
      next: {
        revalidate: 3600, // 缓存1小时
        tags: [`taxonomy-term:${taxonomyName}:${termSlug}`] // 条目页使用不同的缓存标签格式
      },
    } as RequestInit & { next: { revalidate: number; tags: string[] } });

    const json: GraphQLResponse<{ terms: { nodes: TaxonomyTermPageData['term'][] } }> = await res.json();
    if (json.errors) {
      console.error('GraphQL errors:', json.errors);
      return null;
    }

    return json.data?.terms?.nodes?.[0] || null;
  } catch (error) {
    console.error('Failed to fetch taxonomy term info:', error);
    return null;
  }
}

// 使用TaxQuery获取分类法术语下的文章
async function fetchTaxonomyTermPosts(
  taxonomyName: string,
  termSlug: string,
  postsPerPage: number
): Promise<{ nodes: any[]; pageInfo: { hasNextPage: boolean; endCursor: string } } | null> {
  // 将分类法名称转换为大写（GraphQL枚举格式）
  const taxonomyEnum = taxonomyName.toUpperCase();

  const query = `
    query GetPostsByTaxQuerySlug($taxonomy: TaxonomyEnum!, $slugs: [String!], $first: Int) {
      posts(
        first: $first,
        where: {
          taxQuery: {
            relation: AND,
            taxArray: [
              {
                taxonomy: $taxonomy,
                operator: IN,
                terms: $slugs,
                field: SLUG
              }
            ]
          }
        }
      ) {
        pageInfo {
          hasNextPage
          endCursor
        }
        nodes {
          id
          title
          date
          slug
          shortUuid
          excerpt
          __typename
          featuredImage {
            node {
              sourceUrl
              altText
            }
          }
          author {
            node {
              name
              slug
              avatar {
                url
              }
            }
          }
          categories {
            nodes {
              id
              name
              slug
            }
          }
        }
      }
    }
  `;

  try {
    const res = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query,
        variables: {
          taxonomy: taxonomyEnum,
          slugs: [termSlug],
          first: postsPerPage
        }
      }),
      next: {
        revalidate: 600, // ISR 10分钟
        tags: [`taxonomy-term:${taxonomyName}:${termSlug}`] // 条目页使用不同的缓存标签格式
      },
    } as RequestInit & { next: { revalidate: number; tags: string[] } });

    const json: GraphQLResponse<{ posts: { nodes: any[]; pageInfo: { hasNextPage: boolean; endCursor: string } } }> = await res.json();
    if (json.errors) {
      console.error('GraphQL errors:', json.errors);
      return null;
    }

    const posts = json.data?.posts || null;
    if (posts) {
      console.log('[TaxonomyTermPage] fetchTaxonomyTermPosts result:', {
        postsCount: posts.nodes.length,
        pageInfo: posts.pageInfo
      });
    }

    return posts;
  } catch (error) {
    console.error('Failed to fetch taxonomy term posts:', error);
    return null;
  }
}

export async function generateMetadata({ params }: { params: { taxonomy: string; slug: string } }) {
  const taxonomyInfo = await fetchTaxonomyInfo(params.taxonomy);
  const term = await fetchTaxonomyTermInfo(params.taxonomy, params.slug);

  if (!term || !taxonomyInfo) {
    return {
      title: '条目未找到 - Future Decade',
      description: 'Future Decade - AI驱动的新型科技媒体',
    };
  }

  const metaTitle = term.aiSeoTitle || `${term.name} - ${taxonomyInfo.label}`;
  const metaDescription = term.aiSeoDescription ||
    (term.description ? term.description.replace(/<[^>]*>/g, '').substring(0, 160) :
     `${term.name}相关内容 - Future Decade`);
  const canonicalUrl = `https://www.futuredecade.com/taxonomy/${params.taxonomy}/${term.slug}`;

  // 获取术语的第一篇文章作为默认图片（需要单独查询）
  const postsPerPage = await fetchPostsPerPageSetting();
  const posts = await fetchTaxonomyTermPosts(params.taxonomy, params.slug, 1); // 只获取第一篇文章用于图片
  const defaultImage = posts?.nodes?.[0]?.featuredImage?.node?.sourceUrl ||
                      'https://www.futuredecade.com/images/default-og-image.jpg';

  return {
    title: `${metaTitle} - Future Decade`,
    description: metaDescription,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      url: canonicalUrl,
      siteName: 'Future Decade',
      type: 'website',
      images: [
        {
          url: defaultImage,
          width: 1200,
          height: 630,
          alt: metaTitle,
        },
      ],
      locale: 'zh_CN',
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: [defaultImage],
      site: '@FutureDecade',
      creator: '@FutureDecade',
    },
  };
}

export default async function TaxonomyTermPage({ params }: { params: { taxonomy: string; slug: string } }) {
  console.log('[TaxonomyTermPage] Starting with params:', params);

  const postsPerPage = await fetchPostsPerPageSetting();
  const taxonomyInfo = await fetchTaxonomyInfo(params.taxonomy);
  const term = await fetchTaxonomyTermInfo(params.taxonomy, params.slug);
  const posts = await fetchTaxonomyTermPosts(params.taxonomy, params.slug, postsPerPage);

  console.log('[TaxonomyTermPage] Fetch results:', {
    taxonomyInfo: !!taxonomyInfo,
    term: !!term,
    posts: !!posts,
    taxonomyName: taxonomyInfo?.name,
    termName: term?.name
  });

  if (!term || !taxonomyInfo) {
    console.log('[TaxonomyTermPage] Missing data, returning 404');
    return notFound();
  }

  const initialPosts = posts?.nodes || [];
  const initialPageInfo = posts?.pageInfo || null;

  console.log('[TaxonomyTermPage] Server component data:', {
    taxonomyName: taxonomyInfo.name,
    termName: term.name,
    termCount: term.count,
    postsPerPage,
    initialPostsCount: initialPosts.length,
    initialPageInfo
  });

  // 准备JSON-LD结构化数据
  let jsonLd = null;
  if (term.aiSeoJsonLd) {
    try {
      jsonLd = JSON.parse(term.aiSeoJsonLd);
      // 更新URL为正确的前端URL
      if (jsonLd.url) {
        jsonLd.url = `https://www.futuredecade.com/taxonomy/${params.taxonomy}/${term.slug}`;
      }
    } catch (error) {
      console.error('Failed to parse JSON-LD:', error);
    }
  }

  return (
    <>
      {jsonLd && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
      )}
      <TaxonomyTermClientPage
        initialTaxonomy={taxonomyInfo as any}
        initialTerm={term as any}
        initialPosts={initialPosts as any}
        initialPageInfo={initialPageInfo as any}
      />
    </>
  );
}