import React from 'react';
import { notFound } from 'next/navigation';
import Script from 'next/script';
import { getRoutePrefixes } from '@/lib/route-prefixes';
import TaxonomyIndexClientPage from '@/components/pages/TaxonomyIndexClientPage';

// 恢复ISR缓存，1小时重新验证
export const revalidate = 3600;

// 共享的分类法条目接口定义
interface TaxonomyTerm {
  id: string;
  databaseId: number;
  name: string;
  slug: string;
  description?: string;
  count: number;
  bannerImageUrl?: string;
  bannerImage?: {
    sourceUrl: string;
    altText?: string;
    mediaDetails?: {
      width: number;
      height: number;
    };
  };
}

// SEO设置接口
interface SeoSettings {
  aiSeoTitle?: string;
  aiSeoDescription?: string;
  aiSeoJsonLd?: string;
  enabled?: boolean;
  lastUpdated?: string;
}

// 统计信息接口
interface Statistics {
  totalTerms: number;
  totalPosts: number;
  averagePostsPerTerm: number;
}

// 分类法信息接口
interface TaxonomyInfo {
  name: string;
  label: string;
  description?: string;
}

// 分类法索引页面数据接口
interface TaxonomyIndexData {
  seoSettings: SeoSettings;
  terms: TaxonomyTerm[];
  statistics: Statistics;
  taxonomy: TaxonomyInfo;
}

/**
 * 获取分类法索引页面数据
 */
async function fetchTaxonomyIndexData(taxonomy: string): Promise<TaxonomyIndexData> {
  const query = `
    query GetTaxonomyIndexPageData($taxonomy: String!) {
      taxonomyIndexPageData(taxonomy: $taxonomy) {
        seoSettings {
          aiSeoTitle
          aiSeoDescription
          aiSeoJsonLd
          enabled
          lastUpdated
        }
        terms {
          id
          databaseId
          name
          slug
          description
          count
          ... on Company {
            bannerImageUrl
            bannerImage {
              sourceUrl
              altText
              mediaDetails {
                width
                height
              }
            }
          }
          ... on Region {
            bannerImageUrl
            bannerImage {
              sourceUrl
              altText
              mediaDetails {
                width
                height
              }
            }
          }
          ... on Industry {
            bannerImageUrl
            bannerImage {
              sourceUrl
              altText
              mediaDetails {
                width
                height
              }
            }
          }
          ... on Category {
            bannerImageUrl
            bannerImage {
              sourceUrl
              altText
              mediaDetails {
                width
                height
              }
            }
          }
          ... on Tag {
            bannerImageUrl
            bannerImage {
              sourceUrl
              altText
              mediaDetails {
                width
                height
              }
            }
          }
        }
        statistics {
          totalTerms
          totalPosts
          averagePostsPerTerm
        }
        taxonomy {
          name
          label
          description
        }
      }
    }
  `;

  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query,
          variables: { taxonomy }
        }),
        next: {
          revalidate: 3600, // 恢复ISR缓存
          tags: [`taxonomy:${taxonomy}`] // 缓存标签，用于按需重新验证。修正格式以匹配后端。
        }
      }
    );

    const data = await response.json();

    if (data.errors) {
      console.error('GraphQL errors:', data.errors);
      throw new Error(`Failed to fetch taxonomy index data for ${taxonomy}`);
    }

    return data.data.taxonomyIndexPageData;
  } catch (error) {
    console.error(`Error fetching taxonomy index data for ${taxonomy}:`, error);
    throw error;
  }
}

/**
 * 生成SEO元数据
 */
export async function generateMetadata({ params }: { params: { taxonomy: string } }) {
  try {
    const { taxonomy } = params;
    const data = await fetchTaxonomyIndexData(taxonomy);
    const seo = data.seoSettings;

    const metaTitle = seo?.aiSeoTitle || `${data.taxonomy.label}索引 - 探索所有${data.taxonomy.label}`;
    const metaDescription = seo?.aiSeoDescription ||
      `浏览Future Decade的所有${data.statistics.totalTerms}个${data.taxonomy.label}，涵盖${data.statistics.totalPosts}篇优质内容。快速找到您关注的${data.taxonomy.label}。`;

    const canonicalUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com'}/taxonomy/${taxonomy}`;

    // 动态生成OG图片URL
    const ogImageUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com'}/api/og-image/taxonomy-index?taxonomy=${taxonomy}&terms=${data.statistics.totalTerms}&posts=${data.statistics.totalPosts}`;

    return {
      title: `${metaTitle} - Future Decade`,
      description: metaDescription,
      keywords: [
        data.taxonomy.label, `${data.taxonomy.label}索引`, `${data.taxonomy.label}分类`, 'Future Decade',
        '人工智能', '商业洞察', '技术趋势', '创新科技'
      ].join(', '),
      alternates: {
        canonical: canonicalUrl,
      },
      openGraph: {
        title: metaTitle,
        description: metaDescription,
        url: canonicalUrl,
        siteName: 'Future Decade',
        type: 'website',
        images: [
          {
            url: ogImageUrl,
            width: 1200,
            height: 630,
            alt: `${metaTitle} - ${data.statistics.totalTerms}个${data.taxonomy.label}，${data.statistics.totalPosts}篇文章`,
          }
        ],
        locale: 'zh_CN',
      },
      twitter: {
        card: 'summary_large_image',
        title: metaTitle,
        description: metaDescription,
        images: [ogImageUrl],
        site: '@FutureDecade',
        creator: '@FutureDecade',
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: '分类法索引 - Future Decade',
      description: '浏览Future Decade的分类法索引，找到您感兴趣的话题。'
    };
  }
}

/**
 * 分类法索引页面服务器组件
 */
export default async function TaxonomyIndexPage({ params }: { params: { taxonomy: string } }) {
  try {
    const { taxonomy } = params;

    // 并行获取数据
    const [data, routePrefixes] = await Promise.all([
      fetchTaxonomyIndexData(taxonomy),
      getRoutePrefixes()
    ]);

    // 生成JSON-LD结构化数据
    const generateJsonLd = () => {
      const baseJsonLd = {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": data.seoSettings?.aiSeoTitle || `${data.taxonomy.label}索引`,
        "description": data.seoSettings?.aiSeoDescription || `${data.taxonomy.label}索引页面`,
        "url": `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com'}/taxonomy/${taxonomy}`,
        "mainEntity": {
          "@type": "ItemList",
          "numberOfItems": data.statistics.totalTerms,
          "itemListElement": data.terms.map((term, index) => ({
            "@type": "ListItem",
            "position": index + 1,
            "item": {
              "@type": "Thing",
              "@id": `${process.env.NEXT_PUBLIC_SITE_URL}/taxonomy/${taxonomy}/${term.slug}`,
              "name": term.name,
              "description": term.description,
              "url": `${process.env.NEXT_PUBLIC_SITE_URL}/taxonomy/${taxonomy}/${term.slug}`,
              "additionalProperty": {
                "@type": "PropertyValue",
                "name": "articleCount",
                "value": term.count
              }
            }
          }))
        },
        "breadcrumb": {
          "@type": "BreadcrumbList",
          "itemListElement": [
            {
              "@type": "ListItem",
              "position": 1,
              "name": "首页",
              "item": process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com'
            },
            {
              "@type": "ListItem",
              "position": 2,
              "name": `${data.taxonomy.label}索引`,
              "item": `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com'}/taxonomy/${taxonomy}`
            }
          ]
        },
        "publisher": {
          "@type": "Organization",
          "name": "Future Decade",
          "url": process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com',
          "logo": {
            "@type": "ImageObject",
            "url": `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com'}/logo.png`
          }
        }
      };

      // 如果有AI生成的JSON-LD，合并使用
      if (data.seoSettings?.aiSeoJsonLd) {
        try {
          const aiJsonLd = JSON.parse(data.seoSettings.aiSeoJsonLd);
          return { ...baseJsonLd, ...aiJsonLd };
        } catch (error) {
          console.error('Failed to parse AI JSON-LD:', error);
        }
      }

      return baseJsonLd;
    };

    return (
      <>
        {/* 注入JSON-LD结构化数据 */}
        <Script
          id={`taxonomy-${taxonomy}-jsonld`}
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(generateJsonLd()) }}
        />

        {/* 客户端组件 */}
        <TaxonomyIndexClientPage
          initialData={data}
          routePrefixes={routePrefixes}
          taxonomy={taxonomy}
        />
      </>
    );
  } catch (error) {
    console.error(`Error rendering taxonomy index page for ${params.taxonomy}:`, error);
    return notFound();
  }
}