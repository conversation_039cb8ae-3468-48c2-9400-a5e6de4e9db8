'use client';

import { useState } from 'react';
import {
  useNotifications,
  useUnreadNotificationCount,
  useMarkNotificationRead,
  useMarkAllNotificationsRead,
  useDeleteNotification
} from '../../hooks/useNotifications';
import { NotificationStatus, NotificationType } from '../../types/notification';

/**
 * 通知功能测试页面
 */
export default function NotificationTestPage() {
  // 状态
  const [selectedId, setSelectedId] = useState<string>('');
  const [status, setStatus] = useState<NotificationStatus | undefined>(undefined);
  const [type, setType] = useState<NotificationType | undefined>(undefined);
  
  // Hooks
  const { notifications, loading, error, refetch } = useNotifications({
    status,
    type,
    perPage: 20,
  });
  const { unreadCount, refetch: refetchCount } = useUnreadNotificationCount();
  const { markNotificationRead } = useMarkNotificationRead();
  const { markAllNotificationsRead } = useMarkAllNotificationsRead();
  const { deleteNotification } = useDeleteNotification();

  // 处理标记已读
  const handleMarkAsRead = async (id: string) => {
    try {
      await markNotificationRead(id);
      refetch();
      refetchCount();
    } catch (err) {
      console.error('标记已读失败', err);
    }
  };

  // 处理标记所有已读
  const handleMarkAllAsRead = async () => {
    try {
      await markAllNotificationsRead();
      refetch();
      refetchCount();
    } catch (err) {
      console.error('标记所有已读失败', err);
    }
  };

  // 处理删除通知
  const handleDelete = async (id: string) => {
    try {
      const result = await deleteNotification(id);
      if (result && result.success) {
        // 如果当前查看的是被删除的通知，关闭详情面板
        if (selectedId === id) {
          setSelectedId('');
        }
        refetch(); // 刷新通知列表
        refetchCount(); // 刷新未读计数
      } else {
        console.error('删除通知失败: 操作未成功');
      }
    } catch (err) {
      console.error('删除通知失败', err);
    }
  };

  // 清除筛选
  const handleClearFilters = () => {
    setStatus(undefined);
    setType(undefined);
    refetch();
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">通知功能测试页面</h1>
      
      {/* 未读通知数量 */}
      <div className="mb-4 p-4 bg-blue-50 rounded-lg">
        <p className="text-lg">
          未读通知数量: <span className="font-bold text-blue-600">{unreadCount}</span>
        </p>
      </div>

      {/* 操作按钮 */}
      <div className="mb-6 flex space-x-4">
        <button 
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          onClick={() => handleMarkAllAsRead()}
        >
          标记所有为已读
        </button>
        <button 
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          onClick={() => refetch()}
        >
          刷新列表
        </button>
      </div>

      {/* 筛选栏 */}
      <div className="mb-6 flex flex-wrap gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">状态筛选：</label>
          <select 
            className="border rounded p-2"
            value={status || ''}
            onChange={(e) => setStatus(e.target.value ? e.target.value as NotificationStatus : undefined)}
          >
            <option value="">全部状态</option>
            <option value={NotificationStatus.UNREAD}>未读</option>
            <option value={NotificationStatus.READ}>已读</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">类型筛选：</label>
          <select 
            className="border rounded p-2"
            value={type || ''}
            onChange={(e) => setType(e.target.value ? e.target.value as NotificationType : undefined)}
          >
            <option value="">全部类型</option>
            <option value={NotificationType.SYSTEM}>系统通知</option>
            <option value={NotificationType.ACCOUNT}>账户通知</option>
            <option value={NotificationType.PAYMENT}>支付通知</option>
            <option value={NotificationType.MEMBER}>会员通知</option>
          </select>
        </div>

        <div className="flex items-end">
          <button 
            className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400"
            onClick={handleClearFilters}
          >
            清除筛选
          </button>
        </div>
      </div>

      {/* 加载状态 */}
      {loading && (
        <div className="my-4 text-center">
          <p>加载中...</p>
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <div className="my-4 p-4 bg-red-50 text-red-700 rounded-lg">
          <p>发生错误: {error.message}</p>
        </div>
      )}

      {/* 通知列表 */}
      <div className="border rounded-lg overflow-hidden">
        {notifications.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            {loading ? '加载中...' : '暂无通知'}
          </div>
        ) : (
          <table className="w-full table-auto">
            <thead className="bg-gray-100">
              <tr>
                <th className="p-3 text-left">ID</th>
                <th className="p-3 text-left">标题</th>
                <th className="p-3 text-left">类型</th>
                <th className="p-3 text-left">状态</th>
                <th className="p-3 text-left">时间</th>
                <th className="p-3 text-left">操作</th>
              </tr>
            </thead>
            <tbody>
              {notifications.map((notification) => (
                <tr 
                  key={notification.id} 
                  className={`border-t ${notification.status === NotificationStatus.UNREAD ? 'bg-blue-50' : ''} cursor-pointer hover:bg-gray-50`}
                  onClick={() => setSelectedId(selectedId === notification.id ? '' : notification.id)}
                >
                  <td className="p-3">{notification.id}</td>
                  <td className="p-3 font-medium">{notification.title}</td>
                  <td className="p-3">
                    <span className={`px-2 py-1 rounded text-xs ${getTypeColor(notification.type)}`}>
                      {notification.typeName}
                    </span>
                  </td>
                  <td className="p-3">
                    <span className={`px-2 py-1 rounded text-xs ${notification.status === NotificationStatus.UNREAD ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}>
                      {notification.status === NotificationStatus.UNREAD ? '未读' : '已读'}
                    </span>
                  </td>
                  <td className="p-3">{formatDate(notification.createdAt)}</td>
                  <td className="p-3" onClick={(e) => e.stopPropagation()}>
                    <div className="flex space-x-2">
                      {notification.status === NotificationStatus.UNREAD && (
                        <button 
                          onClick={() => handleMarkAsRead(notification.id)}
                          className="px-2 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600"
                        >
                          标为已读
                        </button>
                      )}
                      <button 
                        onClick={() => handleDelete(notification.id)}
                        className="px-2 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600"
                      >
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      {/* 通知内容详情 */}
      {selectedId && (
        <div className="mt-6 border rounded-lg overflow-hidden">
          <div className="bg-gray-100 p-4 flex justify-between items-center">
            <h2 className="text-xl font-semibold">通知详情</h2>
            <button 
              className="text-gray-600 hover:text-gray-800"
              onClick={() => setSelectedId('')}
            >
              关闭
            </button>
          </div>
          <div className="p-4">
            {(() => {
              const notification = notifications.find(n => n.id === selectedId);
              if (!notification) return <p>无法找到该通知</p>;
              
              return (
                <div>
                  <h3 className="text-lg font-medium mb-2">{notification.title}</h3>
                  <p className="text-sm text-gray-500 mb-4">
                    {formatDate(notification.createdAt)} · {notification.typeName}
                  </p>
                  <div className="prose" dangerouslySetInnerHTML={{ __html: notification.content }} />
                </div>
              );
            })()}
          </div>
        </div>
      )}
    </div>
  );
}

// 根据通知类型返回不同的样式类
function getTypeColor(type: NotificationType) {
  switch (type) {
    case NotificationType.SYSTEM:
      return 'bg-purple-100 text-purple-800';
    case NotificationType.ACCOUNT:
      return 'bg-green-100 text-green-800';
    case NotificationType.PAYMENT:
      return 'bg-orange-100 text-orange-800';
    case NotificationType.MEMBER:
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

// 格式化日期
function formatDate(dateString: string) {
  try {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', { 
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (err) {
    return dateString;
  }
} 