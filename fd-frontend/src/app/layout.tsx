import type { Metadata } from 'next'
import './globals.css'
import { Inter } from 'next/font/google'
import ApolloClientProvider from '@/lib/apollo-provider'
import { SettingsProvider } from '@/providers/SettingsProvider'
import { AuthProvider } from '@/contexts/AuthContext'
import UIProvider from '@/providers/UIProvider'
import { ThemeProvider } from '@/contexts/ThemeContext'
import { WebSocketProvider } from '@/contexts/WebSocketContext'
import WebSocketEventHub from '@/contexts/WebSocketEventHub'
import { MenuProvider } from '@/contexts/MenuContext'
import { fetchMenuData } from '@/lib/menu-data'
import React from 'react';

// 全局字体
const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Future Decade',
  description: '未来十年的技术和趋势',
}

export default async function RootLayout({
  children,
  modal,
}: {
  children: React.ReactNode;
  modal: React.ReactNode;
}) {
  // 在根布局中预获取菜单数据
  const menuData = await fetchMenuData();
  const topMenuItems = menuData?.topMenu || [];
  const footerMenuItems = menuData?.footerMenu || [];

  console.log('[RootLayout] Global menu data loaded:', {
    topMenuItems: topMenuItems.length,
    footerMenuItems: footerMenuItems.length
  });

  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <ApolloClientProvider>
          <SettingsProvider>
            <ThemeProvider>
              <UIProvider>
                <AuthProvider>
                  <WebSocketProvider>
                    <MenuProvider
                      topMenuItems={topMenuItems}
                      footerMenuItems={footerMenuItems}
                    >
                      {/* 全局 WebSocket 事件中心 */}
                      <WebSocketEventHub />
                      {children}
                      {modal}
                    </MenuProvider>
                  </WebSocketProvider>
                </AuthProvider>
              </UIProvider>
            </ThemeProvider>
          </SettingsProvider>
        </ApolloClientProvider>
      </body>
    </html>
  )
}