'use client';

import { useQuery } from '@apollo/client';
import { useState } from 'react';
import Link from 'next/link';
import {
  GET_LATEST_POSTS,
  GET_CATEGORIES,
  GET_TAGS,
  GET_USER,
  GET_USERS,
  GET_MEDIA,
  GET_MENUS,
  GET_CUSTOM_POSTS,
  GET_POST_BY_SLUG,
  GET_POST_BY_ID,
  GET_CUSTOM_POST_BY_ID,
  GET_CUSTOM_POST_BY_SLUG,
  GET_PAGES,
  GET_PAGE_BY_SLUG,
  GET_PAGE_BY_ID,
  GET_TAXONOMIES,
  GET_TAXONOMY_TERMS,
  GET_POSTS_BY_CATEGORY,
  GET_POSTS_BY_TAG,
  GET_POSTS_BY_TAXONOMY,
  GET_TAXONOMY_TERM_BY_ID,
  GET_TAXONOMY_TERM_BY_SLUG,
  GET_CATEGORY_BY_SLUG,
  GET_TAG_BY_SLUG,
  GET_CATEGORY_DETAIL,
  GET_TAG_DETAIL,
  GET_CATEGORY_POST,
  GET_CATEGORY_POST_BY_SLUG,
  GET_TAG_POST,
  GET_TAG_POST_BY_SLUG,
  GET_ENABLED_TAXONOMIES,
  GET_ENABLED_POST_TYPES,
  GET_POSTS_BY_TAX_QUERY_ID,
  GET_POSTS_BY_TAX_QUERY_SLUG,
  GET_POSTS_DETAIL_BY_TAX_QUERY_ID,
  GET_POSTS_DETAIL_BY_TAX_QUERY_SLUG,
  GET_ALL_SETTINGS,
  GET_GENERAL_SETTINGS,
  GET_READING_SETTINGS,
  GET_DISCUSSION_SETTINGS,
  GET_WRITING_SETTINGS,
  GET_POST_COMMENTS,
  GET_COMMENT,
  GET_COMMENT_REPLIES,
  GET_COMMENTS_BY_STATUS
} from '../../lib/graphql/queries';

export default function GraphQLTest() {
  const [activeQuery, setActiveQuery] = useState('latestPosts');
  
  // 文章查询
  const [postSlug, setPostSlug] = useState('');
  const [postId, setPostId] = useState('');
  
  // 页面查询
  const [pageSlug, setPageSlug] = useState('');
  const [pageId, setPageId] = useState('');
  
  // 用户和媒体
  const [userId, setUserId] = useState('1');
  const [mediaId, setMediaId] = useState('');
  
  // 自定义内容类型
  const [customPostTypeInput, setCustomPostTypeInput] = useState('POST');
  const [customPostId, setCustomPostId] = useState('');
  const [customPostSlug, setCustomPostSlug] = useState('');
  
  // 分类法相关
  const [taxonomyType, setTaxonomyType] = useState('CATEGORY');
  const [categoryId, setCategoryId] = useState('');
  const [tagId, setTagId] = useState('');
  const [taxonomyId, setTaxonomyId] = useState('');
  const [categorySlug, setCategorySlug] = useState('');
  const [tagSlug, setTagSlug] = useState('');
  const [taxonomySlug, setTaxonomySlug] = useState('');
  
  // 新增TaxQuery相关
  const [taxQueryTaxonomy, setTaxQueryTaxonomy] = useState('CATEGORY');
  const [taxQueryTermId, setTaxQueryTermId] = useState('');
  const [taxQuerySlug, setTaxQuerySlug] = useState('');
  
  const [categoryDetailSlug, setCategoryDetailSlug] = useState<string>('');
  const [tagDetailSlug, setTagDetailSlug] = useState<string>('');
  
  const [categoryPostCategoryId, setCategoryPostCategoryId] = useState<string>('');
  const [categoryPostId, setCategoryPostId] = useState<string>('');
  const [categoryPostSlug, setCategoryPostSlug] = useState<string>('');
  
  const [tagPostTagId, setTagPostTagId] = useState<string>('');
  const [tagPostId, setTagPostId] = useState<string>('');
  const [tagPostSlug, setTagPostSlug] = useState<string>('');

  // 评论相关
  const [commentPostId, setCommentPostId] = useState<string>('');
  const [commentId, setCommentId] = useState<string>('');
  const [commentStatus, setCommentStatus] = useState<string>('APPROVE');

  // 根据activeQuery选择查询
  const renderQueryResult = () => {
    switch (activeQuery) {
      case 'latestPosts':
        return <LatestPostsQuery />;
      case 'postById':
        return <PostByIdQuery id={postId} />;
      case 'postBySlug':
        return <PostBySlugQuery slug={postSlug} />;
      case 'pages':
        return <PagesQuery />;
      case 'pageById':
        return <PageByIdQuery id={pageId} />;
      case 'pageBySlug':
        return <PageBySlugQuery slug={pageSlug} />;
      case 'categories':
        return <CategoriesQuery />;
      case 'tags':
        return <TagsQuery />;
      case 'taxonomies':
        return <TaxonomiesQuery />;
      case 'taxonomyTerms':
        return <TaxonomyTermsQuery taxonomy={taxonomyType} />;
      case 'postsByCategory':
        return <PostsByCategoryQuery categoryId={categoryId} />;
      case 'postsByTag':
        return <PostsByTagQuery tagId={tagId} />;
      case 'postsByTaxonomy':
        return <PostsByTaxonomyQuery taxonomyId={taxonomyId} taxonomyName={taxonomyType} />;
      case 'categoryBySlug':
        return <CategoryBySlugQuery slug={categorySlug} />;
      case 'tagBySlug':
        return <TagBySlugQuery slug={tagSlug} />;
      case 'taxonomyTermById':
        return <TaxonomyTermByIdQuery id={taxonomyId} taxonomy={taxonomyType} />;
      case 'taxonomyTermBySlug':
        return <TaxonomyTermBySlugQuery slug={taxonomySlug} taxonomy={taxonomyType} />;
      case 'postsByTaxQueryId':
        return <PostsByTaxQueryIdQuery taxonomy={taxQueryTaxonomy} termId={taxQueryTermId} />;
      case 'postsByTaxQuerySlug':
        return <PostsByTaxQuerySlugQuery taxonomy={taxQueryTaxonomy} slug={taxQuerySlug} />;
      case 'postsDetailByTaxQueryId':
        return <PostsDetailByTaxQueryIdQuery taxonomy={taxQueryTaxonomy} termId={taxQueryTermId} />;
      case 'postsDetailByTaxQuerySlug':
        return <PostsDetailByTaxQuerySlugQuery taxonomy={taxQueryTaxonomy} slug={taxQuerySlug} />;
      case 'user':
        return <UserQuery userId={userId} />;
      case 'users':
        return <UsersQuery />;
      case 'media':
        return <MediaQuery mediaId={mediaId} />;
      case 'menu':
        return <MenuQuery />;
      case 'customPosts':
        return <CustomPostsQuery type={customPostTypeInput} />;
      case 'customPostById':
        return <CustomPostByIdQuery type={customPostTypeInput} id={customPostId} />;
      case 'customPostBySlug':
        return <CustomPostBySlugQuery type={customPostTypeInput} slug={customPostSlug} />;
      case 'categoryDetail':
        return <CategoryDetailQuery slug={categoryDetailSlug} />;
      case 'tagDetail':
        return <TagDetailQuery slug={tagDetailSlug} />;
      case 'categoryPost':
        return <CategoryPostQuery categoryId={categoryPostCategoryId} postId={categoryPostId} />;
      case 'categoryPostBySlug':
        return <CategoryPostBySlugQuery categoryId={categoryPostCategoryId} postSlug={categoryPostSlug} />;
      case 'tagPost':
        return <TagPostQuery tagId={tagPostTagId} postId={tagPostId} />;
      case 'tagPostBySlug':
        return <TagPostBySlugQuery tagId={tagPostTagId} postSlug={tagPostSlug} />;
      case 'enabledTaxonomies':
        return <EnabledTaxonomiesQuery />;
      case 'enabledPostTypes':
        return <EnabledPostTypesQuery />;
      case 'allSettings':
        return <AllSettingsQuery />;
      case 'generalSettings':
        return <GeneralSettingsQuery />;
      case 'readingSettings':
        return <ReadingSettingsQuery />;
      case 'discussionSettings':
        return <DiscussionSettingsQuery />;
      case 'writingSettings':
        return <WritingSettingsQuery />;
      case 'postComments':
        return <PostCommentsQuery postId={commentPostId} />;
      case 'comment':
        return <CommentQuery id={commentId} />;  
      case 'commentReplies':
        return <CommentRepliesQuery id={commentId} />;
      case 'commentsByStatus':
        return <CommentsByStatusQuery status={commentStatus} />;
      default:
        return <div>请选择一个查询类型</div>;
    }
  };

  return (
    <div className="min-h-screen p-8">
      <div className="max-w-7xl mx-auto">
        <header className="mb-8">
          <h1 className="text-3xl font-bold mb-2">GraphQL 片段测试</h1>
          <p className="text-gray-600">测试 GraphQL 片段查询功能</p>
          <Link href="/" className="text-blue-600 hover:underline mt-2 inline-block">
            返回首页
          </Link>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="md:col-span-1 bg-white p-4 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">选择查询类型</h2>
            <div className="space-y-2">
              <h3 className="font-medium text-gray-700 mt-4 mb-2">文章查询</h3>
              <nav className="space-y-1">
                <button
                  onClick={() => setActiveQuery('latestPosts')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'latestPosts' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  最新文章列表
                </button>
                <button
                  onClick={() => setActiveQuery('postBySlug')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'postBySlug' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  通过Slug获取文章
                </button>
                <button
                  onClick={() => setActiveQuery('postById')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'postById' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  通过ID获取文章
                </button>
              </nav>

              <h3 className="font-medium text-gray-700 mt-4 mb-2">页面查询</h3>
              <nav className="space-y-1">
                <button
                  onClick={() => setActiveQuery('pages')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'pages' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  页面列表
                </button>
                <button
                  onClick={() => setActiveQuery('pageBySlug')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'pageBySlug' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  通过Slug获取页面
                </button>
                <button
                  onClick={() => setActiveQuery('pageById')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'pageById' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  通过ID获取页面
                </button>
              </nav>

              <h3 className="font-medium text-gray-700 mt-4 mb-2">分类标签</h3>
              <nav className="space-y-1">
                <button
                  onClick={() => setActiveQuery('categories')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'categories' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  分类列表
                </button>
                <button
                  onClick={() => setActiveQuery('categoryBySlug')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'categoryBySlug' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  分类详情
                </button>
                <button
                  onClick={() => setActiveQuery('postsByCategory')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'postsByCategory' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  分类文章列表
                </button>
                <button
                  onClick={() => setActiveQuery('tags')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'tags' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  标签列表
                </button>
                <button
                  onClick={() => setActiveQuery('tagBySlug')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'tagBySlug' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  标签详情
                </button>
                <button
                  onClick={() => setActiveQuery('postsByTag')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'postsByTag' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  标签文章列表
                </button>
                <button
                  onClick={() => setActiveQuery('categoryDetail')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'categoryDetail' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  分类详情及文章
                </button>
                <button
                  onClick={() => setActiveQuery('tagDetail')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'tagDetail' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  标签详情及文章
                </button>
                <div className="border-t border-gray-200 my-2"></div>
                <h4 className="text-xs text-gray-500 font-semibold px-3 py-1">Tax Query 查询</h4>
                <button
                  onClick={() => setActiveQuery('postsByTaxQueryId')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded bg-green-50 ${
                    activeQuery === 'postsByTaxQueryId' ? 'bg-green-100 text-green-700' : 'hover:bg-green-100 text-green-600'
                  }`}
                >
                  TaxQuery按ID查询(新)
                </button>
                <button
                  onClick={() => setActiveQuery('postsByTaxQuerySlug')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded bg-green-50 ${
                    activeQuery === 'postsByTaxQuerySlug' ? 'bg-green-100 text-green-700' : 'hover:bg-green-100 text-green-600'
                  }`}
                >
                  TaxQuery按Slug查询(新)
                </button>
                <button
                  onClick={() => setActiveQuery('postsDetailByTaxQueryId')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded bg-green-50 ${
                    activeQuery === 'postsDetailByTaxQueryId' ? 'bg-green-100 text-green-700' : 'hover:bg-green-100 text-green-600'
                  }`}
                >
                  TaxQuery详情按ID查询(新)
                </button>
                <button
                  onClick={() => setActiveQuery('postsDetailByTaxQuerySlug')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded bg-green-50 ${
                    activeQuery === 'postsDetailByTaxQuerySlug' ? 'bg-green-100 text-green-700' : 'hover:bg-green-100 text-green-600'
                  }`}
                >
                  TaxQuery详情按Slug查询(新)
                </button>
              </nav>

              <h3 className="font-medium text-gray-700 mt-4 mb-2">自定义分类法</h3>
              <nav className="space-y-1">
                <button
                  onClick={() => setActiveQuery('taxonomies')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'taxonomies' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  分类法列表
                </button>
                <button
                  onClick={() => setActiveQuery('taxonomyTerms')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'taxonomyTerms' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  分类法条目列表
                </button>
                <button
                  onClick={() => setActiveQuery('taxonomyTermById')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'taxonomyTermById' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  分类法条目详情(ID)
                </button>
                <button
                  onClick={() => setActiveQuery('taxonomyTermBySlug')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'taxonomyTermBySlug' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  分类法条目详情(Slug)
                </button>
                <button
                  onClick={() => setActiveQuery('postsByTaxonomy')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'postsByTaxonomy' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  分类法下的文章
                </button>
              </nav>

              <h3 className="font-medium text-gray-700 mt-4 mb-2">自定义内容类型</h3>
              <nav className="space-y-1">
                <button
                  onClick={() => setActiveQuery('customPosts')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'customPosts' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  自定义内容列表
                </button>
                <button
                  onClick={() => setActiveQuery('customPostBySlug')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'customPostBySlug' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  通过Slug获取自定义内容
                </button>
                <button
                  onClick={() => setActiveQuery('customPostById')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'customPostById' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  通过ID获取自定义内容
                </button>
              </nav>

              <h3 className="font-medium text-gray-700 mt-4 mb-2">其他查询</h3>
              <nav className="space-y-1">
                <button
                  onClick={() => setActiveQuery('user')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'user' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  获取用户详情
                </button>
                <button
                  onClick={() => setActiveQuery('users')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'users' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  获取用户列表
                </button>
                <button
                  onClick={() => setActiveQuery('media')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'media' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  获取媒体详情
                </button>
                <button
                  onClick={() => setActiveQuery('menu')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'menu' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  菜单信息
                </button>
              </nav>

              <h3 className="font-medium text-gray-700 mt-4 mb-2">前端显示设置</h3>
              <nav className="space-y-1">
                <button
                  onClick={() => setActiveQuery('enabledTaxonomies')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'enabledTaxonomies' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  启用的分类法
                </button>
                
                <button
                  onClick={() => setActiveQuery('enabledPostTypes')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'enabledPostTypes' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  启用的文章类型
                </button>
              </nav>
              
              <h3 className="font-medium text-gray-700 mt-4 mb-2">站点设置</h3>
              <nav className="space-y-1">
                <button
                  onClick={() => setActiveQuery('allSettings')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'allSettings' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  所有设置
                </button>
                <button
                  onClick={() => setActiveQuery('generalSettings')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'generalSettings' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  常规设置
                </button>
                <button
                  onClick={() => setActiveQuery('readingSettings')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'readingSettings' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  阅读设置
                </button>
                <button
                  onClick={() => setActiveQuery('discussionSettings')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'discussionSettings' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  讨论设置
                </button>
                <button
                  onClick={() => setActiveQuery('writingSettings')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'writingSettings' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  写作设置
                </button>
              </nav>
            
              <h3 className="font-medium text-gray-700 mt-4 mb-2">评论</h3>
              <nav className="space-y-1">
                <div className="mb-2">
                  <label className="block text-xs text-gray-500 mb-1">文章ID</label>
                  <input
                    type="text"
                    value={commentPostId}
                    onChange={(e) => setCommentPostId(e.target.value)}
                    className="w-full p-1 text-sm border rounded"
                    placeholder="输入文章ID"
                  />
                </div>
                <button
                  onClick={() => setActiveQuery('postComments')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'postComments' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  文章评论
                </button>
                
                <div className="mb-2 mt-4">
                  <label className="block text-xs text-gray-500 mb-1">评论ID</label>
                  <input
                    type="text"
                    value={commentId}
                    onChange={(e) => setCommentId(e.target.value)}
                    className="w-full p-1 text-sm border rounded"
                    placeholder="输入评论ID"
                  />
                </div>
                <button
                  onClick={() => setActiveQuery('comment')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'comment' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  评论详情
                </button>
                <button
                  onClick={() => setActiveQuery('commentReplies')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'commentReplies' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  评论回复
                </button>
                
                <div className="mb-2 mt-4">
                  <label className="block text-xs text-gray-500 mb-1">评论状态</label>
                  <select
                    value={commentStatus}
                    onChange={(e) => setCommentStatus(e.target.value)}
                    className="w-full p-1 text-sm border rounded"
                  >
                    <option value="APPROVE">已批准</option>
                    <option value="HOLD">等待审核</option>
                    <option value="SPAM">垃圾评论</option>
                    <option value="TRASH">回收站</option>
                  </select>
                </div>
                <button
                  onClick={() => setActiveQuery('commentsByStatus')}
                  className={`block w-full text-left px-3 py-2 text-sm rounded ${
                    activeQuery === 'commentsByStatus' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
                  }`}
                >
                  按状态查询评论
                </button>
              </nav>
            </div>

            {/* 参数输入区域 */}
            {activeQuery === 'postBySlug' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">文章Slug</label>
                <input
                  type="text"
                  value={postSlug}
                  onChange={(e) => setPostSlug(e.target.value)}
                  className="w-full border rounded px-3 py-2"
                  placeholder="输入文章slug"
                />
              </div>
            )}

            {activeQuery === 'postById' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">文章ID</label>
                <input
                  type="text"
                  value={postId}
                  onChange={(e) => setPostId(e.target.value)}
                  className="w-full border rounded px-3 py-2"
                  placeholder="输入文章ID"
                />
              </div>
            )}

            {activeQuery === 'pageBySlug' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">页面Slug</label>
                <input
                  type="text"
                  value={pageSlug}
                  onChange={(e) => setPageSlug(e.target.value)}
                  className="w-full border rounded px-3 py-2"
                  placeholder="输入页面slug"
                />
              </div>
            )}

            {activeQuery === 'pageById' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">页面ID</label>
                <input
                  type="text"
                  value={pageId}
                  onChange={(e) => setPageId(e.target.value)}
                  className="w-full border rounded px-3 py-2"
                  placeholder="输入页面ID"
                />
              </div>
            )}

            {activeQuery === 'categoryBySlug' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">分类Slug</label>
                <input
                  type="text"
                  value={categorySlug}
                  onChange={(e) => setCategorySlug(e.target.value)}
                  className="w-full border rounded px-3 py-2"
                  placeholder="输入分类slug"
                />
              </div>
            )}

            {activeQuery === 'postsByCategory' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">分类ID</label>
                <input
                  type="text"
                  value={categoryId}
                  onChange={(e) => setCategoryId(e.target.value)}
                  className="w-full border rounded px-3 py-2"
                  placeholder="输入分类ID"
                />
              </div>
            )}

            {activeQuery === 'tagBySlug' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">标签Slug</label>
                <input
                  type="text"
                  value={tagSlug}
                  onChange={(e) => setTagSlug(e.target.value)}
                  className="w-full border rounded px-3 py-2"
                  placeholder="输入标签slug"
                />
              </div>
            )}

            {activeQuery === 'postsByTag' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">标签ID</label>
                <input
                  type="text"
                  value={tagId}
                  onChange={(e) => setTagId(e.target.value)}
                  className="w-full border rounded px-3 py-2"
                  placeholder="输入标签ID"
                />
              </div>
            )}

            {activeQuery === 'user' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">用户ID</label>
                <input
                  type="text"
                  value={userId}
                  onChange={(e) => setUserId(e.target.value)}
                  className="w-full border rounded px-3 py-2"
                />
              </div>
            )}

            {activeQuery === 'media' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">媒体ID</label>
                <input
                  type="text"
                  value={mediaId}
                  onChange={(e) => setMediaId(e.target.value)}
                  className="w-full border rounded px-3 py-2"
                  placeholder="请输入有效的媒体ID"
                />
                <p className="text-xs text-gray-500 mt-1">请在WordPress管理后台查询有效的媒体ID</p>
              </div>
            )}

            {(activeQuery === 'customPosts' || activeQuery === 'customPostById' || activeQuery === 'customPostBySlug') && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  自定义内容类型
                </label>
                <input
                  type="text"
                  value={customPostTypeInput}
                  onChange={(e) => setCustomPostTypeInput(e.target.value.toUpperCase())}
                  placeholder="输入内容类型（如POST, PAGE等）"
                  className="w-full border rounded px-3 py-2"
                />
                <p className="text-xs text-gray-500 mt-1">请确保输入的是有效的ContentTypeEnum值</p>
              </div>
            )}

            {activeQuery === 'customPostById' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  自定义内容ID
                </label>
                <input
                  type="text"
                  value={customPostId}
                  onChange={(e) => setCustomPostId(e.target.value)}
                  placeholder="输入内容ID"
                  className="w-full border rounded px-3 py-2"
                />
              </div>
            )}

            {activeQuery === 'customPostBySlug' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  自定义内容Slug
                </label>
                <input
                  type="text"
                  value={customPostSlug}
                  onChange={(e) => setCustomPostSlug(e.target.value)}
                  placeholder="输入内容Slug"
                  className="w-full border rounded px-3 py-2"
                />
              </div>
            )}

            {(activeQuery === 'taxonomyTerms' || activeQuery === 'taxonomyTermById' || activeQuery === 'taxonomyTermBySlug' || activeQuery === 'postsByTaxonomy') && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  分类法类型
                </label>
                <input
                  type="text"
                  value={taxonomyType}
                  onChange={(e) => setTaxonomyType(e.target.value.toUpperCase())}
                  placeholder="输入分类法类型（如CATEGORY, TAG等）"
                  className="w-full border rounded px-3 py-2"
                />
                <p className="text-xs text-gray-500 mt-1">请确保输入的是有效的TaxonomyEnum值</p>
              </div>
            )}

            {activeQuery === 'taxonomyTermById' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  分类法条目ID
                </label>
                <input
                  type="text"
                  value={taxonomyId}
                  onChange={(e) => setTaxonomyId(e.target.value)}
                  placeholder="输入分类法条目ID"
                  className="w-full border rounded px-3 py-2"
                />
              </div>
            )}

            {activeQuery === 'taxonomyTermBySlug' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  分类法条目Slug
                </label>
                <input
                  type="text"
                  value={taxonomySlug}
                  onChange={(e) => setTaxonomySlug(e.target.value)}
                  placeholder="输入分类法条目Slug"
                  className="w-full border rounded px-3 py-2"
                />
              </div>
            )}

            {activeQuery === 'postsByTaxonomy' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  分类法条目ID
                </label>
                <input
                  type="text"
                  value={taxonomyId}
                  onChange={(e) => setTaxonomyId(e.target.value)}
                  placeholder="输入分类法条目ID"
                  className="w-full border rounded px-3 py-2"
                />
                <p className="text-xs text-gray-500 mt-1">例如：分类id或标签id等</p>
              </div>
            )}

            {activeQuery === 'categoryDetail' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">分类Slug</label>
                <input
                  type="text"
                  value={categoryDetailSlug}
                  onChange={(e) => setCategoryDetailSlug(e.target.value)}
                  className="w-full border rounded px-3 py-2"
                  placeholder="输入分类slug"
                />
              </div>
            )}

            {activeQuery === 'tagDetail' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">标签Slug</label>
                <input
                  type="text"
                  value={tagDetailSlug}
                  onChange={(e) => setTagDetailSlug(e.target.value)}
                  className="w-full border rounded px-3 py-2"
                  placeholder="输入标签slug"
                />
              </div>
            )}

            {activeQuery === 'categoryPost' && (
              <div className="mt-4 space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">分类ID</label>
                  <input
                    type="text"
                    value={categoryPostCategoryId}
                    onChange={(e) => setCategoryPostCategoryId(e.target.value)}
                    className="w-full border rounded px-3 py-2"
                    placeholder="输入分类ID"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">文章ID</label>
                  <input
                    type="text"
                    value={categoryPostId}
                    onChange={(e) => setCategoryPostId(e.target.value)}
                    className="w-full border rounded px-3 py-2"
                    placeholder="输入文章ID"
                  />
                </div>
              </div>
            )}

            {activeQuery === 'categoryPostBySlug' && (
              <div className="mt-4 space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">分类ID</label>
                  <input
                    type="text"
                    value={categoryPostCategoryId}
                    onChange={(e) => setCategoryPostCategoryId(e.target.value)}
                    className="w-full border rounded px-3 py-2"
                    placeholder="输入分类ID"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">文章Slug</label>
                  <input
                    type="text"
                    value={categoryPostSlug}
                    onChange={(e) => setCategoryPostSlug(e.target.value)}
                    className="w-full border rounded px-3 py-2"
                    placeholder="输入文章Slug"
                  />
                </div>
              </div>
            )}

            {activeQuery === 'tagPost' && (
              <div className="mt-4 space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">标签ID</label>
                  <input
                    type="text"
                    value={tagPostTagId}
                    onChange={(e) => setTagPostTagId(e.target.value)}
                    className="w-full border rounded px-3 py-2"
                    placeholder="输入标签ID"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">文章ID</label>
                  <input
                    type="text"
                    value={tagPostId}
                    onChange={(e) => setTagPostId(e.target.value)}
                    className="w-full border rounded px-3 py-2"
                    placeholder="输入文章ID"
                  />
                </div>
              </div>
            )}

            {activeQuery === 'tagPostBySlug' && (
              <div className="mt-4 space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">标签ID</label>
                  <input
                    type="text"
                    value={tagPostTagId}
                    onChange={(e) => setTagPostTagId(e.target.value)}
                    className="w-full border rounded px-3 py-2"
                    placeholder="输入标签ID"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">文章Slug</label>
                  <input
                    type="text"
                    value={tagPostSlug}
                    onChange={(e) => setTagPostSlug(e.target.value)}
                    className="w-full border rounded px-3 py-2"
                    placeholder="输入文章Slug"
                  />
                </div>
              </div>
            )}
          </div>

          <div className="md:col-span-3 bg-white p-6 rounded-lg shadow">
            {activeQuery === 'postsByCategory' && (
              <div className="mb-6 p-4 border rounded-md">
                <h3 className="font-medium text-lg mb-3">分类ID：</h3>
                <div className="flex items-center">
                  <input 
                    type="text" 
                    value={categoryId} 
                    onChange={(e) => setCategoryId(e.target.value)}
                    placeholder="请输入分类ID"
                    className="flex-1 p-2 border rounded mr-2" 
                  />
                </div>
              </div>
            )}
            
            {activeQuery === 'postsByTag' && (
              <div className="mb-6 p-4 border rounded-md">
                <h3 className="font-medium text-lg mb-3">标签ID：</h3>
                <div className="flex items-center">
                  <input 
                    type="text" 
                    value={tagId} 
                    onChange={(e) => setTagId(e.target.value)}
                    placeholder="请输入标签ID"
                    className="flex-1 p-2 border rounded mr-2" 
                  />
                </div>
              </div>
            )}

            {activeQuery === 'postsByTaxQueryId' && (
              <div className="mb-6 p-4 border rounded-md bg-green-50">
                <h3 className="font-medium text-lg mb-3">TaxQuery按ID查询参数：</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">分类法类型：</label>
                    <div className="flex flex-col space-y-2">
                      <select
                        value={taxQueryTaxonomy}
                        onChange={(e) => setTaxQueryTaxonomy(e.target.value)}
                        className="w-full p-2 border rounded"
                      >
                        <option value="CATEGORY">分类(CATEGORY)</option>
                        <option value="TAG">标签(TAG)</option>
                        <option value="POST_FORMAT">文章格式(POST_FORMAT)</option>
                        <TaxonomyOptions />
                        <option value="_CUSTOM">-- 手动输入自定义分类法 --</option>
                      </select>
                      
                      {taxQueryTaxonomy === '_CUSTOM' && (
                        <div className="mt-2">
                          <input
                            type="text"
                            value=""
                            onChange={(e) => setTaxQueryTaxonomy(e.target.value)}
                            placeholder="请输入自定义分类法名称，例如：COMPANY"
                            className="w-full p-2 border rounded border-green-500"
                          />
                          <p className="text-xs text-green-600 mt-1">
                            注意：分类法名称必须大写，例如：COMPANY、PRODUCT
                          </p>
                        </div>
                      )}
                    </div>
                    
                    <div className="mt-3 text-xs text-gray-600">
                      <p className="font-medium">分类法命名规则:</p>
                      <ul className="list-disc pl-5 space-y-1 mt-1">
                        <li>内置分类法直接使用名称，如：CATEGORY, TAG, POST_FORMAT</li>
                        <li>自定义分类法使用大写名称，如：COMPANY, PRODUCT</li>
                      </ul>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">分类法条目ID：</label>
                    <input
                      type="text"
                      value={taxQueryTermId}
                      onChange={(e) => setTaxQueryTermId(e.target.value)}
                      placeholder="请输入分类法条目ID"
                      className="w-full p-2 border rounded"
                    />
                    <p className="text-xs text-gray-500 mt-1">多个ID使用逗号分隔，如：1,2,3</p>
                  </div>
                  <div className="bg-blue-50 p-3 rounded text-sm">
                    <p className="font-medium text-blue-700">使用Tax Query按ID查询</p>
                    <p className="text-blue-600 mt-1">该查询使用wp-graphql-tax-query插件，能够按照分类法条目ID过滤文章。</p>
                  </div>
                </div>
              </div>
            )}
            
            {activeQuery === 'postsByTaxQuerySlug' && (
              <div className="mb-6 p-4 border rounded-md bg-green-50">
                <h3 className="font-medium text-lg mb-3">TaxQuery按Slug查询参数：</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">分类法类型：</label>
                    <div className="flex flex-col space-y-2">
                      <select
                        value={taxQueryTaxonomy}
                        onChange={(e) => setTaxQueryTaxonomy(e.target.value)}
                        className="w-full p-2 border rounded"
                      >
                        <option value="CATEGORY">分类(CATEGORY)</option>
                        <option value="TAG">标签(TAG)</option>
                        <option value="POST_FORMAT">文章格式(POST_FORMAT)</option>
                        <TaxonomyOptions />
                        <option value="_CUSTOM">-- 手动输入自定义分类法 --</option>
                      </select>
                      
                      {taxQueryTaxonomy === '_CUSTOM' && (
                        <div className="mt-2">
                          <input
                            type="text"
                            value=""
                            onChange={(e) => setTaxQueryTaxonomy(e.target.value)}
                            placeholder="请输入自定义分类法名称，例如：COMPANY"
                            className="w-full p-2 border rounded border-green-500"
                          />
                          <p className="text-xs text-green-600 mt-1">
                            注意：分类法名称必须大写，例如：COMPANY、PRODUCT
                          </p>
                        </div>
                      )}
                    </div>
                    
                    <div className="mt-3 text-xs text-gray-600">
                      <p className="font-medium">分类法命名规则:</p>
                      <ul className="list-disc pl-5 space-y-1 mt-1">
                        <li>内置分类法直接使用名称，如：CATEGORY, TAG, POST_FORMAT</li>
                        <li>自定义分类法使用大写名称，如：COMPANY, PRODUCT</li>
                      </ul>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">分类法条目Slug：</label>
                    <input
                      type="text"
                      value={taxQuerySlug}
                      onChange={(e) => setTaxQuerySlug(e.target.value)}
                      placeholder="请输入分类法条目Slug"
                      className="w-full p-2 border rounded"
                    />
                    <p className="text-xs text-gray-500 mt-1">多个Slug使用逗号分隔，如：slug1,slug2,slug3</p>
                  </div>
                  <div className="bg-blue-50 p-3 rounded text-sm">
                    <p className="font-medium text-blue-700">使用Tax Query按Slug查询</p>
                    <p className="text-blue-600 mt-1">该查询使用wp-graphql-tax-query插件，能够按照分类法条目Slug过滤文章。</p>
                  </div>
                </div>
              </div>
            )}
            
            {activeQuery === 'postsByTaxonomy' && (
              <div className="mb-6 p-4 border rounded-md">
                <h3 className="font-medium text-lg mb-3">分类法设置：</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">分类法类型：</label>
                    <select
                      value={taxonomyType}
                      onChange={(e) => setTaxonomyType(e.target.value)}
                      className="w-full p-2 border rounded"
                    >
                      <option value="CATEGORY">分类(CATEGORY)</option>
                      <option value="TAG">标签(TAG)</option>
                      <option value="POST_FORMAT">文章格式(POST_FORMAT)</option>
                      {/* 可以添加更多自定义分类法 */}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">分类法条目ID：</label>
                    <input
                      type="text"
                      value={taxonomyId}
                      onChange={(e) => setTaxonomyId(e.target.value)}
                      placeholder="请输入分类法条目ID"
                      className="w-full p-2 border rounded"
                    />
                  </div>
                  <div className="bg-yellow-50 p-3 rounded text-sm">
                    <p className="font-medium text-yellow-700">注意：原生查询限制</p>
                    <p className="text-yellow-600 mt-1">WPGraphQL API默认不支持按自定义分类法查询文章列表，建议使用上方的TaxQuery查询。</p>
                  </div>
                </div>
              </div>
            )}
            
            {activeQuery === 'postsDetailByTaxQueryId' && (
              <div className="mb-6 p-4 border rounded-md bg-green-50">
                <h3 className="font-medium text-lg mb-3">TaxQuery详情按ID查询参数：</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">分类法类型：</label>
                    <div className="flex flex-col space-y-2">
                      <select
                        value={taxQueryTaxonomy}
                        onChange={(e) => setTaxQueryTaxonomy(e.target.value)}
                        className="w-full p-2 border rounded"
                      >
                        <option value="CATEGORY">分类(CATEGORY)</option>
                        <option value="TAG">标签(TAG)</option>
                        <option value="POST_FORMAT">文章格式(POST_FORMAT)</option>
                        <TaxonomyOptions />
                        <option value="_CUSTOM">-- 手动输入自定义分类法 --</option>
                      </select>
                      
                      {taxQueryTaxonomy === '_CUSTOM' && (
                        <div className="mt-2">
                          <input
                            type="text"
                            value=""
                            onChange={(e) => setTaxQueryTaxonomy(e.target.value)}
                            placeholder="请输入自定义分类法名称，例如：COMPANY"
                            className="w-full p-2 border rounded border-green-500"
                          />
                          <p className="text-xs text-green-600 mt-1">
                            注意：分类法名称必须大写，例如：COMPANY、PRODUCT
                          </p>
                        </div>
                      )}
                    </div>
                    
                    <div className="mt-3 text-xs text-gray-600">
                      <p className="font-medium">分类法命名规则:</p>
                      <ul className="list-disc pl-5 space-y-1 mt-1">
                        <li>内置分类法直接使用名称，如：CATEGORY, TAG, POST_FORMAT</li>
                        <li>自定义分类法使用大写名称，如：COMPANY, PRODUCT</li>
                      </ul>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">分类法条目ID：</label>
                    <input
                      type="text"
                      value={taxQueryTermId}
                      onChange={(e) => setTaxQueryTermId(e.target.value)}
                      placeholder="请输入分类法条目ID"
                      className="w-full p-2 border rounded"
                    />
                    <p className="text-xs text-gray-500 mt-1">多个ID使用逗号分隔，如：1,2,3</p>
                  </div>
                  <div className="bg-blue-50 p-3 rounded text-sm">
                    <p className="font-medium text-blue-700">使用Tax Query按ID查询详情</p>
                    <p className="text-blue-600 mt-1">该查询使用wp-graphql-tax-query插件，能够按照分类法条目ID过滤文章，并返回完整文章内容。</p>
                  </div>
                </div>
              </div>
            )}
            
            {activeQuery === 'postsDetailByTaxQuerySlug' && (
              <div className="mb-6 p-4 border rounded-md bg-green-50">
                <h3 className="font-medium text-lg mb-3">TaxQuery详情按Slug查询参数：</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">分类法类型：</label>
                    <div className="flex flex-col space-y-2">
                      <select
                        value={taxQueryTaxonomy}
                        onChange={(e) => setTaxQueryTaxonomy(e.target.value)}
                        className="w-full p-2 border rounded"
                      >
                        <option value="CATEGORY">分类(CATEGORY)</option>
                        <option value="TAG">标签(TAG)</option>
                        <option value="POST_FORMAT">文章格式(POST_FORMAT)</option>
                        <TaxonomyOptions />
                        <option value="_CUSTOM">-- 手动输入自定义分类法 --</option>
                      </select>
                      
                      {taxQueryTaxonomy === '_CUSTOM' && (
                        <div className="mt-2">
                          <input
                            type="text"
                            value=""
                            onChange={(e) => setTaxQueryTaxonomy(e.target.value)}
                            placeholder="请输入自定义分类法名称，例如：COMPANY"
                            className="w-full p-2 border rounded border-green-500"
                          />
                          <p className="text-xs text-green-600 mt-1">
                            注意：分类法名称必须大写，例如：COMPANY、PRODUCT
                          </p>
                        </div>
                      )}
                    </div>
                    
                    <div className="mt-3 text-xs text-gray-600">
                      <p className="font-medium">分类法命名规则:</p>
                      <ul className="list-disc pl-5 space-y-1 mt-1">
                        <li>内置分类法直接使用名称，如：CATEGORY, TAG, POST_FORMAT</li>
                        <li>自定义分类法使用大写名称，如：COMPANY, PRODUCT</li>
                      </ul>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">分类法条目Slug：</label>
                    <input
                      type="text"
                      value={taxQuerySlug}
                      onChange={(e) => setTaxQuerySlug(e.target.value)}
                      placeholder="请输入分类法条目Slug"
                      className="w-full p-2 border rounded"
                    />
                    <p className="text-xs text-gray-500 mt-1">多个Slug使用逗号分隔，如：slug1,slug2,slug3</p>
                  </div>
                  <div className="bg-blue-50 p-3 rounded text-sm">
                    <p className="font-medium text-blue-700">使用Tax Query按Slug查询详情</p>
                    <p className="text-blue-600 mt-1">该查询使用wp-graphql-tax-query插件，能够按照分类法条目Slug过滤文章，并返回完整文章内容。</p>
                  </div>
                </div>
              </div>
            )}

            <div className="overflow-auto">
              {renderQueryResult()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// 查询组件

function LatestPostsQuery() {
  const { loading, error, data } = useQuery(GET_LATEST_POSTS);

  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const posts = data?.posts?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">最新文章 (POST_FRAGMENT)</h2>
      {posts.length === 0 ? (
        <p>没有文章</p>
      ) : (
        <div className="space-y-4">
          {posts.map((post: any) => (
            <div key={post.id} className="border-b pb-4">
              <h3 className="text-lg font-medium">{post.title}</h3>
              <div className="text-sm text-gray-500 mt-1">
                发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}
              </div>
              <div className="mt-2">
                <span className="text-sm font-medium">分类:</span>{' '}
                {post.categories?.nodes?.map((cat: any) => cat.name).join(', ') || '无分类'}
              </div>
              <div className="mt-1">
                <span className="text-sm font-medium">Slug:</span> {post.slug}
              </div>
              {post.featuredImage?.node && (
                <div className="mt-1">
                  <span className="text-sm font-medium">特色图片:</span>{' '}
                  {post.featuredImage.node.sourceUrl}
                </div>
              )}
              <div 
                className="mt-2 text-sm text-gray-700"
                dangerouslySetInnerHTML={{ __html: post.excerpt || '' }}
              />
            </div>
          ))}
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function CategoriesQuery() {
  const { loading, error, data } = useQuery(GET_CATEGORIES);

  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const categories = data?.categories?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">分类列表 (CATEGORY_FRAGMENT)</h2>
      {categories.length === 0 ? (
        <p>没有分类</p>
      ) : (
        <div className="space-y-4">
          {categories.map((category: any) => (
            <div key={category.id} className="border-b pb-4">
              <h3 className="text-lg font-medium">{category.name}</h3>
              <div className="mt-1">
                <span className="text-sm font-medium">Slug:</span> {category.slug}
              </div>
              <div className="mt-1">
                <span className="text-sm font-medium">文章数:</span> {category.count}
              </div>
              {category.description && (
                <div className="mt-2 text-sm text-gray-700">{category.description}</div>
              )}
            </div>
          ))}
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function TagsQuery() {
  const { loading, error, data } = useQuery(GET_TAGS);

  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const tags = data?.tags?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">标签列表 (TAG_FRAGMENT)</h2>
      {tags.length === 0 ? (
        <p>没有标签</p>
      ) : (
        <div className="space-y-4">
          {tags.map((tag: any) => (
            <div key={tag.id} className="border-b pb-4">
              <h3 className="text-lg font-medium">{tag.name}</h3>
              <div className="mt-1">
                <span className="text-sm font-medium">Slug:</span> {tag.slug}
              </div>
              <div className="mt-1">
                <span className="text-sm font-medium">文章数:</span> {tag.count}
              </div>
            </div>
          ))}
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function UserQuery({ userId }: { userId: string }) {
  const { loading, error, data } = useQuery(GET_USER, {
    variables: { id: userId },
    skip: !userId
  });

  if (!userId) return <div>请输入用户ID</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const user = data?.user;

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">用户信息 (USER_FRAGMENT)</h2>
      {!user ? (
        <p>未找到用户</p>
      ) : (
        <div className="space-y-4">
          <div className="pb-4">
            <h3 className="text-lg font-medium">{user.name}</h3>
            {user.avatar?.url && (
              <div className="mt-2">
                <span className="text-sm font-medium">头像:</span>{' '}
                <img 
                  src={user.avatar.url} 
                  alt={user.name} 
                  className="w-16 h-16 mt-1 rounded-full"
                />
              </div>
            )}
            <div className="mt-1">
              <span className="text-sm font-medium">Slug:</span> {user.slug}
            </div>
            {user.description && (
              <div className="mt-2 text-sm text-gray-700">{user.description}</div>
            )}
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function MediaQuery({ mediaId }: { mediaId: string }) {
  const { loading, error, data } = useQuery(GET_MEDIA, {
    variables: { id: mediaId },
    skip: !mediaId
  });

  if (!mediaId) return <div>请输入媒体ID</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const mediaItem = data?.mediaItem;

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">媒体信息 (MEDIA_FRAGMENT)</h2>
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          提示：需要输入有效的媒体ID才能获取媒体信息。您可以在WordPress管理后台的媒体库中查看媒体ID。
        </p>
      </div>
      {!mediaItem ? (
        <p>未找到媒体</p>
      ) : (
        <div className="space-y-4">
          <div className="pb-4">
            <h3 className="text-lg font-medium">{mediaItem.title}</h3>
            {mediaItem.sourceUrl && (
              <div className="mt-2">
                <span className="text-sm font-medium">媒体文件:</span>
                {mediaItem.mediaType === 'IMAGE' ? (
                  <div className="mt-2">
                    <img 
                      src={mediaItem.sourceUrl} 
                      alt={mediaItem.altText || mediaItem.title} 
                      className="max-w-full h-auto max-h-64"
                    />
                  </div>
                ) : (
                  <div className="mt-1">
                    <a 
                      href={mediaItem.sourceUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {mediaItem.sourceUrl}
                    </a>
                  </div>
                )}
              </div>
            )}
            <div className="mt-2">
              <span className="text-sm font-medium">类型:</span> {mediaItem.mediaType} ({mediaItem.mimeType})
            </div>
            {mediaItem.altText && (
              <div className="mt-1">
                <span className="text-sm font-medium">替代文本:</span> {mediaItem.altText}
              </div>
            )}
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function MenuQuery() {
  const { loading, error, data } = useQuery(GET_MENUS);
  const [viewMode, setViewMode] = useState('flat'); // 'flat' 或 'hierarchical'

  // 定义菜单项接口
  interface MenuItem {
    id: string;
    parentId: string | null;
    [key: string]: any; // 允许其他属性
  }

  // 定义层次结构菜单项接口
  interface HierarchicalMenuItem extends MenuItem {
    children: HierarchicalMenuItem[];
  }
  
  // 定义菜单接口
  interface Menu {
    id: string;
    name: string;
    menuItems?: {
      nodes?: MenuItem[];
    };
  }

  // 将扁平菜单转换为层次结构
  const flatListToHierarchical = <T extends { [key: string]: any }>(
    data: T[] = [],
    {idKey = 'id', parentKey = 'parentId', childrenKey = 'children'} = {}
  ): Array<T & { [key: string]: any }> => {
    const tree: Array<T & { [key: string]: any }> = [];
    const childrenOf: { [key: string]: Array<T & { [key: string]: any }> } = {};
    
    data.forEach((item) => {
      // 使用类型断言避免TypeScript的索引修改限制
      const newItem = {...item} as any;
      const id = newItem[idKey];
      const parentId = newItem[parentKey] || 0;
      
      childrenOf[id] = childrenOf[id] || [];
      newItem[childrenKey] = childrenOf[id];
      
      parentId
        ? (
            childrenOf[parentId] = childrenOf[parentId] || []
          ).push(newItem)
        : tree.push(newItem);
    });
    
    return tree;
  };

  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const menus = data?.menus?.nodes || [];

  // 递归渲染菜单项
  const renderMenuItems = (items: HierarchicalMenuItem[], depth: number = 0) => {
    return (
      <ul className={`mt-2 space-y-2 ${depth > 0 ? 'ml-4 border-l-2 pl-2 border-gray-200' : ''}`}>
        {items.map((item) => (
          <li key={item.id} className="flex items-start">
            <span className="mr-2">•</span>
            <div>
              <a 
                href={item.url} 
                target={item.target || '_self'} 
                className="text-blue-600 hover:underline"
              >
                {item.label || item.title}
              </a>
              {item.cssClasses?.length > 0 && (
                <span className="ml-2 text-xs text-gray-500">
                  ({item.cssClasses.join(', ')})
                </span>
              )}
              {item.children?.length > 0 && renderMenuItems(item.children, depth + 1)}
            </div>
          </li>
        ))}
      </ul>
    );
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">菜单信息 (MENU_ITEM_FRAGMENT)</h2>
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          注意：此查询获取所有菜单。如果没有显示菜单，请确认您的WordPress站点中是否已创建菜单，
          并且GraphQL API已配置为允许查询菜单数据。
        </p>
        <p className="text-sm text-yellow-700 mt-2">
          在WordPress管理后台，可以在"外观 &gt; 菜单"中创建和管理菜单。
        </p>
      </div>
      
      <div className="mb-4">
        <div className="flex space-x-2">
          <button 
            onClick={() => setViewMode('flat')} 
            className={`px-3 py-1 text-sm rounded ${
              viewMode === 'flat' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 hover:bg-gray-200'
            }`}
          >
            扁平视图
          </button>
          <button 
            onClick={() => setViewMode('hierarchical')} 
            className={`px-3 py-1 text-sm rounded ${
              viewMode === 'hierarchical' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 hover:bg-gray-200'
            }`}
          >
            层次视图
          </button>
        </div>
      </div>
      
      {menus.length === 0 ? (
        <p>未找到菜单</p>
      ) : (
        <div className="space-y-4">
          {menus.map((menu: Menu) => {
            const menuItems = menu.menuItems?.nodes || [];
            const hierarchicalItems = viewMode === 'hierarchical' ? 
              flatListToHierarchical(menuItems) as HierarchicalMenuItem[] : [];
            
            return (
            <div key={menu.id} className="pb-4 border-b">
              <h3 className="text-lg font-medium">{menu.name}</h3>
                {menuItems.length > 0 ? (
                  viewMode === 'flat' ? (
                <ul className="mt-2 space-y-2">
                      {menuItems.map((item) => (
                    <li key={item.id} className="flex items-start">
                      <span className="mr-2">•</span>
                      <div>
                        <a 
                          href={item.url} 
                          target={item.target || '_self'} 
                          className="text-blue-600 hover:underline"
                        >
                              {item.label || item.title}
                        </a>
                        {item.cssClasses?.length > 0 && (
                          <span className="ml-2 text-xs text-gray-500">
                            ({item.cssClasses.join(', ')})
                          </span>
                        )}
                            {item.parentId && (
                              <span className="ml-2 text-xs bg-gray-100 px-1 py-0.5 rounded">
                                父项: {item.parentId}
                          </span>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
                  ) : (
                    renderMenuItems(hierarchicalItems)
                  )
              ) : (
                <p className="mt-2">无菜单项</p>
              )}
            </div>
            );
          })}
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function CustomPostsQuery({ type }: { type: string }) {
  const { loading, error, data } = useQuery(GET_CUSTOM_POSTS, {
    variables: { type: type as any },
    skip: !type
  });

  if (!type) return <div>请选择或输入一个内容类型</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const posts = data?.contentNodes?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">自定义内容类型内容</h2>
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          正在查询内容类型: <strong>{type}</strong>
        </p>
        <p className="text-sm text-yellow-700 mt-1">
          注意：ContentNode是一个接口类型，不同的内容类型具有不同的字段，显示结果会根据实际内容类型而变化。
        </p>
        <p className="text-sm text-yellow-700 mt-1">
          每个返回项的实际类型显示在 "__typename" 字段中。
        </p>
      </div>
      {posts.length === 0 ? (
        <p>没有找到{type}类型的内容</p>
      ) : (
        <div>
          <p className="mb-3">找到 {posts.length} 个 {type} 类型的内容项</p>
          <div className="space-y-4">
            {posts.map((post: any) => (
              <div key={post.id} className="border-b pb-4">
                <div className="mb-2 text-xs font-mono bg-gray-100 p-1 rounded inline-block">
                  类型: {post.__typename}
                </div>
                {post.title && <h3 className="text-lg font-medium">{post.title}</h3>}
                <div className="text-sm text-gray-500 mt-1">
                  发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}
                </div>
                <div className="mt-1">
                  <span className="text-sm font-medium">ID:</span> {post.id}
                </div>
                <div className="mt-1">
                  <span className="text-sm font-medium">Slug:</span> {post.slug}
                </div>
                <div className="mt-1">
                  <span className="text-sm font-medium">URI:</span> {post.uri}
                </div>
                {post.featuredImage?.node && (
                  <div className="mt-1">
                    <span className="text-sm font-medium">特色图片:</span>{' '}
                    {post.featuredImage.node.sourceUrl}
                  </div>
                )}
                {post.author?.node && (
                  <div className="mt-1">
                    <span className="text-sm font-medium">作者:</span>{' '}
                    {post.author.node.name}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function PostBySlugQuery({ slug }: { slug: string }) {
  const { loading, error, data } = useQuery(GET_POST_BY_SLUG, {
    variables: { slug },
    skip: !slug
  });

  if (!slug) return <div>请输入文章Slug</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const post = data?.post;

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">文章详情 (POST_DETAIL_FRAGMENT)</h2>
      {!post ? (
        <p>未找到文章</p>
      ) : (
        <div className="space-y-4">
          <div className="pb-4">
            <h3 className="text-lg font-medium">{post.title}</h3>
            <div className="text-sm text-gray-500 mt-1">
              发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}
            </div>
            
            {post.author?.node && (
              <div className="mt-2 flex items-center">
                {post.author.node.avatar?.url && (
                  <img 
                    src={post.author.node.avatar.url} 
                    alt={post.author.node.name} 
                    className="w-8 h-8 rounded-full mr-2"
                  />
                )}
                <span>
                  <span className="text-sm font-medium">作者:</span>{' '}
                  {post.author.node.name}
                </span>
              </div>
            )}
            
            {post.categories?.nodes?.length > 0 && (
              <div className="mt-1">
                <span className="text-sm font-medium">分类:</span>{' '}
                {post.categories.nodes.map((cat: any) => cat.name).join(', ')}
              </div>
            )}
            
            {post.tags?.nodes?.length > 0 && (
              <div className="mt-1">
                <span className="text-sm font-medium">标签:</span>{' '}
                {post.tags.nodes.map((tag: any) => tag.name).join(', ')}
              </div>
            )}
            
            {post.featuredImage?.node && (
              <div className="mt-4">
                <img 
                  src={post.featuredImage.node.sourceUrl} 
                  alt={post.featuredImage.node.altText || post.title} 
                  className="max-w-full h-auto"
                />
              </div>
            )}
            
            <div
              className="mt-4 prose max-w-none"
              dangerouslySetInnerHTML={{ __html: post.content || '' }}
            />
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function PostByIdQuery({ id }: { id: string }) {
  const { loading, error, data } = useQuery(GET_POST_BY_ID, {
    variables: { id },
    skip: !id
  });

  if (!id) return <div>请输入文章ID</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const post = data?.post;

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">文章详情 (通过ID)</h2>
      {!post ? (
        <p>未找到文章</p>
      ) : (
        <div className="space-y-4">
          <div className="pb-4">
            <h3 className="text-lg font-medium">{post.title}</h3>
            <div className="text-sm text-gray-500 mt-1">
              发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}
            </div>
            
            {post.author?.node && (
              <div className="mt-2 flex items-center">
                {post.author.node.avatar?.url && (
                  <img 
                    src={post.author.node.avatar.url} 
                    alt={post.author.node.name} 
                    className="w-8 h-8 rounded-full mr-2"
                  />
                )}
                <span>
                  <span className="text-sm font-medium">作者:</span>{' '}
                  {post.author.node.name}
                </span>
              </div>
            )}
            
            {post.categories?.nodes?.length > 0 && (
              <div className="mt-1">
                <span className="text-sm font-medium">分类:</span>{' '}
                {post.categories.nodes.map((cat: any) => cat.name).join(', ')}
              </div>
            )}
            
            {post.tags?.nodes?.length > 0 && (
              <div className="mt-1">
                <span className="text-sm font-medium">标签:</span>{' '}
                {post.tags.nodes.map((tag: any) => tag.name).join(', ')}
              </div>
            )}
            
            {post.featuredImage?.node && (
              <div className="mt-4">
                <img 
                  src={post.featuredImage.node.sourceUrl} 
                  alt={post.featuredImage.node.altText || post.title} 
                  className="max-w-full h-auto"
                />
              </div>
            )}
            
            <div
              className="mt-4 prose max-w-none"
              dangerouslySetInnerHTML={{ __html: post.content || '' }}
            />
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function PagesQuery() {
  const { loading, error, data } = useQuery(GET_PAGES);

  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const pages = data?.pages?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">页面列表</h2>
      {pages.length === 0 ? (
        <p>没有页面</p>
      ) : (
        <div className="space-y-4">
          {pages.map((page: any) => (
            <div key={page.id} className="border-b pb-4">
              <h3 className="text-lg font-medium">{page.title}</h3>
              <div className="text-sm text-gray-500 mt-1">
                发布日期: {new Date(page.date).toLocaleDateString('zh-CN')}
              </div>
              <div className="mt-1">
                <span className="text-sm font-medium">Slug:</span> {page.slug}
              </div>
              <div className="mt-1">
                <span className="text-sm font-medium">URI:</span> {page.uri}
              </div>
              {page.featuredImage?.node && (
                <div className="mt-1">
                  <span className="text-sm font-medium">特色图片:</span>{' '}
                  {page.featuredImage.node.sourceUrl}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function PageByIdQuery({ id }: { id: string }) {
  const { loading, error, data } = useQuery(GET_PAGE_BY_ID, {
    variables: { id },
    skip: !id
  });

  if (!id) return <div>请输入页面ID</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const page = data?.page;

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">页面详情 (通过ID)</h2>
      {!page ? (
        <p>未找到页面</p>
      ) : (
        <div className="space-y-4">
          <div className="pb-4">
            <h3 className="text-lg font-medium">{page.title}</h3>
            <div className="text-sm text-gray-500 mt-1">
              发布日期: {new Date(page.date).toLocaleDateString('zh-CN')}
            </div>
            
            {page.author?.node && (
              <div className="mt-2 flex items-center">
                {page.author.node.avatar?.url && (
                  <img 
                    src={page.author.node.avatar.url} 
                    alt={page.author.node.name} 
                    className="w-8 h-8 rounded-full mr-2"
                  />
                )}
                <span>
                  <span className="text-sm font-medium">作者:</span>{' '}
                  {page.author.node.name}
                </span>
              </div>
            )}
            
            {page.featuredImage?.node && (
              <div className="mt-4">
                <img 
                  src={page.featuredImage.node.sourceUrl} 
                  alt={page.featuredImage.node.altText || page.title} 
                  className="max-w-full h-auto"
                />
              </div>
            )}
            
            <div
              className="mt-4 prose max-w-none"
              dangerouslySetInnerHTML={{ __html: page.content || '' }}
            />
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function PageBySlugQuery({ slug }: { slug: string }) {
  const { loading, error, data } = useQuery(GET_PAGE_BY_SLUG, {
    variables: { slug },
    skip: !slug
  });

  if (!slug) return <div>请输入页面Slug</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const page = data?.page;

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">页面详情 (通过Slug)</h2>
      {!page ? (
        <p>未找到页面</p>
      ) : (
        <div className="space-y-4">
          <div className="pb-4">
            <h3 className="text-lg font-medium">{page.title}</h3>
            <div className="text-sm text-gray-500 mt-1">
              发布日期: {new Date(page.date).toLocaleDateString('zh-CN')}
            </div>
            
            {page.author?.node && (
              <div className="mt-2 flex items-center">
                {page.author.node.avatar?.url && (
                  <img 
                    src={page.author.node.avatar.url} 
                    alt={page.author.node.name} 
                    className="w-8 h-8 rounded-full mr-2"
                  />
                )}
                <span>
                  <span className="text-sm font-medium">作者:</span>{' '}
                  {page.author.node.name}
                </span>
              </div>
            )}
            
            {page.featuredImage?.node && (
              <div className="mt-4">
                <img 
                  src={page.featuredImage.node.sourceUrl} 
                  alt={page.featuredImage.node.altText || page.title} 
                  className="max-w-full h-auto"
                />
              </div>
            )}
            
            <div
              className="mt-4 prose max-w-none"
              dangerouslySetInnerHTML={{ __html: page.content || '' }}
            />
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function TaxonomiesQuery() {
  const { loading, error, data } = useQuery(GET_TAXONOMIES);

  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const taxonomies = data?.taxonomies?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">自定义分类法列表</h2>
      {taxonomies.length === 0 ? (
        <p>没有分类法</p>
      ) : (
        <div className="space-y-4">
          {taxonomies.map((taxonomy: any) => (
            <div key={taxonomy.name} className="border-b pb-4">
              <h3 className="text-lg font-medium">{taxonomy.label || taxonomy.name}</h3>
              <div className="text-sm text-gray-500 mt-1">
                <span className="text-sm font-medium">名称:</span> {taxonomy.name}
              </div>
              <div className="mt-1">
                <span className="text-sm font-medium">层级:</span> {taxonomy.hierarchical ? '是' : '否'}
              </div>
              <div className="mt-1">
                <span className="text-sm font-medium">REST Base:</span> {taxonomy.restBase}
              </div>
              {taxonomy.description && (
                <div className="mt-2 text-sm">
                  <span className="font-medium">描述:</span> {taxonomy.description}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function TaxonomyTermsQuery({ taxonomy }: { taxonomy: string }) {
  const { loading, error, data } = useQuery(GET_TAXONOMY_TERMS, {
    variables: { taxonomy: taxonomy as any },
    skip: !taxonomy
  });

  if (!taxonomy) return <div>请输入分类法类型</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const terms = data?.terms?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">分类法 "{taxonomy}" 项目</h2>
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          正在查询分类法: <strong>{taxonomy}</strong>
        </p>
        <p className="text-sm text-yellow-700 mt-1">
          分类法类型常见值: CATEGORY（分类）、TAG（标签）、POST_FORMAT（文章格式）、自定义分类法名称
        </p>
      </div>
      {terms.length === 0 ? (
        <p>没有找到分类法 {taxonomy} 的项目</p>
      ) : (
        <div>
          <p className="mb-3">找到 {terms.length} 个 {taxonomy} 分类法的项目</p>
          <div className="space-y-4">
            {terms.map((term: any) => (
              <div key={term.id} className="border-b pb-4">
                <div className="mb-2 text-xs font-mono bg-gray-100 p-1 rounded inline-block">
                  类型: {term.__typename}
                </div>
                <h3 className="text-lg font-medium">{term.name}</h3>
                <div className="mt-1">
                  <span className="text-sm font-medium">ID:</span> {term.id}
                </div>
                <div className="mt-1">
                  <span className="text-sm font-medium">Slug:</span> {term.slug}
                </div>
                <div className="mt-1">
                  <span className="text-sm font-medium">URI:</span> {term.uri}
                </div>
                {term.count !== undefined && (
                  <div className="mt-1">
                    <span className="text-sm font-medium">内容数量:</span> {term.count}
                  </div>
                )}
                {term.description && (
                  <div className="mt-1">
                    <span className="text-sm font-medium">描述:</span> {term.description}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function CustomPostByIdQuery({ type, id }: { type: string; id: string }) {
  const { loading, error, data } = useQuery(GET_CUSTOM_POST_BY_ID, {
    variables: { type: type as any, id },
    skip: !type || !id
  });

  if (!type || !id) return <div>请输入内容类型和ID</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const post = data?.contentNode;

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">自定义内容详情 (通过ID)</h2>
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          正在查询内容类型: <strong>{type}</strong>, ID: <strong>{id}</strong>
        </p>
      </div>
      {!post ? (
        <p>未找到内容</p>
      ) : (
        <div className="space-y-4">
          <div className="pb-4">
            <div className="mb-2 text-xs font-mono bg-gray-100 p-1 rounded inline-block">
              类型: {post.__typename}
            </div>
            {post.title && <h3 className="text-lg font-medium">{post.title}</h3>}
            {post.date && (
              <div className="text-sm text-gray-500 mt-1">
                发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}
              </div>
            )}
            <div className="mt-1">
              <span className="text-sm font-medium">ID:</span> {post.id}
            </div>
            <div className="mt-1">
              <span className="text-sm font-medium">Slug:</span> {post.slug}
            </div>
            <div className="mt-1">
              <span className="text-sm font-medium">URI:</span> {post.uri}
            </div>
            
            {post.author?.node && (
              <div className="mt-2 flex items-center">
                {post.author.node.avatar?.url && (
                  <img 
                    src={post.author.node.avatar.url} 
                    alt={post.author.node.name} 
                    className="w-8 h-8 rounded-full mr-2"
                  />
                )}
                <span>
                  <span className="text-sm font-medium">作者:</span>{' '}
                  {post.author.node.name}
                </span>
              </div>
            )}
            
            {post.featuredImage?.node && (
              <div className="mt-4">
                <img 
                  src={post.featuredImage.node.sourceUrl} 
                  alt={post.featuredImage.node.altText || (post.title || '')} 
                  className="max-w-full h-auto"
                />
              </div>
            )}
            
            {post.excerpt && (
              <div className="mt-3">
                <span className="text-sm font-medium">摘要:</span>
                <div
                  className="mt-1 text-sm text-gray-700"
                  dangerouslySetInnerHTML={{ __html: post.excerpt }}
                />
              </div>
            )}
            
            {post.content && (
              <div>
                <span className="text-sm font-medium">内容:</span>
              <div
                  className="mt-1 prose max-w-none"
                dangerouslySetInnerHTML={{ __html: post.content }}
              />
              </div>
            )}
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function CustomPostBySlugQuery({ type, slug }: { type: string; slug: string }) {
  const { loading, error, data } = useQuery(GET_CUSTOM_POST_BY_SLUG, {
    variables: { type: type as any, slug },
    skip: !type || !slug
  });

  if (!type || !slug) return <div>请输入内容类型和Slug</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const posts = data?.contentNodes?.nodes || [];
  const post = posts.length > 0 ? posts[0] : null;

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">自定义内容详情 (通过Slug)</h2>
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          正在查询内容类型: <strong>{type}</strong>, Slug: <strong>{slug}</strong>
        </p>
      </div>
      {!post ? (
        <p>未找到内容</p>
      ) : (
        <div className="space-y-4">
          <div className="pb-4">
            <div className="mb-2 text-xs font-mono bg-gray-100 p-1 rounded inline-block">
              类型: {post.__typename}
            </div>
            {post.title && <h3 className="text-lg font-medium">{post.title}</h3>}
            {post.date && (
            <div className="text-sm text-gray-500 mt-1">
              发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}
              </div>
            )}
            <div className="mt-1">
              <span className="text-sm font-medium">ID:</span> {post.id}
            </div>
            <div className="mt-1">
              <span className="text-sm font-medium">Slug:</span> {post.slug}
            </div>
            <div className="mt-1">
              <span className="text-sm font-medium">URI:</span> {post.uri}
            </div>
            
            {post.author?.node && (
              <div className="mt-2 flex items-center">
                {post.author.node.avatar?.url && (
                  <img 
                    src={post.author.node.avatar.url} 
                    alt={post.author.node.name} 
                    className="w-8 h-8 rounded-full mr-2"
                  />
                )}
                <span>
                  <span className="text-sm font-medium">作者:</span>{' '}
                  {post.author.node.name}
                </span>
              </div>
            )}
            
            {post.featuredImage?.node && (
              <div className="mt-4">
                <img 
                  src={post.featuredImage.node.sourceUrl} 
                  alt={post.featuredImage.node.altText || (post.title || '')} 
                  className="max-w-full h-auto"
                />
              </div>
            )}
            
            {post.excerpt && (
              <div className="mt-3">
                <span className="text-sm font-medium">摘要:</span>
                <div
                  className="mt-1 text-sm text-gray-700"
                  dangerouslySetInnerHTML={{ __html: post.excerpt }}
                />
              </div>
            )}
            
            {post.content && (
              <div>
                <span className="text-sm font-medium">内容:</span>
                <div
                  className="mt-1 prose max-w-none"
                  dangerouslySetInnerHTML={{ __html: post.content }}
                />
              </div>
            )}
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function PostsByCategoryQuery({ categoryId }: { categoryId: string }) {
  const { loading, error, data } = useQuery(GET_POSTS_BY_CATEGORY, {
    variables: { categoryId: parseInt(categoryId, 10) },
    skip: !categoryId
  });

  if (!categoryId) return <div>请输入分类ID</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const posts = data?.posts?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">分类 "{categoryId}" 下的文章列表</h2>
      {posts.length === 0 ? (
        <p>没有找到分类 {categoryId} 下的文章</p>
      ) : (
        <div className="space-y-4">
          {posts.map((post: any) => (
            <div key={post.id} className="border-b pb-4">
              <h3 className="text-lg font-medium">{post.title}</h3>
              <div className="text-sm text-gray-500 mt-1">
                发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}
              </div>
              <div className="mt-1">
                <span className="text-sm font-medium">分类:</span>{' '}
                {post.categories?.nodes?.map((cat: any) => cat.name).join(', ')}
              </div>
              <div className="mt-1">
                <span className="text-sm font-medium">Slug:</span> {post.slug}
              </div>
              {post.featuredImage?.node && (
                <div className="mt-1">
                  <span className="text-sm font-medium">特色图片:</span>{' '}
                  {post.featuredImage.node.sourceUrl}
                </div>
              )}
              <div 
                className="mt-2 text-sm text-gray-700"
                dangerouslySetInnerHTML={{ __html: post.excerpt || '' }}
              />
            </div>
          ))}
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function PostsByTagQuery({ tagId }: { tagId: string }) {
  const { loading, error, data } = useQuery(GET_POSTS_BY_TAG, {
    variables: { tagId },
    skip: !tagId
  });

  if (!tagId) return <div>请输入标签ID</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const posts = data?.posts?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">标签 "{tagId}" 下的文章列表</h2>
      {posts.length === 0 ? (
        <p>没有找到标签 {tagId} 下的文章</p>
      ) : (
        <div className="space-y-4">
          {posts.map((post: any) => (
            <div key={post.id} className="border-b pb-4">
              <h3 className="text-lg font-medium">{post.title}</h3>
              <div className="text-sm text-gray-500 mt-1">
                发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}
              </div>
              <div className="mt-1">
                <span className="text-sm font-medium">标签:</span>{' '}
                {post.tags?.nodes?.map((tag: any) => tag.name).join(', ')}
              </div>
              <div className="mt-1">
                <span className="text-sm font-medium">Slug:</span> {post.slug}
              </div>
              {post.featuredImage?.node && (
                <div className="mt-1">
                  <span className="text-sm font-medium">特色图片:</span>{' '}
                  {post.featuredImage.node.sourceUrl}
                </div>
              )}
              <div 
                className="mt-2 text-sm text-gray-700"
                dangerouslySetInnerHTML={{ __html: post.excerpt || '' }}
              />
            </div>
          ))}
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function PostsByTaxonomyQuery({ taxonomyId, taxonomyName }: { taxonomyId: string; taxonomyName: string }) {
  const { loading, error, data } = useQuery(GET_POSTS_BY_TAXONOMY, {
    variables: { 
      termId: taxonomyId, 
      taxonomy: taxonomyName 
    },
    skip: !taxonomyId || !taxonomyName
  });

  if (!taxonomyId || !taxonomyName) return <div>请输入分类法条目ID和分类法名称</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const posts = data?.posts?.nodes || [];
  const term = data?.terms?.nodes?.[0]; // 获取第一个匹配的分类法条目

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">
        {term ? `分类法 "${term.name}" 下的文章列表` : `分类法 "${taxonomyName}" 下的文章列表`}
      </h2>
      
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          <strong>注意</strong>: 当前查询返回所有文章，未能按分类法进行过滤。这是因为WPGraphQL API限制。
          在实际应用中，您需要获取所有文章并在前端进行过滤，或使用自定义插件扩展GraphQL API。
        </p>
      </div>
      
      {posts.length === 0 ? (
        <p>没有找到相关文章</p>
      ) : (
        <div className="space-y-4">
          {posts.map((post: any) => (
            <div key={post.id} className="border-b pb-4">
              <h3 className="text-lg font-medium">{post.title}</h3>
              <div className="text-sm text-gray-500 mt-1">
                发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}
              </div>
              <div className="mt-1">
                <span className="text-sm font-medium">分类:</span>{' '}
                {post.categories?.nodes?.map((cat: any) => cat.name).join(', ')}
              </div>
              <div className="mt-1">
                <span className="text-sm font-medium">Slug:</span> {post.slug}
              </div>
              {post.featuredImage?.node && (
                <div className="mt-1">
                  <span className="text-sm font-medium">特色图片:</span>{' '}
                  {post.featuredImage.node.sourceUrl}
                </div>
              )}
              <div 
                className="mt-2 text-sm text-gray-700"
                dangerouslySetInnerHTML={{ __html: post.excerpt || '' }}
              />
            </div>
          ))}
        </div>
      )}
      
      {term && (
        <div className="mt-6 p-4 bg-blue-50 rounded border border-blue-200">
          <h3 className="text-lg font-medium">分类法信息</h3>
          <p><strong>ID:</strong> {term.id}</p>
          <p><strong>名称:</strong> {term.name}</p>
          <p><strong>类型:</strong> {term.__typename}</p>
          <p><strong>URI:</strong> {term.uri}</p>
          {term.count !== undefined && (
            <p><strong>内容数量:</strong> {term.count}</p>
          )}
          {term.description && (
            <p><strong>描述:</strong> {term.description}</p>
          )}
        </div>
      )}
      
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function CategoryBySlugQuery({ slug }: { slug: string }) {
  const { loading, error, data } = useQuery(GET_CATEGORY_BY_SLUG, {
    variables: { slug },
    skip: !slug
  });

  if (!slug) return <div>请输入分类Slug</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const category = data?.category;

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">分类 "{slug}" 详情</h2>
      {!category ? (
        <p>未找到分类</p>
      ) : (
        <div className="space-y-4">
          <div className="pb-4">
            <h3 className="text-lg font-medium">{category.name}</h3>
            <div className="text-sm text-gray-500 mt-1">
              文章数: {category.count}
            </div>
            {category.description && (
              <div className="mt-2 text-sm text-gray-700">{category.description}</div>
            )}
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function TagBySlugQuery({ slug }: { slug: string }) {
  const { loading, error, data } = useQuery(GET_TAG_BY_SLUG, {
    variables: { slug },
    skip: !slug
  });

  if (!slug) return <div>请输入标签Slug</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const tag = data?.tag;

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">标签 "{slug}" 详情</h2>
      {!tag ? (
        <p>未找到标签</p>
      ) : (
        <div className="space-y-4">
          <div className="pb-4">
            <h3 className="text-lg font-medium">{tag.name}</h3>
            <div className="text-sm text-gray-500 mt-1">
              文章数: {tag.count}
            </div>
            {tag.description && (
              <div className="mt-2 text-sm text-gray-700">{tag.description}</div>
            )}
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function TaxonomyTermByIdQuery({ id, taxonomy }: { id: string; taxonomy: string }) {
  const { loading, error, data } = useQuery(GET_TAXONOMY_TERM_BY_ID, {
    variables: { id, taxonomy },
    skip: !id || !taxonomy
  });

  if (!id || !taxonomy) return <div>请输入分类法类型和条目ID</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const term = data?.terms?.nodes?.[0];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">分类法条目详情 (通过ID)</h2>
      <div className="mb-4 bg-purple-50 p-4 rounded border border-purple-200">
        <h3 className="text-lg font-medium text-purple-800 mb-2">分类法命名规则提示</h3>
        <ul className="list-disc pl-5 space-y-1">
          <li>内置分类法使用大写，如：<code className="bg-white px-2 py-1 rounded">CATEGORY</code>, <code className="bg-white px-2 py-1 rounded">TAG</code></li>
          <li>自定义分类法也使用大写，如：<code className="bg-white px-2 py-1 rounded">COMPANY</code>, <code className="bg-white px-2 py-1 rounded">PRODUCT</code></li>
          <li>当前查询中使用的值：<code className="bg-white px-2 py-1 rounded">{taxonomy}</code></li>
        </ul>
      </div>
      
      {!term ? (
        <p>未找到条目数据</p>
      ) : (
        <div className="bg-white border rounded-lg p-5">
          <h3 className="text-lg font-medium">{term.name}</h3>
          <div className="text-sm text-gray-500 mt-1">类型: {term.__typename}</div>
          
          <div className="mt-4 grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">ID:</p>
              <p>{term.id}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Database ID:</p>
              <p>{term.databaseId}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Slug:</p>
              <p>{term.slug}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">URI:</p>
              <p>{term.uri}</p>
            </div>
          </div>
        </div>
      )}
      
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function TaxonomyTermBySlugQuery({ slug, taxonomy }: { slug: string; taxonomy: string }) {
  const { loading, error, data } = useQuery(GET_TAXONOMY_TERM_BY_SLUG, {
    variables: { slug: [slug], taxonomy },
    skip: !slug || !taxonomy
  });

  if (!slug || !taxonomy) return <div>请输入分类法类型和条目Slug</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const term = data?.terms?.nodes?.[0];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">分类法条目详情 (通过Slug)</h2>
      <div className="mb-4 bg-purple-50 p-4 rounded border border-purple-200">
        <h3 className="text-lg font-medium text-purple-800 mb-2">分类法命名规则提示</h3>
        <ul className="list-disc pl-5 space-y-1">
          <li>内置分类法使用大写，如：<code className="bg-white px-2 py-1 rounded">CATEGORY</code>, <code className="bg-white px-2 py-1 rounded">TAG</code></li>
          <li>自定义分类法也使用大写，如：<code className="bg-white px-2 py-1 rounded">COMPANY</code>, <code className="bg-white px-2 py-1 rounded">PRODUCT</code></li>
          <li>当前查询中使用的值：<code className="bg-white px-2 py-1 rounded">{taxonomy}</code></li>
        </ul>
      </div>
      
      {!term ? (
        <p>未找到条目数据</p>
      ) : (
        <div className="bg-white border rounded-lg p-5">
          <h3 className="text-lg font-medium">{term.name}</h3>
          <div className="text-sm text-gray-500 mt-1">类型: {term.__typename}</div>
          
          <div className="mt-4 grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">ID:</p>
              <p>{term.id}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Database ID:</p>
              <p>{term.databaseId}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Slug:</p>
              <p>{term.slug}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">URI:</p>
              <p>{term.uri}</p>
            </div>
          </div>
        </div>
      )}
      
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function CategoryDetailQuery({ slug }: { slug: string }) {
  const { loading, error, data } = useQuery(GET_CATEGORY_DETAIL, {
    variables: { slug },
    skip: !slug
  });

  if (!slug) return <div>请输入分类Slug</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const category = data?.category;

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">分类详情及文章</h2>
      {!category ? (
        <p>未找到分类信息</p>
      ) : (
        <div>
          <div className="bg-gray-50 p-4 mb-4 rounded">
            <h3 className="text-lg font-medium">{category.name}</h3>
            {category.description && (
              <div className="mt-2 text-gray-700">{category.description}</div>
            )}
            <div className="mt-2 text-sm">
              <span className="font-medium">ID:</span> {category.id}
            </div>
            <div className="mt-1 text-sm">
              <span className="font-medium">Slug:</span> {category.slug}
            </div>
            <div className="mt-1 text-sm">
              <span className="font-medium">文章数:</span> {category.count}
            </div>
          </div>

          <h3 className="text-lg font-medium mb-3">分类下的文章</h3>
          {category.posts?.nodes?.length > 0 ? (
            <div className="space-y-4">
              {category.posts.nodes.map((post: any) => (
                <div key={post.id} className="border-b pb-4">
                  <h4 className="font-medium">{post.title}</h4>
                  <div className="text-sm text-gray-500 mt-1">
                    发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}
                  </div>
                  <div className="mt-1 text-sm">
                    <span className="font-medium">Slug:</span> {post.slug}
                  </div>
                  <div 
                    className="mt-2 text-sm text-gray-700"
                    dangerouslySetInnerHTML={{ __html: post.excerpt || '' }}
                  />
                </div>
              ))}
            </div>
          ) : (
            <p>该分类下没有文章</p>
          )}
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function TagDetailQuery({ slug }: { slug: string }) {
  const { loading, error, data } = useQuery(GET_TAG_DETAIL, {
    variables: { slug },
    skip: !slug
  });

  if (!slug) return <div>请输入标签Slug</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const tag = data?.tag;

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">标签详情及文章</h2>
      {!tag ? (
        <p>未找到标签信息</p>
      ) : (
        <div>
          <div className="bg-gray-50 p-4 mb-4 rounded">
            <h3 className="text-lg font-medium">{tag.name}</h3>
            {tag.description && (
              <div className="mt-2 text-gray-700">{tag.description}</div>
            )}
            <div className="mt-2 text-sm">
              <span className="font-medium">ID:</span> {tag.id}
            </div>
            <div className="mt-1 text-sm">
              <span className="font-medium">Slug:</span> {tag.slug}
            </div>
            <div className="mt-1 text-sm">
              <span className="font-medium">文章数:</span> {tag.count}
            </div>
          </div>

          <h3 className="text-lg font-medium mb-3">标签下的文章</h3>
          {tag.posts?.nodes?.length > 0 ? (
            <div className="space-y-4">
              {tag.posts.nodes.map((post: any) => (
                <div key={post.id} className="border-b pb-4">
                  <h4 className="font-medium">{post.title}</h4>
                  <div className="text-sm text-gray-500 mt-1">
                    发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}
                  </div>
                  <div className="mt-1 text-sm">
                    <span className="font-medium">Slug:</span> {post.slug}
                  </div>
                  <div 
                    className="mt-2 text-sm text-gray-700"
                    dangerouslySetInnerHTML={{ __html: post.excerpt || '' }}
                  />
                </div>
              ))}
            </div>
          ) : (
            <p>该标签下没有文章</p>
          )}
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
} 

function CategoryPostQuery({ categoryId, postId }: { categoryId: string; postId: string }) {
  const { loading, error, data } = useQuery(GET_CATEGORY_POST, {
    variables: { 
      categoryId: parseInt(categoryId, 10),
      postId 
    },
    skip: !categoryId || !postId
  });

  if (!categoryId || !postId) return <div>请输入分类ID和文章ID</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const post = data?.post;
  const categoryPosts = data?.posts?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">分类下的文章详情 (通过ID)</h2>
      <div className="mb-4">
        <h3 className="text-lg font-medium mb-2">分类下的文章列表</h3>
        {categoryPosts.length === 0 ? (
          <p>该分类下没有文章</p>
        ) : (
          <div className="bg-gray-50 p-4 rounded">
            <ul className="list-disc pl-5">
              {categoryPosts.map((p: any) => (
                <li key={p.id} className={p.id === post?.id ? "font-semibold" : ""}>
                  {p.title} <span className="text-gray-500">({p.id})</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
      
      {!post ? (
        <p>未找到指定文章</p>
      ) : (
        <div className="space-y-4">
          <div className="bg-white border rounded-lg p-5">
            <h3 className="text-xl font-semibold mb-2">{post.title}</h3>
            <div className="text-sm text-gray-500 mb-3">发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}</div>
            
            {post.featuredImage?.node && (
              <div className="mb-4">
                <img 
                  src={post.featuredImage.node.sourceUrl} 
                  alt={post.featuredImage.node.altText || post.title}
                  className="w-full h-auto max-h-64 object-cover rounded"
                />
              </div>
            )}
            
            <div className="flex flex-wrap gap-2 mb-3">
              {post.categories?.nodes?.map((cat: any) => (
                <span key={cat.id} className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                  {cat.name}
                </span>
              ))}
              {post.tags?.nodes?.map((tag: any) => (
                <span key={tag.id} className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
                  {tag.name}
                </span>
              ))}
            </div>
            
            <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: post.content || '' }} />
          </div>
        </div>
      )}
      
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function CategoryPostBySlugQuery({ categoryId, postSlug }: { categoryId: string; postSlug: string }) {
  const { loading, error, data } = useQuery(GET_CATEGORY_POST_BY_SLUG, {
    variables: { 
      categoryId: parseInt(categoryId, 10),
      postSlug 
    },
    skip: !categoryId || !postSlug
  });

  if (!categoryId || !postSlug) return <div>请输入分类ID和文章Slug</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const post = data?.post;
  const categoryPosts = data?.posts?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">分类下的文章详情 (通过Slug)</h2>
      <div className="mb-4">
        <h3 className="text-lg font-medium mb-2">分类下的文章列表</h3>
        {categoryPosts.length === 0 ? (
          <p>该分类下没有文章</p>
        ) : (
          <div className="bg-gray-50 p-4 rounded">
            <ul className="list-disc pl-5">
              {categoryPosts.map((p: any) => (
                <li key={p.id} className={p.slug === postSlug ? "font-semibold" : ""}>
                  {p.title} <span className="text-gray-500">({p.slug})</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
      
      {!post ? (
        <p>未找到指定文章</p>
      ) : (
        <div className="space-y-4">
          <div className="bg-white border rounded-lg p-5">
            <h3 className="text-xl font-semibold mb-2">{post.title}</h3>
            <div className="text-sm text-gray-500 mb-3">发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}</div>
            
            {post.featuredImage?.node && (
              <div className="mb-4">
                <img 
                  src={post.featuredImage.node.sourceUrl} 
                  alt={post.featuredImage.node.altText || post.title}
                  className="w-full h-auto max-h-64 object-cover rounded"
                />
              </div>
            )}
            
            <div className="flex flex-wrap gap-2 mb-3">
              {post.categories?.nodes?.map((cat: any) => (
                <span key={cat.id} className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                  {cat.name}
                </span>
              ))}
              {post.tags?.nodes?.map((tag: any) => (
                <span key={tag.id} className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
                  {tag.name}
                </span>
              ))}
            </div>
            
            <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: post.content || '' }} />
          </div>
        </div>
      )}
      
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function TagPostQuery({ tagId, postId }: { tagId: string; postId: string }) {
  const { loading, error, data } = useQuery(GET_TAG_POST, {
    variables: { tagId, postId },
    skip: !tagId || !postId
  });

  if (!tagId || !postId) return <div>请输入标签ID和文章ID</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const post = data?.post;
  const tagPosts = data?.posts?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">标签下的文章详情 (通过ID)</h2>
      <div className="mb-4">
        <h3 className="text-lg font-medium mb-2">标签下的文章列表</h3>
        {tagPosts.length === 0 ? (
          <p>该标签下没有文章</p>
        ) : (
          <div className="bg-gray-50 p-4 rounded">
            <ul className="list-disc pl-5">
              {tagPosts.map((p: any) => (
                <li key={p.id} className={p.id === post?.id ? "font-semibold" : ""}>
                  {p.title} <span className="text-gray-500">({p.id})</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
      
      {!post ? (
        <p>未找到指定文章</p>
      ) : (
        <div className="space-y-4">
          <div className="bg-white border rounded-lg p-5">
            <h3 className="text-xl font-semibold mb-2">{post.title}</h3>
            <div className="text-sm text-gray-500 mb-3">发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}</div>
            
            {post.featuredImage?.node && (
              <div className="mb-4">
                <img 
                  src={post.featuredImage.node.sourceUrl} 
                  alt={post.featuredImage.node.altText || post.title}
                  className="w-full h-auto max-h-64 object-cover rounded"
                />
              </div>
            )}
            
            <div className="flex flex-wrap gap-2 mb-3">
              {post.categories?.nodes?.map((cat: any) => (
                <span key={cat.id} className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                  {cat.name}
                </span>
              ))}
              {post.tags?.nodes?.map((tag: any) => (
                <span key={tag.id} className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
                  {tag.name}
                </span>
              ))}
            </div>
            
            <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: post.content || '' }} />
          </div>
        </div>
      )}
      
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function TagPostBySlugQuery({ tagId, postSlug }: { tagId: string; postSlug: string }) {
  const { loading, error, data } = useQuery(GET_TAG_POST_BY_SLUG, {
    variables: { tagId, postSlug },
    skip: !tagId || !postSlug
  });

  if (!tagId || !postSlug) return <div>请输入标签ID和文章Slug</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const post = data?.post;
  const tagPosts = data?.posts?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">标签下的文章详情 (通过Slug)</h2>
      <div className="mb-4">
        <h3 className="text-lg font-medium mb-2">标签下的文章列表</h3>
        {tagPosts.length === 0 ? (
          <p>该标签下没有文章</p>
        ) : (
          <div className="bg-gray-50 p-4 rounded">
            <ul className="list-disc pl-5">
              {tagPosts.map((p: any) => (
                <li key={p.id} className={p.slug === postSlug ? "font-semibold" : ""}>
                  {p.title} <span className="text-gray-500">({p.slug})</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
      
      {!post ? (
        <p>未找到指定文章</p>
      ) : (
        <div className="space-y-4">
          <div className="bg-white border rounded-lg p-5">
            <h3 className="text-xl font-semibold mb-2">{post.title}</h3>
            <div className="text-sm text-gray-500 mb-3">发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}</div>
            
            {post.featuredImage?.node && (
              <div className="mb-4">
                <img 
                  src={post.featuredImage.node.sourceUrl} 
                  alt={post.featuredImage.node.altText || post.title}
                  className="w-full h-auto max-h-64 object-cover rounded"
                />
              </div>
            )}
            
            <div className="flex flex-wrap gap-2 mb-3">
              {post.categories?.nodes?.map((cat: any) => (
                <span key={cat.id} className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                  {cat.name}
                </span>
              ))}
              {post.tags?.nodes?.map((tag: any) => (
                <span key={tag.id} className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
                  {tag.name}
                </span>
              ))}
            </div>
            
            <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: post.content || '' }} />
          </div>
        </div>
      )}
      
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
} 

// 添加前端显示设置查询组件

function EnabledTaxonomiesQuery() {
  const { loading, error, data } = useQuery(GET_ENABLED_TAXONOMIES);

  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const enabledTaxonomies = data?.frontendDisplaySettings?.taxonomies || [];
  const allTaxonomies = data?.taxonomies?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">前端显示设置 - 启用的分类法</h2>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white border rounded-lg p-5">
          <h3 className="text-lg font-medium mb-3">已启用的分类法</h3>
          {enabledTaxonomies.length === 0 ? (
            <p className="text-gray-500">未启用任何分类法</p>
          ) : (
            <ul className="space-y-2">
              {enabledTaxonomies.map((tax: string) => (
                <li key={tax} className="flex items-center bg-green-50 p-2 rounded">
                  <span className="inline-block w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  <span className="font-medium">{tax}</span>
                </li>
              ))}
            </ul>
          )}
        </div>
        
        <div className="bg-white border rounded-lg p-5">
          <h3 className="text-lg font-medium mb-3">所有可用分类法</h3>
          <div className="max-h-96 overflow-y-auto">
            <table className="min-w-full">
              <thead>
                <tr className="bg-gray-50">
                  <th className="py-2 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                  <th className="py-2 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标签</th>
                  <th className="py-2 px-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {allTaxonomies.map((tax: any) => {
                  const isEnabled = enabledTaxonomies.includes(tax.name);
                  return (
                    <tr key={tax.name} className={isEnabled ? "bg-green-50" : ""}>
                      <td className="py-2 px-3 text-sm">
                        <code>{tax.name}</code>
                      </td>
                      <td className="py-2 px-3 text-sm">{tax.label}</td>
                      <td className="py-2 px-3 text-center">
                        {isEnabled ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            已启用
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            未启用
                          </span>
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function EnabledPostTypesQuery() {
  const { loading, error, data } = useQuery(GET_ENABLED_POST_TYPES);

  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const enabledPostTypes = data?.frontendDisplaySettings?.postTypes || [];
  const allPostTypes = data?.contentTypes?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">启用的文章类型</h2>
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
        <p className="text-sm text-blue-700">
          当前显示所有已注册的文章类型，前端启用的文章类型已标记。
        </p>
      </div>
      
      {allPostTypes.length === 0 ? (
        <p>没有找到文章类型</p>
      ) : (
        <div className="space-y-4">
          {allPostTypes.map((postType: any) => {
            const isEnabled = enabledPostTypes.includes(postType.name);
            return (
              <div 
                key={postType.name}
                className={`p-4 rounded border ${isEnabled ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'}`}
              >
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">
                    {postType.label}
                    {isEnabled && <span className="ml-2 text-xs font-normal bg-green-600 text-white px-2 py-1 rounded">已启用</span>}
                  </h3>
                  <div className="text-xs font-mono bg-gray-100 dark:bg-gray-800 p-1 rounded">
                    {postType.name}
                  </div>
                </div>
                
                {postType.description && (
                  <p className="text-sm mt-2 text-gray-600">{postType.description}</p>
                )}
                
                <div className="mt-3 grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="font-medium">分层结构:</span> {postType.hierarchical ? '是' : '否'}
                  </div>
                  <div>
                    <span className="font-medium">存档页:</span> {postType.hasArchive ? '是' : '否'}
                  </div>
                  <div>
                    <span className="font-medium">单数名称:</span> {postType.labels?.singularName}
                  </div>
                  <div>
                    <span className="font-medium">复数名称:</span> {postType.labels?.name}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function PostsByTaxQueryIdQuery({ taxonomy, termId }: { taxonomy: string; termId: string }) {
  const { loading, error, data } = useQuery(GET_POSTS_BY_TAX_QUERY_ID, {
    variables: { taxonomy, termId: termId ? termId.split(',') : [] },
    skip: !taxonomy || !termId
  });

  if (!taxonomy || !termId) return <div>请输入分类法类型和条目ID</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const posts = data?.posts?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">分类法 "{taxonomy}" 下的文章列表 (通过ID)</h2>
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          正在查询分类法: <strong>{taxonomy}</strong>
        </p>
        <p className="text-sm text-yellow-700 mt-1">
          分类法类型常见值: CATEGORY（分类）、TAG（标签）、POST_FORMAT（文章格式）、自定义分类法名称
        </p>
      </div>
      {posts.length === 0 ? (
        <p>没有找到分类法 {taxonomy} 下的文章</p>
      ) : (
        <div>
          <p className="mb-3">找到 {posts.length} 个 {taxonomy} 分类法的文章</p>
          <div className="space-y-4">
            {posts.map((post: any) => (
              <div key={post.id} className="border-b pb-4">
                <div className="mb-2 text-xs font-mono bg-gray-100 p-1 rounded inline-block">
                  类型: {post.__typename}
                </div>
                <h3 className="text-lg font-medium">{post.title}</h3>
                <div className="text-sm text-gray-500 mt-1">
                  发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}
                </div>
                <div className="mt-1">
                  <span className="text-sm font-medium">ID:</span> {post.id}
                </div>
                <div className="mt-1">
                  <span className="text-sm font-medium">Slug:</span> {post.slug}
                </div>
                <div className="mt-1">
                  <span className="text-sm font-medium">URI:</span> {post.uri}
                </div>
                {post.featuredImage?.node && (
                  <div className="mt-1">
                    <span className="text-sm font-medium">特色图片:</span>{' '}
                    {post.featuredImage.node.sourceUrl}
                  </div>
                )}
                {post.author?.node && (
                  <div className="mt-1">
                    <span className="text-sm font-medium">作者:</span>{' '}
                    {post.author.node.name}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
        </div>
  );
}

function PostsByTaxQuerySlugQuery({ taxonomy, slug }: { taxonomy: string; slug: string }) {
  const { loading, error, data } = useQuery(GET_POSTS_BY_TAX_QUERY_SLUG, {
    variables: { taxonomy, slugs: slug ? slug.split(',') : [] },
    skip: !taxonomy || !slug
  });

  if (!taxonomy || !slug) return <div>请输入分类法类型和条目Slug</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const posts = data?.posts?.nodes || [];

                  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">分类法 "{taxonomy}" 下的文章列表 (通过Slug)</h2>
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          正在查询分类法: <strong>{taxonomy}</strong>
        </p>
        <p className="text-sm text-yellow-700 mt-1">
          分类法类型常见值: CATEGORY（分类）、TAG（标签）、POST_FORMAT（文章格式）、自定义分类法名称
        </p>
      </div>
      {posts.length === 0 ? (
        <p>没有找到分类法 {taxonomy} 下的文章</p>
      ) : (
        <div>
          <p className="mb-3">找到 {posts.length} 个 {taxonomy} 分类法的文章</p>
          <div className="space-y-4">
            {posts.map((post: any) => (
              <div key={post.id} className="border-b pb-4">
                <div className="mb-2 text-xs font-mono bg-gray-100 p-1 rounded inline-block">
                  类型: {post.__typename}
                </div>
                <h3 className="text-lg font-medium">{post.title}</h3>
                <div className="text-sm text-gray-500 mt-1">
                  发布日期: {new Date(post.date).toLocaleDateString('zh-CN')}
                </div>
                <div className="mt-1">
                  <span className="text-sm font-medium">Slug:</span> {post.slug}
                </div>
                <div className="mt-1">
                  <span className="text-sm font-medium">URI:</span> {post.uri}
                </div>
                {post.featuredImage?.node && (
                  <div className="mt-1">
                    <span className="text-sm font-medium">特色图片:</span>{' '}
                    {post.featuredImage.node.sourceUrl}
                  </div>
                )}
                {post.author?.node && (
                  <div className="mt-1">
                    <span className="text-sm font-medium">作者:</span>{' '}
                    {post.author.node.name}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

// 获取并显示所有可用分类法选项的组件
function TaxonomyOptions() {
  const { loading, error, data } = useQuery(GET_TAXONOMIES);
  
  if (loading || error || !data?.taxonomies?.nodes) return null;
  
  // 过滤掉内置分类法，只保留自定义分类法
  const customTaxonomies = data.taxonomies.nodes.filter((taxonomy: any) => {
    const name = taxonomy.name;
    return name !== 'category' && name !== 'post_tag' && name !== 'post_format';
  });
  
  if (customTaxonomies.length === 0) return null;
  
  return (
    <>
      <optgroup label="自定义分类法">
        {customTaxonomies.map((taxonomy: any) => (
          <option key={taxonomy.name} value={`_${taxonomy.name.toUpperCase()}`}>
            {taxonomy.label} (_
            {taxonomy.name.toUpperCase()})
          </option>
        ))}
      </optgroup>
    </>
  );
}

// 使用taxQuery通过分类法ID获取文章详情
function PostsDetailByTaxQueryIdQuery({ taxonomy, termId }: { taxonomy: string; termId: string }) {
  const { loading, error, data } = useQuery(GET_POSTS_DETAIL_BY_TAX_QUERY_ID, {
    variables: { taxonomy, termId: termId ? termId.split(',') : [] },
    skip: !taxonomy || !termId
  });

  if (!taxonomy || !termId) return <div>请输入分类法类型和条目ID</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const posts = data?.posts?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">分类法 "{taxonomy}" 下的文章详情 (通过ID)</h2>
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          正在查询分类法: <strong>{taxonomy}</strong>
        </p>
        <p className="text-sm text-yellow-700 mt-1">
          查询ID: <strong>{termId}</strong>
        </p>
      </div>
      {posts.length === 0 ? (
        <p>没有找到分类法 {taxonomy} 下的文章</p>
      ) : (
        <div>
          <p className="mb-3">找到 {posts.length} 个文章</p>
          <div className="space-y-6">
            {posts.map((post: any) => (
              <div key={post.id} className="border p-4 rounded-lg shadow-sm">
                <div className="mb-2 text-xs font-mono bg-gray-100 p-1 rounded inline-block">
                  类型: {post.__typename}
                </div>
                <h3 className="text-xl font-medium">{post.title}</h3>
                <div className="flex flex-wrap gap-2 mt-2">
                  <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                    发布于: {new Date(post.date).toLocaleDateString('zh-CN')}
                          </span>
                  <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded">
                    ID: {post.id}
                  </span>
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                    Slug: {post.slug}
                  </span>
                </div>

                <div className="mt-3">
                  <span className="text-sm font-medium">分类:</span>{' '}
                  {post.categories?.nodes?.map((cat: any) => (
                    <span key={cat.id} className="inline-block bg-gray-100 rounded px-2 py-1 text-xs mr-1">
                      {cat.name}
                    </span>
                  ))}
                </div>

                <div className="mt-3">
                  <span className="text-sm font-medium">标签:</span>{' '}
                  {post.tags?.nodes?.map((tag: any) => (
                    <span key={tag.id} className="inline-block bg-gray-100 rounded px-2 py-1 text-xs mr-1">
                      {tag.name}
                    </span>
                  ))}
                </div>

                {post.author?.node && (
                  <div className="mt-3 flex items-center">
                    <span className="text-sm font-medium mr-2">作者:</span>
                    <div className="flex items-center">
                      {post.author.node.avatar?.url && (
                        <img 
                          src={post.author.node.avatar.url} 
                          alt={post.author.node.name} 
                          className="w-6 h-6 rounded-full mr-2"
                        />
                      )}
                      <span>{post.author.node.name}</span>
                    </div>
                  </div>
                )}

                {post.featuredImage?.node && (
                  <div className="mt-3">
                    <span className="text-sm font-medium block mb-1">特色图片:</span>
                    <img 
                      src={post.featuredImage.node.sourceUrl} 
                      alt={post.featuredImage.node.altText || post.title} 
                      className="max-w-xs rounded"
                    />
                  </div>
                )}
                
                <div className="mt-4">
                  <span className="text-sm font-medium block mb-1">摘要:</span>
                  <div 
                    className="text-sm text-gray-700 bg-gray-50 p-3 rounded"
                    dangerouslySetInnerHTML={{ __html: post.excerpt || '无摘要' }}
                  />
                </div>
                
                <div className="mt-4">
                  <span className="text-sm font-medium block mb-2">内容:</span>
                  <div 
                    className="text-sm text-gray-700 bg-gray-50 p-3 rounded overflow-auto max-h-60"
                    dangerouslySetInnerHTML={{ __html: post.content || '无内容' }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

// 使用taxQuery通过分类法Slug获取文章详情
function PostsDetailByTaxQuerySlugQuery({ taxonomy, slug }: { taxonomy: string; slug: string }) {
  const { loading, error, data } = useQuery(GET_POSTS_DETAIL_BY_TAX_QUERY_SLUG, {
    variables: { taxonomy, slugs: slug ? slug.split(',') : [] },
    skip: !taxonomy || !slug
  });

  if (!taxonomy || !slug) return <div>请输入分类法类型和条目Slug</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const posts = data?.posts?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">分类法 "{taxonomy}" 下的文章详情 (通过Slug)</h2>
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          正在查询分类法: <strong>{taxonomy}</strong>
        </p>
        <p className="text-sm text-yellow-700 mt-1">
          查询Slug: <strong>{slug}</strong>
        </p>
          </div>
      {posts.length === 0 ? (
        <p>没有找到分类法 {taxonomy} 下的文章</p>
      ) : (
        <div>
          <p className="mb-3">找到 {posts.length} 个文章</p>
          <div className="space-y-6">
            {posts.map((post: any) => (
              <div key={post.id} className="border p-4 rounded-lg shadow-sm">
                <div className="mb-2 text-xs font-mono bg-gray-100 p-1 rounded inline-block">
                  类型: {post.__typename}
        </div>
                <h3 className="text-xl font-medium">{post.title}</h3>
                <div className="flex flex-wrap gap-2 mt-2">
                  <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                    发布于: {new Date(post.date).toLocaleDateString('zh-CN')}
                          </span>
                  <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded">
                    ID: {post.id}
                  </span>
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                    Slug: {post.slug}
                  </span>
      </div>
      
                <div className="mt-3">
                  <span className="text-sm font-medium">分类:</span>{' '}
                  {post.categories?.nodes?.map((cat: any) => (
                    <span key={cat.id} className="inline-block bg-gray-100 rounded px-2 py-1 text-xs mr-1">
                      {cat.name}
                    </span>
                  ))}
                </div>

                <div className="mt-3">
                  <span className="text-sm font-medium">标签:</span>{' '}
                  {post.tags?.nodes?.map((tag: any) => (
                    <span key={tag.id} className="inline-block bg-gray-100 rounded px-2 py-1 text-xs mr-1">
                      {tag.name}
                    </span>
                  ))}
                </div>

                {post.author?.node && (
                  <div className="mt-3 flex items-center">
                    <span className="text-sm font-medium mr-2">作者:</span>
                    <div className="flex items-center">
                      {post.author.node.avatar?.url && (
                        <img 
                          src={post.author.node.avatar.url} 
                          alt={post.author.node.name} 
                          className="w-6 h-6 rounded-full mr-2"
                        />
                      )}
                      <span>{post.author.node.name}</span>
                    </div>
                  </div>
                )}

                {post.featuredImage?.node && (
                  <div className="mt-3">
                    <span className="text-sm font-medium block mb-1">特色图片:</span>
                    <img 
                      src={post.featuredImage.node.sourceUrl} 
                      alt={post.featuredImage.node.altText || post.title} 
                      className="max-w-xs rounded"
                    />
                  </div>
                )}
                
                <div className="mt-4">
                  <span className="text-sm font-medium block mb-1">摘要:</span>
                  <div 
                    className="text-sm text-gray-700 bg-gray-50 p-3 rounded"
                    dangerouslySetInnerHTML={{ __html: post.excerpt || '无摘要' }}
                  />
                </div>
                
                <div className="mt-4">
                  <span className="text-sm font-medium block mb-2">内容:</span>
                  <div 
                    className="text-sm text-gray-700 bg-gray-50 p-3 rounded overflow-auto max-h-60"
                    dangerouslySetInnerHTML={{ __html: post.content || '无内容' }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

// 添加用户列表查询组件
function UsersQuery() {
  const { loading, error, data } = useQuery(GET_USERS);

  // 定义用户类型接口
  interface User {
    id: string;
    name: string;
    email?: string;
    nickname?: string;
    description?: string;
    url?: string;
    avatar?: {
      url?: string;
    };
  }

  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  return (
    <div className="bg-white p-4 rounded-lg shadow">
      <h2 className="text-xl font-semibold mb-4">用户列表</h2>
      
      {data && data.users && data.users.nodes ? (
        <div className="space-y-4">
          <p className="text-gray-500">找到 {data.users.nodes.length} 位用户</p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {data.users.nodes.map((user: User) => (
              <div key={user.id} className="border rounded p-3">
                <div className="flex items-center space-x-3">
                  {user.avatar?.url && (
                    <img src={user.avatar.url} alt={user.name} className="w-10 h-10 rounded-full" />
                  )}
                  <div>
                    <h3 className="font-medium">{user.name}</h3>
                    {user.nickname && <p className="text-sm text-gray-500">{user.nickname}</p>}
          </div>
        </div>
                {user.description && (
                  <p className="text-sm mt-2 text-gray-600">{user.description}</p>
                )}
                <div className="mt-2 text-xs text-gray-500">
                  <p>ID: {user.id}</p>
                  {user.email && <p>邮箱: {user.email}</p>}
                  {user.url && <p>网站: {user.url}</p>}
      </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="text-gray-500">没有找到用户</div>
      )}
      
      <div className="mt-4 p-3 bg-gray-50 rounded">
        <h3 className="text-sm font-medium mb-2">GraphQL 查询</h3>
        <pre className="text-xs overflow-x-auto">
          {`query GetUsers {
  users(first: 10) {
    nodes {
      id
      name
      email
      nickname
      description
      url
      avatar {
        url
      }
    }
  }
}`}
        </pre>
      </div>
    </div>
  );
}

function AllSettingsQuery() {
  const { loading, error, data } = useQuery(GET_ALL_SETTINGS);

  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const allSettings = data?.allSettings || {};
  const settingsEntries = Object.entries(allSettings);

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">所有设置</h2>
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          正在查询所有WordPress设置。
        </p>
      </div>
      {settingsEntries.length > 0 ? (
        <div>
          <p className="mb-3">找到 {settingsEntries.length} 个设置项</p>
          <div className="space-y-4">
            {settingsEntries.map(([key, value]) => (
              <div key={key} className="border p-4 rounded-lg shadow-sm">
                <div className="mb-2 text-xs font-mono bg-gray-100 p-1 rounded inline-block">
                  设置名称: {key}
                </div>
                <h3 className="text-lg font-medium">{key}</h3>
                <div className="text-sm text-gray-500 mt-1">
                  设置值: {JSON.stringify(value)}
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <p>没有找到设置项</p>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function GeneralSettingsQuery() {
  const { loading, error, data } = useQuery(GET_GENERAL_SETTINGS);

  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const generalSettings = data?.generalSettings || {};
  const settingsEntries = Object.entries(generalSettings);

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">常规设置</h2>
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          正在查询WordPress常规设置。
        </p>
      </div>
      {settingsEntries.length > 0 ? (
        <div>
          <p className="mb-3">找到 {settingsEntries.length} 个常规设置项</p>
          <div className="space-y-4">
            {settingsEntries.map(([key, value]) => (
              <div key={key} className="border p-4 rounded-lg shadow-sm">
                <div className="mb-2 text-xs font-mono bg-gray-100 p-1 rounded inline-block">
                  设置名称: {key}
                </div>
                <h3 className="text-lg font-medium">{key}</h3>
                <div className="text-sm text-gray-500 mt-1">
                  设置值: {JSON.stringify(value)}
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <p>没有找到常规设置项</p>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function ReadingSettingsQuery() {
  const { loading, error, data } = useQuery(GET_READING_SETTINGS);

  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const readingSettings = data?.readingSettings || {};
  const settingsEntries = Object.entries(readingSettings);

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">阅读设置</h2>
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          正在查询WordPress阅读设置。
        </p>
      </div>
      {settingsEntries.length > 0 ? (
        <div>
          <p className="mb-3">找到 {settingsEntries.length} 个阅读设置项</p>
          <div className="space-y-4">
            {settingsEntries.map(([key, value]) => (
              <div key={key} className="border p-4 rounded-lg shadow-sm">
                <div className="mb-2 text-xs font-mono bg-gray-100 p-1 rounded inline-block">
                  设置名称: {key}
                </div>
                <h3 className="text-lg font-medium">{key}</h3>
                <div className="text-sm text-gray-500 mt-1">
                  设置值: {JSON.stringify(value)}
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <p>没有找到阅读设置项</p>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function DiscussionSettingsQuery() {
  const { loading, error, data } = useQuery(GET_DISCUSSION_SETTINGS);

  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const discussionSettings = data?.discussionSettings || {};
  const settingsEntries = Object.entries(discussionSettings);

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">讨论设置</h2>
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          正在查询WordPress讨论设置。
        </p>
      </div>
      {settingsEntries.length > 0 ? (
        <div>
          <p className="mb-3">找到 {settingsEntries.length} 个讨论设置项</p>
          <div className="space-y-4">
            {settingsEntries.map(([key, value]) => (
              <div key={key} className="border p-4 rounded-lg shadow-sm">
                <div className="mb-2 text-xs font-mono bg-gray-100 p-1 rounded inline-block">
                  设置名称: {key}
                </div>
                <h3 className="text-lg font-medium">{key}</h3>
                <div className="text-sm text-gray-500 mt-1">
                  设置值: {JSON.stringify(value)}
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <p>没有找到讨论设置项</p>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function WritingSettingsQuery() {
  const { loading, error, data } = useQuery(GET_WRITING_SETTINGS);

  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const writingSettings = data?.writingSettings || {};
  const settingsEntries = Object.entries(writingSettings);

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">写作设置</h2>
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          正在查询WordPress写作设置。
        </p>
      </div>
      {settingsEntries.length > 0 ? (
        <div>
          <p className="mb-3">找到 {settingsEntries.length} 个写作设置项</p>
          <div className="space-y-4">
            {settingsEntries.map(([key, value]) => (
              <div key={key} className="border p-4 rounded-lg shadow-sm">
                <div className="mb-2 text-xs font-mono bg-gray-100 p-1 rounded inline-block">
                  设置名称: {key}
                </div>
                <h3 className="text-lg font-medium">{key}</h3>
                <div className="text-sm text-gray-500 mt-1">
                  设置值: {JSON.stringify(value)}
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <p>没有找到写作设置项</p>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function PostCommentsQuery({ postId }: { postId: string }) {
  const { loading, error, data } = useQuery(GET_POST_COMMENTS, {
    variables: { postId },
    skip: !postId
  });

  if (!postId) return <div>请输入文章ID</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const post = data?.post;
  const comments = post?.comments?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">文章评论列表</h2>
      {!post ? (
        <p>未找到文章</p>
      ) : (
        <div>
          <div className="mb-4 bg-gray-50 p-3 rounded">
            <h3 className="text-lg font-medium">{post.title}</h3>
            <p className="text-sm text-gray-500">评论数: {post.commentCount || 0}</p>
            <p className="text-sm text-gray-500">评论状态: {post.commentStatus}</p>
          </div>
          
          {comments.length === 0 ? (
            <p>没有找到评论</p>
          ) : (
            <div className="space-y-4">
              {comments.map((comment: any) => (
                <div key={comment.id} className="border p-4 rounded shadow-sm">
                  <div className="flex items-start">
                    {comment.author?.node?.avatar?.url && (
                      <img 
                        src={comment.author.node.avatar.url} 
                        alt={comment.author.node.name || '匿名用户'} 
                        className="w-10 h-10 rounded-full mr-3"
                      />
                    )}
                    <div>
                      <div className="font-medium">
                        {comment.author?.node?.name || '匿名用户'}
                        {comment.author?.node?.url && (
                          <a href={comment.author.node.url} className="text-blue-500 text-sm ml-2" target="_blank">
                            网站
                          </a>
                        )}
                      </div>
                      <div className="text-xs text-gray-500">
                        {new Date(comment.date).toLocaleString('zh-CN')}
                      </div>
                    </div>
                  </div>
                  <div 
                    className="mt-2 text-sm text-gray-700 prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ __html: comment.content }}
                  />
                  {comment.parentId && (
                    <div className="mt-2 text-xs bg-gray-100 px-2 py-1 inline-block rounded">
                      回复评论: {comment.parentId}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function CommentQuery({ id }: { id: string }) {
  const { loading, error, data } = useQuery(GET_COMMENT, {
    variables: { id },
    skip: !id
  });

  if (!id) return <div>请输入评论ID</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const comment = data?.comment;

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">评论详情</h2>
      {!comment ? (
        <p>未找到评论</p>
      ) : (
        <div className="space-y-4">
          <div className="border p-4 rounded shadow-sm">
            <div className="mb-3">
              <span className="text-sm text-gray-500">评论ID: {comment.id}</span>
              <div className="text-sm text-gray-500">数据库ID: {comment.databaseId}</div>
            </div>
            
            <div className="flex items-start">
              {comment.author?.node?.avatar?.url && (
                <img 
                  src={comment.author.node.avatar.url} 
                  alt={comment.author.node.name || '匿名用户'} 
                  className="w-10 h-10 rounded-full mr-3"
                />
              )}
              <div>
                <div className="font-medium">
                  {comment.author?.node?.name || '匿名用户'}
                  {comment.author?.node?.url && (
                    <a href={comment.author.node.url} className="text-blue-500 text-sm ml-2" target="_blank">
                      网站
                    </a>
                  )}
                </div>
                <div className="text-xs text-gray-500">
                  {new Date(comment.date).toLocaleString('zh-CN')}
                </div>
              </div>
            </div>
            
            <div 
              className="mt-3 text-sm text-gray-700 prose prose-sm max-w-none"
              dangerouslySetInnerHTML={{ __html: comment.content }}
            />
            
            {comment.parentId && (
              <div className="mt-2 text-xs bg-gray-100 px-2 py-1 inline-block rounded">
                回复评论: {comment.parentId}
              </div>
            )}
            
            <div className="mt-3 pt-3 border-t">
              <div className="text-sm text-gray-500">评论状态: <span className="font-medium">{comment.status}</span></div>
              {comment.post && (
                <div className="mt-1">
                  <span className="text-sm text-gray-500">所属文章: </span>
                  <a href={`?postId=${comment.post.id}`} className="text-blue-500 text-sm">
                    {comment.post.title}
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function CommentRepliesQuery({ id }: { id: string }) {
  const { loading, error, data } = useQuery(GET_COMMENT_REPLIES, {
    variables: { id },
    skip: !id
  });

  if (!id) return <div>请输入评论ID</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const comment = data?.comment;
  const replies = comment?.replies?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">评论回复列表</h2>
      {replies.length === 0 ? (
        <p>没有找到回复</p>
      ) : (
        <div className="space-y-4">
          {replies.map((reply: any) => (
            <div key={reply.id} className="border-b pb-4">
              <h3 className="text-lg font-medium">{reply.title}</h3>
              <div className="text-sm text-gray-500 mt-1">
                发布日期: {new Date(reply.date).toLocaleDateString('zh-CN')}
              </div>
              <div className="mt-1">
                <span className="text-sm font-medium">回复者:</span> {reply.author?.node?.name}
              </div>
              <div className="mt-2 text-sm text-gray-700">
                {reply.content}
              </div>
            </div>
          ))}
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

function CommentsByStatusQuery({ status }: { status: string }) {
  const { loading, error, data } = useQuery(GET_COMMENTS_BY_STATUS, {
    variables: { status: [status] },
    skip: !status
  });

  if (!status) return <div>请选择评论状态</div>;
  if (loading) return <div className="text-center py-4">加载中...</div>;
  if (error) return <div className="text-red-500">错误: {error.message}</div>;

  const comments = data?.comments?.nodes || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">评论列表 (状态: {status})</h2>
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-700">
          注意：只有管理员和编辑等有适当权限的用户才能查看非已批准状态的评论。
        </p>
      </div>
      
      {comments.length === 0 ? (
        <p>没有找到评论</p>
      ) : (
        <div>
          <p className="text-sm text-gray-500 mb-3">找到 {comments.length} 条评论</p>
          <div className="space-y-4">
            {comments.map((comment: any) => (
              <div key={comment.id} className="border p-4 rounded shadow-sm">
                <div className="flex items-start">
                  {comment.author?.node?.avatar?.url && (
                    <img 
                      src={comment.author.node.avatar.url} 
                      alt={comment.author.node.name || '匿名用户'} 
                      className="w-10 h-10 rounded-full mr-3"
                    />
                  )}
                  <div>
                    <div className="font-medium">
                      {comment.author?.node?.name || '匿名用户'}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(comment.date).toLocaleString('zh-CN')}
                    </div>
                  </div>
                </div>
                
                <div 
                  className="mt-2 text-sm text-gray-700 prose prose-sm max-w-none"
                  dangerouslySetInnerHTML={{ __html: comment.content }}
                />
                
                {comment.post && (
                  <div className="mt-3 pt-2 border-t text-xs text-gray-500">
                    文章: <a href="#" className="text-blue-500">{comment.post.title}</a>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
      <pre className="mt-6 bg-gray-100 p-4 rounded overflow-auto text-xs">
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
}

// 查询示例部分，修改自定义文章类型查询
const customPostsQueryExample = `
# 查询自定义内容类型列表
query GetCustomPostsList($type: ContentTypeEnum!) {
  contentNodes(first: 10, where: { contentTypes: [$type] }) {
    nodes {
      __typename
      id
      slug
      ... on NodeWithTitle {
        title
      }
      ... on NodeWithExcerpt {
        excerpt
      }
      ... on Note {
        title
        content
        excerpt
      }
    }
  }
}

# 变量
{
  "type": "NOTE" # 自定义内容类型名称，需要大写
}
`;

// 查询自定义分类法的示例
const taxonomyQueryExample = `
# 查询自定义分类法列表
query GetTaxonomyTerms($taxonomy: TaxonomyEnum!) {
  terms(first: 10, where: { taxonomy: $taxonomy }) {
    nodes {
      id
      name
      slug
      uri
    }
  }
}

# 变量
{
  "taxonomy": "COMPANY" # 自定义分类法名称，需要大写，不需要添加下划线前缀
}
`;

// 文章查询示例
const postQueryExample = `
# 根据短UUID查询内容
query GetContentNodeByUuid($uuid: String!) {
  contentNodeByUuid(uuid: $uuid) {
    __typename
    id
    databaseId
    slug
    uri
    date
    ... on NodeWithTitle {
      title
    }
    ... on NodeWithExcerpt {
      excerpt
    }
    ... on NodeWithContentEditor {
      content
    }
    ... on NodeWithComments {
      commentCount
      commentStatus
    }
    ... on Note {
      title
      content
      excerpt
    }
  }
}

# 变量
{
  "uuid": "230515-123456" # 文章短UUID，格式：YYMMDD-123456
}
`;