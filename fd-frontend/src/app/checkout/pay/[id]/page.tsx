'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useQuery, useMutation } from '@apollo/client';
import { GET_PAYMENT_ORDER } from '../../../../lib/graphql/queries';
import { GET_PAYMENT_URL, CHECK_PAYMENT_STATUS } from '../../../../lib/graphql/mutations';
import PaymentProcessor from '../../../../components/payment/PaymentProcessor';
import BalancePaymentForm from '../../../../components/payment/BalancePaymentForm';
import PointsPaymentForm from '../../../../components/payment/PointsPaymentForm';
import Link from 'next/link';
import MainLayout from '@/components/layouts/MainLayout';

enum PaymentStep {
  LOADING,
  PAYMENT_PROCESSOR,
  ERROR,
  PAYMENT_SUCCESS
}

export default function PayOrderPage() {
  const params = useParams();
  const router = useRouter();
  const orderId = params.id as string;
  
  const [currentStep, setCurrentStep] = useState<PaymentStep>(PaymentStep.LOADING);
  const [orderDetails, setOrderDetails] = useState<any>(null);
  const [error, setError] = useState('');
  
  // 获取订单信息
  const { loading, error: orderError, data } = useQuery(GET_PAYMENT_ORDER, {
    variables: { id: orderId },
    skip: !orderId
  });
  
  // 检查支付状态
  const [checkPaymentStatus] = useMutation(CHECK_PAYMENT_STATUS);
  
  // 处理订单数据
  useEffect(() => {
    if (!loading && data?.paymentOrder) {
      setOrderDetails(data.paymentOrder);
      
      // 如果订单已支付，直接显示成功
      if (data.paymentOrder.paymentStatus === 'paid') {
        setCurrentStep(PaymentStep.PAYMENT_SUCCESS);
      } else if (data.paymentOrder.paymentStatus === 'unpaid') {
        setCurrentStep(PaymentStep.PAYMENT_PROCESSOR);
      } else {
        setError('订单状态不允许支付');
        setCurrentStep(PaymentStep.ERROR);
      }
    } else if (orderError) {
      setError(orderError.message);
      setCurrentStep(PaymentStep.ERROR);
    }
  }, [loading, data, orderError]);
  
  // 处理支付成功
  const handlePaymentSuccess = () => {
    setCurrentStep(PaymentStep.PAYMENT_SUCCESS);
  };
  
  // 处理余额支付成功
  const handleBalancePaymentSuccess = (newOrderId: string, orderNumber: string) => {
    setCurrentStep(PaymentStep.PAYMENT_SUCCESS);
  };
  
  // 处理积分支付成功
  const handlePointsPaymentSuccess = (newOrderId: string, orderNumber: string) => {
    setCurrentStep(PaymentStep.PAYMENT_SUCCESS);
  };
  
  // 处理取消支付
  const handleCancelPayment = () => {
    router.push('/checkout/orders');
  };
  
  // 处理错误
  const handleError = (error: Error) => {
    setError(error.message);
    setCurrentStep(PaymentStep.ERROR);
  };
  
  // 返回订单列表
  const goToOrders = () => {
    router.push('/checkout/orders');
  };
  
  // 根据当前步骤渲染不同内容
  const renderContent = () => {
    switch (currentStep) {
      case PaymentStep.LOADING:
        return (
          <div className="flex justify-center items-center p-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
            <span className="ml-3">加载订单信息...</span>
          </div>
        );
        
      case PaymentStep.PAYMENT_PROCESSOR:
        if (orderDetails?.paymentMethod === 'wallet') {
          return (
            <BalancePaymentForm
              title={orderDetails.title}
              amount={orderDetails.amount}
              description={orderDetails.description}
              orderId={orderId}
              onPaymentSuccess={handleBalancePaymentSuccess}
              onError={handleError}
            />
          );
        } else if (orderDetails?.paymentMethod === 'points') {
          return (
            <PointsPaymentForm
              title={orderDetails.title}
              amount={orderDetails.amount}
              description={orderDetails.description}
              orderId={orderId}
              onPaymentSuccess={handlePointsPaymentSuccess}
              onError={handleError}
            />
          );
        } else {
          return (
            <PaymentProcessor
              orderId={orderId}
              paymentMethod={orderDetails?.paymentMethod || ''}
              onPaymentSuccess={handlePaymentSuccess}
              onCancel={handleCancelPayment}
            />
          );
        }
        
      case PaymentStep.ERROR:
        return (
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto flex items-center justify-center rounded-full bg-red-100">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="text-xl font-bold mt-4">支付出错</h2>
            <p className="text-gray-500 mt-2">
              {error || '处理订单时发生错误'}
            </p>
            <button
              onClick={goToOrders}
              className="mt-6 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
            >
              返回订单列表
            </button>
          </div>
        );
        
      case PaymentStep.PAYMENT_SUCCESS:
        return (
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto flex items-center justify-center rounded-full bg-green-100">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-xl font-bold mt-4">支付成功！</h2>
            <p className="text-gray-500 mt-2">
              您的订单已支付成功
            </p>
            <button
              onClick={goToOrders}
              className="mt-6 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
            >
              查看全部订单
            </button>
          </div>
        );
        
      default:
        return null;
    }
  };
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">订单支付</h1>
          <Link href="/checkout/orders" className="text-sm text-primary-600 hover:underline">
            &larr; 返回订单列表
          </Link>
        </div>
        
        {orderDetails && currentStep !== PaymentStep.ERROR && currentStep !== PaymentStep.PAYMENT_SUCCESS && (
          <div className="bg-white dark:bg-gray-800 p-6 rounded-md shadow-sm border border-gray-200 mb-6">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-semibold">{orderDetails.title}</h3>
                <div className="text-gray-500 text-sm mt-1">
                  订单号: {orderDetails.orderNumber}
                </div>
              </div>
              <div className="text-2xl font-bold text-primary-600">
                ¥{parseFloat(orderDetails.amount).toFixed(2)}
              </div>
            </div>
          </div>
        )}
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          {renderContent()}
        </div>
      </div>
    </MainLayout>
  );
} 