'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import OrderForm from '../../components/payment/OrderForm';
import PaymentProcessor from '../../components/payment/PaymentProcessor';
import BalancePaymentForm from '../../components/payment/BalancePaymentForm';
import PointsPaymentForm from '../../components/payment/PointsPaymentForm';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/layouts/MainLayout';

enum PaymentStep {
  ORDER_FORM,
  PAYMENT_PROCESSOR,
  PAYMENT_SUCCESS
}

export default function PaymentPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentStep, setCurrentStep] = useState<PaymentStep>(PaymentStep.ORDER_FORM);
  const [orderId, setOrderId] = useState('');
  const [orderAmount, setOrderAmount] = useState(0);
  const [orderTitle, setOrderTitle] = useState('');
  const [orderDescription, setOrderDescription] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('');
  const [orderNumber, setOrderNumber] = useState('');
  const [error, setError] = useState('');
  const [productType, setProductType] = useState('');
  const [productId, setProductId] = useState('');
  const [metadata, setMetadata] = useState('');
  
  // 从URL参数中获取商品信息
  useEffect(() => {
    if (searchParams) {
      const title = searchParams.get('title');
      const amount = searchParams.get('amount');
      const description = searchParams.get('description');
      const product_type = searchParams.get('product_type');
      const product_id = searchParams.get('product_id');
      const metadataParam = searchParams.get('metadata');
      
      if (title) setOrderTitle(title);
      if (amount) setOrderAmount(parseFloat(amount));
      if (description) setOrderDescription(description);
      if (product_type) setProductType(product_type);
      if (product_id) setProductId(product_id);
      if (metadataParam) setMetadata(metadataParam);
    }
  }, [searchParams]);
  
  // 处理订单创建成功
  const handleOrderCreated = (newOrderId: string, method: string, orderData: any = {}) => {
    setOrderId(newOrderId);
    setPaymentMethod(method);
    
    // 保存订单信息以备余额支付使用
    setOrderAmount(orderData.amount || 0);
    setOrderTitle(orderData.title || '');
    setOrderDescription(orderData.description || '');
    
    setCurrentStep(PaymentStep.PAYMENT_PROCESSOR);
  };
  
  // 处理支付成功
  const handlePaymentSuccess = () => {
    setCurrentStep(PaymentStep.PAYMENT_SUCCESS);

    // 如果是会员升级，支付成功后跳转到会员升级成功页面
    if (productType === 'member_level') {
      // 从metadata中提取returnUrl
      let returnUrl = null;
      try {
        if (metadata) {
          const metadataObj = JSON.parse(metadata);
          returnUrl = metadataObj.returnUrl;
        }
      } catch (e) {
        console.error('解析metadata失败:', e);
      }

      const successUrl = returnUrl
        ? `/membership/payment-success?returnUrl=${encodeURIComponent(returnUrl)}`
        : '/membership/payment-success';
      router.push(successUrl);
      return;
    }
  };
  
  // 处理余额支付成功
  const handleBalancePaymentSuccess = (newOrderId: string, newOrderNumber: string) => {
    setOrderId(newOrderId);
    setOrderNumber(newOrderNumber);
    setCurrentStep(PaymentStep.PAYMENT_SUCCESS);

    // 如果是会员升级，支付成功后跳转到会员升级成功页面
    if (productType === 'member_level') {
      // 从metadata中提取returnUrl
      let returnUrl = null;
      try {
        if (metadata) {
          const metadataObj = JSON.parse(metadata);
          returnUrl = metadataObj.returnUrl;
        }
      } catch (e) {
        console.error('解析metadata失败:', e);
      }

      const successUrl = returnUrl
        ? `/membership/payment-success?returnUrl=${encodeURIComponent(returnUrl)}`
        : '/membership/payment-success';
      router.push(successUrl);
      return;
    }
  };
  
  // 处理取消支付
  const handleCancelPayment = () => {
    setCurrentStep(PaymentStep.ORDER_FORM);
    setOrderId('');
    setPaymentMethod('');
  };
  
  // 处理错误
  const handleError = (error: Error) => {
    setError(error.message);
  };
  
  // 返回首页
  const goToHome = () => {
    router.push('/');
  };
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">支付中心</h1>
        
        {error && (
          <div className="bg-red-50 text-red-600 p-4 rounded-md mb-6">
            <p>{error}</p>
            <button 
              onClick={() => setError('')}
              className="text-sm underline mt-2"
            >
              关闭
            </button>
          </div>
        )}
        
        {currentStep === PaymentStep.ORDER_FORM && (
          <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg p-6">
            <OrderForm 
              onOrderCreated={handleOrderCreated} 
              productTitle={orderTitle} 
              productAmount={orderAmount} 
              productDescription={orderDescription}
              productType={productType}
              productId={productId}
              metadata={metadata}
            />
          </div>
        )}
        
        {currentStep === PaymentStep.PAYMENT_PROCESSOR && (
          <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg p-6">
            {paymentMethod === 'wallet' ? (
              <BalancePaymentForm
                title={orderTitle || `订单 #${orderId}`}
                amount={orderAmount}
                description={orderDescription}
                orderId={orderId}
                onPaymentSuccess={handleBalancePaymentSuccess}
                onError={handleError}
                productType={productType}
                productId={productId}
                metadata={metadata}
              />
            ) : paymentMethod === 'points' ? (
              <PointsPaymentForm
                title={orderTitle || `订单 #${orderId}`}
                amount={orderAmount}
                description={orderDescription}
                orderId={orderId}
                onPaymentSuccess={handleBalancePaymentSuccess}
                onError={handleError}
                productType={productType}
                productId={productId}
                metadata={metadata}
              />
            ) : (
              <PaymentProcessor
                orderId={orderId}
                paymentMethod={paymentMethod}
                onPaymentSuccess={handlePaymentSuccess}
                onCancel={handleCancelPayment}
                productType={productType}
                productId={productId}
                metadata={metadata}
              />
            )}
          </div>
        )}
        
        {currentStep === PaymentStep.PAYMENT_SUCCESS && (
          <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg p-6 text-center">
            <div className="w-16 h-16 mx-auto flex items-center justify-center rounded-full bg-green-100">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-xl font-bold mt-4">支付成功！</h2>
            <p className="text-gray-500 mt-2">
              您的订单{orderNumber ? `（#${orderNumber}）` : ''}已支付成功
            </p>
            <button
              onClick={goToHome}
              className="mt-6 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
            >
              返回首页
            </button>
          </div>
        )}
      </div>
    </MainLayout>
  );
} 