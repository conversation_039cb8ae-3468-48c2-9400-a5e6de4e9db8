'use client';

import React, { useState } from 'react';
import { useQuery } from '@apollo/client';
import { GET_USER_ORDERS } from '../../../lib/graphql/queries';
import Link from 'next/link';
import MainLayout from '@/components/layouts/MainLayout';

type OrderStatus = 'UNPAID' | 'PAID' | 'CANCELLED' | 'REFUNDED' | null;

export default function OrdersPage() {
  const [status, setStatus] = useState<OrderStatus>(null);
  const { loading, error, data, fetchMore } = useQuery(GET_USER_ORDERS, {
    variables: { 
      first: 10, 
      status: status 
    }
  });
  
  const handleLoadMore = () => {
    if (data?.viewer?.orders?.pageInfo?.hasNextPage) {
      fetchMore({
        variables: {
          first: 10,
          after: data.viewer.orders.pageInfo.endCursor,
          status: status
        },
        updateQuery: (prev, { fetchMoreResult }) => {
          if (!fetchMoreResult) return prev;
          return {
            viewer: {
              ...prev.viewer,
              orders: {
                ...fetchMoreResult.viewer.orders,
                nodes: [
                  ...(prev.viewer.orders.nodes || []),
                  ...(fetchMoreResult.viewer.orders.nodes || [])
                ]
              }
            }
          };
        }
      });
    }
  };
  
  const handleStatusChange = (newStatus: OrderStatus) => {
    setStatus(newStatus);
  };
  
  const getStatusText = (status: string) => {
    switch (status) {
      case 'unpaid': return '未支付';
      case 'paid': return '已支付';
      case 'cancelled': return '已取消';
      case 'refunded': return '已退款';
      default: return '未知状态';
    }
  };
  
  const getStatusClass = (status: string) => {
    switch (status) {
      case 'unpaid': return 'bg-yellow-100 text-yellow-800';
      case 'paid': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      case 'refunded': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">我的订单</h1>
        
        <div className="mb-6 flex flex-wrap gap-2">
          <button
            onClick={() => handleStatusChange(null)}
            className={`px-4 py-2 rounded-md ${
              status === null ? 'bg-primary-500 text-white' : 'bg-gray-200'
            }`}
          >
            全部
          </button>
          <button
            onClick={() => handleStatusChange('PAID')}
            className={`px-4 py-2 rounded-md ${
              status === 'PAID' ? 'bg-primary-500 text-white' : 'bg-gray-200'
            }`}
          >
            已支付
          </button>
          <button
            onClick={() => handleStatusChange('UNPAID')}
            className={`px-4 py-2 rounded-md ${
              status === 'UNPAID' ? 'bg-primary-500 text-white' : 'bg-gray-200'
            }`}
          >
            未支付
          </button>
          <button
            onClick={() => handleStatusChange('CANCELLED')}
            className={`px-4 py-2 rounded-md ${
              status === 'CANCELLED' ? 'bg-primary-500 text-white' : 'bg-gray-200'
            }`}
          >
            已取消
          </button>
          <button
            onClick={() => handleStatusChange('REFUNDED')}
            className={`px-4 py-2 rounded-md ${
              status === 'REFUNDED' ? 'bg-primary-500 text-white' : 'bg-gray-200'
            }`}
          >
            已退款
          </button>
        </div>
        
        {loading && (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-white p-4 rounded-md shadow-sm animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-6 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        )}
        
        {error && (
          <div className="text-red-500 p-4 bg-red-50 rounded-md">
            获取订单失败: {error.message}
          </div>
        )}
        
        {!loading && !error && (
          <>
            {data?.viewer?.orders?.nodes?.length > 0 ? (
              <div className="space-y-4">
                {data.viewer.orders.nodes.map((order: any) => (
                  <div key={order.id} className="bg-white dark:bg-gray-800 p-4 rounded-md shadow-sm border border-gray-200">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-lg font-medium">{order.title}</h3>
                        <div className="text-gray-500 text-sm">
                          订单号: {order.orderNumber}
                        </div>
                        <div className="text-gray-500 text-sm">
                          创建时间: {new Date(order.createdAt).toLocaleString()}
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className="text-lg font-bold">¥{order.amount.toFixed(2)}</div>
                        <span className={`px-2 py-1 rounded-md text-xs mt-2 ${getStatusClass(order.paymentStatus)}`}>
                          {getStatusText(order.paymentStatus)}
                        </span>
                      </div>
                    </div>
                    
                    {order.paymentStatus === 'unpaid' && (
                      <div className="mt-4 flex justify-end">
                        <Link 
                          href={`/checkout/pay/${order.id}`}
                          className="px-4 py-2 bg-primary-500 text-white rounded-md text-sm"
                        >
                          立即支付
                        </Link>
                      </div>
                    )}
                  </div>
                ))}
                
                {data.viewer.orders.pageInfo.hasNextPage && (
                  <div className="flex justify-center mt-6">
                    <button
                      onClick={handleLoadMore}
                      className="px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300"
                    >
                      加载更多
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-gray-500">暂无订单</div>
                <Link 
                  href="/checkout" 
                  className="mt-4 inline-block px-4 py-2 bg-primary-500 text-white rounded-md"
                >
                  去创建订单
                </Link>
              </div>
            )}
          </>
        )}
      </div>
    </MainLayout>
  );
} 