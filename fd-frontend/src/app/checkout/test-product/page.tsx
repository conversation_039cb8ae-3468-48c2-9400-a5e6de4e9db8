'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

const products = [
  {
    title: '黄金会员',
    amount: '199',
    description: '升级成为黄金会员，享受专属特权',
    product_type: 'member_level',
    product_id: '2',
    metadata: JSON.stringify({ from_level_id: 1, to_level_id: 2 }),
  },
  {
    title: '钻石会员',
    amount: '499',
    description: '升级成为钻石会员，解锁全部功能',
    product_type: 'member_level',
    product_id: '3',
    metadata: JSON.stringify({ from_level_id: 2, to_level_id: 3 }),
  },
  {
    title: '《前端性能优化》电子书',
    amount: '99',
    description: '深入了解现代前端应用的性能瓶颈与优化方案',
    product_type: 'ebook',
    product_id: '101',
    metadata: JSON.stringify({ author: 'CodeMaster' }),
  },
  {
    title: '一小时技术咨询',
    amount: '999',
    description: '与我们的资深工程师进行一对一沟通',
    product_type: 'service',
    product_id: '201',
    metadata: JSON.stringify({ duration: '1 hour' }),
  },
];

export default function TestProductListPage() {
  const router = useRouter();

  const handleBuyNow = (product: typeof products[0]) => {
    const params = new URLSearchParams({
      title: product.title,
      amount: product.amount,
      description: product.description,
      product_type: product.product_type,
      product_id: product.product_id,
      metadata: product.metadata,
    });

    router.push(`/checkout?${params.toString()}`);
  };

  return (
    <div className="max-w-4xl mx-auto p-4 sm:p-6 lg:p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">商品列表测试</h1>
        <Link href="/checkout/orders" className="text-sm text-primary-600 hover:underline">
          查看我的订单 &rarr;
        </Link>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {products.map((product, index) => (
          <div key={index} className="bg-white border border-gray-200 rounded-lg shadow-sm flex flex-col">
            <div className="p-6 flex-grow">
              <h2 className="text-xl font-semibold mb-2">{product.title}</h2>
              <p className="text-gray-600 mb-4">{product.description}</p>
            </div>
            <div className="px-6 pb-6">
              <div className="text-3xl font-bold mb-4">
                <span className="text-xl">¥</span>{product.amount}
              </div>
              <button
                onClick={() => handleBuyNow(product)}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                立即购买
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 