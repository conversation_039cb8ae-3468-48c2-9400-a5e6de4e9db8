// @ts-nocheck
import ArticlePage, { generateMetadata as generateArticleMetadata } from '@/app/post/[uuid]/[slug]/page';
import { getRoutePrefixes } from '@/lib/api';
import { notFound } from 'next/navigation';

/**
 * 动态元数据生成
 * - 验证URL前缀是否为当前启用的文章前缀
 * - 如果是，则调用并返回原始文章页面的元数据
 * - 如果不是，则返回空，由 notFound 处理
 */
export async function generateMetadata(props: { params: { prefix: string, uuid: string, slug: string } }) {
  const prefixes = await getRoutePrefixes();
  const { prefix } = props.params;

  // 关键验证：URL中的前缀必须与系统配置的文章前缀完全匹配
  if (prefix !== prefixes.postPrefix) {
    // 前缀不匹配，不生成元数据，后续将由页面组件触发404
    return {};
  }

  // 前缀匹配，调用并返回原始文章页面的元数据生成函数
  return generateArticleMetadata(props);
}

/**
 * 动态路由代理页面组件
 * - 验证URL前缀
 * - 如果有效，则渲染并返回原始的文章页面组件
 * - 如果无效，则调用 notFound()
 */
export default async function DynamicPrefixArticlePage(props: { params: { prefix: string, uuid: string, slug: string } }) {
  const prefixes = await getRoutePrefixes();
  const { prefix } = props.params;

  // 关键验证：URL中的前缀必须与系统配置的文章前缀完全匹配
  if (prefix !== prefixes.postPrefix) {
    notFound();
  }

  // 验证通过，直接调用并渲染真正的文章页面组件，将所有props透传过去
  // 这里没有任何UI重复，只是组件的调用和渲染
  return <ArticlePage {...props} />;
} 