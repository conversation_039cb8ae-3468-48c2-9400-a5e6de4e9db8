import TaxonomyBanner from '@/components/ui/TaxonomyBanner';

export default function TestBannerPage() {
  return (
    <div>
      <h1 className="text-2xl font-bold p-4">Banner 组件测试页面</h1>
      
      {/* 测试分类Banner - 有图片 */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold p-4">分类Banner（有图片）</h2>
        <TaxonomyBanner
          title="科技前沿"
          description="探索最新的科技趋势和创新技术，了解未来科技发展方向。"
          count={42}
          bannerImageUrl="https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1200&h=600&fit=crop"
          breadcrumbs={[
            { label: '首页', href: '/' },
            { label: '分类', href: '/categories' },
            { label: '科技前沿', href: '#' }
          ]}
          gradientColors="from-blue-600 via-purple-600 to-indigo-700"
          icon={
            <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
              <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
            </svg>
          }
        />
      </div>

      {/* 测试标签Banner - 无图片 */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold p-4">标签Banner（无图片）</h2>
        <TaxonomyBanner
          title="人工智能"
          description="关于人工智能技术、应用和发展的相关内容。"
          count={28}
          breadcrumbs={[
            { label: '首页', href: '/' },
            { label: '标签', href: '/tags' },
            { label: '人工智能', href: '#' }
          ]}
          gradientColors="from-pink-500 via-red-500 to-orange-500"
          titlePrefix="#"
          icon={
            <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M9.243 3.03a1 1 0 01.727 1.213L9.53 6h2.94l.56-2.243a1 1 0 111.94.486L14.53 6H17a1 1 0 110 2h-2.97l-1 4H15a1 1 0 110 2h-2.47l-.56 2.242a1 1 0 11-1.94-.485L10.47 14H7.53l-.56 2.242a1 1 0 11-1.94-.485L5.47 14H3a1 1 0 110-2h2.97l1-4H5a1 1 0 110-2h2.47l.56-2.243a1 1 0 011.213-.727zM9.03 8l-1 4h2.94l1-4H9.03z" clipRule="evenodd" />
            </svg>
          }
        />
      </div>

      {/* 测试自定义分类法Banner */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold p-4">自定义分类法Banner</h2>
        <TaxonomyBanner
          title="产品评测"
          description="深度评测各类科技产品，为您提供专业的购买建议。"
          count={15}
          breadcrumbs={[
            { label: '首页', href: '/' },
            { label: '产品分类', href: '/taxonomy/product' },
            { label: '产品评测', href: '#' }
          ]}
          gradientColors="from-green-600 via-teal-600 to-cyan-600"
          icon={
            <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
          }
        />
      </div>
    </div>
  );
}
