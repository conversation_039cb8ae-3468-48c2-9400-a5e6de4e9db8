import CptDetailPage, { generateMetadata as generateCptMetadata } from '@/app/post-type/[type]/[uuid]/[slug]/page';
import { Metadata } from 'next';

// 这是一个别名页面，用于响应 /note/[uuid]/[slug] 路由
// 它直接复用了通用的自定义类型详情页组件，并硬编码了 type='note'

// 我们还需要重新导出元数据生成函数，并传入正确的类型
export async function generateMetadata({ params }: { params: { uuid: string; slug: string } }): Promise<Metadata> {
  const noteParams = { ...params, type: 'note' };
  return generateCptMetadata({ params: noteParams });
}

export default async function NoteDetailPage({ params }: { params: { uuid: string; slug: string } }) {
  const noteParams = { ...params, type: 'note' };

  return <CptDetailPage params={noteParams} />;
} 