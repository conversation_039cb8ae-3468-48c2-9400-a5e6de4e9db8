'use client';

import React, { useContext, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/layouts/MainLayout';
import { AuthContext } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react'; // 使用一个加载图标
import { UserActivityTabs } from '@/components/dashboard/UserActivityTabs';

const DashboardPage = () => {
  const authContext = useContext(AuthContext);
  const router = useRouter();

  useEffect(() => {
    // 如果AuthContext已加载完毕，但用户未登录，则重定向到登录页
    if (authContext && !authContext.isLoading && !authContext.isAuthenticated) {
      router.push('/login');
    }
  }, [authContext, router]);

  // 在加载状态或用户未认证时，显示加载指示器
  if (!authContext || authContext.isLoading || !authContext.isAuthenticated) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center h-[50vh]">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      </MainLayout>
    );
  }

  // 如果已认证，显示个人中心内容
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-10">
        <header className="mb-8">
            <h1 className="text-4xl font-bold text-gray-800">个人中心</h1>
            <p className="text-lg text-gray-600 mt-2">
              欢迎回来，{authContext.user?.name}！在这里管理您的动态和内容。
            </p>
        </header>
        
        <UserActivityTabs />

      </div>
    </MainLayout>
  );
};

export default DashboardPage; 