'use client'

import { useState } from 'react'
import MainLayout from '@/components/layouts/MainLayout'
import {
  useSendPrivateMessage,
  useConversationMessages,
  useMarkConversationAsRead,
  useDeleteConversation,
  useUnreadMessageCount
} from '@/hooks/useMessages'
import { useLazyQuery } from '@apollo/client'
import {
  GET_MY_CONVERSATIONS,
  GET_CONVERSATION_MESSAGES
} from '@/lib/graphql/queries'
import type {
  ViewerPrivateConversations,
  PrivateMessageConversation,
  PrivateConversationData
} from '@/types/messages'

const RECIPIENT_ID = 'dXNlcjoxMw==' // 'aaatest' user ID

export default function TestMessagingPage() {
  // --- Block 1: Send Message ---
  const {
    sendMessage,
    loading: sendLoading,
    error: sendError
  } = useSendPrivateMessage()
  const [messageContent, setMessageContent] = useState(
    `一条来自前端测试页面的消息，发送于 ${new Date().toLocaleString()}`
  )
  const [sendResultData, setSendResultData] = useState<any>(null)

  const handleSendMessage = async () => {
    setSendResultData(null)
    try {
      const result = await sendMessage({
        variables: {
          recipientId: RECIPIENT_ID,
          content: messageContent
        }
      })
      console.log('消息发送成功:', result)
      setSendResultData(result)
      // The list will refetch automatically due to the hook's config
    } catch (e) {
      console.error('发送消息失败:', e)
      setSendResultData({ error: e })
    }
  }

  // --- Block 2: Get Conversations List ---
  const [
    fetchConversations,
    {
      data: conversationsData,
      loading: conversationsLoading,
      error: conversationsError
    }
  ] = useLazyQuery<ViewerPrivateConversations>(GET_MY_CONVERSATIONS, {
    fetchPolicy: 'network-only' // Use network-only to always get the latest
  })

  const handleFetchConversations = () => {
    fetchConversations({
      variables: {
        first: 10
      }
    })
  }
  
  // --- Block 3: Conversation Actions (Mark as Read, Delete) ---
  const { markAsRead, loading: markLoading } = useMarkConversationAsRead();
  const { deleteConversation, loading: deleteLoading } = useDeleteConversation();

  const handleMarkAsRead = async (conversationId: string) => {
    try {
      await markAsRead({ variables: { conversationId } });
    } catch (e) {
      console.error('Mark as read failed:', e);
      alert('Mark as read failed!');
    }
  };

  const handleDeleteConversation = async (conversationId: string) => {
    if (window.confirm('Are you sure you want to delete this conversation?')) {
        try {
            await deleteConversation({ variables: { conversationId } });
        } catch (e) {
            console.error('Delete failed:', e);
            alert('Delete failed!');
        }
    }
  };


  // --- Block 4: Get Total Unread Count ---
  const { data: unreadData, loading: unreadLoading, refetch: refetchUnreadCount } = useUnreadMessageCount();
  

  // --- Block 5: Get Messages for a specific Conversation ---
  const [conversationIdForMessages, setConversationIdForMessages] = useState('');
  const [fetchMessages, { data: messagesData, loading: messagesLoading, error: messagesError }] = useLazyQuery<PrivateConversationData>(GET_CONVERSATION_MESSAGES);

  const handleFetchMessages = () => {
    if (conversationIdForMessages) {
        fetchMessages({ variables: { id: conversationIdForMessages, first: 20 } });
    } else {
        alert('Please enter a Conversation ID.');
    }
  };


  return (
    <MainLayout>
      <div className="container mx-auto p-8 space-y-12">
        {/* --- UI for Sending Message --- */}
        <div>
          <h1 className="text-2xl font-bold mb-4">1. 发送私信测试</h1>
          <p className="text-gray-600 mb-4">
            向用户 `aaatest` (ID: `{RECIPIENT_ID}`) 发送消息。发送成功后，下面的会话列表会自动刷新。
          </p>
          <textarea
            value={messageContent}
            onChange={e => setMessageContent(e.target.value)}
            className="w-full p-2 border rounded-lg"
            rows={3}
          />
          <button
            onClick={handleSendMessage}
            disabled={sendLoading}
            className="mt-4 px-6 py-2 bg-blue-500 text-white font-semibold rounded-lg shadow-md hover:bg-blue-600 disabled:bg-gray-400"
          >
            {sendLoading ? '正在发送...' : '发送'}
          </button>
          {/* ... result display ... */}
        </div>

        <hr/>

        {/* --- UI for Unread Count --- */}
        <div>
            <h1 className="text-2xl font-bold mb-4">2. 获取全站未读消息总数</h1>
             <button
                onClick={() => refetchUnreadCount()}
                disabled={unreadLoading}
                className="mt-4 px-6 py-2 bg-purple-500 text-white font-semibold rounded-lg shadow-md hover:bg-purple-600 disabled:bg-gray-400"
            >
                {unreadLoading ? '正在获取...' : '刷新未读总数'}
            </button>
            {unreadData && (
                <p className="mt-4 text-lg">
                    当前全站未读消息总数: <span className="font-bold text-red-500">{unreadData.viewer?.unreadMessageCount ?? 'N/A'}</span>
                </p>
            )}
        </div>
        
        <hr/>

        {/* --- UI for Conversation List --- */}
        <div>
          <h1 className="text-2xl font-bold mb-4">3. 获取会话列表 (带操作)</h1>
          <button
            onClick={handleFetchConversations}
            disabled={conversationsLoading}
            className="mt-4 px-6 py-2 bg-green-500 text-white font-semibold rounded-lg shadow-md hover:bg-green-600 disabled:bg-gray-400"
          >
            {conversationsLoading ? '正在获取...' : '刷新我的会话列表'}
          </button>

          <div className="mt-8">
            {conversationsLoading && <p>Loading conversations...</p>}
            {conversationsError && <p className="text-red-500">Error: {conversationsError.message}</p>}
            {conversationsData && (
              <ul className="space-y-4">
                {conversationsData.viewer.privateConversations.edges.map(
                  ({ node }: { node: PrivateMessageConversation }) => (
                    <li key={node.id} className="p-4 border rounded-lg bg-white shadow-sm">
                      <p><strong>With:</strong> {node.otherUser.name} ({node.otherUser.id})</p>
                      <p><strong>Last Message:</strong> "{node.lastMessage.content}"</p>
                      <p><strong>Unread:</strong> <span className="font-bold text-red-500">{node.unreadCount}</span></p>
                      <p className="text-xs text-gray-500">Updated: {new Date(node.updatedAt).toLocaleString()}</p>
                      <p className="text-xs text-gray-500">Conv. ID: <code className="bg-gray-100 p-1 rounded">{node.id}</code></p>
                      <div className="mt-2 space-x-2">
                        <button onClick={() => handleMarkAsRead(node.id)} disabled={markLoading} className="px-3 py-1 text-sm bg-yellow-500 text-white rounded hover:bg-yellow-600 disabled:bg-gray-400">Mark as Read</button>
                        <button onClick={() => handleDeleteConversation(node.id)} disabled={deleteLoading} className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 disabled:bg-gray-400">Delete</button>
                      </div>
                    </li>
                  )
                )}
              </ul>
            )}
          </div>
        </div>
        
        <hr/>
        
        {/* --- UI for getting messages for a conversation --- */}
        <div>
            <h1 className="text-2xl font-bold mb-4">4. 获取指定会话的消息列表</h1>
            <input 
                type="text"
                placeholder="Paste a Conversation ID here"
                value={conversationIdForMessages}
                onChange={(e) => setConversationIdForMessages(e.target.value)}
                className="w-full p-2 border rounded-lg"
            />
            <button
                onClick={handleFetchMessages}
                disabled={messagesLoading}
                className="mt-4 px-6 py-2 bg-indigo-500 text-white font-semibold rounded-lg shadow-md hover:bg-indigo-600 disabled:bg-gray-400"
            >
                {messagesLoading ? '正在获取...' : '获取消息'}
            </button>
            <div className="mt-8">
                {messagesLoading && <p>Loading messages...</p>}
                {messagesError && <p className="text-red-500">Error: {messagesError.message}</p>}
                {messagesData && (
                    <div className="p-4 bg-gray-50 rounded-lg">
                        <h2 className="font-bold">
                          Messages with {messagesData.privateConversation.otherUser.name}:
                        </h2>
                        <ul className="mt-4 space-y-2">
                          {messagesData.privateConversation.messages?.edges.map(
                            ({ node }) => (
                              <li
                                key={node.id}
                                className={`p-2 rounded-lg ${node.sender.id === messagesData.privateConversation.otherUser.id ? 'bg-gray-200' : 'bg-blue-100 text-right'}`}>
                                <p>{node.content}</p>
                                <p className="text-xs text-gray-500">By: {node.sender.name} at {new Date(node.sentAt).toLocaleTimeString()}</p>
                              </li>
                            )
                          )}
                        </ul>
                    </div>
                )}
            </div>
        </div>

      </div>
    </MainLayout>
  )
}