@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
body {
  @apply text-gray-900 bg-white;
}

/* 适配暗模式 */
@media (prefers-color-scheme: dark) {
  body {
    @apply text-gray-200 bg-gray-900;
  }
}

/* 文章模板相关样式 */
.article-container {
  @apply mb-12;
}

/* 封面模板样式 */
.cover-image {
  @apply mb-8 shadow-xl;
}

/* 英雄标题模板样式 */
.hero-header {
  @apply shadow-sm;
}

/* 视频模板样式 */
.video-container {
  @apply relative overflow-hidden;
  padding-top: 56.25%; /* 16:9宽高比 */
}

.video-container video {
  @apply absolute top-0 left-0 w-full h-full object-cover;
}

/* 文章内容样式增强 */
.article-content {
  @apply text-lg;
}

.article-content h2 {
  @apply text-2xl font-bold mt-8 mb-4;
}

.article-content h3 {
  @apply text-xl font-bold mt-6 mb-3;
}

.article-content p {
  @apply mb-4;
}

.article-content ul, .article-content ol {
  @apply ml-6 mb-4;
}

.article-content img {
  @apply rounded-lg my-6 mx-auto shadow-md;
} 