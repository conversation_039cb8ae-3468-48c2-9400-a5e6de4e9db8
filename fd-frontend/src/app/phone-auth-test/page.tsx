import PhoneVerificationTest from '@/components/PhoneVerificationTest/PhoneVerificationTest';

export const metadata = {
  title: '手机注册API直接测试',
  description: '直接测试手机注册API与后端通信',
};

export default function PhoneVerificationTestPage() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6 text-center">手机注册API格式测试</h1>
      <div className="mb-6 max-w-md mx-auto p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-sm text-blue-600">
          此页面用于直接测试mutation格式与后端API通信。后端已临时修改为跳过验证码校验，
          可以直接测试PHONE_REGISTER mutation的格式是否与后端匹配。
        </p>
      </div>
      <PhoneVerificationTest />
    </div>
  );
} 