import { notFound } from 'next/navigation';
import CategoryClientPage from '@/components/pages/CategoryClientPage';

// 分类查询 & 文章列表查询
const GRAPHQL_ENDPOINT = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql';

// 获取分页设置的查询
const GET_POSTS_PER_PAGE_QUERY = `
  query GetPostsPerPageSetting {
    postsPerPageSetting
  }
`;

// 默认每页文章数量
const DEFAULT_POSTS_PER_PAGE = 12;

interface GraphQLResponse<T> {
  data?: T;
  errors?: any;
}

interface CategoryPageData {
  category?: {
    id: string;
    databaseId: number;
    name: string;
    slug: string;
    description?: string;
    count?: number;
    aiSeoTitle?: string;
    aiSeoDescription?: string;
    aiSeoJsonLd?: string;
    bannerImageUrl?: string;
    bannerImage?: {
      sourceUrl: string;
      altText?: string;
      mediaDetails?: {
        width: number;
        height: number;
      };
    };
    posts?: {
      nodes: any[];
      pageInfo: {
        hasNextPage: boolean;
        endCursor: string;
      };
    };
  };
}

// 获取分页设置
async function fetchPostsPerPageSetting(): Promise<number> {
  try {
    const res = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: GET_POSTS_PER_PAGE_QUERY }),
      next: { revalidate: 3600 }, // 缓存1小时
    } as RequestInit & { next: { revalidate: number } });

    const json = await res.json();
    return json.data?.postsPerPageSetting || DEFAULT_POSTS_PER_PAGE;
  } catch (error) {
    console.error('Failed to fetch posts per page setting:', error);
    return DEFAULT_POSTS_PER_PAGE;
  }
}

async function fetchCategoryPageData(slug: string, postsPerPage: number): Promise<CategoryPageData['category'] | null> {
  const query = `
    query GetCategoryPage($slug: ID!, $first: Int) {
      category(id: $slug, idType: SLUG) {
        id
        databaseId
        name
        slug
        description
        count
        aiSeoTitle
        aiSeoDescription
        aiSeoJsonLd
        bannerImageUrl
        bannerImage {
          sourceUrl
          altText
          mediaDetails {
            width
            height
          }
        }
        posts(first: $first) {
          nodes {
            id
            title
            date
            slug
            shortUuid
            excerpt
            __typename
            featuredImage {
              node {
                sourceUrl
                altText
              }
            }
            author {
              node {
                name
                slug
                avatar {
                  url
                }
              }
            }
            categories {
              nodes {
                id
                name
                slug
              }
            }
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
    }
  `;

  const res = await fetch(GRAPHQL_ENDPOINT, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ query, variables: { slug, first: postsPerPage } }),
    next: {
      revalidate: 600,
      tags: [`category:${slug}`] // 添加缓存标签，用于按需重新验证
    },
  } as RequestInit & { next: { revalidate: number; tags: string[] } });

  const json: GraphQLResponse<CategoryPageData> = await res.json();
  if (json.errors) {
    console.error('GraphQL errors:', json.errors);
  }

  const category = json.data?.category || null;
  if (category) {
    console.log('[CategoryPage] fetchCategoryPageData result:', {
      categoryName: category.name,
      categoryCount: category.count,
      initialPostsCount: category.posts?.nodes.length,
      initialPageInfo: category.posts?.pageInfo
    });
  }

  return category;
}

export const revalidate = 600; // ISR: 10分钟

export async function generateMetadata({ params }: { params: { slug: string } }) {
  const postsPerPage = await fetchPostsPerPageSetting();
  const category = await fetchCategoryPageData(params.slug, postsPerPage);
  if (!category) {
    return {
      title: '分类未找到 - Future Decade',
      description: 'Future Decade - AI驱动的新型科技媒体',
    };
  }

  const metaTitle = category.aiSeoTitle || category.name;
  const metaDescription = category.aiSeoDescription || `${category.name} - Future Decade`;
  const canonicalUrl = `https://www.futuredecade.com/${category.slug}`;

  // 获取分类的第一篇文章作为默认图片
  const defaultImage = category.posts?.nodes?.[0]?.featuredImage?.node?.sourceUrl ||
                      'https://www.futuredecade.com/images/default-og-image.jpg';

  return {
    title: `${metaTitle} - Future Decade`,
    description: metaDescription,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      url: canonicalUrl,
      siteName: 'Future Decade',
      type: 'website',
      images: [
        {
          url: defaultImage,
          width: 1200,
          height: 630,
          alt: metaTitle,
        },
      ],
      locale: 'zh_CN',
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: [defaultImage],
      site: '@FutureDecade',
      creator: '@FutureDecade',
    },
    other: {
      // JSON-LD 结构化数据将在组件中单独注入
    },
  };
}

export default async function CategoryPage({ params }: { params: { slug: string } }) {
  const postsPerPage = await fetchPostsPerPageSetting();
  const category = await fetchCategoryPageData(params.slug, postsPerPage);

  if (!category) {
    return notFound();
  }

  const initialPosts = category.posts?.nodes || [];
  const initialPageInfo = category.posts?.pageInfo || null;

  console.log('[CategoryPage] Server component data:', {
    categoryName: category.name,
    categoryCount: category.count,
    postsPerPage,
    initialPostsCount: initialPosts.length,
    initialPageInfo
  });

  // 准备JSON-LD结构化数据
  let jsonLd = null;
  if (category.aiSeoJsonLd) {
    try {
      jsonLd = JSON.parse(category.aiSeoJsonLd);
      // 更新URL为正确的前端URL
      if (jsonLd.url) {
        jsonLd.url = `https://www.futuredecade.com/${category.slug}`;
      }
    } catch (error) {
      console.error('Failed to parse JSON-LD:', error);
    }
  }

  return (
    <>
      {jsonLd && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
      )}
      <CategoryClientPage
        initialCategory={category as any}
        initialPosts={initialPosts as any}
        initialPageInfo={initialPageInfo as any}
      />
    </>
  );
}