'use client';

import React from 'react';
import CommentList from '@/components/comments/CommentList';
import { Comment } from '@/types/post';

/**
 * 评论UI测试页面
 * 展示知乎风格的三级评论系统
 */
const TestCommentsPage = () => {
  // 模拟评论数据
  const mockComments: Comment[] = [
    // 一级评论1
    {
      id: 'comment:1',
      databaseId: 1,
      content: '像我这种出身农村贫寒家庭的男人，从小能不怕吗？稍弱错一步，就万劫不复，没有谁能给你兜底，自己也不忍心太而划之把错成本转嫁父母，只能谨小慎微，怕这怕那。现在成了中年老爷，才有点不怕了，因为自己给自己负责了。',
      date: '2024-05-14T10:30:00Z',
      status: 'APPROVE',
      author: {
        node: {
          name: '夜暮飞烟',
          avatar: {
            url: 'https://picsum.photos/40/40?random=1'
          }
        }
      }
    },
    // 二级评论1-1
    {
      id: 'comment:2',
      databaseId: 2,
      content: '所以更不要怕，本来就一无所有又有什么可失去',
      date: '2024-05-15T08:20:00Z',
      parentId: '1',
      status: 'APPROVE',
      author: {
        node: {
          name: '新陈代血',
          avatar: {
            url: 'https://picsum.photos/40/40?random=2'
          }
        }
      }
    },
    // 三级评论1-1-1
    {
      id: 'comment:3',
      databaseId: 3,
      content: '白古河北燕赵大地多慷慨义士，😎',
      date: '2024-05-15T09:15:00Z',
      parentId: '2',
      status: 'APPROVE',
      author: {
        node: {
          name: 'EthanHunt',
          avatar: {
            url: 'https://picsum.photos/40/40?random=3'
          }
        }
      }
    },
    // 三级评论1-1-2
    {
      id: 'comment:4',
      databaseId: 4,
      content: '你不是河北的么',
      date: '2024-05-15T10:05:00Z',
      parentId: '2',
      status: 'APPROVE',
      author: {
        node: {
          name: '悠悠',
          avatar: {
            url: 'https://picsum.photos/40/40?random=4'
          }
        }
      }
    },
    // 一级评论2
    {
      id: 'comment:5',
      databaseId: 5,
      content: '人生充满了一个又一个的坎，冻死迎风站，饿死不弯腰，大家都不应该跪',
      date: '2024-05-13T14:20:00Z',
      status: 'APPROVE',
      author: {
        node: {
          name: '索娜娜',
          avatar: {
            url: 'https://picsum.photos/40/40?random=5'
          }
        }
      }
    },
    // 一级评论3
    {
      id: 'comment:6',
      databaseId: 6,
      content: '都一样😂',
      date: '2024-05-14T16:45:00Z',
      status: 'APPROVE',
      author: {
        node: {
          name: '被骗了四十五年',
          avatar: {
            url: 'https://picsum.photos/40/40?random=6'
          }
        }
      }
    }
  ];

  const handleCommentAdded = () => {
    console.log('评论已添加');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            知乎风格评论系统
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            三级评论结构，支持折叠展开和排序功能
          </p>
        </div>

        {/* 模拟文章内容 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            测试文章标题
          </h2>
          <div className="text-gray-700 dark:text-gray-300 leading-relaxed">
            这是一篇测试文章的内容。下面的评论区展示了知乎风格的三级评论系统，
            包括一级评论、二级回复和三级回复的嵌套结构。
          </div>
        </div>

        {/* 评论区 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <CommentList
            comments={mockComments}
            postId={1}
            onCommentAdded={handleCommentAdded}
            isCustomType={false}
          />
        </div>
      </div>
    </div>
  );
};

export default TestCommentsPage;
