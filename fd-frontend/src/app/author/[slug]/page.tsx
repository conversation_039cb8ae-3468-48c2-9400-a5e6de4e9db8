'use client';

import React, { useState, useEffect } from 'react';
import { gql, useQuery } from '@apollo/client';
import MainLayout from '@/components/layouts/MainLayout';
import ArticleListView, { Article } from '@/components/ArticleListView';
import ViewModeSwitcher, { ViewMode, ColumnCount } from '@/components/ViewModeSwitcher';
import { useRoutePrefixes } from '@/hooks';
import { useAuthorPosts } from '@/hooks';
import { Loader2 } from 'lucide-react';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import InfiniteScroll from '@/components/InfiniteScroll';
import ArticleListSkeleton from '@/components/ArticleListSkeleton';
import { Post } from '@/types/post';

// 查询作者公开资料（含databaseId）
const GET_AUTHOR_PROFILE = gql`
  query GetAuthorProfile($slug: String!) {
    getAuthorProfile(slug: $slug) {
      name
      description
      slug
      databaseId
      avatar {
        url
      }
    }
  }
`;

// 头部组件
function AuthorProfileHeader({ profile }: { profile: any }) {
  return (
    <header className="bg-gray-100 dark:bg-gray-800 rounded-lg p-8 text-center mb-12">
      {profile.avatar?.url && (
        <img
          src={profile.avatar.url}
          alt={profile.name}
          className="w-32 h-32 rounded-full mx-auto mb-4 border-4 border-white shadow-lg"
        />
      )}
      <h1 className="text-4xl font-extrabold text-gray-900 dark:text-white">
        {profile.name}
      </h1>
      {profile.description && (
        <p className="mt-4 text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          {profile.description}
        </p>
      )}
    </header>
  );
}

export default function AuthorPage({ params }: { params: { slug: string } }) {
  const { slug } = params;

  const { data: profileData, loading: profileLoading, error: profileError } = useQuery(
    GET_AUTHOR_PROFILE,
    {
      variables: { slug },
    }
  );

  const profile = profileData?.getAuthorProfile;

  const authorId: number | undefined = profile?.databaseId;

  const {
    posts,
    endCursor,
    hasNextPage,
    loading: postsLoading,
    error: postsError,
    loadMore,
  } = useAuthorPosts({ authorId: authorId || 0, after: undefined });

  const { prefixes } = useRoutePrefixes();

  // 搜索过滤
  const [searchTerm, setSearchTerm] = useState('');

  // 视图模式状态
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [columns, setColumns] = useState<ColumnCount>(3);

  // 初始化视图偏好
  useEffect(() => {
    try {
      const savedMode = localStorage.getItem('fd_view_mode') as ViewMode;
      const savedColumns = parseInt(localStorage.getItem('fd_columns_count') || '3') as ColumnCount;
      if (savedMode) setViewMode(savedMode);
      if (savedColumns) setColumns(savedColumns);
    } catch (e) {
      console.error('无法读取视图偏好设置:', e);
    }
  }, []);

  const handleViewModeChange = (mode: ViewMode, cols?: ColumnCount) => {
    setViewMode(mode);
    if (cols) setColumns(cols);
  };
  
  // 处理加载更多
  const handleLoadMore = () => {
    if (endCursor && !postsLoading) {
      loadMore(endCursor);
    }
  };

  const filteredPosts = posts.filter((post: Post) =>
    post.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (profileLoading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center h-[50vh]">
          <Loader2 className="h-12 w-12 animate-spin text-gray-500" />
        </div>
      </MainLayout>
    );
  }

  if (profileError || !profile) {
    notFound();
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-10">
        {/* 作者信息头部 */}
        <AuthorProfileHeader profile={profile} />

        {/* 搜索 & 视图切换器 */}
        <div className="mb-6 flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div className="relative sm:w-64">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索文章..."
              className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            )}
          </div>

          <ViewModeSwitcher currentMode={viewMode} columns={columns} onChange={handleViewModeChange} />
        </div>

        {/* 文章列表 */}
        {postsLoading && posts.length === 0 ? (
          <div className="text-center py-8">
            <ArticleListSkeleton mode={viewMode} count={3} columns={columns} />
          </div>
        ) : filteredPosts.length === 0 ? (
          <div className="text-center py-8 text-gray-500">暂无文章或未找到匹配的文章</div>
        ) : (
          <InfiniteScroll
            hasMore={!!hasNextPage}
            loading={postsLoading}
            onLoadMore={handleLoadMore}
            loadingComponent={<ArticleListSkeleton mode={viewMode} count={3} columns={columns} />}
            totalCount={posts.length}
          >
            <ArticleListView
              articles={filteredPosts as Article[]}
              mode={viewMode}
              columns={columns}
              showFeaturedImage
              showExcerpt
              showDate
              showAuthor
              showCategory
              showReadMore
              routePrefixes={prefixes}
            />
          </InfiniteScroll>
        )}

        {/* 返回按钮 */}
        <div className="mt-12 text-center">
          <Link href="/" className="text-blue-600 hover:text-blue-800">
            ← 返回首页
          </Link>
        </div>
      </div>
    </MainLayout>
  );
} 