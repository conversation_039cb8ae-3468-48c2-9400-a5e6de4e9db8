// @ts-nocheck
"use client";

import React, { Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import AuthCard from '../../../components/ui/AuthCard';
import LoginTabs from '../../../components/auth/LoginTabs';
import ProtectedRoute from '../../../components/auth/ProtectedRoute';
import MainLayout from '@/components/layouts/MainLayout';

function LoginPageContent() {
  const searchParams = useSearchParams();

  // 从URL参数中获取回调地址
  const callbackUrlParam = searchParams?.get('callbackUrl');
  const redirectParam = searchParams?.get('redirect');
  const callbackUrl = callbackUrlParam || redirectParam || '/';

  // 添加调试日志
  console.log('LoginPage - callbackUrlParam:', callbackUrlParam);
  console.log('LoginPage - redirectParam:', redirectParam);
  console.log('LoginPage - final callbackUrl:', callbackUrl);

  return (
    <MainLayout>
      <ProtectedRoute requireGuest callbackForGuest={callbackUrl}>
        <AuthCard
          title="登录账户"
          description="欢迎回来，请输入您的账户信息"
          footer={
            <div className="text-center">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                继续使用即表示您同意我们的
                <a href="/terms" className="text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300">
                  服务条款
                </a>和
                <a href="/privacy" className="text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300">
                  隐私政策
                </a>
              </p>
            </div>
          }
        >
          <LoginTabs callbackUrl={callbackUrl} />
        </AuthCard>
      </ProtectedRoute>
    </MainLayout>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={
      <MainLayout>
        <div className="flex justify-center items-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      </MainLayout>
    }>
      <LoginPageContent />
    </Suspense>
  );
}