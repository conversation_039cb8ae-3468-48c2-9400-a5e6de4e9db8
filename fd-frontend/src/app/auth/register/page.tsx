// @ts-nocheck

import React from 'react';
import AuthCard from '../../../components/ui/AuthCard';
import RegisterForm from '../../../components/auth/RegisterForm';
import PhoneRegisterForm from '../../../components/auth/PhoneRegisterForm';
import ProtectedRoute from '../../../components/auth/ProtectedRoute';
import Tabs from '../../../components/ui/Tabs';
import MainLayout from '@/components/layouts/MainLayout';

export default function RegisterPage({ searchParams }: { searchParams?: { redirect?: string; callbackUrl?: string } }) {
  // 优先使用 callbackUrl，其次使用 redirect，最后默认为首页
  const callbackUrl = searchParams?.callbackUrl ?? searchParams?.redirect ?? '/';

  return (
    <MainLayout>
      <ProtectedRoute requireGuest callbackForGuest={callbackUrl}>
        <AuthCard 
          title="创建账户" 
          description="欢迎加入Future Decade，请完成以下信息创建您的账户"
          footer={
            <div className="text-center">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                点击注册即表示您同意我们的
                <a href="/terms" className="text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300">
                  服务条款
                </a>和
                <a href="/privacy" className="text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300">
                  隐私政策
                </a>
              </p>
            </div>
          }
        >
          <Tabs 
            tabs={[
              {
                id: 'email',
                label: '邮箱注册',
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                ),
                content: <RegisterForm callbackUrl={callbackUrl} />
              },
              {
                id: 'phone',
                label: '手机注册',
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                ),
                content: <PhoneRegisterForm callbackUrl={callbackUrl} />
              }
            ]}
            defaultTab="email"
            renderInactive={false}
          />
        </AuthCard>
      </ProtectedRoute>
    </MainLayout>
  );
} 