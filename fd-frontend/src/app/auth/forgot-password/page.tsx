import React from 'react';
import { Metadata } from 'next';
import AuthCard from '../../../components/ui/AuthCard';
import ForgotPasswordForm from '../../../components/auth/ForgotPasswordForm';
import ProtectedRoute from '../../../components/auth/ProtectedRoute';
import MainLayout from '@/components/layouts/MainLayout';

export const metadata: Metadata = {
  title: '忘记密码 - Future Decade',
  description: '重置您的Future Decade账户密码',
};

export default function ForgotPasswordPage() {
  return (
    <MainLayout>
      <ProtectedRoute requireGuest>
        <AuthCard 
          title="忘记密码" 
          description="请选择一种验证方式来重置您的密码"
        >
          <ForgotPasswordForm />
        </AuthCard>
      </ProtectedRoute>
    </MainLayout>
  );
} 