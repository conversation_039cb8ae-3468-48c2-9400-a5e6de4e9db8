import React from 'react';
import { Metadata } from 'next';
import AuthCard from '../../../components/ui/AuthCard';
import ResetPasswordForm from '../../../components/auth/ResetPasswordForm';
import ProtectedRoute from '../../../components/auth/ProtectedRoute';
import MainLayout from '@/components/layouts/MainLayout';

export const metadata: Metadata = {
  title: '重置密码 - Future Decade',
  description: '使用验证码重置您的Future Decade账户密码',
};

export default function ResetPasswordPage() {
  return (
    <MainLayout>
      <ProtectedRoute requireGuest>
        <AuthCard 
          title="重置密码" 
          description="请输入验证码和新密码完成重置"
        >
          <ResetPasswordForm />
        </AuthCard>
      </ProtectedRoute>
    </MainLayout>
  );
} 