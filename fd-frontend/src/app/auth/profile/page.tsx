"use client";

import React from 'react';
import { useAuth } from '../../../hooks/useAuth';
import { redirect } from 'next/navigation';
import PageTitle from '../../../components/ui/PageTitle';
import ProfileInfo from '../../../components/profile/ProfileInfo';
import MainLayout from '@/components/layouts/MainLayout';

export default function ProfilePage() {
  const { isAuthenticated, isLoading } = useAuth();
  
  // 如果用户加载完成但未登录，重定向到登录页面
  if (!isLoading && !isAuthenticated) {
    redirect('/auth/login');
  }
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <PageTitle title="个人中心" description="查看和管理您的个人资料" />
        
        <div className="mt-6">
          <ProfileInfo />
        </div>
      </div>
    </MainLayout>
  );
} 