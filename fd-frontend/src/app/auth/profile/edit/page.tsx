"use client";

import React from 'react';
import { useAuth } from '../../../../hooks/useAuth';
import { redirect } from 'next/navigation';
import PageTitle from '../../../../components/ui/PageTitle';
import ProfileEdit from '../../../../components/profile/ProfileEdit';
import MainLayout from '@/components/layouts/MainLayout';

export default function ProfileEditPage() {
  const { isAuthenticated, isLoading } = useAuth();
  
  // 如果用户加载完成但未登录，重定向到登录页面
  if (!isLoading && !isAuthenticated) {
    redirect('/auth/login');
  }
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <PageTitle title="编辑个人资料" description="更新您的个人信息" />
        
        <div className="mt-6">
          <ProfileEdit />
        </div>
      </div>
    </MainLayout>
  );
} 