"use client";

import React from 'react';
import { useAuth } from '../../../../hooks/useAuth';
import { redirect } from 'next/navigation';
import PageTitle from '../../../../components/ui/PageTitle';
import BindEmailSection from '../../../../components/profile/BindEmailSection';
import MainLayout from '@/components/layouts/MainLayout';

export default function BindEmailPage() {
  const { isAuthenticated, isLoading } = useAuth();
  
  // 如果用户加载完成但未登录，重定向到登录页面
  if (!isLoading && !isAuthenticated) {
    redirect('/auth/login');
  }
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <PageTitle title="绑定邮箱" description="绑定邮箱方便账号安全验证和密码找回" />
        
        <div className="mt-6">
          <BindEmailSection />
        </div>
        
        <div className="mt-4 text-center">
          <a href="/profile" className="text-primary-600 hover:underline">
            返回个人资料
          </a>
        </div>
      </div>
    </MainLayout>
  );
} 