"use client";

import React from 'react';
import { useAuth } from '../../../hooks/useAuth';
import { redirect } from 'next/navigation';
import PageTitle from '../../../components/ui/PageTitle';
import ChangePasswordForm from '../../../components/auth/ChangePasswordForm';
import MainLayout from '@/components/layouts/MainLayout';

export default function ChangePasswordPage() {
  const { isAuthenticated, isLoading } = useAuth();
  
  // 如果用户加载完成但未登录，重定向到登录页面
  if (!isLoading && !isAuthenticated) {
    redirect('/auth/login');
  }
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <PageTitle title="修改密码" description="更新您的账户密码" />
        
        <div className="mt-6">
          <ChangePasswordForm />
        </div>
      </div>
    </MainLayout>
  );
} 