import React from 'react';
import { Metadata } from 'next';
import AuthCard from '../../../components/ui/AuthCard';
import PhoneResetPasswordForm from '../../../components/auth/PhoneResetPasswordForm';
import ProtectedRoute from '../../../components/auth/ProtectedRoute';
import MainLayout from '@/components/layouts/MainLayout';

export const metadata: Metadata = {
  title: '手机验证重置密码 - Future Decade',
  description: '使用手机号验证重置您的Future Decade账户密码',
};

export default function PhoneResetPasswordPage() {
  return (
    <MainLayout>
      <ProtectedRoute requireGuest>
        <AuthCard 
          title="重置密码" 
          description="我们将向您的手机发送验证码以重置密码"
        >
          <PhoneResetPasswordForm />
        </AuthCard>
      </ProtectedRoute>
    </MainLayout>
  );
} 