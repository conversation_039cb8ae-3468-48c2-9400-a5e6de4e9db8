'use client';

import React, { useState } from 'react';
import MainLayout from '@/components/layouts/MainLayout';
import PaywallCard from '@/components/post/PaywallCard';
import PaywallCardCompact from '@/components/post/PaywallCardCompact';
import PaywallRenderer from '@/components/post/PaywallRenderer';

export default function PaywallDemoPage() {
  const [selectedDemo, setSelectedDemo] = useState<'card' | 'compact' | 'renderer'>('card');

  // 模拟付费墙HTML内容
  const mockPaywallContent = `
    <p>这是文章的预览内容，展示了文章的开头部分...</p>
    <div class="fd-member-access-denied">
      <h3>Members Only Content</h3>
      <p>This content is reserved for a higher membership level.</p>
      <a href="/auth/login" class="button">Log In</a>
      <a href="/auth/register" class="button">Register</a>
      <a href="/membership/upgrade" class="button">Upgrade Your Membership</a>
    </div>
  `;

  const handleUnlock = async () => {
    console.log('模拟解锁文章');
    alert('这是演示页面，实际解锁功能将在后续开发中实现');
  };

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto py-10 px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">付费墙UI组件演示</h1>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            展示不同样式的付费墙卡片组件，包括完整版、紧凑版和渲染器版本。
          </p>

          {/* 选项卡 */}
          <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1 mb-8">
            <button
              onClick={() => setSelectedDemo('card')}
              className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedDemo === 'card'
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              完整版卡片
            </button>
            <button
              onClick={() => setSelectedDemo('compact')}
              className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedDemo === 'compact'
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              紧凑版卡片
            </button>
            <button
              onClick={() => setSelectedDemo('renderer')}
              className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedDemo === 'renderer'
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              渲染器组件
            </button>
          </div>
        </div>

        {/* 演示内容 */}
        <div className="space-y-8">
          {selectedDemo === 'card' && (
            <div>
              <h2 className="text-2xl font-semibold mb-4">完整版付费墙卡片</h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                这是功能完整的付费墙卡片，包含精美的设计、动画效果和完整的交互功能。
              </p>
              
              <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
                <PaywallCard
                  postId={123}
                  postTitle="示例文章标题"
                  requiredMemberLevel={2}
                  unlockPrice={9.99}
                  isUnlocked={false}
                  onUnlock={handleUnlock}
                />
              </div>
            </div>
          )}

          {selectedDemo === 'compact' && (
            <div>
              <h2 className="text-2xl font-semibold mb-4">紧凑版付费墙卡片</h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                这是紧凑版的付费墙卡片，适合在空间有限的地方使用，保持了核心功能。
              </p>
              
              <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
                <PaywallCardCompact
                  postId={123}
                  postTitle="示例文章标题"
                  requiredMemberLevel={2}
                  unlockPrice={9.99}
                  isUnlocked={false}
                  onUnlock={handleUnlock}
                />
              </div>
            </div>
          )}

          {selectedDemo === 'renderer' && (
            <div>
              <h2 className="text-2xl font-semibold mb-4">付费墙渲染器组件</h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                这个组件可以解析后端返回的HTML内容，自动检测付费墙并渲染为精美的卡片。
              </p>
              
              <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
                <PaywallRenderer
                  content={mockPaywallContent}
                  postId={123}
                  postTitle="示例文章标题"
                  unlockPrice={9.99}
                  requiredMemberLevel={2}
                  isUnlocked={false}
                  variant="default"
                  onUnlock={handleUnlock}
                  legacyMode={true}
                  showLoading={true}
                />
              </div>
            </div>
          )}

          {/* 特性说明 */}
          <div className="mt-12 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-xl font-semibold mb-4">组件特性</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2">🎨 精美设计</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  采用现代化的渐变背景、阴影效果和圆角设计，提供视觉上的吸引力。
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-2">📱 响应式布局</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  完全响应式设计，在各种设备尺寸上都能完美显示。
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-2">✨ 动画效果</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  包含悬停动画、脉冲效果和平滑过渡，提升用户体验。
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-2">🌙 深色模式</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  完整支持深色模式，自动适应用户的主题偏好。
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-2">🔄 智能渲染</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  自动解析后端HTML，无缝替换为精美的付费墙卡片。
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-2">⚡ 高性能</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  优化的组件结构和CSS，确保快速渲染和流畅交互。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
