"use client";

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import MainLayout from '@/components/layouts/MainLayout';
import PageTitle from '@/components/ui/PageTitle';
import Button from '@/components/ui/Button';

export default function PaymentSuccessPage() {
  const router = useRouter();
  const { refreshUser } = useAuth();
  const searchParams = useSearchParams();
  const returnUrl = searchParams?.get('returnUrl');

  // 倒计时状态
  const [countdown, setCountdown] = useState(5); // 5秒倒计时
  const [isCountdownActive, setIsCountdownActive] = useState(!!returnUrl); // 只有有返回URL时才启动倒计时

  // 支付成功后刷新用户信息，获取最新会员等级
  useEffect(() => {
    refreshUser();
  }, [refreshUser]);

  // 倒计时逻辑
  useEffect(() => {
    if (!isCountdownActive || !returnUrl) return;

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          // 倒计时结束，自动跳转
          router.push(returnUrl);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isCountdownActive, returnUrl, router]);

  // 取消倒计时
  const cancelCountdown = () => {
    setIsCountdownActive(false);
  };

  // 立即返回
  const returnNow = () => {
    if (returnUrl) {
      router.push(returnUrl);
    }
  };
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <PageTitle title="支付成功" description="您已成功完成支付" />
        
        <div className="mt-6 bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg p-6 text-center">
          <div className="mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-16 w-16 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            恭喜您，会员升级成功！
          </h2>
          
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            您的会员等级已更新，现在您可以享受更多会员特权。
          </p>

          {/* 倒计时提示 */}
          {isCountdownActive && returnUrl && (
            <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg shadow-sm">
              <div className="flex items-center justify-center text-blue-700 dark:text-blue-300">
                <div className="relative mr-3">
                  <div className="w-5 h-5 border-2 border-blue-300 dark:border-blue-600 rounded-full animate-spin border-t-blue-600 dark:border-t-blue-400"></div>
                </div>
                <span className="text-sm font-medium">
                  <span className="inline-flex items-center">
                    <span className="bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full text-xs font-bold mr-2 min-w-[24px] text-center">
                      {countdown}
                    </span>
                    秒后自动返回文章页面
                  </span>
                </span>
              </div>
            </div>
          )}
          
          {/* 操作按钮 */}
          <div className="space-y-4">
            {returnUrl ? (
              <>
                {/* 主要操作区域 */}
                <div className="flex flex-col sm:flex-row justify-center gap-3">
                  <Button
                    variant="primary"
                    onClick={returnNow}
                    className="flex items-center justify-center"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    立即返回文章
                  </Button>

                  {isCountdownActive && (
                    <Button
                      variant="outline"
                      onClick={cancelCountdown}
                      className="flex items-center justify-center"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                      取消自动跳转
                    </Button>
                  )}
                </div>

                {/* 次要操作区域 */}
                <div className="flex flex-col sm:flex-row justify-center gap-3 pt-2 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    variant="ghost"
                    onClick={() => router.push('/auth/profile')}
                    className="text-sm"
                  >
                    查看个人资料
                  </Button>

                  <Button
                    variant="ghost"
                    onClick={() => router.push('/')}
                    className="text-sm"
                  >
                    返回首页
                  </Button>
                </div>
              </>
            ) : (
              /* 没有返回URL时的默认按钮 */
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Button
                  variant="primary"
                  onClick={() => router.push('/auth/profile')}
                >
                  查看个人资料
                </Button>

                <Button
                  variant="outline"
                  onClick={() => router.push('/')}
                >
                  返回首页
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
} 