"use client";

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../hooks/useAuth';
import { redirect, useSearchParams } from 'next/navigation';
import { useQuery } from '@apollo/client';
import { gql } from '@apollo/client';
import PageTitle from '../../../components/ui/PageTitle';
import MainLayout from '@/components/layouts/MainLayout';
import MembershipCard from '@/components/membership/MembershipCard';
import { MemberLevel } from '@/types/user-types';

const GET_ALL_MEMBER_LEVELS = gql`
  query GetAllMemberLevels {
    allMemberLevels {
      id
      name
      description
      priority
      price
      duration
      durationUnit
      tier
    }
  }
`;

export default function MembershipUpgradePage() {
  const { isAuthenticated, isLoading, user, refreshUser } = useAuth();
  const [currentLevel, setCurrentLevel] = useState<MemberLevel | null>(null);
  const searchParams = useSearchParams();
  const returnUrl = searchParams?.get('returnUrl');

  // 获取所有会员等级
  const { data, loading, error, refetch: refetchLevels } = useQuery(GET_ALL_MEMBER_LEVELS);

  // 处理升级成功
  const handleUpgradeSuccess = async () => {
    // 重新获取用户信息和会员等级
    await Promise.all([
      refreshUser(),
      refetchLevels()
    ]);
  };

  // 如果用户加载完成但未登录，重定向到登录页面
  if (!isLoading && !isAuthenticated) {
    redirect('/auth/login');
  }

  // 当用户信息加载完成后，设置当前会员等级
  useEffect(() => {
    if (user?.memberLevel) {
      setCurrentLevel(user.memberLevel);
    }
  }, [user]);

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <PageTitle title="会员升级" description="升级您的会员等级，享受更多权益" />
        
        {loading && (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
            <p className="mt-2 text-gray-600 dark:text-gray-400">加载中...</p>
          </div>
        )}
        
        {error && (
          <div className="text-center py-12">
            <p className="text-red-500">加载会员等级信息失败</p>
          </div>
        )}
        
        {data?.allMemberLevels && (
          <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {data.allMemberLevels.map((level: MemberLevel) => (
              <MembershipCard
                key={level.id}
                level={level}
                isCurrent={level.id === currentLevel?.id}
                canUpgrade={!currentLevel || (level.priority || 0) > (currentLevel.priority || 0)}
                currentLevel={currentLevel}
                onUpgradeSuccess={handleUpgradeSuccess}
                returnUrl={returnUrl}
              />
            ))}
          </div>
        )}
        
        {data?.allMemberLevels?.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-600 dark:text-gray-400">暂无可用的会员等级</p>
          </div>
        )}
      </div>
    </MainLayout>
  );
} 