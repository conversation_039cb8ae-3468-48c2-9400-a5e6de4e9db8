'use client';

import { useState } from 'react';

const GRAPHQL_ENDPOINT = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql';

export default function DebugTaxonomyPage() {
  const [taxonomyName, setTaxonomyName] = useState('company');
  const [termSlug, setTermSlug] = useState('');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testTaxonomyInfo = async () => {
    setLoading(true);
    try {
      const query = `
        query GetTaxonomy($name: ID!) {
          taxonomy(id: $name, idType: NAME) {
            id
            name
            label
            description
            hierarchical
            restBase
          }
        }
      `;

      const res = await fetch(GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query, variables: { name: taxonomyName } }),
      });

      const json = await res.json();
      setResult({ type: 'taxonomy', data: json });
    } catch (error) {
      setResult({ type: 'error', error: error instanceof Error ? error.message : String(error) });
    }
    setLoading(false);
  };

  const testTermInfo = async () => {
    if (!termSlug) return;
    
    setLoading(true);
    try {
      const taxonomyEnum = taxonomyName.toUpperCase();
      const query = `
        query GetTaxonomyTermInfo($taxonomy: TaxonomyEnum!, $slug: [String]!) {
          terms(where: { taxonomies: [$taxonomy], slug: $slug }) {
            nodes {
              __typename
              id
              databaseId
              name
              slug
              description
              uri
              ... on Company {
                count
                bannerImageUrl
                bannerImage {
                  sourceUrl
                  altText
                }
              }
              ... on Region {
                count
                bannerImageUrl
                bannerImage {
                  sourceUrl
                  altText
                }
              }
              ... on Industry {
                count
                bannerImageUrl
                bannerImage {
                  sourceUrl
                  altText
                }
              }
              ... on Category {
                count
                bannerImageUrl
                bannerImage {
                  sourceUrl
                  altText
                }
              }
              ... on Tag {
                count
                bannerImageUrl
                bannerImage {
                  sourceUrl
                  altText
                }
              }
            }
          }
        }
      `;

      const res = await fetch(GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query,
          variables: {
            taxonomy: taxonomyEnum,
            slug: [termSlug]
          }
        }),
      });

      const json = await res.json();
      setResult({ type: 'term', data: json });
    } catch (error) {
      setResult({ type: 'error', error: error instanceof Error ? error.message : String(error) });
    }
    setLoading(false);
  };

  const testTaxQuery = async () => {
    if (!termSlug) return;
    
    setLoading(true);
    try {
      const taxonomyEnum = taxonomyName.toUpperCase();
      const query = `
        query GetPostsByTaxQuerySlug($taxonomy: TaxonomyEnum!, $slugs: [String!], $first: Int) {
          posts(
            first: $first,
            where: {
              taxQuery: {
                relation: AND,
                taxArray: [
                  {
                    taxonomy: $taxonomy,
                    operator: IN,
                    terms: $slugs,
                    field: SLUG
                  }
                ]
              }
            }
          ) {
            pageInfo {
              hasNextPage
              endCursor
            }
            nodes {
              id
              title
              slug
              __typename
            }
          }
        }
      `;

      const res = await fetch(GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query,
          variables: {
            taxonomy: taxonomyEnum,
            slugs: [termSlug],
            first: 5
          }
        }),
      });

      const json = await res.json();
      setResult({ type: 'posts', data: json });
    } catch (error) {
      setResult({ type: 'error', error: error instanceof Error ? error.message : String(error) });
    }
    setLoading(false);
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">分类法调试页面</h1>
      
      <div className="mb-6 space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">分类法名称:</label>
          <input
            type="text"
            value={taxonomyName}
            onChange={(e) => setTaxonomyName(e.target.value)}
            className="border rounded px-3 py-2 w-64"
            placeholder="例如: company, region, industry"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-2">条目Slug:</label>
          <input
            type="text"
            value={termSlug}
            onChange={(e) => setTermSlug(e.target.value)}
            className="border rounded px-3 py-2 w-64"
            placeholder="例如: apple, beijing"
          />
        </div>
      </div>

      <div className="space-x-4 mb-6">
        <button
          onClick={testTaxonomyInfo}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          测试分类法信息
        </button>
        
        <button
          onClick={testTermInfo}
          disabled={loading || !termSlug}
          className="bg-green-500 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          测试条目信息
        </button>
        
        <button
          onClick={testTaxQuery}
          disabled={loading || !termSlug}
          className="bg-purple-500 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          测试TaxQuery文章
        </button>
      </div>

      {loading && <p>加载中...</p>}

      {result && (
        <div className="mt-6">
          <h2 className="text-xl font-semibold mb-4">结果 ({result.type}):</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto text-sm">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
