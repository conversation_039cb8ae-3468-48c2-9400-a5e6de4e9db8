'use client';

import React, { useState, useEffect } from 'react';
import { ApolloClient, InMemoryCache, useQuery } from '@apollo/client';
import { useRoutePrefixes, useSlugMappingTable } from '@/hooks';
import { POST_BY_UUID_QUERY, POST_BY_UUID_FALLBACK_QUERY } from '@/lib/graphql/queries';

// 创建一个独立的Apollo客户端用于测试
const testClient = new ApolloClient({
  uri: process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql',
  cache: new InMemoryCache(),
});

// 定义类型接口
interface UrlInfo {
  currentUrl: string;
  pathSegments: string[];
}

// 添加正确的错误类型定义
type GraphQLError = {
  message: string;
  locations?: Array<{ line: number; column: number }>;
  path?: Array<string | number>;
  extensions?: Record<string, any>;
};

// Tab选项类型
type TabOption = 'url' | 'prefixes' | 'slugmap' | 'uuid' | 'custom-type-uuid';

// 合并的路由测试组件
export default function RouteTestPage() {
  // 当前活动标签
  const [activeTab, setActiveTab] = useState<TabOption>('url');
  
  // UUID 测试状态
  const [uuid, setUuid] = useState("und0JhUykw");
  const [directResult, setDirectResult] = useState<any>(null);
  const [directError, setDirectError] = useState<string | null>(null);
  const [fallbackResult, setFallbackResult] = useState<any>(null);
  const [fallbackError, setFallbackError] = useState<string | null>(null);
  
  // 自定义类型UUID测试状态
  const [customTypeUuid, setCustomTypeUuid] = useState("230628-123456");
  const [customType, setCustomType] = useState("note");
  const [customTypeResult, setCustomTypeResult] = useState<any>(null);
  const [customTypeError, setCustomTypeError] = useState<string | null>(null);
  
  // URL信息
  const [urlInfo, setUrlInfo] = useState<UrlInfo>({
    currentUrl: "",
    pathSegments: []
  });
  
  // 使用 hooks 获取路由前缀和 slug 映射表
  const { prefixes, loading: prefixesLoading, error: prefixesError } = useRoutePrefixes();
  const { mappings, loading: mappingsLoading, error: mappingsError } = useSlugMappingTable();
  
  // 更新URL信息
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setUrlInfo({
        currentUrl: window.location.href,
        pathSegments: window.location.pathname.split('/').filter(Boolean)
      });
    }
  }, []);
  
  // 执行文章路由测试
  const runPostRouteTests = async () => {
    // 重置所有结果
    setDirectResult(null);
    setDirectError(null);
    setFallbackResult(null);
    setFallbackError(null);
    
    // 测试1: 直接通过postByUuid查询
    try {
      const { data } = await testClient.query({
        query: POST_BY_UUID_QUERY,
        variables: { uuid },
        fetchPolicy: 'network-only'
      });
      setDirectResult(data);
    } catch (error) {
      setDirectError(error instanceof Error ? error.message : String(error));
    }
    
    // 测试2: 通过meta查询
    try {
      const { data } = await testClient.query({
        query: POST_BY_UUID_FALLBACK_QUERY,
        variables: { uuid },
        fetchPolicy: 'network-only'
      });
      setFallbackResult(data);
    } catch (error) {
      setFallbackError(error instanceof Error ? error.message : String(error));
    }
  };
  
  // 执行自定义类型文章UUID测试
  const runCustomTypeUuidTest = async () => {
    // 重置结果
    setCustomTypeResult(null);
    setCustomTypeError(null);
    
    try {
      // 通过GraphQL查询获取自定义类型内容
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            query: `
              query GetContentNodeByUuid($uuid: String!) {
                contentNodeByUuid(uuid: $uuid) {
                  __typename
                  id
                  databaseId
                  slug
                  uri
                  date
                  ... on NodeWithTitle {
                    title
                  }
                  ... on NodeWithContentEditor {
                    content
                  }
                  ... on NodeWithFeaturedImage {
                    featuredImage {
                      node {
                        sourceUrl
                        altText
                      }
                    }
                  }
                }
              }
            `,
            variables: {
              uuid: customTypeUuid
            }
          }),
          cache: 'no-store'
        }
      );
      
      const data = await response.json();
      setCustomTypeResult(data.data);
      
      if (data.errors) {
        setCustomTypeError(data.errors.map((e: GraphQLError) => e.message).join(', '));
      }
    } catch (error) {
      setCustomTypeError(error instanceof Error ? error.message : String(error));
    }
  };
  
  // 生成测试URL
  const generateTestUrls = () => {
    if (!prefixes) return null;
    
    const { postPrefix } = prefixes;
    const slug = directResult?.postByUuid?.slug || 
                fallbackResult?.posts?.nodes?.[0]?.slug || 
                'unknown-slug';
                
    const urls = [
      { label: "当前URL格式", url: `/article/${uuid}/${slug}` },
      { label: "正确前缀(单数)", url: `/article/${uuid}/${slug}` },
      { label: "正确前缀(复数)", url: `/articles/${uuid}/${slug}` },
      { label: "设置的前缀", url: `/${postPrefix}/${uuid}/${slug}` }
    ];
    
    return (
      <div className="mt-6 bg-white p-4 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-3">测试URL</h3>
        <ul className="space-y-2">
          {urls.map((item, index) => (
            <li key={index} className="flex items-center">
              <span className="font-medium mr-2 w-32">{item.label}:</span>
              <a 
                href={item.url}
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-500 hover:underline"
              >
                {item.url}
              </a>
              {item.url === `/${postPrefix}/${uuid}/${slug}` && (
                <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                  推荐访问
                </span>
              )}
            </li>
          ))}
        </ul>
      </div>
    );
  };
  
  // 生成自定义类型测试URL
  const generateCustomTypeTestUrls = () => {
    if (!customTypeResult?.contentNodeByUuid) return null;
    
    const post = customTypeResult.contentNodeByUuid;
    const slug = post.slug || 'unknown-slug';
    
    const urls = [
      { 
        label: "自定义类型URL", 
        url: `/post-type/${customType}/${customTypeUuid}/${slug}` 
      }
    ];
    
    return (
      <div className="mt-6 bg-white p-4 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-3">测试URL</h3>
        <ul className="space-y-2">
          {urls.map((item, index) => (
            <li key={index} className="flex items-center">
              <span className="font-medium mr-2 w-32">{item.label}:</span>
              <a 
                href={item.url}
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-500 hover:underline"
              >
                {item.url}
              </a>
            </li>
          ))}
        </ul>
      </div>
    );
  };
  
  // 根据映射类型生成易读的描述
  const getMappingTypeDescription = (type: string): string => {
    switch (type) {
      case 'page': return '页面';
      case 'category': return '分类';
      case 'tag': return '标签';
      case 'taxonomy_archive': return '自定义分类法';
      case 'post_type': return '自定义文章类型';
      default: return type;
    }
  };
  
  // 生成基于映射类型的示例URL
  const generateMappingUrl = (mapping: any): string => {
    if (!mapping) return '';
    
    switch (mapping.type) {
      case 'page':
        return `/${mapping.slug}`;
      case 'category':
        return prefixes?.categoryPrefix 
          ? `/${prefixes.categoryPrefix}/${mapping.slug}` 
          : `/${mapping.slug}`;
      case 'tag':
        return prefixes?.tagPrefix 
          ? `/${prefixes.tagPrefix}/${mapping.slug}` 
          : `/${mapping.slug}`;
      case 'taxonomy_archive':
        return `/${mapping.slug}`;
      case 'post_type':
        return `/${mapping.slug}`;
      default:
        return `/${mapping.slug}`;
    }
  };

  // Tab 选项渲染
  const renderTabs = () => {
    const tabs = [
      { id: 'url', label: 'URL信息' },
      { id: 'prefixes', label: '路由前缀设置' },
      { id: 'slugmap', label: 'Slug映射表' },
      { id: 'uuid', label: '文章UUID测试' },
      { id: 'custom-type-uuid', label: '自定义类型UUID测试' }
    ];

    return (
      <div className="border-b border-gray-200 mb-6">
        <ul className="flex flex-wrap -mb-px">
          {tabs.map((tab) => (
            <li key={tab.id} className="mr-2">
              <button
                onClick={() => setActiveTab(tab.id as TabOption)}
                className={`inline-block py-3 px-4 text-sm font-medium ${
                  activeTab === tab.id
                    ? 'text-blue-600 border-b-2 border-blue-600 rounded-t-lg'
                    : 'text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 border-transparent'
                }`}
              >
                {tab.label}
              </button>
            </li>
          ))}
        </ul>
      </div>
    );
  };

  // URL信息标签页
  const renderUrlInfoTab = () => (
    <div className="bg-white p-4 rounded-lg shadow">
      <h2 className="text-xl font-medium mb-4">当前URL信息</h2>
      <div className="mb-2">
        <span className="font-medium">完整URL:</span> {urlInfo.currentUrl}
      </div>
      <div>
        <span className="font-medium">路径段:</span>{" "}
        {urlInfo.pathSegments.map((segment, index) => (
          <span key={index} className="inline-block bg-gray-100 px-2 py-1 rounded mr-2">
            {segment}
          </span>
        ))}
      </div>
    </div>
  );
  
  // 路由前缀设置标签页
  const renderPrefixesTab = () => (
    <div className="bg-white p-4 rounded-lg shadow">
      <h2 className="text-xl font-medium mb-4">路由前缀设置</h2>
      
      {prefixesLoading ? (
        <div className="p-4">加载路由设置中...</div>
      ) : prefixesError ? (
        <div className="p-4 text-red-500">
          <h2 className="text-xl font-bold mb-2">加载设置出错</h2>
          <p>{prefixesError.message}</p>
        </div>
      ) : (
        <div className="space-y-4">
          <div>
            <h3 className="font-medium">分类前缀:</h3>
            <p className="mt-1">
              {prefixes.categoryPrefix === null ? (
                <span className="text-gray-500">未设置 (无前缀)</span>
              ) : (
                <code className="bg-gray-100 px-2 py-1 rounded">/{prefixes.categoryPrefix}/</code>
              )}
            </p>
            <p className="mt-1 text-sm text-gray-600">
              示例URL: 
              {prefixes.categoryPrefix ? (
                <span className="ml-1">/{ prefixes.categoryPrefix }/technology</span>
              ) : (
                <span className="ml-1">/technology</span>
              )}
            </p>
          </div>
          
          <div>
            <h3 className="font-medium">标签前缀:</h3>
            <p className="mt-1">
              {prefixes.tagPrefix === null ? (
                <span className="text-gray-500">未设置 (无前缀)</span>
              ) : (
                <code className="bg-gray-100 px-2 py-1 rounded">/{prefixes.tagPrefix}/</code>
              )}
            </p>
            <p className="mt-1 text-sm text-gray-600">
              示例URL: 
              {prefixes.tagPrefix ? (
                <span className="ml-1">/{ prefixes.tagPrefix }/wordpress</span>
              ) : (
                <span className="ml-1">/wordpress</span>
              )}
            </p>
          </div>
          
          <div>
            <h3 className="font-medium">文章前缀:</h3>
            <p className="mt-1">
              <code className="bg-gray-100 px-2 py-1 rounded">/{prefixes.postPrefix}/</code>
            </p>
            <p className="mt-1 text-sm text-gray-600">
              示例URL: <span className="ml-1">/{ prefixes.postPrefix }/abc123/sample-article</span>
            </p>
          </div>
          
          <div>
            <h3 className="font-medium">分类索引页路径:</h3>
            <p className="mt-1">
              <code className="bg-gray-100 px-2 py-1 rounded">/{prefixes.categoryIndexRoute}</code>
            </p>
            <p className="mt-1 text-sm text-gray-600">
              分类索引页URL: <span className="ml-1">/{prefixes.categoryIndexRoute}</span>
            </p>
          </div>
          
          <div>
            <h3 className="font-medium">标签索引页路径:</h3>
            <p className="mt-1">
              <code className="bg-gray-100 px-2 py-1 rounded">/{prefixes.tagIndexRoute}</code>
            </p>
            <p className="mt-1 text-sm text-gray-600">
              标签索引页URL: <span className="ml-1">/{prefixes.tagIndexRoute}</span>
            </p>
          </div>
        </div>
      )}
      
      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
        <h3 className="text-lg font-medium mb-2">提示</h3>
        <p>这些设置可以在WordPress后台 &gt; 外观 &gt; 主题设置 &gt; 路由设置 中修改。</p>
        <p className="mt-2">修改设置后，刷新此页面查看更新后的配置。</p>
      </div>
    </div>
  );

  // Slug映射表标签页
  const renderSlugMapTab = () => (
    <div className="bg-white p-4 rounded-lg shadow">
      <h2 className="text-xl font-medium mb-4">Slug映射表测试</h2>
      
      {mappingsLoading ? (
        <div className="p-4">加载Slug映射表中...</div>
      ) : mappingsError ? (
        <div className="p-4 text-red-500">
          <h2 className="text-xl font-bold mb-2">加载映射表出错</h2>
          <p>{mappingsError.message}</p>
        </div>
      ) : (
        <>
          <div className="mb-4">
            <p>当前映射表包含 <span className="font-medium">{mappings.length}</span> 个条目</p>
          </div>
          
          <div className="overflow-auto mb-4">
            <table className="min-w-full bg-white">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Slug
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    类型
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    分类法
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    示例URL
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {mappings.map((item, index) => (
                  <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {item.slug}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {getMappingTypeDescription(item.type)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.taxonomy || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <a 
                        href={generateMappingUrl(item)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline"
                      >
                        {generateMappingUrl(item)}
                      </a>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <div className="p-4 bg-yellow-50 rounded-lg">
            <h3 className="text-lg font-medium mb-2">关于Slug映射表</h3>
            <p>Slug映射表用于解决无前缀环境下的路由识别问题。当分类前缀和标签前缀都为空时，系统通过映射表确定一级路径应该解析为什么类型的内容。</p>
            <p className="mt-2">例如，系统可以通过映射表确定 <code>/news</code> 是一个分类页面而不是标签页面。</p>
            <p className="mt-2">映射表在WordPress后台自动维护，每当您创建、编辑或删除页面、分类和标签时会自动更新。</p>
          </div>
        </>
      )}
    </div>
  );

  // UUID测试标签页
  const renderUuidTestTab = () => (
    <div className="bg-white p-4 rounded-lg shadow">
      <h2 className="text-xl font-medium mb-4">文章路由 UUID 测试</h2>
      
      <div className="mb-6">
        <div className="flex space-x-4 items-center">
          <div className="flex-grow">
            <label htmlFor="uuid" className="block text-sm font-medium text-gray-700 mb-1">
              文章UUID
            </label>
            <input
              type="text"
              id="uuid"
              value={uuid}
              onChange={(e) => setUuid(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="输入文章UUID"
            />
          </div>
          <button
            onClick={runPostRouteTests}
            className="mt-5 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md"
          >
            运行测试
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* 测试1: 直接通过postByUuid查询 */}
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <h2 className="text-lg font-medium mb-3">测试1: 直接查询 postByUuid</h2>
          {directError ? (
            <div className="text-red-500 bg-red-50 p-3 rounded">
              <p className="font-medium">错误:</p>
              <p>{directError}</p>
            </div>
          ) : directResult ? (
            <div>
              <div className="mb-2">
                <span className="font-medium">状态:</span>{" "}
                {directResult.postByUuid ? (
                  <span className="text-green-500">成功</span>
                ) : (
                  <span className="text-yellow-500">未找到文章</span>
                )}
              </div>
              {directResult.postByUuid ? (
                <>
                  <div className="mb-2">
                    <span className="font-medium">文章ID:</span>{" "}
                    <span>{directResult.postByUuid.databaseId}</span>
                  </div>
                  <div className="mb-2">
                    <span className="font-medium">标题:</span>{" "}
                    <span>{directResult.postByUuid.title}</span>
                  </div>
                  <div className="mb-2">
                    <span className="font-medium">Slug:</span>{" "}
                    <span>{directResult.postByUuid.slug}</span>
                  </div>
                  <div className="mb-2">
                    <span className="font-medium">UUID:</span>{" "}
                    <span>{directResult.postByUuid.shortUuid}</span>
                  </div>
                </>
              ) : (
                <p>未找到匹配的文章</p>
              )}
            </div>
          ) : (
            <p className="text-gray-500">请点击"运行测试"按钮开始测试</p>
          )}
        </div>
        
        {/* 测试2: 通过meta查询 */}
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <h2 className="text-lg font-medium mb-3">测试2: 备用Meta查询</h2>
          {fallbackError ? (
            <div className="text-red-500 bg-red-50 p-3 rounded">
              <p className="font-medium">错误:</p>
              <p>{fallbackError}</p>
            </div>
          ) : fallbackResult ? (
            <div>
              <div className="mb-2">
                <span className="font-medium">状态:</span>{" "}
                {fallbackResult.posts?.nodes?.length > 0 ? (
                  <span className="text-green-500">成功</span>
                ) : (
                  <span className="text-yellow-500">未找到文章</span>
                )}
              </div>
              {fallbackResult.posts?.nodes?.length > 0 ? (
                <>
                  <div className="mb-2">
                    <span className="font-medium">文章ID:</span>{" "}
                    <span>{fallbackResult.posts.nodes[0].databaseId}</span>
                  </div>
                  <div className="mb-2">
                    <span className="font-medium">标题:</span>{" "}
                    <span>{fallbackResult.posts.nodes[0].title}</span>
                  </div>
                  <div className="mb-2">
                    <span className="font-medium">Slug:</span>{" "}
                    <span>{fallbackResult.posts.nodes[0].slug}</span>
                  </div>
                  <div className="mb-2">
                    <span className="font-medium">UUID:</span>{" "}
                    <span>{fallbackResult.posts.nodes[0].shortUuid}</span>
                  </div>
                </>
              ) : (
                <p>未找到匹配的文章</p>
              )}
            </div>
          ) : (
            <p className="text-gray-500">请点击"运行测试"按钮开始测试</p>
          )}
        </div>
      </div>
      
      {/* 生成测试URL */}
      {(directResult?.postByUuid || fallbackResult?.posts?.nodes?.length > 0) && generateTestUrls()}
    </div>
  );

  // 自定义类型UUID测试标签页
  const renderCustomTypeUuidTestTab = () => (
    <div className="bg-white p-4 rounded-lg shadow">
      <h2 className="text-xl font-medium mb-4">自定义类型UUID测试</h2>
      
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
          <div>
            <label htmlFor="custom-type" className="block text-sm font-medium text-gray-700 mb-1">
              文章类型（例如：note, product）
            </label>
            <input
              type="text"
              id="custom-type"
              value={customType}
              onChange={(e) => setCustomType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="输入自定义文章类型"
            />
          </div>
          <div>
            <label htmlFor="custom-uuid" className="block text-sm font-medium text-gray-700 mb-1">
              UUID（格式：YYMMDD-123456）
            </label>
            <input
              type="text"
              id="custom-uuid"
              value={customTypeUuid}
              onChange={(e) => setCustomTypeUuid(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="输入文章UUID"
            />
          </div>
        </div>
        <div className="mt-4">
          <button
            onClick={runCustomTypeUuidTest}
            className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md"
          >
            运行测试
          </button>
        </div>
      </div>
      
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <h2 className="text-lg font-medium mb-3">自定义类型查询结果</h2>
        {customTypeError ? (
          <div className="text-red-500 bg-red-50 p-3 rounded">
            <p className="font-medium">错误:</p>
            <p>{customTypeError}</p>
          </div>
        ) : customTypeResult ? (
          <div>
            <div className="mb-2">
              <span className="font-medium">状态:</span>{" "}
              {customTypeResult.contentNodeByUuid ? (
                <span className="text-green-500">成功</span>
              ) : (
                <span className="text-yellow-500">未找到内容</span>
              )}
            </div>
            {customTypeResult.contentNodeByUuid ? (
              <>
                <div className="mb-2">
                  <span className="font-medium">类型名称:</span>{" "}
                  <span>{customTypeResult.contentNodeByUuid.__typename}</span>
                </div>
                <div className="mb-2">
                  <span className="font-medium">内容ID:</span>{" "}
                  <span>{customTypeResult.contentNodeByUuid.databaseId}</span>
                </div>
                <div className="mb-2">
                  <span className="font-medium">标题:</span>{" "}
                  <span>{customTypeResult.contentNodeByUuid.title}</span>
                </div>
                <div className="mb-2">
                  <span className="font-medium">Slug:</span>{" "}
                  <span>{customTypeResult.contentNodeByUuid.slug}</span>
                </div>
                <div className="mb-2">
                  <span className="font-medium">URI:</span>{" "}
                  <span>{customTypeResult.contentNodeByUuid.uri}</span>
                </div>
              </>
            ) : (
              <p>未找到匹配的内容</p>
            )}
          </div>
        ) : (
          <p className="text-gray-500">请点击"运行测试"按钮开始测试</p>
        )}
      </div>
      
      {/* 生成测试URL */}
      {customTypeResult?.contentNodeByUuid && generateCustomTypeTestUrls()}
    </div>
  );

  // 根据当前活动标签渲染内容
  const renderActiveTabContent = () => {
    switch (activeTab) {
      case 'url':
        return renderUrlInfoTab();
      case 'prefixes':
        return renderPrefixesTab();
      case 'slugmap':
        return renderSlugMapTab();
      case 'uuid':
        return renderUuidTestTab();
      case 'custom-type-uuid':
        return renderCustomTypeUuidTestTab();
      default:
        return null;
    }
  };
  
  return (
    <div className="container mx-auto py-8 px-4 mb-16">
      <h1 className="text-2xl font-bold mb-6">路由测试中心</h1>
      
      {/* 标签页导航 */}
      {renderTabs()}
      
      {/* 标签页内容 */}
      {renderActiveTabContent()}
    </div>
  );
} 