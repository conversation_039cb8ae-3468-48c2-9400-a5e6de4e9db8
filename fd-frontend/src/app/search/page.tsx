'use client';

import { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { SearchProvider, useSearch } from './SearchProvider';
import Button from '@/components/ui/Button';
import { useUrlBuilder } from '@/hooks';
import InfiniteScroll from '@/components/InfiniteScroll';
import SearchResultItem from '@/components/SearchResultItem';
import SearchResultSkeleton from '@/components/SearchResultSkeleton';
import { getContentTypeLabel, getTitle } from '@/utils/content-utils';
import { formatDate } from '@/utils/date-utils';
import MainLayout from '@/components/layouts/MainLayout';

/**
 * 搜索内容组件
 */
function SearchContent() {
  const { query, results, loading, error, search } = useSearch();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const searchParams = useSearchParams();
  const router = useRouter();
  const { buildPostUrl } = useUrlBuilder();
  
  // 无限加载状态
  const [visibleCount, setVisibleCount] = useState(10); // 初始显示10条
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  
  // 返回顶部按钮状态
  const [showBackToTop, setShowBackToTop] = useState(false);

  // 初始化搜索词
  useEffect(() => {
    const q = searchParams.get('q') || '';
    setSearchTerm(q);
  }, [searchParams]);
  
  // 监听滚动以显示/隐藏返回顶部按钮
  useEffect(() => {
    const handleScroll = () => {
      // 当滚动超过300px时显示按钮
      setShowBackToTop(window.scrollY > 300);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  // 返回顶部功能
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // 处理搜索提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      // 更新URL中的查询参数，使其与搜索框输入同步
      router.push(`/search?q=${encodeURIComponent(searchTerm)}`);
      search(searchTerm);
      // 重置分页状态
      setVisibleCount(10);
    }
  };
  
  // 按内容类型分组结果
  const resultsByType = useMemo(() => {
    const grouped: Record<string, any[]> = { all: [] };
    
    results.forEach(result => {
      const type = result.__typename || result.contentType;
      if (!grouped[type]) {
        grouped[type] = [];
      }
      grouped[type].push(result);
      grouped.all.push(result);
    });
    
    return grouped;
  }, [results]);
  
  // 获取可用的内容类型Tab选项
  const contentTypeTabs = useMemo(() => {
    const tabs = [
      { id: 'all', label: '全部', count: results.length }
    ];
    
    // 添加有结果的内容类型标签
    Object.entries(resultsByType).forEach(([type, items]) => {
      if (type !== 'all' && items.length > 0) {
        tabs.push({
          id: type,
          label: getContentTypeLabel(type),
          count: items.length
        });
      }
    });
    
    return tabs;
  }, [resultsByType, results.length]);
  
  // 根据当前选中的标签过滤结果
  const filteredResults = useMemo(() => {
    if (activeTab === 'all') {
      return results;
    }
    return results.filter(result => (result.__typename || result.contentType) === activeTab);
  }, [results, activeTab]);
  
  // 可见结果列表
  const visibleResults = useMemo(() => {
    return filteredResults.slice(0, visibleCount);
  }, [filteredResults, visibleCount]);
  
  // 是否还有更多结果
  const hasMore = useMemo(() => {
    return visibleCount < filteredResults.length;
  }, [visibleCount, filteredResults.length]);
  
  // 加载更多结果
  const loadMoreResults = useCallback(() => {
    if (!hasMore || isLoadingMore) return;
    
    setIsLoadingMore(true);
    
    // 模拟异步加载延迟
    setTimeout(() => {
      // 增加可见结果数量，每次增加10条
      setVisibleCount(prev => Math.min(prev + 10, filteredResults.length));
      setIsLoadingMore(false);
    }, 300);
  }, [hasMore, isLoadingMore, filteredResults.length]);
  
  // 当切换标签时重置可见数量
  useEffect(() => {
    setVisibleCount(10);
  }, [activeTab]);

  // 根据内容类型获取正确的URL
  const getLinkByContentType = (result: any) => {
    const type = result.__typename || result.contentType;
    
    if (type === 'Post') {
      return buildPostUrl(result.shortUuid, result.slug);
    } 
    
    // 处理自定义内容类型（不依赖下划线前缀）
    if (type && type !== 'Post' && type !== 'Page') {
      // 转换为小写用于路由
      const customType = type.toLowerCase();
      // 如果有shortUuid，构建正确的URL
      if (result.shortUuid) {
        const sanitizedSlug = result.slug ? encodeURIComponent(result.slug) : '';
        return `/${customType}/${result.shortUuid}/${sanitizedSlug}`;
      }
      // 否则退回到直接使用类型和slug
      return `/${customType}/${result.slug}`;
    }
    
    // 默认情况
    return result.uri || '#';
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">搜索结果</h1>
      
      {/* 搜索表单 */}
      <form onSubmit={handleSubmit} className="mb-8 flex gap-2">
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="输入关键词搜索..."
          className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <Button type="submit" variant="primary">
          搜索
        </Button>
      </form>

      {/* 返回顶部按钮 */}
      {showBackToTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 p-2 bg-blue-500 text-white rounded-full shadow-lg hover:bg-blue-600 transition-colors z-10"
          aria-label="返回顶部"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
          </svg>
        </button>
      )}

      {/* 加载状态 */}
      {loading && (
        <div className="space-y-4">
          {Array(5).fill(0).map((_, i) => (
            <SearchResultSkeleton key={i} />
          ))}
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <div className="p-4 border border-red-300 bg-red-50 rounded-lg text-red-700 mb-4">
          <p>搜索出错: {error.message}</p>
        </div>
      )}

      {/* 搜索结果 */}
      {!loading && results.length === 0 && query && (
        <div className="p-4 border rounded-lg">
          <p>未找到与 "{query}" 相关的结果</p>
        </div>
      )}

      {!loading && results.length > 0 && (
        <div>
          {/* 内容类型选项卡 */}
          <div className="mb-6 border-b">
            <ul className="flex flex-wrap -mb-px text-sm font-medium text-center">
              {contentTypeTabs.map((tab) => (
                <li key={tab.id} className="mr-2">
                  <button
                    onClick={() => setActiveTab(tab.id)}
                    className={`inline-block p-4 rounded-t-lg ${
                      activeTab === tab.id
                        ? 'text-blue-600 border-b-2 border-blue-600'
                        : 'hover:text-gray-600 hover:border-gray-300 border-b-2 border-transparent'
                    }`}
                  >
                    {tab.label} ({tab.count})
                  </button>
                </li>
              ))}
            </ul>
          </div>
          
          <p className="text-sm text-gray-500 mb-4">找到 {filteredResults.length} 条结果</p>
          
          <InfiniteScroll
            hasMore={hasMore}
            loading={isLoadingMore}
            onLoadMore={loadMoreResults}
            totalCount={filteredResults.length}
            loadingComponent={
              <div className="space-y-4">
                <SearchResultSkeleton />
                <SearchResultSkeleton />
              </div>
            }
          >
            <div className="space-y-4">
              {visibleResults.map((result) => (
                <SearchResultItem 
                  key={result.id} 
                  result={result} 
                  getLinkByContentType={getLinkByContentType} 
                />
              ))}
            </div>
          </InfiniteScroll>
        </div>
      )}

      {/* 调试输出 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-8 p-4 border-t">
          <p className="text-xs text-gray-500">调试信息:</p>
          <pre className="text-xs overflow-auto bg-gray-100 p-2 rounded mt-1">
            {JSON.stringify({ 
              query, 
              resultsCount: results.length, 
              resultsByType: Object.fromEntries(
                Object.entries(resultsByType).map(([k, v]) => [k, v.length])
              ),
              visibleCount,
              hasMore,
              error 
            }, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}

/**
 * 搜索页面主组件
 */
export default function SearchPage() {
  return (
    <MainLayout>
      <SearchProvider>
        <SearchContent />
      </SearchProvider>
    </MainLayout>
  );
} 