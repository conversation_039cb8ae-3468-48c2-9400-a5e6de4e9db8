'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useSearchParams } from 'next/navigation';
import { useVITheme } from '@/contexts/VIThemeContext';

// 搜索结果类型定义
interface SearchResult {
  id: string;
  title: any;
  slug: string;
  date?: string;
  content?: string;
  excerpt?: string;
  contentType: string;
  shortUuid?: string;
  featuredImage?: any;
  uri?: string;
  __typename?: string;
}

// 搜索上下文类型定义
interface SearchContextType {
  query: string;
  results: SearchResult[];
  loading: boolean;
  error: Error | null;
  search: (searchTerm: string) => Promise<void>;
}

// 创建搜索上下文
const SearchContext = createContext<SearchContextType>({
  query: '',
  results: [],
  loading: false,
  error: null,
  search: async () => {},
});

// 搜索提供者属性类型
interface SearchProviderProps {
  children: ReactNode;
}

// 使用搜索Hook
export const useSearch = () => useContext(SearchContext);

/**
 * 搜索提供者组件
 * 负责搜索状态管理和搜索引擎选择
 */
export function SearchProvider({ children }: SearchProviderProps) {
  const searchParams = useSearchParams();
  const initialQuery = searchParams.get('q') || '';
  const [query, setQuery] = useState(initialQuery);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { settings } = useVITheme();

  // 搜索函数
  const search = async (searchTerm: string, retryCount = 0): Promise<void> => {
    if (!searchTerm.trim()) {
      setResults([]);
      return;
    }

    setLoading(true);
    setError(null);
    setQuery(searchTerm);

    try {
      // 根据设置选择搜索引擎类型
      const searchEngine = settings.searchEngineType || 'graphql';
      console.log(`使用${searchEngine}搜索引擎`);

      // 如果设置为使用Meilisearch，优先尝试
      if (searchEngine === 'meilisearch') {
        try {
          await searchWithMeilisearch(searchTerm);
        } catch (meilisearchError) {
          console.error('Meilisearch搜索失败:', meilisearchError);
          
          // Meilisearch失败，自动切换到GraphQL搜索
          console.log('自动切换到GraphQL搜索引擎');
          await searchWithGraphQL(searchTerm);
        }
      } else {
        // 直接使用GraphQL搜索
        await searchWithGraphQL(searchTerm);
      }
    } catch (err) {
      console.error('搜索错误:', err);
      setError(err instanceof Error ? err : new Error('搜索过程中发生未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 使用Meilisearch搜索
  const searchWithMeilisearch = async (searchTerm: string, retryCount = 0): Promise<void> => {
    // 尝试连接Meilisearch
    const maxRetries = 3;
    const retryDelay = 1000; // 毫秒

    try {
      const response = await fetch(`${settings.meilisearchApiUrl}/indexes/${settings.meilisearchIndexName}/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': settings.meilisearchApiKey ? `Bearer ${settings.meilisearchApiKey}` : '',
        },
        body: JSON.stringify({
          q: searchTerm,
          limit: 20,
        }),
      });

      if (!response.ok) {
        throw new Error(`搜索请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Meilisearch响应:', data);

      // 确保搜索结果包含必要字段
      if (!data.hits) {
        throw new Error('搜索结果格式无效');
      }

      // 格式化搜索结果
      const formattedResults: SearchResult[] = data.hits.map((hit: any) => ({
        id: hit.id || `result-${Math.random().toString(36).substr(2, 9)}`,
        title: hit.title || '无标题',
        slug: hit.slug || '',
        date: hit.date || null,
        content: hit.content || '',
        excerpt: hit.excerpt || '',
        contentType: hit.contentType || 'post',
        shortUuid: hit.shortUuid || '',
        featuredImage: hit.featuredImage || null,
        uri: hit.uri || null,
        // 将contentType映射为GraphQL类型格式的__typename，移除下划线前缀
        __typename: hit.__typename || (hit.contentType === 'post' ? 'Post' : 
                   hit.contentType === 'page' ? 'Page' :
                   hit.contentType ? hit.contentType.charAt(0).toUpperCase() + hit.contentType.slice(1) : 'Post')
      }));

      setResults(formattedResults);
    } catch (meilisearchError) {
      console.error('Meilisearch错误:', meilisearchError);

      // 如果可以重试，则尝试重新连接
      if (retryCount < maxRetries) {
        console.log(`尝试重新连接Meilisearch (${retryCount + 1}/${maxRetries})...`);
        setTimeout(() => {
          searchWithMeilisearch(searchTerm, retryCount + 1);
        }, retryDelay);
        return;
      }

      // 重试失败，抛出错误由上层处理切换到GraphQL
      throw new Error('Meilisearch连接失败');
    }
  };

  // 使用GraphQL搜索
  const searchWithGraphQL = async (searchTerm: string): Promise<void> => {
    if (!searchTerm) return;

    try {
      // 硬编码 GraphQL 端点
      const graphqlEndpoint = 'https://admin.futuredecade.com/graphql';
      
      console.log('使用 GraphQL 端点:', graphqlEndpoint);
      
      // 首先尝试获取可用的自定义文章类型
      const postTypesResponse = await fetch(graphqlEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            query GetEnabledPostTypes {
              frontendDisplaySettings {
                postTypes
              }
            }
          `
        })
      });
      
      let enabledPostTypes = ['POST', 'PAGE'];
      
      if (postTypesResponse.ok) {
        const postTypesResult = await postTypesResponse.json();
        if (postTypesResult?.data?.frontendDisplaySettings?.postTypes) {
          const customTypes = postTypesResult.data.frontendDisplaySettings.postTypes;
          // 将自定义类型转为大写，以符合 GraphQL 枚举要求
          const upperCaseTypes = customTypes.map((type: string) => type.toUpperCase());
          // 移除重复的类型
          upperCaseTypes.forEach((type: string) => {
            if (!enabledPostTypes.includes(type)) {
              enabledPostTypes.push(type);
            }
          });
          console.log('搜索的内容类型:', enabledPostTypes);
        }
      }
      
      // 构建内联片段部分，确保正确处理自定义类型
      let customTypeFragments = '';
      
      enabledPostTypes.forEach((type: string) => {
        if (type !== 'POST' && type !== 'PAGE') {
          // 修正：使用正确的类型名称格式（首字母大写，无下划线前缀）
          const typeName = type.charAt(0).toUpperCase() + type.slice(1).toLowerCase();
          customTypeFragments += `
          ... on ${typeName} {
            shortUuid
            databaseId
          }`;
        }
      });
      
      const response = await fetch(graphqlEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            query SearchContent($search: String!, $types: [ContentTypeEnum], $first: Int = 1000) {
              contentNodes(
                first: $first, 
                where: { 
                  search: $search,
                  contentTypes: $types
                }
              ) {
                nodes {
                  __typename
                  id
                  databaseId
                  ... on NodeWithTitle {
                    title
                  }
                  ... on NodeWithContentEditor {
                    content
                  }
                  ... on NodeWithExcerpt {
                    excerpt
                  }
                  ... on NodeWithFeaturedImage {
                    featuredImage {
                      node {
                        sourceUrl
                        altText
                      }
                    }
                  }
                  ... on UniformResourceIdentifiable {
                    uri
                  }
                  ... on ContentNode {
                    slug
                    date
                    modified
                  }
                  ... on Post {
                    shortUuid
                  }
                  ${customTypeFragments}
                }
              }
            }
          `,
          variables: { 
            search: searchTerm, 
            types: enabledPostTypes, 
            first: 1000 
          }
        }),
      });
      
      if (!response.ok) {
        throw new Error(`GraphQL请求失败: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.errors) {
        throw new Error(result.errors[0].message || 'GraphQL查询错误');
      }
      
      const data = result.data;
      
      // 确保有搜索结果
      if (!data || !data.contentNodes || !data.contentNodes.nodes) {
        throw new Error('未找到搜索结果');
      }
      
      console.log('GraphQL搜索响应:', data.contentNodes.nodes);
      
      // 格式化GraphQL搜索结果
      const formattedResults: SearchResult[] = data.contentNodes.nodes.map((node: any) => {
        if (!node) return null;
        
        // 确保title字段有值
        let title = '无标题';
        if (node.title && typeof node.title === 'string') {
          title = node.title;
        } else if (node.title && typeof node.title === 'object' && node.title.rendered) {
          title = node.title.rendered;
        }
        
        return {
          id: node.id || `result-${Math.random().toString(36).substr(2, 9)}`,
          title: title,
          slug: node.slug || '',
          date: node.date || null,
          content: node.content || '',
          excerpt: node.excerpt || '',
          contentType: node.__typename || 'post',
          shortUuid: node.shortUuid || '',
          featuredImage: node.featuredImage || null,
          uri: node.uri || null,
          __typename: node.__typename || '',
        };
      }).filter(Boolean);
      
      setResults(formattedResults);
    } catch (graphqlError) {
      console.error('GraphQL搜索错误:', graphqlError);
      throw new Error('GraphQL搜索失败');
    }
  };

  // 初始化搜索
  useEffect(() => {
    if (initialQuery) {
      search(initialQuery);
    }
  }, [initialQuery]);

  // 提供上下文
  const contextValue: SearchContextType = {
    query,
    results,
    loading,
    error,
    search,
  };

  return (
    <SearchContext.Provider value={contextValue}>
      {children}
    </SearchContext.Provider>
  );
} 