'use client';

import React from 'react';
import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] px-4 py-16">
      <div className="text-center max-w-2xl">
        {/* 404数字 */}
        <h1 className="text-9xl font-bold text-blue-600 mb-4">404</h1>
        
        {/* 标题 */}
        <h2 className="text-3xl font-semibold mb-4">页面未找到</h2>
        
        {/* 描述 */}
        <p className="text-gray-600 text-lg mb-8">
          抱歉，您访问的页面不存在或已被移除。
          <br />
          请检查URL是否正确，或返回首页继续浏览。
        </p>
        
        {/* 可能的原因 */}
        <div className="bg-gray-50 p-6 rounded-lg mb-8 text-left">
          <h3 className="text-xl font-medium mb-3">可能的原因：</h3>
          <ul className="list-disc list-inside text-gray-600 space-y-2">
            <li>URL拼写错误</li>
            <li>内容已被移除或重命名</li>
            <li>您可能没有访问此内容的权限</li>
            <li>URL前缀不正确（例如使用了错误的文章前缀）</li>
          </ul>
        </div>
        
        {/* 按钮 */}
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Link 
            href="/"
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            返回首页
          </Link>
          
          <button 
            onClick={() => window.history.back()}
            className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-3 px-6 rounded-lg transition-colors"
          >
            返回上一页
          </button>
        </div>
      </div>
    </div>
  );
} 