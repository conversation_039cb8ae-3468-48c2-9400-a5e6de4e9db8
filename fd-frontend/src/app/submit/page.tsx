'use client'

import { useState, useId, useEffect, useContext } from 'react'
import { useMutation, gql, useLazyQuery } from '@apollo/client'
import { useSearchParams, useRouter } from 'next/navigation'
import TiptapEditor from '@/components/editor/TiptapEditor'
import MainLayout from '@/components/layouts/MainLayout'
import { useToast } from '@/providers/UIProvider'
import Button from '@/components/ui/Button'
import { Loader2 } from 'lucide-react'
import { AuthContext } from '@/contexts/AuthContext'

// 定义订阅者用的 GraphQL Mutation
const CREATE_POST_SUBMISSION = gql`
  mutation CreatePostSubmission(
    $title: String!
    $contentHtml: String!
    $contentJson: String!
  ) {
    createPostSubmission(
      input: {
        title: $title
        contentHtml: $contentHtml
        contentJson: $contentJson
      }
    ) {
      success
      postId
      postStatus
    }
  }
`

// 定义作者及以上角色用的 GraphQL Mutations
const CREATE_MY_POST = gql`
  mutation CreateMyPost($title: String!, $content: String!, $status: String!) {
    createMyPost(input: { title: $title, content: $content, status: $status }) {
      post {
        id
        status
      }
    }
  }
`;

const UPDATE_MY_POST = gql`
  mutation UpdateMyPost($id: ID!, $title: String!, $content: String!, $status: String!) {
    updateMyPost(input: { id: $id, title: $title, content: $content, status: $status }) {
      post {
        id
        status
      }
    }
  }
`;

const GET_SUBMISSION_CONTENT = gql`
  query GetSubmissionContent($id: ID!) {
    post(id: $id, idType: DATABASE_ID) {
      id
      title
      content
    }
  }
`;

export default function SubmitPage() {
  const toast = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();
  const editId = searchParams.get('edit');

  // 2. 从 AuthContext 获取用户信息
  const auth = useContext(AuthContext);
  const userRole = auth?.user?.role;
  const isAuthorOrHigher = userRole && ['author', 'editor', 'administrator'].includes(userRole);

  const [title, setTitle] = useState('')
  const [editorContent, setEditorContent] = useState('')
  const [editorJson, setEditorJson] = useState('')
  const [editorKey, setEditorKey] = useState(useId());
  const [isEditMode, setIsEditMode] = useState(false);
  const [editGlobalId, setEditGlobalId] = useState<string | null>(null);

  const [weixinUrl, setWeixinUrl] = useState('');
  const [isFetching, setIsFetching] = useState(false);

  // 编辑用查询
  const [getSubmissionContent, { loading: loadingContent }] = useLazyQuery(GET_SUBMISSION_CONTENT, {
    onCompleted: (data) => {
      if (data.post) {
        setTitle(data.post.title);
        setEditorContent(data.post.content);
        setEditGlobalId(data.post.id);
        setIsEditMode(true);
        setEditorKey(Math.random().toString());
      }
    },
    fetchPolicy: 'network-only'
  });

  useEffect(() => {
    if (editId) {
      getSubmissionContent({ variables: { id: editId } });
    }
  }, [editId, getSubmissionContent]);

  // --- 变更 Hooks ---
  // 订阅者用
  const [createPostSubmission, { loading: creatingSubmission }] = useMutation(CREATE_POST_SUBMISSION, {
    onCompleted: (data) => {
      if (data.createPostSubmission.success) {
        toast.success('您的文章已进入审核队列，感谢您的贡献！');
        router.push('/dashboard');
      } else {
        toast.error('提交失败，请稍后重试。');
      }
    },
  });
  
  // 作者用
  const [createMyPost, { loading: creatingMyPost }] = useMutation(CREATE_MY_POST);
  const [updateMyPost, { loading: updatingMyPost }] = useMutation(UPDATE_MY_POST);

  const handleEditorUpdate = ({ html, json }: { html: string; json: string; }) => {
    setEditorContent(html);
    setEditorJson(json);
  };

  const handleFetchArticle = async () => {
    if (!weixinUrl.trim() || !weixinUrl.includes('mp.weixin.qq.com')) {
      toast.warning('请输入一个有效的微信文章链接。');
      return;
    }

    setIsFetching(true);
    try {
      const response = await fetch('/api/weixin-fetch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url: weixinUrl }),
      });

      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || '采集失败，请稍后重试');
      }

      toast.success('采集成功，文章标题和内容已自动填充。');
      
      setTitle(result.title);
      setEditorContent(result.contentHtml);
      setEditorKey(Math.random().toString());
    } catch (err: any) {
      toast.error('采集失败: ' + err.message);
    } finally {
      setIsFetching(false);
    }
  };
  
  // 3. 改造 handleSubmit
  const handleSubmit = async (status: 'publish' | 'draft' | 'pending' = 'pending') => {
    if (!title.trim()) {
      toast.warning('请输入标题');
      return;
    }
    if (!editorContent.trim() || editorContent === '<p></p>') {
      toast.warning('请输入内容');
      return;
    }

    try {
      if (isAuthorOrHigher) {
        // 作者及以上角色的逻辑
        if (isEditMode && editGlobalId) {
          await updateMyPost({
            variables: { id: editGlobalId, title, content: editorContent, status }
          });
          toast.success('文章更新成功！');
        } else {
          await createMyPost({
            variables: { title, content: editorContent, status }
          });
          toast.success(`文章已${status === 'publish' ? '发布' : '保存为草稿'}！`);
        }
        router.push('/dashboard');

      } else {
        // 订阅者逻辑 (保持不变)
        await createPostSubmission({
          variables: { title, contentHtml: editorContent, contentJson: editorJson }
        });
      }
    } catch (e: any) {
      console.error('操作失败:', e);
      toast.error(`操作失败: ${e.message}`);
    }
  }

  if (auth?.isLoading || loadingContent) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center h-[50vh]">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          <p className="ml-2">正在加载...</p>
        </div>
      </MainLayout>
    );
  }
  
  if (!auth?.isAuthenticated) {
    return (
        <MainLayout>
            <div className="text-center py-20">
                <h1 className="text-2xl">请先登录</h1>
                <p className="text-gray-600 mt-2">只有登录用户才能访问此页面。</p>
            </div>
        </MainLayout>
    )
  }

  const isLoading = creatingSubmission || creatingMyPost || updatingMyPost;

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto my-8 p-6 bg-white rounded-lg shadow-md">
        <h1 className="text-3xl font-bold mb-6 text-gray-800 border-b pb-4">
          {isAuthorOrHigher
            ? (isEditMode ? '编辑文章' : '发布新文章')
            : '撰写新文章'
          }
        </h1>

        <div className="mb-6 p-4 border rounded-lg bg-slate-50 dark:bg-slate-800">
          <label htmlFor="weixin-url">微信文章采集 (可选)</label>
          <p className="text-sm text-slate-500 dark:text-slate-400 mt-1 mb-2">
            输入微信文章链接，一键采集内容到编辑器。图片因防盗链可能无法显示。
          </p>
          <div className="flex items-center gap-2">
            <input
              id="weixin-url"
              type="url"
              value={weixinUrl}
              onChange={(e) => setWeixinUrl(e.target.value)}
              placeholder="https://mp.weixin.qq.com/s/..."
              className="flex-grow p-2 border rounded-md"
              disabled={isFetching}
            />
            <Button onClick={handleFetchArticle} loading={isFetching}>
              {isFetching ? '采集中...' : '一键采集'}
            </Button>
          </div>
        </div>
        
        <div className="space-y-6">
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="文章标题"
            className="w-full text-2xl font-semibold p-2 border-b-2 focus:outline-none focus:border-blue-500"
          />

          <TiptapEditor
            key={editorKey}
            content={editorContent}
            onUpdate={handleEditorUpdate}
          />
        </div>

        <div className="mt-8 pt-4 border-t flex justify-end items-center gap-4">
          {isAuthorOrHigher ? (
            <>
              <Button 
                onClick={() => handleSubmit('draft')}
                variant="secondary"
                loading={isLoading}
              >
                保存草稿
              </Button>
              <Button 
                onClick={() => handleSubmit('publish')}
                loading={isLoading}
              >
                {isEditMode ? '更新文章' : '立即发布'}
              </Button>
            </>
          ) : (
            <Button 
              onClick={() => handleSubmit('pending')}
              loading={isLoading}
            >
              提交审核
            </Button>
          )}
        </div>
      </div>
    </MainLayout>
  )
} 