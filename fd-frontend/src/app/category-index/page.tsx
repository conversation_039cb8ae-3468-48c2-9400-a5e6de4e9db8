import React from 'react';
import { notFound } from 'next/navigation';
import Script from 'next/script';
import { getRoutePrefixes } from '@/lib/route-prefixes';
import CategoryIndexClientPage from '@/components/pages/CategoryIndexClientPage';

// 启用ISR缓存，1小时重新验证
export const revalidate = 3600;

// 共享的分类接口定义
interface Category {
  id: string;
  databaseId: number;
  name: string;
  slug: string;
  description?: string;
  count: number;
  bannerImageUrl?: string;
  bannerImage?: {
    sourceUrl: string;
    altText?: string;
    mediaDetails?: {
      width: number;
      height: number;
    };
  };
}

// SEO设置接口
interface SeoSettings {
  aiSeoTitle?: string;
  aiSeoDescription?: string;
  aiSeoJsonLd?: string;
  enabled?: boolean;
  lastUpdated?: string;
}

// 统计信息接口
interface Statistics {
  totalCategories: number;
  totalPosts: number;
  averagePostsPerCategory: number;
}

// 分类索引页面数据接口
interface CategoryIndexData {
  seoSettings: SeoSettings;
  categories: Category[];
  statistics: Statistics;
}

/**
 * 获取分类索引页面数据
 */
async function fetchCategoryIndexData(): Promise<CategoryIndexData> {
  const query = `
    query GetCategoryIndexPageData {
      categoryIndexPageData {
        seoSettings {
          aiSeoTitle
          aiSeoDescription
          aiSeoJsonLd
          enabled
          lastUpdated
        }
        categories {
          id
          databaseId
          name
          slug
          description
          count
          bannerImageUrl
          bannerImage {
            sourceUrl
            altText
            mediaDetails {
              width
              height
            }
          }
        }
        statistics {
          totalCategories
          totalPosts
          averagePostsPerCategory
        }
      }
    }
  `;

  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query }),
        next: {
          revalidate: 3600, // ISR缓存
          tags: ['category-index-page'] // 缓存标签，用于按需重新验证
        }
      }
    );

    const data = await response.json();

    if (data.errors) {
      console.error('GraphQL errors:', data.errors);
      throw new Error('Failed to fetch category index data');
    }

    return data.data.categoryIndexPageData;
  } catch (error) {
    console.error('Error fetching category index data:', error);
    throw error;
  }
}

/**
 * 生成SEO元数据
 */
export async function generateMetadata() {
  try {
    const data = await fetchCategoryIndexData();
    const seo = data.seoSettings;

    const metaTitle = seo?.aiSeoTitle || '分类索引 - 探索所有文章分类';
    const metaDescription = seo?.aiSeoDescription ||
      `浏览Future Decade的所有${data.statistics.totalCategories}个文章分类，涵盖${data.statistics.totalPosts}篇优质内容。从科技创新到商业洞察，找到您感兴趣的话题。`;

    const canonicalUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com'}/category-index`;

    // 动态生成OG图片URL
    const ogImageUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com'}/api/og-image/category-index?categories=${data.statistics.totalCategories}&posts=${data.statistics.totalPosts}`;

    return {
      title: `${metaTitle} - Future Decade`,
      description: metaDescription,
      keywords: [
        '文章分类', '内容分类', '科技媒体', 'Future Decade',
        '人工智能', '商业洞察', '技术趋势', '创新科技'
      ].join(', '),
      alternates: {
        canonical: canonicalUrl,
      },
      openGraph: {
        title: metaTitle,
        description: metaDescription,
        url: canonicalUrl,
        siteName: 'Future Decade',
        type: 'website',
        images: [
          {
            url: ogImageUrl,
            width: 1200,
            height: 630,
            alt: `${metaTitle} - ${data.statistics.totalCategories}个分类，${data.statistics.totalPosts}篇文章`,
          }
        ],
        locale: 'zh_CN',
      },
      twitter: {
        card: 'summary_large_image',
        title: metaTitle,
        description: metaDescription,
        images: [ogImageUrl],
        site: '@FutureDecade',
        creator: '@FutureDecade',
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: '分类索引 - Future Decade',
      description: '浏览Future Decade的所有文章分类，找到您感兴趣的话题。'
    };
  }
}

/**
 * 分类索引页面服务器组件
 */
export default async function CategoryIndexPage() {
  try {
    // 并行获取数据
    const [data, routePrefixes] = await Promise.all([
      fetchCategoryIndexData(),
      getRoutePrefixes()
    ]);

    // 生成JSON-LD结构化数据
    const generateJsonLd = () => {
      const baseJsonLd = {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": data.seoSettings?.aiSeoTitle || "分类索引",
        "description": data.seoSettings?.aiSeoDescription || "文章分类索引页面",
        "url": `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com'}/category-index`,
        "mainEntity": {
          "@type": "ItemList",
          "numberOfItems": data.statistics.totalCategories,
          "itemListElement": data.categories.map((category, index) => ({
            "@type": "ListItem",
            "position": index + 1,
            "item": {
              "@type": "Thing",
              "@id": `${process.env.NEXT_PUBLIC_SITE_URL}/${routePrefixes.categoryPrefix || 'category'}/${category.slug}`,
              "name": category.name,
              "description": category.description,
              "url": `${process.env.NEXT_PUBLIC_SITE_URL}/${routePrefixes.categoryPrefix || 'category'}/${category.slug}`,
              "additionalProperty": {
                "@type": "PropertyValue",
                "name": "articleCount",
                "value": category.count
              }
            }
          }))
        },
        "breadcrumb": {
          "@type": "BreadcrumbList",
          "itemListElement": [
            {
              "@type": "ListItem",
              "position": 1,
              "name": "首页",
              "item": process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com'
            },
            {
              "@type": "ListItem",
              "position": 2,
              "name": "分类索引",
              "item": `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com'}/category-index`
            }
          ]
        },
        "publisher": {
          "@type": "Organization",
          "name": "Future Decade",
          "url": process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com',
          "logo": {
            "@type": "ImageObject",
            "url": `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com'}/logo.png`
          }
        }
      };

      // 如果有AI生成的JSON-LD，合并使用
      if (data.seoSettings?.aiSeoJsonLd) {
        try {
          const aiJsonLd = JSON.parse(data.seoSettings.aiSeoJsonLd);
          return { ...baseJsonLd, ...aiJsonLd };
        } catch (error) {
          console.error('Failed to parse AI JSON-LD:', error);
        }
      }

      return baseJsonLd;
    };

    return (
      <>
        {/* 注入JSON-LD结构化数据 */}
        <Script
          id="category-index-jsonld"
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(generateJsonLd()) }}
        />

        {/* 客户端组件 */}
        <CategoryIndexClientPage
          initialData={data}
          routePrefixes={routePrefixes}
        />
      </>
    );
  } catch (error) {
    console.error('Error rendering category index page:', error);
    return notFound();
  }
}