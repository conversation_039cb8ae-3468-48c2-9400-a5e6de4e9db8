'use client';

import React, { useState } from 'react';
import MainLayout from '@/components/layouts/MainLayout';
import Button from '@/components/ui/Button';
import Dropdown from '@/components/ui/Dropdown';
import { useToast, useModal, useDialog } from '@/providers/UIProvider';

/**
 * UI组件演示页面
 */
export default function UIDemo() {
  const toast = useToast();
  const { openModal } = useModal();
  const { openDialog, confirmDialog } = useDialog();
  const [count, setCount] = useState(0);

  // 显示Toast通知
  const showToast = (type: 'info' | 'success' | 'warning' | 'error') => {
    switch (type) {
      case 'success':
        toast.success('操作成功完成！');
        break;
      case 'warning':
        toast.warning('请注意此操作可能有风险。');
        break;
      case 'error':
        toast.error('操作失败，请重试。');
        break;
      case 'info':
      default:
        toast.info('这是一条信息提示。');
        break;
    }
  };

  // 打开模态框
  const showModal = () => {
    openModal({
      title: '欢迎使用模态框',
      content: (
        <div className="py-4">
          <p className="mb-4">这是一个可复用的模态框组件，支持自定义内容和底部操作按钮。</p>
          <p>当前计数：{count}</p>
          <div className="mt-4">
            <Button variant="primary" onClick={() => setCount(count + 1)}>增加计数</Button>
          </div>
        </div>
      ),
      footer: (
        <div className="flex justify-end">
          <Button variant="primary" color="primary">确认</Button>
        </div>
      ),
      size: 'md',
    });
  };

  // 打开确认对话框
  const showDialog = () => {
    openDialog({
      title: '确认操作',
      message: '您确定要执行此操作吗？此操作不可撤销。',
      type: 'warning',
      confirmLabel: '确认执行',
      cancelLabel: '取消',
      onConfirm: () => {
        toast.success('操作已确认！');
      },
      onCancel: () => {
        toast.info('操作已取消。');
      },
    });
  };

  // 使用Promise方式的对话框
  const handleConfirmAction = async () => {
    const confirmed = await confirmDialog({
      title: '删除确认',
      message: '您确定要删除此项目吗？此操作不可撤销。',
      type: 'danger',
      confirmLabel: '删除',
      cancelLabel: '取消',
    });

    if (confirmed) {
      toast.success('项目已成功删除！');
    } else {
      toast.info('已取消删除操作。');
    }
  };

  // 下拉菜单项
  const dropdownItems = [
    { id: 'edit', label: '编辑', onClick: () => toast.info('点击了编辑') },
    { id: 'duplicate', label: '复制', onClick: () => toast.info('点击了复制') },
    { id: 'archive', label: '归档', onClick: () => toast.info('点击了归档') },
    { id: 'divider', label: '', divider: true },
    { id: 'delete', label: '删除', danger: true, onClick: handleConfirmAction },
  ];

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto py-8 px-4">
        <h1 className="text-2xl font-bold mb-8">UI组件演示</h1>

        <section className="mb-12">
          <h2 className="text-xl font-semibold mb-4">Toast通知</h2>
          <div className="flex flex-wrap gap-4">
            <Button variant="primary" onClick={() => showToast('info')} color="primary">显示信息通知</Button>
            <Button variant="primary" onClick={() => showToast('success')} color="success">显示成功通知</Button>
            <Button variant="primary" onClick={() => showToast('warning')} color="warning">显示警告通知</Button>
            <Button variant="primary" onClick={() => showToast('error')} color="error">显示错误通知</Button>
          </div>
        </section>

        <section className="mb-12">
          <h2 className="text-xl font-semibold mb-4">模态框</h2>
          <Button variant="primary" onClick={showModal} color="primary">打开模态框</Button>
        </section>

        <section className="mb-12">
          <h2 className="text-xl font-semibold mb-4">对话框</h2>
          <div className="flex gap-4">
            <Button variant="primary" onClick={showDialog} color="warning">显示确认对话框</Button>
            <Button variant="primary" onClick={handleConfirmAction} color="error">显示删除确认</Button>
          </div>
        </section>

        <section className="mb-12">
          <h2 className="text-xl font-semibold mb-4">下拉菜单</h2>
          <div className="flex gap-4">
            <Dropdown
              trigger={<Button variant="primary">点击下拉菜单</Button>}
              items={dropdownItems}
              placement="bottom-left"
              triggerType="click"
            />
            <Dropdown
              trigger={<Button variant="primary">悬停下拉菜单</Button>}
              items={dropdownItems}
              placement="bottom-right"
              triggerType="hover"
            />
          </div>
        </section>
      </div>
    </MainLayout>
  );
} 