'use client';

import { useState, useMemo } from 'react';

export default function TestFetchPage() {
    const [url, setUrl] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [data, setData] = useState<any>(null);
    const [sideloadRes, setSideloadRes] = useState<any>(null);

    const images = useMemo(() => {
        if (!data?.contentHtml) return [] as string[];
        const $div = document.createElement('div');
        $div.innerHTML = data.contentHtml;
        const imgs = Array.from($div.querySelectorAll('img')) as HTMLImageElement[];
        return imgs.map(img => img.getAttribute('data-src') || img.src).filter(Boolean);
    }, [data]);

    const handleFetch = async () => {
        setIsLoading(true);
        setError(null);
        setData(null);

        try {
            const response = await fetch('/api/weixin-fetch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ url }),
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Failed to fetch');
            }

            setData(result);

        } catch (err: any) {
            setError(err.message);
        } finally {
            setIsLoading(false);
        }
    };

    const testSideload = async () => {
        if (!images.length) return;
        setSideloadRes('加载中...');
        try {
            const wpUrl = `${process.env.NEXT_PUBLIC_WORDPRESS_URL}/wp-json/fd-grabweixin/v1/sideload-image`;
            const res = await fetch(wpUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ imageUrl: images[0] }),
            });
            const json = await res.json();
            setSideloadRes(JSON.stringify(json));
        } catch (e: any) {
            setSideloadRes('错误:' + e.message);
        }
    };

    return (
        <div style={{ padding: '2rem', fontFamily: 'sans-serif' }}>
            <h1>微信文章采集测试</h1>
            <p>输入一个微信公众号文章链接来测试采集功能。</p>
            <div style={{ display: 'flex', gap: '1rem', marginBottom: '1rem' }}>
                <input
                    type="text"
                    value={url}
                    onChange={(e) => setUrl(e.target.value)}
                    placeholder="粘贴微信文章链接..."
                    style={{ width: '400px', padding: '0.5rem' }}
                    disabled={isLoading}
                />
                <button onClick={handleFetch} disabled={isLoading} style={{ padding: '0.5rem 1rem' }}>
                    {isLoading ? '采集中...' : '开始采集'}
                </button>
            </div>

            {error && (
                <div style={{ color: 'red', marginBottom: '1rem' }}>
                    <strong>错误:</strong> {error}
                </div>
            )}

            {data && (
                <div>
                    <h2>采集结果:</h2>
                    <div style={{ background: '#f5f5f5', padding: '1rem', borderRadius: '8px' }}>
                        <h3>标题: {data.title}</h3>
                        <p><strong>描述:</strong> {data.description}</p>
                        <h4>正文 HTML:</h4>
                        <div 
                            style={{ border: '1px solid #ccc', padding: '1rem', maxHeight: '400px', overflowY: 'auto' }}
                            dangerouslySetInnerHTML={{ __html: data.contentHtml }} 
                        />
                    </div>
                </div>
            )}

            {images.length > 0 && (
                <div style={{ marginTop: '1rem' }}>
                    <p>检测到 {images.length} 张图片，首图URL:</p>
                    <code style={{ fontSize: '12px' }}>{images[0]}</code>
                    <div style={{ marginTop: '0.5rem' }}>
                        <button onClick={testSideload} style={{ padding: '0.25rem 0.5rem' }}>测试旁加载首图</button>
                    </div>
                    {sideloadRes && <pre style={{ whiteSpace: 'pre-wrap' }}>{sideloadRes}</pre>}
                </div>
            )}
        </div>
    );
} 