import React from 'react';
import { notFound } from 'next/navigation';
import Script from 'next/script';
import Image from 'next/image';
import { getPageBySlug } from '../../../lib/api';
import MainLayout from '@/components/layouts/MainLayout';
import { BlockRenderer } from '@/components/blocks';

// 启用ISR缓存，1小时重新验证
export const revalidate = 3600;

// 数据获取逻辑
async function fetchPageData(slug: string) {
  return getPageBySlug(slug);
}

/**
 * 为页面动态生成元数据
 * 获取AI生成的SEO信息用于页面元数据
 */
export async function generateMetadata({ params }: { params: { slug: string } }) {
  try {
    const page = await fetchPageData(params.slug);

    if (!page) {
      return {
        title: '页面未找到 - Future Decade',
        description: 'Future Decade - AI驱动的新型科技媒体',
      };
    }

    // 优先使用AI生成的SEO信息
    const metaTitle = page.aiSeoTitle || page.title;
    const metaDescription = page.aiSeoDescription || `${page.title} - Future Decade`;
    const canonicalUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com'}/page/${params.slug}`;

    return {
      title: `${metaTitle} - Future Decade`,
      description: metaDescription,
      alternates: {
        canonical: canonicalUrl,
      },
      openGraph: {
        title: metaTitle,
        description: metaDescription,
        url: canonicalUrl,
        siteName: 'Future Decade',
        type: 'article',
        images: page.featuredImage?.node?.sourceUrl ? [
          {
            url: page.featuredImage.node.sourceUrl,
            width: 1200,
            height: 630,
            alt: page.featuredImage.node.altText || metaTitle,
          }
        ] : [],
        locale: 'zh_CN',
      },
      twitter: {
        card: 'summary_large_image',
        title: metaTitle,
        description: metaDescription,
        images: page.featuredImage?.node?.sourceUrl ? [page.featuredImage.node.sourceUrl] : [],
        site: '@FutureDecade',
        creator: '@FutureDecade',
      },
    };
  } catch (error) {
    console.error(`Error generating metadata for slug "${params.slug}":`, error);
    return {
      title: `${params.slug} - Future Decade`,
      description: 'Future Decade - AI驱动的新型科技媒体',
    };
  }
}

/**
 * 页面详情组件 (服务器组件)
 * 通过slug获取并展示页面内容
 */
export default async function PageDetail({ params }: { params: { slug: string } }) {
  try {
    const page = await fetchPageData(params.slug);

    if (!page) {
      return notFound();
    }

  return (
    <MainLayout>
      {/* 注入 JSON-LD */}
      {page.aiSeoJsonLd && (
        <Script
          id="page-jsonld"
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: page.aiSeoJsonLd }}
        />
      )}
      
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6 text-gray-800">{page.title}</h1>

        {page.featuredImage && (
          <div className="mb-8">
            <Image
              src={page.featuredImage.node.sourceUrl}
              alt={page.featuredImage.node.altText || page.title}
              width={1200}
              height={630}
              className="w-full h-auto rounded-lg shadow-md"
              priority
            />
          </div>
        )}

        {page.blocks && page.blocks.length > 0 ? (
          <div className="prose max-w-none">
            <BlockRenderer blocks={page.blocks} />
          </div>
        ) : (
          <div 
            className="prose max-w-none"
            dangerouslySetInnerHTML={{ __html: page.content || '' }}
          />
        )}
      </div>
    </MainLayout>
  );
  } catch (error) {
    console.error(`Error rendering page for slug "${params.slug}":`, error);
    return notFound();
  }
}