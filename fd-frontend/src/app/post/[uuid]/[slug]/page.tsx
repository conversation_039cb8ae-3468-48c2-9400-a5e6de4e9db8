// @ts-nocheck
import React from 'react';
import Script from 'next/script';
import { notFound, redirect } from 'next/navigation';
import { getPostByUuid, isValidUuid, getFdMemberSettings } from '@/lib/api';
import RelatedPosts from '@/components/post/RelatedPosts';
import ShareButtons from '@/components/share/ShareButtons';
import { Post } from '@/types/post';
import MainLayout from '@/components/layouts/MainLayout';
import dynamic from 'next/dynamic';

const CommentSection = dynamic(() => import('@/components/comments/CommentSection'), {
  loading: () => <div className="text-center p-8">加载评论区...</div>,
});

// 导入文章模板组件
import { 
  StandardTemplate, 
  CoverTemplate, 
  HeroTitleTemplate, 
  VideoTemplate 
} from '@/components/templates';

// 设置ISR模式，60分钟重新验证
export const revalidate = 3600;

/**
 * 比较两个slug是否匹配，考虑编码差异
 * @param slug1 第一个slug
 * @param slug2 第二个slug
 * @returns 是否匹配
 */
function slugsMatch(slug1: string, slug2: string): boolean {
  // 解码用于比较的slug
  const decoded1 = decodeURIComponent(slug1);
  const decoded2 = decodeURIComponent(slug2);
  
  // 比较解码后的slug
  if (decoded1 === decoded2) {
    return true;
  }
  
  // 检查是否包含中文字符
  const hasChinese = /[\u4e00-\u9fa5]/.test(decoded1) || /[\u4e00-\u9fa5]/.test(decoded2);
  
  // 对于中文slug，进行更宽松的比较
  if (hasChinese) {
    // 如果长度相似，视为匹配以避免重定向循环
    const lengthDifference = Math.abs(decoded1.length - decoded2.length);
    if (lengthDifference <= 5) {
      return true;
    }
    
    // 移除常见标点和空格后比较
    const clean1 = decoded1.replace(/[-_《》【】\s'"：，。、？！（）()]/g, '');
    const clean2 = decoded2.replace(/[-_《》【】\s'"：，。、？！（）()]/g, '');
    
    if (clean1 === clean2) {
      return true;
    }
  }
  
  return false;
}

// 设置动态元数据
export async function generateMetadata({ params }: { params: { uuid: string; slug: string } }) {
  const { uuid, slug } = params;

  // 验证UUID格式
  if (!isValidUuid(uuid)) {
    console.warn(`无效的UUID格式: "${uuid}"，应为YYMMDD-123456格式`);
    return { title: '无效的文章ID' };
  }

  const post = await getPostByUuid(uuid);

  if (!post) return { title: '文章未找到' };

  // 从摘要中移除HTML标签，以获得纯文本
  const cleanExcerpt = post.excerpt ? post.excerpt.replace(/<[^>]*>?/gm, '') : '';

  const metaTitle = post.aiSeoTitle || post.title;
  const metaDescription = post.aiSeoDescription || cleanExcerpt || '';

  // 获取路由前缀用于构建canonical URL
  let postPrefix = 'articles';
  try {
    const { getRoutePrefixes } = await import('@/lib/route-prefixes');
    const routePrefixes = await getRoutePrefixes();
    postPrefix = routePrefixes.postPrefix || 'articles';
  } catch (error) {
    console.error('Error fetching route prefixes for metadata:', error);
  }

  const canonicalUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com'}/${postPrefix}/${uuid}/${slug}`;

  return {
    title: `${metaTitle} - Future Decade`,
    description: metaDescription,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      url: canonicalUrl,
      siteName: 'Future Decade',
      type: 'article',
      images: post.featuredImage?.node?.sourceUrl ? [
        {
          url: post.featuredImage.node.sourceUrl,
          width: 1200,
          height: 630,
          alt: post.featuredImage.node.altText || metaTitle,
        }
      ] : [],
      locale: 'zh_CN',
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: post.featuredImage?.node?.sourceUrl ? [post.featuredImage.node.sourceUrl] : [],
      site: '@FutureDecade',
      creator: '@FutureDecade',
    },
  };
}

export default async function ArticlePage({ params }: { 
  params: { uuid: string; slug: string } 
}) {
  const { uuid, slug } = params;
  
  // 验证UUID格式 (YYMMDD-123456)，如果无效则立即返回404
  if (!isValidUuid(uuid)) {
    console.error(`无效的UUID格式: "${uuid}"`);
    return notFound();
  }
  
  const [post, settings] = await Promise.all([
    getPostByUuid(uuid),
    getFdMemberSettings()
  ]);
  
  // 如果文章不存在，返回404
  if (!post) {
    return notFound();
  }
  
  // 从摘要中移除HTML标签，以获得纯文本
  const cleanExcerpt = post.excerpt ? post.excerpt.replace(/<[^>]*>?/gm, '') : '';
  
  // 调试输出：显示相关文章数量
  console.log(`===相关文章调试信息===`);
  console.log(`文章标题: ${post.title}`);
  console.log(`文章ID: ${post.databaseId}`);
  console.log(`相关文章数量: ${post.relatedPosts?.length || 0}`);
  console.log(`相关文章标题: ${post.relatedPosts?.map((p: Post) => p.title).join(', ')}`);
  console.log(`=====================`);
  
  // 获取路由前缀数据，用于正确重定向
  let postPrefix = 'articles'; // 默认值
  try {
    const { getRoutePrefixes } = await import('@/lib/route-prefixes');
    const routePrefixes = await getRoutePrefixes();
    postPrefix = routePrefixes.postPrefix || 'articles';
  } catch (error) {
    console.error('Error fetching post prefix:', error);
  }
  
  // 检查slug是否匹配，同时考虑编码差异
  if (!slugsMatch(post.slug, slug)) {
    // 尝试重定向到正确的URL
    return redirect(`/${postPrefix}/${uuid}/${encodeURIComponent(post.slug)}`);
  }

  // 根据postTemplate.templateType选择不同的模板渲染
  const templateType = post.postTemplate?.templateType?.[0] || 'standard';
  
  // 选择要使用的模板组件
  let TemplateComponent;
  switch (templateType) {
    case 'cover':
      TemplateComponent = CoverTemplate;
      break;
    case 'hero_title':
      TemplateComponent = HeroTitleTemplate;
      break;
    case 'video':
      TemplateComponent = VideoTemplate;
      break;
    case 'standard':
    default:
      TemplateComponent = StandardTemplate;
      break;
  }
  
  return (
    <MainLayout>
      <div className="post-container">
        {/* 注入 JSON-LD */}
        {post.aiSeoJsonLd && (
          <Script
            id="post-jsonld"
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: post.aiSeoJsonLd }}
          />
        )}
        
        <TemplateComponent post={post} paywallVariant={settings.paywallVariant} />
        
        {/* 分享按钮 */}
        <div className="max-w-4xl mx-auto px-4">
          <ShareButtons 
            postId={post.databaseId.toString()} 
            postTitle={post.title}
            postUrl={`${process.env.NEXT_PUBLIC_SITE_URL || ''}/${postPrefix}/${uuid}/${post.slug}`}
            postExcerpt={cleanExcerpt}
            shareImage={post.shareImage}
          />
        </div>
        
        {/* 相关文章 */}
        {post.relatedPosts && post.relatedPosts.length > 0 && (
          <RelatedPosts posts={post.relatedPosts} />
        )}
        
        {/* 评论区 */}
        <div className="max-w-4xl mx-auto px-4">
          <CommentSection 
            postId={post.databaseId || 0} 
            commentStatus={post.commentStatus} 
            initialData={{
              nodes: post.comments?.nodes ?? [],
              pageInfo: post.comments?.pageInfo ?? { hasNextPage: false, endCursor: null },
            }}
          />
        </div>
      </div>
    </MainLayout>
  );
} 