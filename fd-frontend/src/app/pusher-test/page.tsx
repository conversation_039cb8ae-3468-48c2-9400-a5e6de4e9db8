'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useWebSocket } from '@/contexts/WebSocketContext';
import { useAuth } from '@/hooks/useAuth';
import { getAuthToken } from '@/utils/auth-utils';

// --- STYLES ---
const styles = {
  container: { fontFamily: 'sans-serif', padding: '20px', maxWidth: '1200px', margin: 'auto' },
  header: { borderBottom: '2px solid #eee', paddingBottom: '10px', marginBottom: '20px' },
  grid: { display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' },
  section: { border: '1px solid #ccc', borderRadius: '8px', padding: '15px' },
  sectionTitle: { marginTop: '0', borderBottom: '1px solid #eee', paddingBottom: '10px' },
  logContainer: { height: '400px', overflowY: 'auto', backgroundColor: '#f9f9f9', padding: '10px', borderRadius: '4px', fontSize: '14px' },
  logEntry: { marginBottom: '8px', paddingBottom: '8px', borderBottom: '1px dashed #ddd' },
  eventName: { fontWeight: 'bold', color: '#007bff' },
  statusConnected: { color: 'green', fontWeight: 'bold' },
  statusDisconnected: { color: 'red', fontWeight: 'bold' },
  code: { backgroundColor: '#eef', padding: '2px 5px', borderRadius: '3px', fontFamily: 'monospace' },
  button: { padding: '8px 15px', marginRight: '10px', cursor: 'pointer', border: '1px solid #ccc', borderRadius: '4px' }
} as const; // 使用 const断言，确保TS推断出最具体的类型

// --- Test Page Component ---
export default function PusherTestPage() {
  const { socket, isConnected } = useWebSocket();
  const { isAuthenticated, user } = useAuth();
  const [logs, setLogs] = useState<any[]>([]);
  const logContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll log
  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logs]);

  // Log WebSocket events
  useEffect(() => {
    if (!socket) return;

    const logEvent = (event: string, ...args: any[]) => {
      setLogs((prevLogs: any[]) => [...prevLogs, { event, data: args, timestamp: new Date() }]);
    };
    
    socket.onAny(logEvent);

    return () => {
      socket.offAny(logEvent);
    };
  }, [socket]);
  
  // Manual trigger for unlock test
  const handleTestUnlock = () => {
      const postId = prompt("请输入要测试解锁的文章ID:", "1");
      if(postId) {
        const url = `/wp-admin/?test_unlock_push=true&post_id=${postId}`;
        window.open(url, '_blank');
      }
  };

  return (
    <div style={styles.container}>
      <header style={styles.header}>
        <h1>WebSocket Pusher Test Page</h1>
      </header>
      
      <div style={styles.grid}>
        {/* --- Status Section --- */}
        <section style={styles.section}>
          <h2 style={styles.sectionTitle}>Connection Status</h2>
          <p>
            <strong>WebSocket Status:</strong>{' '}
            {isConnected ? <span style={styles.statusConnected}>CONNECTED</span> : <span style={styles.statusDisconnected}>DISCONNECTED</span>}
          </p>
          <p>
            <strong>User Status:</strong>{' '}
            {isAuthenticated ? <span>Logged-in as <code style={styles.code}>{user?.name}</code> (ID: {user?.databaseId})</span> : <span>Guest User</span>}
          </p>
          {isAuthenticated && user?.memberLevel && (
             <p><strong>Membership Level:</strong> <code style={styles.code}>{user.memberLevel.name} (ID: {user.memberLevel.id})</code></p>
          )}
           {isAuthenticated && <p><strong>JWT Token:</strong> <textarea readOnly style={{ width: '100%', height: '60px', fontSize: '12px' }} value={getAuthToken() || ''} /></p>}
        </section>

        {/* --- Actions Section --- */}
        <section style={styles.section}>
            <h2 style={styles.sectionTitle}>Test Actions</h2>
            <p>在WordPress后台进行操作 (例如更新文章)，查看下面的事件日志。</p>
            <div>
                <button onClick={handleTestUnlock} style={styles.button} disabled={!isAuthenticated}>
                    Test Post Unlock
                </button>
            </div>
            <p style={{ fontSize: '12px', color: '#666' }}>
                文章解锁测试需要您先登录，并且需要在 `fd-pusher.php` 中临时添加测试代码。
            </p>
        </section>
      </div>

      {/* --- Event Log Section --- */}
      <section style={{...styles.section, marginTop: '20px'}}>
        <h2 style={styles.sectionTitle}>Live Event Log</h2>
        <div ref={logContainerRef} style={styles.logContainer}>
          {logs.map((log, index) => (
            <div key={index} style={styles.logEntry}>
              <div><strong>Timestamp:</strong> {log.timestamp.toLocaleTimeString()}</div>
              <div><strong style={styles.eventName}>Event: {log.event}</strong></div>
              <div>
                <strong>Data:</strong>
                <pre style={styles.code}>{JSON.stringify(log.data, null, 2)}</pre>
              </div>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
} 