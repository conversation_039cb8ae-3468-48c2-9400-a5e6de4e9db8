import MainLayout from '@/components/layouts/MainLayout'
import ConversationList from '@/components/messaging/ConversationList'
import ChatWindow from '@/components/messaging/ChatWindow'

function PleaseSelectAConversation() {
  return (
    <div className="flex h-full items-center justify-center bg-gray-50">
      <div className="text-center">
        <h2 className="text-2xl font-semibold text-gray-700">请选择一个会话</h2>
        <p className="mt-2 text-gray-500">从左侧选择一个已有会话，或开始一个新会话。</p>
      </div>
    </div>
  )
}


export default function MessagesPage({ params }: { params: { id?: string[] } }) {
  const conversationId = params.id?.[0]

  return (
    <MainLayout>
      <div className="flex h-[85vh] border border-gray-200 bg-white rounded-lg overflow-hidden shadow-sm my-4">
        <div className="w-1/3 border-r border-gray-200 bg-white">
          <ConversationList />
        </div>
        <main className="w-2/3">
          {conversationId ? (
            <ChatWindow conversationId={conversationId} />
          ) : (
            <PleaseSelectAConversation />
          )}
        </main>
      </div>
    </MainLayout>
  )
} 