import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { RoutePrefixes } from './types/routes';
import { getRoutePrefixes } from './lib/api'; // 导入统一的函数
import { isProtectedRoute, isGuestOnlyRoute } from './config/routes-config';

// 定义auth相关页面的路径映射
const AUTH_PATHS = [
  'login',
  'register',
  'forgot-password',
  'reset-password',
  'change-password',
  'profile'
];

/**
 * 中间件，处理路由前缀和索引页路径
 * 功能:
 * 1. 将错误的文章路径前缀返回404（不再进行重定向）
 * 2. 将自定义的分类索引页路径内部重写到category-index（保持用户友好URL）
 * 3. 将自定义的标签索引页路径内部重写到tag-index（保持用户友好URL）
 * 4. 将category-index重定向到自定义的分类索引页路径
 * 5. 将tag-index重定向到自定义的标签索引页路径
 * 6. 将/post/[uuid]/[slug]内部重写到[prefix]/[uuid]/[slug]
 * 7. 严格验证[prefix]/[uuid]/[slug]格式，前缀不正确直接返回404
 * 8. 处理自定义类型URL格式: /post-type/[type]/[uuid]/[slug]
 * 9. 处理页面路由: 将/page/[slug]重定向到/[slug]
 * 10. 将/{categoryPrefix}/{slug}重写到/category/{slug}（有分类前缀时）
 * 11. 将/{tagPrefix}/{slug}重写到/tag/{slug}（有标签前缀时）
 * 12. 将/category/{slug}重定向到/{categoryPrefix}/{slug}（有分类前缀时）
 *    或重定向到/slug（无分类前缀时）
 * 13. 将/tag/{slug}重定向到/{tagPrefix}/{slug}（有标签前缀时）
 *    或重定向到/slug（无标签前缀时）
 * 14. 将/taxonomy/[taxonomy]/[slug]重定向到/[taxonomy]/[slug]（自定义分类法条目）
 * 15. 将/taxonomy/[taxonomy]重定向到/[taxonomy]（自定义分类法归档页）
 * 16. 无前缀情况下通过slug映射表解析单级路径并重写到相应内部路径
 * 17. 将/[taxonomy]/[slug]重写到/taxonomy/[taxonomy]/[slug]（处理自定义分类法条目的友好URL）
 * 18. 将/[taxonomy]重写到/taxonomy/[taxonomy]（处理自定义分类法归档页的友好URL）
 * 19. 将自定义类型路径 /post-type/[type] 重定向到 /[type]
 * 20. 将单级路径 /[type] 内部重写到 /post-type/[type]（当[type]是自定义文章类型时）
 * 21. 将/post-type/[type]/[uuid]/[slug]重定向到/[type]/[uuid]/[slug]
 * 22. 将/[type]/[uuid]/[slug]内部重写到/post-type/[type]/[uuid]/[slug]
 * 23. 路由认证保护：如果是受保护路由，则检查用户是否已登录，如果未登录则重定向到登录页面
 * 24. 路由认证保护：如果是仅限游客路由，则检查用户是否已登录，如果已登录则重定向到首页
 * 25. 将/auth/[path]路径重定向到/[path]（如/auth/login -> /login）
 * 26. 将/[path]路径内部重写到/auth/[path]（如/login -> /auth/login）
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const pathSegments = pathname.split('/').filter(Boolean);
  
  // 更新UUID验证正则表达式为新格式: YYMMDD-123456
  const UUID_REGEX = /^\d{6}-\d{6}$/;
  
  try {
    // 处理auth路径
    // ------------------------------
    // 情况25: 将/auth/[path]路径重定向到/[path]
    if (pathSegments.length >= 2 && pathSegments[0] === 'auth' && AUTH_PATHS.includes(pathSegments[1])) {
      const restPath = pathSegments.slice(1).join('/');
      if (process.env.NODE_ENV === 'development') {
        console.log(`将/auth/${restPath}重定向到/${restPath}`);
      }
      return NextResponse.redirect(new URL(`/${restPath}`, request.url));
    }

    // 情况26: 将/[path]路径内部重写到/auth/[path]
    if (pathSegments.length >= 1 && AUTH_PATHS.includes(pathSegments[0])) {
      // 排除一些可能的特殊情况，比如可能有名为"login"的标签或分类
      const isAuthPath = true; // 这里可以添加更多的判断逻辑

      if (isAuthPath) {
        const fullPath = pathSegments.join('/');
        if (process.env.NODE_ENV === 'development') {
          console.log(`将/${fullPath}重写到/auth/${fullPath}`);
        }
        return NextResponse.rewrite(new URL(`/auth/${fullPath}`, request.url));
      }
    }
    // ------------------------------
    
    // 认证路由保护逻辑
    // ------------------------------
    // 从Cookie中获取认证令牌
    const authToken = request.cookies.get('auth_token')?.value;
    const isLoggedIn = !!authToken;
    
    // 如果是受保护的路由，但用户未登录，则重定向到登录页面
    if (isProtectedRoute(pathname) && !isLoggedIn) {
      const callbackUrl = encodeURIComponent(request.nextUrl.href);
      return NextResponse.redirect(new URL(`/login?callbackUrl=${callbackUrl}`, request.url));
    }
    
    // 如果是只允许未登录用户访问的路由，但用户已登录，则重定向到首页
    if (isGuestOnlyRoute(pathname) && isLoggedIn) {
      return NextResponse.redirect(new URL('/', request.url));
    }
    // ------------------------------
    
    // 获取路由前缀和索引页路径设置
    const prefixes: RoutePrefixes = await getRoutePrefixes();
    
    // 开发环境下记录路由前缀，便于调试
    if (process.env.NODE_ENV === 'development') {
      console.log('中间件使用的路由前缀:', {
        postPrefix: prefixes.postPrefix,
        categoryPrefix: prefixes.categoryPrefix,
        tagPrefix: prefixes.tagPrefix,
        categoryIndexRoute: prefixes.categoryIndexRoute,
        tagIndexRoute: prefixes.tagIndexRoute
      });
    }
    
    // 如果已配置了自定义路径且不是默认值
    const hasCustomCategoryRoute = prefixes.categoryIndexRoute !== 'category-index';
    const hasCustomTagRoute = prefixes.tagIndexRoute !== 'tag-index';
    
    // 检查是否配置了分类和标签前缀
    const hasCategoryPrefix = prefixes.categoryPrefix !== null && prefixes.categoryPrefix !== '';
    const hasTagPrefix = prefixes.tagPrefix !== null && prefixes.tagPrefix !== '';
    
    // 情况21: 将/post-type/[type]/[uuid]/[slug]重定向到/[type]/[uuid]/[slug]
    if (pathSegments.length === 4 && pathSegments[0] === 'post-type') {
      const [_, type, uuid, slug] = pathSegments;
      const isUuidPattern = UUID_REGEX.test(uuid);
      
      if (isUuidPattern) {
        // 重定向到友好URL（用户会看到URL变化）
        return NextResponse.redirect(
          new URL(`/${type}/${uuid}/${slug}`, request.url)
        );
      }
    }
    
    // --- fast-lane detail rule ---
    if (pathSegments.length === 4 && pathSegments[0] === 'post-type') {
      const [_, type, uuid, slug] = pathSegments;
      const isUuidPattern = UUID_REGEX.test(uuid);
      
      if (isUuidPattern) {
        // 这是有效的自定义类型详情页URL，无需重写
        return NextResponse.next();
      }
    }
    
    // --- generic redirect /post-type/[type]/[uuid]/[slug] -> /[type]/[uuid]/[slug] ---
    if (pathSegments.length === 4 && pathSegments[0] === 'post-type') {
      const [_, type, uuid, slug] = pathSegments;

      const isUuidPattern = UUID_REGEX.test(uuid);
          
      if (isUuidPattern) {
        // 重定向到友好URL（用户会看到URL变化）
        return NextResponse.redirect(
          new URL(`/${type}/${uuid}/${slug}`, request.url)
            );
          }
    }
    
    // --- generic redirect /post-type/[type] -> /[type] ---
    if (pathSegments.length === 2 && pathSegments[0] === 'post-type') {
      const [_, type] = pathSegments;
      
      // 重定向到单级路径（用户会看到URL变化）
      return NextResponse.redirect(
        new URL(`/${type}`, request.url)
      );
    }
    
    // 情况20: 将单级路径 /[type] 内部重写到 /post-type/[type]（当[type]是自定义文章类型时）
    if (pathSegments.length === 1) {
      const potentialType = pathSegments[0];
      
      // 排除已知的特殊路径
      const specialPaths = ['category-index', 'tag-index', prefixes.categoryIndexRoute, prefixes.tagIndexRoute, 'post'];
      if (!specialPaths.includes(potentialType)) {
        // 使用slug映射表查询是否为自定义文章类型
        const slugResponse = await fetch(
          `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              query: `
                query ResolveSingleSlug {
                  resolveSinglePathSlug(slug: "${potentialType}") {
                    slug
                    type
                    id
                  }
                }
              `
            }),
            next: { revalidate: 0 }
          } as RequestInit
        );
        
        const slugData = await slugResponse.json();
        const resolvedSlug = slugData?.data?.resolveSinglePathSlug;
        
        if (resolvedSlug && resolvedSlug.type === 'post_type') {
          // 内部重写（用户不会看到URL变化）
          return NextResponse.rewrite(
            new URL(`/post-type/${potentialType}`, request.url)
          );
        }
      }
    }
    
    // 情况1: 用户访问 category-index，但已设置自定义路径
    if (pathSegments.length === 1 && pathSegments[0] === 'category-index' && hasCustomCategoryRoute) {
      // 重定向到自定义路径
      return NextResponse.redirect(
        new URL(`/${prefixes.categoryIndexRoute}`, request.url)
      );
    }
    
    // 情况2: 用户访问 tag-index，但已设置自定义路径
    if (pathSegments.length === 1 && pathSegments[0] === 'tag-index' && hasCustomTagRoute) {
      // 重定向到自定义路径
      return NextResponse.redirect(
        new URL(`/${prefixes.tagIndexRoute}`, request.url)
      );
    }
    
    // 情况3: 用户访问自定义的分类索引页路径
    if (pathSegments.length === 1 && pathSegments[0] === prefixes.categoryIndexRoute && hasCustomCategoryRoute) {
      // 使用重写而非重定向，保持URL不变但内部映射到category-index
      return NextResponse.rewrite(
        new URL('/category-index', request.url)
      );
    }
    
    // 情况4: 用户访问自定义的标签索引页路径
    if (pathSegments.length === 1 && pathSegments[0] === prefixes.tagIndexRoute && hasCustomTagRoute) {
      // 使用重写而非重定向，保持URL不变但内部映射到tag-index
      return NextResponse.rewrite(
        new URL('/tag-index', request.url)
      );
    }
    
    // --- 文章URL处理逻辑 (已更新) ---

    // 情况5: 用户访问内部路由 /post/[uuid]/[slug]
    // 应该永久重定向到公开的、带前缀的规范URL
    if (pathSegments.length === 3 && pathSegments[0] === 'post') {
      const [_, uuid, slug] = pathSegments;
      const isUuidPattern = UUID_REGEX.test(uuid);
      
      if (isUuidPattern) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[Middleware] Redirecting internal route /post/... to public route /${prefixes.postPrefix}/...`);
        }
        // 执行301永久重定向
        const publicUrl = new URL(`/${prefixes.postPrefix}/${uuid}/${slug}`, request.url);
        return NextResponse.redirect(publicUrl, 301);
      }
    }

    // 原情况6 (将 /{postPrefix}/... 重写到 /post/...) 已被移除。
    // 因为我们创建了 app/[prefix]/[uuid]/[slug]/page.tsx 文件，
    // Next.js 现在可以直接解析带前缀的URL，不再需要中间件重写。
    // 这从根本上解决了RSC 404问题。
    
    // --- 文章URL处理逻辑结束 ---


    // 情况7: 处理自定义类型路径 - 详情页
    if (pathSegments.length === 4 && pathSegments[0] === 'post-type') {
      const [_, type, uuid, slug] = pathSegments;
      const isUuidPattern = UUID_REGEX.test(uuid);
      
      if (isUuidPattern) {
        // 这是有效的自定义类型详情页URL，无需重写
        return NextResponse.next();
      }
    }
    
    // 情况9: 处理页面路由: /page/[slug]重定向到/[slug]
    if (pathSegments.length === 2 && pathSegments[0] === 'page') {
      const [_, slug] = pathSegments;
      
      // 记录重定向信息，便于调试
      if (process.env.NODE_ENV === 'development') {
        console.log(`将/page/${slug}重定向到/${slug}`);
      }
      
      // 重定向到单级路径
      return NextResponse.redirect(
        new URL(`/${slug}`, request.url)
      );
    }
    
    // 情况10: 处理分类页路由 - 当分类前缀存在时，将/{categoryPrefix}/{slug}重写到/category/{slug}
    if (hasCategoryPrefix && pathSegments.length === 2 && pathSegments[0] === prefixes.categoryPrefix) {
      const [_, slug] = pathSegments;
      
      // 记录重写信息，便于调试
      if (process.env.NODE_ENV === 'development') {
        console.log(`将/${prefixes.categoryPrefix}/${slug}重写到/category/${slug}`);
      }
      
      // 使用重写保持URL不变但内部映射到category路径
      return NextResponse.rewrite(
        new URL(`/category/${slug}`, request.url)
      );
    }
    
    // 情况11: 处理标签页路由 - 当标签前缀存在时，将/{tagPrefix}/{slug}重写到/tag/{slug}
    if (hasTagPrefix && pathSegments.length === 2 && pathSegments[0] === prefixes.tagPrefix) {
      const [_, slug] = pathSegments;
      
      // 记录重写信息，便于调试
      if (process.env.NODE_ENV === 'development') {
        console.log(`将/${prefixes.tagPrefix}/${slug}重写到/tag/${slug}`);
      }
      
      // 使用重写保持URL不变但内部映射到tag路径
      return NextResponse.rewrite(
        new URL(`/tag/${slug}`, request.url)
      );
    }
    
    // 情况12: 处理内部分类路径 - 当分类前缀存在时重定向到友好URL，当前缀不存在时重定向到单级路径
    if (pathSegments.length === 2 && pathSegments[0] === 'category') {
      const [_, slug] = pathSegments;
      
      if (hasCategoryPrefix) {
        // 有分类前缀，重定向到带前缀的URL
        // 记录重定向信息，便于调试
        if (process.env.NODE_ENV === 'development') {
          console.log(`将/category/${slug}重定向到/${prefixes.categoryPrefix}/${slug}`);
        }
        
        // 使用重定向到用户友好URL
        return NextResponse.redirect(
          new URL(`/${prefixes.categoryPrefix}/${slug}`, request.url)
        );
      } else {
        // 无分类前缀，重定向到单级路径
        // 记录重定向信息，便于调试
        if (process.env.NODE_ENV === 'development') {
          console.log(`将/category/${slug}重定向到/${slug}`);
        }
        
        // 重定向到单级路径
        return NextResponse.redirect(
          new URL(`/${slug}`, request.url)
        );
      }
    }
    
    // 情况13: 处理内部标签路径 - 当标签前缀存在时重定向到友好URL，当前缀不存在时重定向到单级路径
    if (pathSegments.length === 2 && pathSegments[0] === 'tag') {
      const [_, slug] = pathSegments;
      
      if (hasTagPrefix) {
        // 有标签前缀，重定向到带前缀的URL
        // 记录重定向信息，便于调试
        if (process.env.NODE_ENV === 'development') {
          console.log(`将/tag/${slug}重定向到/${prefixes.tagPrefix}/${slug}`);
        }
        
        // 使用重定向到用户友好URL
        return NextResponse.redirect(
          new URL(`/${prefixes.tagPrefix}/${slug}`, request.url)
        );
      } else {
        // 无标签前缀，重定向到单级路径
        // 记录重定向信息，便于调试
        if (process.env.NODE_ENV === 'development') {
          console.log(`将/tag/${slug}重定向到/${slug}`);
        }
        
        // 重定向到单级路径
        return NextResponse.redirect(
          new URL(`/${slug}`, request.url)
        );
      }
    }
    
    // 情况14: 处理自定义分类法路径 - 将/taxonomy/[taxonomy]/[slug]重定向到/[taxonomy]/[slug]
    if (pathSegments.length === 3 && pathSegments[0] === 'taxonomy') {
      const [_, taxonomy, slug] = pathSegments;
      
      // 记录重定向信息，便于调试
      if (process.env.NODE_ENV === 'development') {
        console.log(`将/taxonomy/${taxonomy}/${slug}重定向到/${taxonomy}/${slug}`);
      }
      
      // 重定向到二级路径 /[taxonomy]/[slug]
      return NextResponse.redirect(
        new URL(`/${taxonomy}/${slug}`, request.url)
      );
    }
    
    // 情况15: 处理自定义分类法归档页 - 将/taxonomy/[taxonomy]重定向到/[taxonomy]
    if (pathSegments.length === 2 && pathSegments[0] === 'taxonomy') {
      const [_, taxonomy] = pathSegments;
      
      // 记录重定向信息，便于调试
      if (process.env.NODE_ENV === 'development') {
        console.log(`将/taxonomy/${taxonomy}重定向到/${taxonomy}`);
      }
      
      // 重定向到单级路径
      return NextResponse.redirect(
        new URL(`/${taxonomy}`, request.url)
      );
    }
    
    // 情况16: 处理无前缀情况下的单级路径 - 根据slug映射表确定类型
    if (pathSegments.length === 1 && 
        (!hasCategoryPrefix || !hasTagPrefix) && 
        // 排除已知的内部路径
        !['category-index', 'tag-index', prefixes.categoryIndexRoute, prefixes.tagIndexRoute].includes(pathSegments[0])) {
      
      const slug = pathSegments[0];
      
      // 记录解析信息，便于调试
      if (process.env.NODE_ENV === 'development') {
        console.log(`尝试解析无前缀路径: /${slug}`);
      }
      
      try {
        // 查询slug映射表解析单级路径
        const slugResponse = await fetch(
          `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              query: `
                query ResolveSingleSlug {
                  resolveSinglePathSlug(slug: "${slug}") {
                    slug
                    type
                    id
                    taxonomy
                  }
                }
              `
            }),
            next: { revalidate: 0 } // 不缓存结果，确保获取最新映射
          } as RequestInit
        );
        
        const slugData = await slugResponse.json();
        const resolvedSlug = slugData?.data?.resolveSinglePathSlug;
        
        if (resolvedSlug) {
          // 记录解析结果，便于调试
          if (process.env.NODE_ENV === 'development') {
            console.log(`解析结果: `, resolvedSlug);
          }
          
          // 根据解析结果路由到正确的内部路径
          switch (resolvedSlug.type) {
            case 'page':
              // 页面路由
              return NextResponse.rewrite(
                new URL(`/page/${slug}`, request.url)
              );
              
            case 'category':
              // 分类页路由
              return NextResponse.rewrite(
                new URL(`/category/${slug}`, request.url)
              );
              
            case 'tag':
              // 标签页路由
              return NextResponse.rewrite(
                new URL(`/tag/${slug}`, request.url)
              );
              
            case 'taxonomy_archive':
              // 自定义分类法归档页
              const taxonomy = resolvedSlug.taxonomy;
              
              // 记录重写信息，便于调试
              if (process.env.NODE_ENV === 'development') {
                console.log(`将/${slug}重写到/taxonomy/${taxonomy}`);
              }
              
              return NextResponse.rewrite(
                new URL(`/taxonomy/${taxonomy}`, request.url)
              );
              
            case 'post_type':
              // 自定义文章类型归档页
              const postType = resolvedSlug.id;
              return NextResponse.rewrite(
                new URL(`/post-type/${postType}`, request.url)
              );
              
            default:
              // 未知类型，继续处理
              if (process.env.NODE_ENV === 'development') {
                console.log(`未知的内容类型: ${resolvedSlug.type}`);
              }
              break;
          }
        } else {
          // 未能解析slug，可能是不存在的路径
          if (process.env.NODE_ENV === 'development') {
            console.log(`未能在slug映射表中找到: ${slug}`);
          }
        }
      } catch (error) {
        console.error('Error resolving slug:', error);
      }
    }
    
    // 情况17: 处理自定义分类法条目的友好URL - 将/[taxonomy]/[slug]重写到/taxonomy/[taxonomy]/[slug]
    if (pathSegments.length === 2) {
      const [taxonomy, slug] = pathSegments;
      
      // 先检查这是否是分类或标签路径（如果有前缀）
      if ((hasCategoryPrefix && taxonomy === prefixes.categoryPrefix) || 
          (hasTagPrefix && taxonomy === prefixes.tagPrefix)) {
        // 这是分类或标签路径，已在前面处理过，跳过
        return NextResponse.next();
      }
      
      try {
        // 查询taxonomy是否是有效的自定义分类法
        const taxResponse = await fetch(
          `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              query: `
                query ResolveSingleSlug {
                  resolveSinglePathSlug(slug: "${taxonomy}") {
                    slug
                    type
                    id
                    taxonomy
                  }
                }
              `
            }),
            next: { revalidate: 0 }
          } as RequestInit
        );
        
        const taxData = await taxResponse.json();
        const resolvedTax = taxData?.data?.resolveSinglePathSlug;
        
        if (resolvedTax && resolvedTax.type === 'taxonomy_archive') {
          // 记录重写信息，便于调试
          if (process.env.NODE_ENV === 'development') {
            console.log(`将/${taxonomy}/${slug}重写到/taxonomy/${taxonomy}/${slug}`);
          }
          
          // 重写到内部路径
          return NextResponse.rewrite(
            new URL(`/taxonomy/${taxonomy}/${slug}`, request.url)
          );
        }
      } catch (error) {
        console.error('Error resolving taxonomy:', error);
      }
    }
    
    // 情况18: 处理自定义分类法归档页的友好URL - 将/[taxonomy]重写到/taxonomy/[taxonomy]
    // 注意：这已经在情况16中处理了，当slug映射表返回taxonomy_archive类型时
  } catch (error) {
    console.error('Error in middleware:', error);
    // 出错时不阻止请求继续
  }
  
  return NextResponse.next();
}

// 匹配所有动态段路径
export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
}; 