'use client';

import React, { useState, useEffect } from 'react';
import { useVerificationCode } from '../hooks/useVerificationCode';

/**
 * 验证码示例组件
 * 演示如何使用useVerificationCode Hook进行各种验证码操作
 */
export default function VerificationCodeExample() {
  // 使用通用验证码Hook，设置默认类型为email_verification
  const { 
    sendVerificationCode, 
    verifyVerificationCode, 
    isLoading, 
    error, 
    countdown,
    clearError 
  } = useVerificationCode({ defaultType: 'email_verification' });
  
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [verificationType, setVerificationType] = useState('email_verification');
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);
  const [isVerified, setIsVerified] = useState(false);
  
  // 发送验证码
  const handleSendCode = async (e: React.FormEvent) => {
    e.preventDefault();
    setResult(null);
    
    // 根据不同的验证类型设置不同的邮件模板
    let templateData;
    if (verificationType === 'email_verification') {
      templateData = {
        subject: '请验证您的邮箱',
        title: '邮箱验证',
        intro: '感谢您注册我们的网站。请验证您的邮箱地址。',
        expire_notice: '此验证码将在24小时内有效。'
      };
    } else if (verificationType === 'password_reset') {
      templateData = {
        subject: '密码重置验证码',
        title: '密码重置',
        intro: '您请求重置密码。',
        expire_notice: '此验证码将在10分钟内有效。如果您没有请求重置密码，请忽略此邮件。'
      };
    } else if (verificationType === 'sensitive_action') {
      templateData = {
        subject: '安全操作验证',
        title: '敏感操作确认',
        intro: '您正在进行敏感操作。为确保安全，请输入以下验证码完成操作。',
        expire_notice: '此验证码将在5分钟内有效，为了您的账户安全，请勿泄露验证码。'
      };
    }
    
    try {
      const response = await sendVerificationCode(email, verificationType, templateData);
      setResult(response);
    } catch (error) {
      console.error('发送验证码失败:', error);
      setResult({ success: false, message: error instanceof Error ? error.message : '未知错误' });
    }
  };
  
  // 验证验证码
  const handleVerifyCode = async (e: React.FormEvent) => {
    e.preventDefault();
    setResult(null);
    
    try {
      const response = await verifyVerificationCode(code, email, verificationType);
      setResult({
        success: response.success,
        message: response.message
      });
      setIsVerified(response.isValid);
    } catch (error) {
      console.error('验证码验证失败:', error);
      setResult({ success: false, message: error instanceof Error ? error.message : '未知错误' });
    }
  };
  
  // 根据验证类型返回说明文本
  const getVerificationDescription = () => {
    switch (verificationType) {
      case 'email_verification':
        return '邮箱验证用于确认用户提供的邮箱地址是有效且属于该用户的。';
      case 'password_reset':
        return '密码重置验证用于确认用户身份，以便允许重置密码。';
      case 'sensitive_action':
        return '敏感操作验证用于确认用户身份，防止未授权的敏感操作。';
      default:
        return '请选择验证类型以获取更多信息。';
    }
  };
  
  // 清除错误信息
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);
  
  return (
    <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-md">
      <h1 className="text-2xl font-bold mb-4">验证码功能示例</h1>
      
      <div className="mb-6">
        <p className="text-gray-600 text-sm">{getVerificationDescription()}</p>
      </div>
      
      {result && (
        <div className={`mb-4 p-3 rounded ${result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          <p>{result.message}</p>
        </div>
      )}
      
      {error && (
        <div className="mb-4 p-3 rounded bg-red-100 text-red-800">
          <p>{error}</p>
        </div>
      )}
      
      <form className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">验证类型</label>
          <select
            value={verificationType}
            onChange={(e) => setVerificationType(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
            disabled={isLoading}
          >
            <option value="email_verification">邮箱验证</option>
            <option value="password_reset">密码重置</option>
            <option value="sensitive_action">敏感操作确认</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">邮箱地址</label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
            placeholder="请输入邮箱地址"
            disabled={isLoading}
            required
          />
        </div>
        
        <div>
          <button
            type="button"
            onClick={handleSendCode}
            disabled={isLoading || !email || countdown > 0}
            className={`w-full px-4 py-2 text-white font-medium rounded-md
              ${isLoading || !email || countdown > 0
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'}
            `}
          >
            {countdown > 0 ? `重新发送 (${countdown}s)` : '发送验证码'}
          </button>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">验证码</label>
          <input
            type="text"
            value={code}
            onChange={(e) => setCode(e.target.value)}
            className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm
              ${isVerified ? 'border-green-500' : 'border-gray-300'}
              ${isVerified ? 'bg-green-50' : ''}
            `}
            placeholder="请输入验证码"
            disabled={isLoading}
            required
          />
        </div>
        
        <div>
          <button
            type="button"
            onClick={handleVerifyCode}
            disabled={isLoading || !code || !email}
            className={`w-full px-4 py-2 text-white font-medium rounded-md
              ${isLoading || !code || !email
                ? 'bg-gray-400 cursor-not-allowed'
                : isVerified
                  ? 'bg-green-600 hover:bg-green-700'
                  : 'bg-purple-600 hover:bg-purple-700'}
            `}
          >
            {isVerified ? '已验证' : '验证'}
          </button>
        </div>
      </form>
      
      {isVerified && (
        <div className="mt-6 p-4 bg-green-100 rounded-md">
          <h2 className="text-lg font-semibold text-green-800">验证成功!</h2>
          <p className="text-green-700">
            您已成功完成 {verificationType === 'email_verification' ? '邮箱验证' : 
              verificationType === 'password_reset' ? '密码重置验证' : '敏感操作验证'}
          </p>
        </div>
      )}
    </div>
  );
} 