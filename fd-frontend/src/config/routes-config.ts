/**
 * 路由配置
 * 
 * 定义应用程序中各种路由的访问权限
 */

// 需要认证的路由（只允许已登录用户访问）
export const protectedRoutes = [
  '/profile',
  '/profile/edit',
  '/user/settings',
  '/user/favorites',
  '/user/comments',
];

// 只允许未登录用户访问的路由（已登录用户将被重定向到首页）
export const guestOnlyRoutes = [
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
];

/**
 * 检查路径是否匹配受保护路由
 * @param path 需要检查的路径
 * @param routePatterns 路由模式数组
 * @returns 如果路径匹配任何一个路由模式，则返回true
 */
export const matchRoute = (path: string, routePatterns: string[]): boolean => {
  return routePatterns.some(pattern => {
    // 精确匹配
    if (pattern === path) return true;
    
    // 带通配符的匹配，例如 /profile/* 匹配 /profile/edit
    if (pattern.endsWith('/*')) {
      const basePattern = pattern.slice(0, -2);
      return path.startsWith(basePattern);
    }
    
    return false;
  });
};

/**
 * 检查路径是否需要认证
 * @param path 需要检查的路径
 * @returns 如果路径需要认证，则返回true
 */
export const isProtectedRoute = (path: string): boolean => {
  return matchRoute(path, protectedRoutes);
};

/**
 * 检查路径是否只允许未登录用户访问
 * @param path 需要检查的路径
 * @returns 如果路径只允许未登录用户访问，则返回true
 */
export const isGuestOnlyRoute = (path: string): boolean => {
  return matchRoute(path, guestOnlyRoutes);
}; 