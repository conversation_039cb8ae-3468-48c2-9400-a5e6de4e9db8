/**
 * 统一的GraphQL API封装
 * 将所有的hooks统一导出，提供一致的API接口
 */

// 导出所有公共hooks
export * from '../hooks/usePost';
export * from '../hooks/usePosts';
export * from '../hooks/useCategory';
export * from '../hooks/useCustomPost';
export * from '../hooks/useTaxonomy';
export * from '../hooks/useMenu';

// 导出新增的hooks
export * from '../hooks/useUser';
export * from '../hooks/useSettings';
export * from '../hooks/useComment';
export * from '../hooks/useSearch';
export * from '../hooks/useNotifications';

// 导出常用的Apollo Client工具函数
export { useLazyQuery, useMutation, useApolloClient } from '@apollo/client';

// 导出我们创建的新Hook: useTaxonomyTerms
export { useTaxonomyTerms } from '../hooks/useTaxonomyTerms';

// 导出新创建的Hook: useTaxonomyTerm
export { useTaxonomyTerm } from '../hooks/useTaxonomyTerm'; 