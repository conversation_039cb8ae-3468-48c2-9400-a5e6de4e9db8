// 路由前缀缓存和获取工具

// 默认路由前缀配置
const DEFAULT_PREFIXES = {
  postPrefix: 'articles',
  categoryPrefix: null,
  tagPrefix: 'topics',
  categoryIndexRoute: 'category-index',
  tagIndexRoute: 'tag-index',
  customTypePrefix: 'post-type'
};

// 内存缓存
let cachedPrefixes: any = null;
let cacheTime = 0;
const CACHE_DURATION = 3600000; // 1小时缓存

/**
 * 获取路由前缀配置
 * 使用内存缓存和ISR缓存双重优化
 */
export async function getRoutePrefixes() {
  const now = Date.now();
  
  // 检查内存缓存
  if (cachedPrefixes && (now - cacheTime) < CACHE_DURATION) {
    return cachedPrefixes;
  }
  
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: `
            query GetRoutePrefixes {
              routePrefixes {
                postPrefix
                categoryPrefix
                tagPrefix
                categoryIndexRoute
                tagIndexRoute
                customTypePrefix
              }
            }
          `
        }),
        next: { revalidate: 3600 } // ISR缓存1小时
      }
    );
    
    const data = await response.json();
    const prefixes = data?.data?.routePrefixes || DEFAULT_PREFIXES;
    
    // 更新内存缓存
    cachedPrefixes = { ...DEFAULT_PREFIXES, ...prefixes };
    cacheTime = now;
    
    console.log('[RoutePrefixes] Fetched and cached:', cachedPrefixes);
    
    return cachedPrefixes;
  } catch (error) {
    console.error('Error fetching route prefixes:', error);
    
    // 如果有缓存的数据，返回缓存数据
    if (cachedPrefixes) {
      return cachedPrefixes;
    }
    
    // 否则返回默认配置
    return DEFAULT_PREFIXES;
  }
}

/**
 * 清除路由前缀缓存
 * 用于测试或强制刷新
 */
export function clearRoutePrefixesCache() {
  cachedPrefixes = null;
  cacheTime = 0;
}

/**
 * 获取缓存状态
 * 用于调试
 */
export function getRoutePrefixesCacheStatus() {
  const now = Date.now();
  const isValid = cachedPrefixes && (now - cacheTime) < CACHE_DURATION;
  
  return {
    hasCachedData: !!cachedPrefixes,
    isValid,
    cacheAge: cachedPrefixes ? now - cacheTime : 0,
    data: cachedPrefixes
  };
}
