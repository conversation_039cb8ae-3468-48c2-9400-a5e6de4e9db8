/**
 * GraphQL 相关工具函数
 */

/**
 * 将CPT的slug转换为GraphQL规范的类型名称 (e.g., "note" -> "Note")
 * @param slug CPT slug
 * @returns GraphQL 类型名称
 */
function toGraphQLTypeName(slug: string): string {
  if (!slug) return '';
  // 处理可能存在的连字符，例如 "my-post" -> "MyPost"
  return slug.split('-').map(part => part.charAt(0).toUpperCase() + part.slice(1)).join('');
}

interface AcfField {
    name: string;
    type: {
        name: string;
        kind: 'SCALAR' | 'OBJECT';
    };
}

/**
 * 根据文章类型和其ACF字段列表，动态构建GraphQL查询字符串。
 * @param postType CPT的slug (e.g., "note")
 * @param acfContainerTypeName ACF字段组的GraphQL类型名 (e.g., "NoteFields")
 * @param acfFields ACF字段列表
 * @returns 一个完整的GraphQL查询字符串
 */
export function buildDynamicPostQuery(
    postType: string, 
    acfContainerTypeName: string | null,
    acfFields: AcfField[]
): string {
  const postGraphQLTypeName = toGraphQLTypeName(postType);
  const acfContainerFieldName = acfContainerTypeName ? `${postType.toLowerCase()}Fields` : null;

  let acfFieldsString = '';
  // 构建反应数据字段（如果是支持反应的类型）
  const reactionFields = `
          likeCount: reactionCount(type: LIKE)
          userHasLiked: userHasReacted(type: LIKE)
          bookmarkCount: reactionCount(type: BOOKMARK)
          userHasBookmarked: userHasReacted(type: BOOKMARK)
          recommendCount: reactionCount(type: RECOMMEND)
          userHasRecommended: userHasReacted(type: RECOMMEND)
          commentCount`;

  if (acfContainerFieldName && acfContainerTypeName && acfFields && acfFields.length > 0) {
    const fieldNames = acfFields
        .filter(field => field.type.kind === 'SCALAR') // 暂时只处理标量，防止查询复杂对象
        .map(field => field.name)
        .join('\n            ');

    if (fieldNames) {
        acfFieldsString = `
        ... on ${postGraphQLTypeName} {
          title
          excerpt
          shortUuid
          ${reactionFields}
          ${acfContainerFieldName} {
            ${fieldNames}
          }
        }
      `;
    } else {
        acfFieldsString = `
        ... on ${postGraphQLTypeName} {
          title
          excerpt
          shortUuid
          ${reactionFields}
        }
      `;
    }
  } else {
    // 即使没有ACF字段，也要确保查询 shortUuid 和反应数据
    acfFieldsString = `
        ... on ${postGraphQLTypeName} {
          title
          excerpt
          shortUuid
          ${reactionFields}
        }
      `;
  }

  // 返回最终的、结合了标准字段和动态ACF字段的查询
  return `
    query GetDynamicContentNodes(
      $first: Int
      $after: String
      $contentTypes: [ContentTypeEnum]
    ) {
      contentNodes(
        first: $first
        after: $after
        where: { contentTypes: $contentTypes }
      ) {
        nodes {
          __typename
          id
          databaseId
          date
          slug
          uri
          ... on NodeWithFeaturedImage {
            featuredImage {
              node {
                sourceUrl
                altText
              }
            }
          }
          ... on NodeWithAuthor {
            author {
              node {
                id
                name
                slug
                nickname
                avatar {
                  url
                }
              }
            }
          }
          ${acfFieldsString}
        }
        edges {
          cursor
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  `;
} 