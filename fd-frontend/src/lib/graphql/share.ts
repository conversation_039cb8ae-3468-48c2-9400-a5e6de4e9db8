import { gql } from '@apollo/client';

/**
 * 查询站点的分享设置
 */
export const GET_SHARE_SETTINGS = gql`
  query GetShareSettings {
    fdShareSettings {
      isEnabled
      platforms
      posterLogo
      defaultThumb
      wechatDesc
    }
  }
`;

/**
 * 查询微信JSSDK配置
 */
export const GET_WECHAT_CONFIG = gql`
  query GetWeChatConfig($url: String!) {
    wechatSdkConfig(url: $url) {
      appId
      timestamp
      nonceStr
      signature
    }
  }
`;

/**
 * 创建海报数据
 */
export const GENERATE_POSTER_MUTATION = gql`
  mutation CreatePoster($postId: ID!) {
    generatePostPoster(input: {postId: $postId}) {
      posterData {
        title
        excerpt
        authorName
        authorAvatar
        featuredImage
        posterLogo
      }
    }
  }
`; 