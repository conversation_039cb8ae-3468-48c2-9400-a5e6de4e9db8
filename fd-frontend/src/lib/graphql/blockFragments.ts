import { gql } from '@apollo/client';

// 基础区块字段
export const CORE_BLOCK_FIELDS = gql`
  fragment CoreBlockFields on Block {
    __typename
    name
    isDynamic
    dynamicContent
    saveContent
    innerBlocks {
      __typename
    }
  }
`;

// 段落区块
export const PARAGRAPH_BLOCK_FRAGMENT = gql`
  fragment ParagraphBlockFragment on CoreParagraphBlock {
    ...CoreBlockFields
    attributes {
      ... on CoreParagraphBlockAttributes {
        content
        align
        dropCap
        backgroundColor
        textColor
      }
    }
  }
  ${CORE_BLOCK_FIELDS}
`;

// 标题区块
export const HEADING_BLOCK_FRAGMENT = gql`
  fragment HeadingBlockFragment on CoreHeadingBlock {
    ...CoreBlockFields
    attributes {
      ... on CoreHeadingBlockAttributes {
        content
        level
        align
        textColor
      }
    }
  }
  ${CORE_BLOCK_FIELDS}
`;

// 图像区块
export const IMAGE_BLOCK_FRAGMENT = gql`
  fragment ImageBlockFragment on CoreImageBlock {
    ...CoreBlockFields
    attributes {
      ... on CoreImageBlockAttributes {
        url
        alt
        caption
        width
        height
        sizeSlug
        align
      }
    }
  }
  ${CORE_BLOCK_FIELDS}
`;

// 列表区块
export const LIST_BLOCK_FRAGMENT = gql`
  fragment ListBlockFragment on CoreListBlock {
    ...CoreBlockFields
    attributes {
      ... on CoreListBlockAttributes {
        values
        ordered
        start
      }
    }
  }
  ${CORE_BLOCK_FIELDS}
`;

// 引用区块
export const QUOTE_BLOCK_FRAGMENT = gql`
  fragment QuoteBlockFragment on CoreQuoteBlock {
    ...CoreBlockFields
    attributes {
      ... on CoreQuoteBlockAttributes {
        value
        citation
        align
      }
    }
  }
  ${CORE_BLOCK_FIELDS}
`;

// 组合所有区块片段
export const ALL_SUPPORTED_BLOCKS_FRAGMENT = gql`
  fragment AllSupportedBlocksFragment on Block {
    __typename
    ...CoreBlockFields
    ... on CoreParagraphBlock {
      ...ParagraphBlockFragment
    }
    ... on CoreHeadingBlock {
      ...HeadingBlockFragment
    }
    ... on CoreImageBlock {
      ...ImageBlockFragment
    }
    ... on CoreListBlock {
      ...ListBlockFragment
    }
    ... on CoreQuoteBlock {
      ...QuoteBlockFragment
    }
    innerBlocks {
      __typename
      ...CoreBlockFields
      ... on CoreParagraphBlock {
        ...ParagraphBlockFragment
      }
      ... on CoreHeadingBlock {
        ...HeadingBlockFragment
      }
      ... on CoreImageBlock {
        ...ImageBlockFragment
      }
      ... on CoreListBlock {
        ...ListBlockFragment
      }
      ... on CoreQuoteBlock {
        ...QuoteBlockFragment
      }
    }
  }
  ${CORE_BLOCK_FIELDS}
  ${PARAGRAPH_BLOCK_FRAGMENT}
  ${HEADING_BLOCK_FRAGMENT}
  ${IMAGE_BLOCK_FRAGMENT}
  ${LIST_BLOCK_FRAGMENT}
  ${QUOTE_BLOCK_FRAGMENT}
`; 