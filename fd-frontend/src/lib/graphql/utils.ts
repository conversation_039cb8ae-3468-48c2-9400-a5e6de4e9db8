// 这个工具文件提供了方便的函数，使组件能够更容易地使用GraphQL查询

/**
 * 将REST API中的分页参数转换为GraphQL兼容的形式
 * @param params REST API分页参数
 * @returns GraphQL兼容的分页变量
 */
export const convertPaginationParams = (params: { 
  per_page?: number; 
  page?: number;
}) => {
  const { per_page = 10, page = 1 } = params;
  return {
    first: per_page,
    after: page > 1 ? btoa(`arrayconnection:${(page - 1) * per_page - 1}`) : null
  };
};

/**
 * 格式化WordPress日期
 * @param dateString WordPress日期字符串
 * @param options 格式化选项
 * @returns 格式化后的日期字符串
 */
export const formatDate = (
  dateString: string, 
  options: Intl.DateTimeFormatOptions = { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  }
): string => {
  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('zh-CN', options).format(date);
  } catch (error) {
    console.error('日期格式化错误:', error);
    return dateString;
  }
};

/**
 * 解析和清理WordPress内容中的HTML
 * @param content WordPress HTML内容
 * @returns 清理后的HTML内容
 */
export const parseContent = (content: string): string => {
  if (!content) {
    return '';
  }
  
  // 这里可以实现任何必要的HTML清理和处理逻辑
  // 例如:
  // - 替换相对URL为绝对URL
  // - 添加target="_blank"到外部链接
  // - 处理WordPress短代码
  // - 移除不必要的CSS类
  
  return content;
};

/**
 * 将REST API响应转换为GraphQL格式
 * 这在从旧的REST API移植到GraphQL时很有用
 * @param restData REST API响应数据
 * @returns GraphQL格式的数据
 */
export const convertRestToGraphQL = (restData: any): any => {
  if (!restData) {
    return null;
  }
  
  // 处理数组
  if (Array.isArray(restData)) {
    return {
      nodes: restData.map(item => convertSingleRestItem(item))
    };
  }
  
  // 处理单个项目
  return convertSingleRestItem(restData);
};

/**
 * 转换单个REST API项目为GraphQL兼容格式
 * @param item REST API项目
 * @returns GraphQL格式的项目
 */
const convertSingleRestItem = (item: any): any => {
  if (!item) {
    return null;
  }
  
  const result: any = { ...item };
  
  // 处理特色图像
  if (item._embedded && item._embedded['wp:featuredmedia']) {
    result.featuredImage = {
      node: {
        sourceUrl: item._embedded['wp:featuredmedia'][0]?.source_url || '',
        altText: item._embedded['wp:featuredmedia'][0]?.alt_text || ''
      }
    };
    delete result._embedded['wp:featuredmedia'];
  }
  
  // 处理作者
  if (item._embedded && item._embedded['author']) {
    result.author = {
      node: {
        id: item._embedded['author'][0]?.id,
        name: item._embedded['author'][0]?.name,
        slug: item._embedded['author'][0]?.slug,
        avatar: {
          url: item._embedded['author'][0]?.avatar_urls?.['96'] || ''
        }
      }
    };
    delete result._embedded['author'];
  }
  
  // 处理分类
  if (item._embedded && item._embedded['wp:term'] && item._embedded['wp:term'][0]) {
    result.categories = {
      nodes: item._embedded['wp:term'][0].map((term: any) => ({
        id: term.id,
        name: term.name,
        slug: term.slug
      }))
    };
  }
  
  // 处理标签
  if (item._embedded && item._embedded['wp:term'] && item._embedded['wp:term'][1]) {
    result.tags = {
      nodes: item._embedded['wp:term'][1].map((term: any) => ({
        id: term.id,
        name: term.name,
        slug: term.slug
      }))
    };
  }
  
  // 删除嵌入数据
  delete result._embedded;
  
  return result;
}; 