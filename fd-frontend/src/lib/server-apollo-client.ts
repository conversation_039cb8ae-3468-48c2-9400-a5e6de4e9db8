// @ts-nocheck
import { cookies } from 'next/headers';
import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';

export function getServerApollo() {
  const cookieStore = cookies();
  const token = cookieStore.get('fd_auth_token')?.value;

  const httpLink = createHttpLink({
    uri: process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'https://admin.futuredecade.com/graphql',
    headers: token ? { Authorization: `Bearer ${token}` } : {},
    // 使用全局 fetch (Node18+)；无需额外依赖 cross-fetch
    fetch: fetch,
  });

  return new ApolloClient({
    link: httpLink,
    cache: new InMemoryCache(),
    defaultOptions: {
      query: { fetchPolicy: 'no-cache' },
    },
  });
} 