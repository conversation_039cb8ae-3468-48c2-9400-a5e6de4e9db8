// 菜单数据服务端获取工具
interface MenuItem {
  id: string;
  title: string;
  label: string;
  url: string;
  target?: string;
  parentId?: string;
  cssClasses?: string[];
  children?: MenuItem[];
}

interface Menu {
  id: string;
  name: string;
  menuItems?: {
    nodes: MenuItem[];
  };
}

interface MenusData {
  menus: {
    nodes: Menu[];
  };
}

/**
 * 将扁平菜单列表转换为层次结构
 */
const flatListToHierarchical = (
  data: MenuItem[] = [],
  {idKey = 'id', parentKey = 'parentId', childrenKey = 'children'} = {}
): MenuItem[] => {
  const tree: MenuItem[] = [];
  const childrenOf: { [key: string]: MenuItem[] } = {};
  
  data.forEach((item) => {
    const newItem = {...item} as any;
    const id = newItem[idKey];
    const parentId = newItem[parentKey] || 0;
    
    childrenOf[id] = childrenOf[id] || [];
    newItem[childrenKey] = childrenOf[id];
    
    parentId
      ? (
          childrenOf[parentId] = childrenOf[parentId] || []
        ).push(newItem)
      : tree.push(newItem);
  });
  
  return tree;
};

/**
 * 服务端获取菜单数据
 */
export async function fetchMenuData(): Promise<{
  topMenu: MenuItem[];
  footerMenu: MenuItem[];
} | null> {
  const GRAPHQL_ENDPOINT = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql';
  
  const query = `
    query GetMenus {
      menus {
        nodes {
          id
          name
          menuItems {
            nodes {
              id
              title
              label
              url
              target
              parentId
              cssClasses
            }
          }
        }
      }
    }
  `;

  try {
    const res = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query }),
      next: {
        revalidate: 600, // 10分钟缓存，与category页面相同
        tags: ['menus'] // 缓存标签，用于按需重新验证
      },
    } as RequestInit & { next: { revalidate: number; tags: string[] } });

    const json: { data?: MenusData; errors?: any[] } = await res.json();
    
    if (json.errors) {
      console.error('GraphQL errors:', json.errors);
      return null;
    }

    const menus = json.data?.menus?.nodes || [];
    
    // 找到顶部菜单和底部菜单
    const topMenuData = menus.find((menu: Menu) => menu.name === '顶部菜单');
    const footerMenuData = menus.find((menu: Menu) => menu.name === '底部菜单');
    
    // 转换为层次结构
    const topMenu = topMenuData ? flatListToHierarchical(topMenuData.menuItems?.nodes || []) : [];
    const footerMenu = footerMenuData ? flatListToHierarchical(footerMenuData.menuItems?.nodes || []) : [];

    console.log('[MenuData] Server-side menu data fetched:', {
      topMenuItems: topMenu.length,
      footerMenuItems: footerMenu.length
    });

    return {
      topMenu,
      footerMenu
    };
  } catch (error) {
    console.error('Error fetching menu data:', error);
    return null;
  }
}

/**
 * 获取特定菜单的数据
 */
export async function fetchMenuByName(menuName: string): Promise<MenuItem[]> {
  const menuData = await fetchMenuData();
  
  if (!menuData) return [];
  
  switch (menuName) {
    case '顶部菜单':
      return menuData.topMenu;
    case '底部菜单':
      return menuData.footerMenu;
    default:
      return [];
  }
}
