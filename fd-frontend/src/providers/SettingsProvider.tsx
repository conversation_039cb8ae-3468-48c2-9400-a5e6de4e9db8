'use client';

import React, { ReactNode } from 'react';
import { useQuery } from '@apollo/client';
import { SettingsContext, SettingsContextType } from '@/contexts/SettingsContext';
import { GET_POSTS_PER_PAGE_SETTING } from '@/lib/graphql/queries';

interface SettingsProviderProps {
  children: ReactNode;
}

/**
 * SettingsProvider组件
 * - 获取全局设置 (如分页数量)
 * - 通过SettingsContext将设置提供给整个应用
 */
export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  // 从GraphQL获取分页设置
  const { data, loading, error } = useQuery(GET_POSTS_PER_PAGE_SETTING);

  // 如果出错，记录错误并使用默认值
  if (error) {
    console.error('Failed to fetch posts per page setting:', error);
  }

  // 准备要提供给Context的值
  // 在加载中或获取失败时，使用默认值12
  const settingsValue: SettingsContextType = {
    postsPerPage: data?.postsPerPageSetting || 12,
  };

  return (
    <SettingsContext.Provider value={settingsValue}>
      {children}
    </SettingsContext.Provider>
  );
}; 