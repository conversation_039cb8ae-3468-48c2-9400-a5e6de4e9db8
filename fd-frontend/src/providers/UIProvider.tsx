'use client';

import React, { ReactNode } from 'react';
import { VIThemeProvider } from '@/contexts/VIThemeContext';
import { ToastProvider } from '@/contexts/ToastContext';
import { ModalProvider } from '@/contexts/ModalContext';
import { DialogProvider } from '@/contexts/DialogContext';

interface UIProviderProps {
  children: ReactNode;
}

/**
 * UI提供程序 - 包含所有UI上下文提供程序
 * 这是应用程序的顶层提供程序，包含所有UI相关的上下文
 */
export default function UIProvider({ children }: UIProviderProps) {
  return (
    <VIThemeProvider>
      <ToastProvider>
        <ModalProvider>
          <DialogProvider>
            {children}
          </DialogProvider>
        </ModalProvider>
      </ToastProvider>
    </VIThemeProvider>
  );
}

// 导出hooks，方便使用
export { useVITheme } from '@/contexts/VIThemeContext';
export { useToast } from '@/contexts/ToastContext';
export { useModal } from '@/contexts/ModalContext';
export { useDialog } from '@/contexts/DialogContext'; 