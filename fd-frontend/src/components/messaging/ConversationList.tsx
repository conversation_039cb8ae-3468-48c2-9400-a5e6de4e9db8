'use client'

import { useState, useCallback } from 'react'
import Link from 'next/link'
import { useParams, useRouter } from 'next/navigation'
import { useMyConversations, useSendPrivateMessage } from '@/hooks/useMessages'
import { PrivateMessageConversation, ConversationParticipant } from '@/types/messages'
import { useLazyQuery } from '@apollo/client'
import { SEARCH_USERS } from '@/lib/graphql/queries'
import { debounce } from 'lodash'

function UserSearchResult({
  user,
  onSelect
}: {
  user: ConversationParticipant
  onSelect: (user: ConversationParticipant) => void
}) {
  return (
    <div
      onClick={() => onSelect(user)}
      className="flex items-center p-3 hover:bg-gray-100 cursor-pointer rounded-lg"
    >
      <img
        src={user.avatar?.url || '/default-avatar.png'}
        alt={user.name}
        className="w-10 h-10 rounded-full mr-4"
      />
      <span className="font-semibold">{user.name}</span>
    </div>
  )
}

function NewConversationModal({ onClose }: { onClose: () => void }) {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedUser, setSelectedUser] = useState<ConversationParticipant | null>(null)
  const [messageContent, setMessageContent] = useState('')

  const [searchUsers, { data, loading, error }] = useLazyQuery(SEARCH_USERS)
  const { sendMessage, loading: sendingMessage } = useSendPrivateMessage()

  const debouncedSearch = useCallback(
    debounce((search: string) => {
      if (search.trim().length > 1) {
        searchUsers({ variables: { search } })
      }
    }, 300),
    []
  )

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
    debouncedSearch(e.target.value)
  }

  const handleSendMessage = async () => {
    if (!selectedUser || !messageContent.trim()) {
      alert('请选择一个用户并输入消息。')
      return
    }

    try {
      const result = await sendMessage({
        variables: {
          recipientId: selectedUser.id,
          content: messageContent
        }
      })
      
      const conversationId = result.data?.sendPrivateMessage.conversation.id
      if (conversationId) {
        onClose();
        router.push(`/messages/${conversationId}`)
      } else {
        throw new Error("Could not retrieve conversation ID.")
      }
    } catch (err) {
      console.error('发送消息失败:', err)
      if (err instanceof Error) {
        alert(`发起会话失败：${err.message}`)
      } else {
        alert('发起会话时发生未知错误。')
      }
    }
  }
  
    if (selectedUser) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
          <div className="p-4 border-b flex justify-between items-center">
            <h2 className="text-xl font-semibold">收件人：{selectedUser.name}</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-800">&times;</button>
          </div>
          <div className="p-4">
            <textarea
              className="w-full p-2 border rounded-md"
              rows={5}
              placeholder={`给 ${selectedUser.name} 发消息…`}
              value={messageContent}
              onChange={(e) => setMessageContent(e.target.value)}
            />
          </div>
          <div className="p-4 border-t flex justify-end">
            <button onClick={() => setSelectedUser(null)} className="px-4 py-2 text-gray-600">返回</button>
            <button
              onClick={handleSendMessage}
              disabled={sendingMessage || !messageContent.trim()}
              className="px-6 py-2 bg-blue-500 text-white font-semibold rounded-lg disabled:bg-gray-400"
            >
              {sendingMessage ? '发送中…' : '发送'}
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-start pt-20">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="p-4 border-b flex justify-between items-center">
          <h2 className="text-xl font-semibold">新建会话</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-800">&times;</button>
        </div>
        <div className="p-4">
          <input
            type="text"
            placeholder="搜索用户…"
            value={searchTerm}
            onChange={handleSearchChange}
            className="w-full p-2 border rounded-lg"
          />
        </div>
        <div className="px-4 pb-4 h-64 overflow-y-auto">
            {loading && <p className="text-center text-gray-500">搜索中…</p>}
            {error && <p className="text-center text-red-500">错误：{error.message}</p>}
            {data && data.searchableUsers.length === 0 && searchTerm.length > 1 && (
                <p className="text-center text-gray-500">未找到用户</p>
            )}
            {data?.searchableUsers.map((user: ConversationParticipant) => (
                <UserSearchResult key={user.id} user={user} onSelect={setSelectedUser} />
            ))}
        </div>
      </div>
    </div>
  )
}

function ConversationListItem({
  conversation,
  isActive
}: {
  conversation: PrivateMessageConversation
  isActive: boolean
}) {
  const { otherUser, lastMessage, unreadCount } = conversation
  const activeClass = isActive ? 'bg-gray-200' : 'hover:bg-gray-100'

  return (
    <Link href={`/messages/${conversation.id}`}>
      <div
        className={`p-4 border-b border-gray-200 cursor-pointer ${activeClass}`}
      >
        <div className="flex justify-between items-center">
          <h3 className="font-semibold">{otherUser.name}</h3>
          {unreadCount > 0 && (
            <span className="bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
              {unreadCount}
            </span>
          )}
        </div>
        <p className="text-sm text-gray-600 truncate">{lastMessage.content}</p>
      </div>
    </Link>
  )
}

export default function ConversationList() {
  const { data, loading, error } = useMyConversations({ first: 20 })
  const params = useParams()
  const activeConversationId = params.id ? String(params.id) : null
  const [isModalOpen, setIsModalOpen] = useState(false)

  if (loading) {
    return <div className="p-4">正在加载会话…</div>
  }

  if (error) {
    return (
      <div className="p-4 text-red-500">错误：{error.message}</div>
    )
  }

  const conversations = data?.viewer.privateConversations.edges || []

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b">
        <button
          onClick={() => setIsModalOpen(true)}
          className="w-full px-4 py-2 bg-blue-500 text-white font-semibold rounded-lg shadow-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          + 新建会话
        </button>
      </div>
      <div className="h-full overflow-y-auto">
        {conversations.length === 0 ? (
          <div className="p-4 text-gray-500 text-center">暂无会话</div>
        ) : (
          conversations.map(({ node }: { node: PrivateMessageConversation }) => (
            <ConversationListItem
              key={node.id}
              conversation={node}
              isActive={node.id === activeConversationId}
            />
          ))
        )}
      </div>
      {isModalOpen && (
        <NewConversationModal onClose={() => setIsModalOpen(false)} />
      )}
    </div>
  )
} 