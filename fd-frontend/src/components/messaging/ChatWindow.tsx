'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import {
  useConversationMessages,
  useSendPrivateMessage,
  useMarkConversationAsRead,
  useDeleteConversation
} from '@/hooks/useMessages'
import { GET_CONVERSATION_MESSAGES } from '@/lib/graphql/queries'
import {
  PrivateConversationData,
  PrivateMessage,
  ConversationParticipant
} from '@/types/messages'
import type { ApolloCache } from '@apollo/client'

interface MessageProps {
  message: PrivateMessage;
  otherUser: ConversationParticipant;
}

function Message({ message, otherUser }: MessageProps) {
  const isOwnMessage = message.sender.id !== otherUser.id
  const bubbleClasses = isOwnMessage
    ? 'bg-blue-500 text-white self-end'
    : 'bg-gray-200 text-gray-800 self-start'
  return (
    <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
      <div className={`p-3 rounded-lg max-w-lg ${bubbleClasses}`}>
        <p>{message.content}</p>
        <p className="text-xs opacity-75 mt-1">
          {new Date(message.sentAt).toLocaleTimeString()}
        </p>
      </div>
    </div>
  )
}

function MessageInput({
  conversationId,
  recipientId
}: {
  conversationId: string
  recipientId: string
}) {
  const [content, setContent] = useState('')
  const { sendMessage, loading } = useSendPrivateMessage()

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!content.trim()) return

    try {
      await sendMessage({
        variables: {
          recipientId,
          content
        },
        optimisticResponse: {
          sendPrivateMessage: {
            __typename: 'SendPrivateMessagePayload',
            success: true,
            sentMessage: {
              __typename: 'PrivateMessage',
              id: 'optimistic-' + Math.random().toString(36).slice(2),
              content
            },
            conversation: {
              __typename: 'PrivateMessageConversation',
              id: conversationId
            }
          }
        } as any,
        update: (cache, { data }) => {
          const payload = data?.sendPrivateMessage
          if (!payload) return

          // 构造新的消息节点（附带当前时间和发送者占位信息）
          const newMessageNode = {
            __typename: 'PrivateMessage',
            id: payload.sentMessage.id,
            content: payload.sentMessage.content,
            sentAt: new Date().toISOString(),
            isRead: true,
            sender: {
              __typename: 'ConversationParticipant',
              id: 'me',
              name: 'You'
            }
          }

          // 读取当前会话的消息列表
          const existing = cache.readQuery<PrivateConversationData>({
            query: GET_CONVERSATION_MESSAGES,
            variables: { id: conversationId, first: 50 }
          })

          if (!existing?.privateConversation?.messages) return

          const newEdge = {
            __typename: 'PrivateMessageEdge',
            node: newMessageNode
          }

          cache.writeQuery({
            query: GET_CONVERSATION_MESSAGES,
            variables: { id: conversationId, first: 50 },
            data: {
              privateConversation: {
                ...existing.privateConversation,
                messages: {
                  ...existing.privateConversation.messages,
                  edges: [
                    ...existing.privateConversation.messages.edges,
                    newEdge
                  ]
                }
              }
            }
          })
        }
      })
      setContent('')
    } catch (error) {
      console.error('Failed to send message:', error)
      alert('Failed to send message.')
    }
  }

  return (
    <form onSubmit={handleSubmit} className="p-4 border-t border-gray-200">
      <div className="flex items-center">
        <input
          type="text"
          value={content}
          onChange={e => setContent(e.target.value)}
          placeholder="输入消息…"
          className="flex-grow p-2 border rounded-full px-4"
          disabled={loading}
        />
        <button
          type="submit"
          disabled={loading}
          className="ml-4 px-6 py-2 bg-blue-500 text-white font-semibold rounded-full disabled:bg-gray-400"
        >
          {loading ? '…' : '发送'}
        </button>
      </div>
    </form>
  )
}

export default function ChatWindow({
  conversationId
}: {
  conversationId: string
}) {
  const router = useRouter()
  const { data, loading, error } = useConversationMessages({
    id: conversationId,
    first: 50 // Fetch more messages initially
  })

  const messagesEndRef = useRef<null | HTMLDivElement>(null)

  // 标记会话已读
  const { markAsRead } = useMarkConversationAsRead()

  // 删除会话
  const { deleteConversation, loading: deleting } = useDeleteConversation()

  const handleDeleteConversation = async () => {
    const confirmed = window.confirm('确定要删除此会话吗？此操作无法撤销。')
    if (!confirmed) return

    try {
      await deleteConversation({ variables: { conversationId } })
      router.push('/messages')
    } catch (err) {
      console.error('删除会话失败:', err)
      alert('删除会话失败。')
    }
  }

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [data])

  useEffect(() => {
    if (conversationId) {
      markAsRead({ variables: { conversationId } }).catch(() => {})
    }
  }, [conversationId])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        正在加载消息…
      </div>
    )
  }
  if (error) {
    return (
      <div className="flex items-center justify-center h-full text-red-500">
        错误：{error.message}
      </div>
    )
  }

  if (!data || !data.privateConversation) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        未找到会话
      </div>
    )
  }

  const { privateConversation } = data as PrivateConversationData
  const { messages, otherUser } = privateConversation

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 className="text-xl font-semibold">{otherUser.name}</h2>
        <button
          onClick={handleDeleteConversation}
          disabled={deleting}
          className="text-red-500 hover:text-red-700 text-sm disabled:opacity-50"
        >
          {deleting ? '删除中…' : '删除会话'}
        </button>
      </div>
      <div className="flex-grow p-4 overflow-y-auto space-y-4">
        {messages?.edges.map(({ node }) => (
          <Message key={node.id} message={node} otherUser={otherUser} />
        ))}
        <div ref={messagesEndRef} />
      </div>
      <MessageInput
        conversationId={conversationId}
        recipientId={otherUser.id}
      />
    </div>
  )
} 