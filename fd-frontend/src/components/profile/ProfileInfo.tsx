"use client";

import React from 'react';
import Link from 'next/link';
import { useAuth } from '../../hooks/useAuth';
import Button from '../ui/Button';

const ProfileInfo: React.FC = () => {
  const { user, isLoading, unbindPhone } = useAuth();
  
  // 处理解绑手机号
  const handleUnbindPhone = async () => {
    if (confirm('确定要解绑手机号吗？')) {
      try {
        const result = await unbindPhone();
        if (result.success) {
          alert('手机号解绑成功');
        } else {
          alert(`解绑失败: ${result.message}`);
        }
      } catch (error) {
        alert('解绑手机号时发生错误');
        console.error(error);
      }
    }
  };
  
  if (isLoading) {
    return (
      <div className="animate-pulse flex flex-col space-y-4">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
        <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
      </div>
    );
  }
  
  if (!user) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600 dark:text-gray-400 mb-4">未登录或会话已过期</p>
        <Link href="/login">
          <Button variant="primary">请重新登录</Button>
        </Link>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">个人资料</h2>
        <Link 
          href="/profile/edit" 
          className="mt-2 sm:mt-0 inline-flex justify-center items-center px-4 py-2 text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          编辑资料
        </Link>
      </div>
      
      <div className="bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">用户信息</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">您的个人详细信息</p>
        </div>
        
        <div className="px-4 py-5 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row items-center gap-6">
            <div className="relative">
              <div className="w-32 h-32 rounded-full bg-gray-200 dark:bg-gray-700 overflow-hidden">
                {user?.avatar?.url ? (
                  <img 
                    src={user.avatar.url} 
                    alt={user.name || "用户头像"} 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                )}
              </div>
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white mb-1">{user.name}</h1>
              {user.nickname && <p className="text-gray-600 dark:text-gray-400 mb-2">{user.nickname}</p>}
              <Link 
                href="/profile/edit" 
                className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none"
              >
                更新头像
              </Link>
            </div>
          </div>
        </div>
        
        <div className="px-4 py-5 sm:p-6">
          <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div className="sm:col-span-1">
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">用户名</dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-white">{user.name}</dd>
            </div>
            
            <div className="sm:col-span-1">
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">显示名称</dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-white">{user.nickname || '未设置'}</dd>
            </div>
            
            <div className="sm:col-span-1">
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">电子邮箱</dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-white">{user.email || '未设置'}</dd>
            </div>
            
            <div className="sm:col-span-1">
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">会员等级</dt>
              <dd className="mt-1 text-sm">
                {user.memberLevel ? (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-800 dark:text-primary-100">
                    {user.memberLevel.name}
                  </span>
                ) : (
                  <span className="text-gray-500 dark:text-gray-400">普通会员</span>
                )}
              </dd>
            </div>
            
            <div className="sm:col-span-2">
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">个人简介</dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-white whitespace-pre-line">
                {user.description || '未设置个人简介'}
              </dd>
            </div>
            
            <div className="sm:col-span-1">
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">姓氏</dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-white">{user.lastName || '未设置'}</dd>
            </div>
            
            <div className="sm:col-span-1">
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">名字</dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-white">{user.firstName || '未设置'}</dd>
            </div>
          </dl>
        </div>
      </div>
      
      {user.memberLevel && (
        <div className="bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg mb-6">
          <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <div>
              <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">会员等级</h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">您当前的会员级别和权益</p>
            </div>
            <span className="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium bg-primary-100 text-primary-800 dark:bg-primary-800 dark:text-primary-100">
              {user.memberLevel.name}
            </span>
          </div>
          
          <div className="px-4 py-5 sm:p-6">
            {user.memberLevel.description ? (
              <p className="text-sm text-gray-600 dark:text-gray-300">{user.memberLevel.description}</p>
            ) : (
              <p className="text-sm text-gray-500 dark:text-gray-400">感谢您成为我们的会员</p>
            )}
            
            {user.memberExpiration && (
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                <span className="font-medium">会员到期时间: </span>
                {new Date(user.memberExpiration).toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric' })}
              </p>
            )}
            
            <div className="mt-4">
              <Link href="/membership/upgrade" className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                升级会员
              </Link>
            </div>
          </div>
        </div>
      )}
      
      <div className="bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">账户安全</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">管理您的登录信息</p>
        </div>
        
        <div className="px-4 py-5 sm:p-6">
          <div className="space-y-4">
            <div>
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">密码</h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">上次更新时间: 未知</p>
                </div>
                <Link href="/change-password">
                  <Button variant="outline" size="sm">
                    修改密码
                  </Button>
                </Link>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">手机号</h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {user?.phone ? `已绑定: ${user.phone}` : '未绑定手机号'}
                  </p>
                </div>
                {user?.phone ? (
                  <div className="flex gap-2">
                    <Link href="/profile/bind-phone">
                      <Button variant="outline" size="sm">
                        更换手机号
                      </Button>
                    </Link>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={handleUnbindPhone}
                    >
                      解绑手机号
                    </Button>
                  </div>
                ) : (
                  <Link href="/profile/bind-phone">
                    <Button variant="outline" size="sm">
                      绑定手机号
                    </Button>
                  </Link>
                )}
              </div>
            </div>
            
            <div>
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">邮箱</h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {user?.email ? `已绑定: ${user.email}` : '未绑定邮箱'}
                  </p>
                </div>
                <Link href="/profile/bind-email">
                  <Button variant="outline" size="sm">
                    {user?.email ? '更换邮箱' : '绑定邮箱'}
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileInfo; 