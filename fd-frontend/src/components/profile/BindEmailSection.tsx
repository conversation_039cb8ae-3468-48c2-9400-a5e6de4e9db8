"use client";

import React, { useState } from 'react';
import { useAuth } from '../../hooks/useAuth';
import Button from '../ui/Button';
import FormField from '../ui/FormField';
import FormError from '../ui/FormError';
import FormSuccess from '../ui/FormSuccess';

/**
 * 邮箱绑定组件
 * 提供基于token的两步邮箱绑定流程
 */
const BindEmailSection: React.FC = () => {
  const { 
    user, 
    sendEmailBindingCode, 
    verifyEmailBindingCodeAndGetToken, 
    bindEmail
  } = useAuth();
  
  // 步骤状态
  const [step, setStep] = useState<'initial' | 'verify' | 'bind'>('initial');
  
  // 表单状态
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [token, setToken] = useState('');
  
  // UI状态
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  
  // 处理发送验证码
  const handleSendCode = async () => {
    if (!email) {
      setError('请输入邮箱地址');
      return;
    }
    
    // 简单的邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('请输入有效的邮箱地址');
      return;
    }
    
    setError('');
    setIsLoading(true);
    
    try {
      const result = await sendEmailBindingCode(email);
      
      if (result.success) {
        setSuccess('验证码已发送，请查收您的邮箱');
        setStep('verify');
        
        // 开始倒计时
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        setError(result.message || '发送验证码失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '发送验证码失败');
    } finally {
      setIsLoading(false);
    }
  };
  
  // 处理验证码验证并获取token
  const handleVerifyCode = async () => {
    if (!email || !code) {
      setError('请输入邮箱地址和验证码');
      return;
    }
    
    console.log('[调试-绑定邮箱] 验证验证码:', { email, code });
    
    setError('');
    setSuccess('');
    setIsLoading(true);
    
    try {
      console.log('[调试-绑定邮箱] 调用 verifyEmailBindingCodeAndGetToken API...');
      const result = await verifyEmailBindingCodeAndGetToken(email, code);
      
      console.log('[调试-绑定邮箱] 验证结果:', {
        success: result.success,
        message: result.message,
        token: result.token ? `${result.token.substring(0, 10)}...${result.token.substring(result.token.length - 10)}` : '未获取到'
      });
      
      if (result.success && result.token) {
        setToken(result.token);
        setSuccess('验证码验证成功，请点击绑定按钮完成绑定');
        setStep('bind');
      } else {
        setError(result.message || '验证码验证失败');
      }
    } catch (err) {
      console.error('[调试-绑定邮箱] 验证码验证异常:', err);
      setError(err instanceof Error ? err.message : '验证码验证失败');
    } finally {
      setIsLoading(false);
    }
  };
  
  // 处理绑定邮箱
  const handleBindEmail = async () => {
    if (!email || !token) {
      setError('缺少必要参数');
      return;
    }
    
    console.log('[调试-绑定邮箱] 开始绑定邮箱:', { 
      email, 
      token: token ? `${token.substring(0, 10)}...` : '空'
    });
    
    setError('');
    setSuccess('');
    setIsLoading(true);
    
    try {
      console.log('[调试-绑定邮箱] 调用 bindEmail API...');
      const result = await bindEmail({
        email,
        token
      });
      
      console.log('[调试-绑定邮箱] bindEmail API 返回结果:', result);
      
      if (result.success) {
        console.log('[调试-绑定邮箱] 邮箱绑定成功!');
        setSuccess('邮箱绑定成功！');
        
        // 重置表单和状态
        setTimeout(() => {
          setEmail('');
          setCode('');
          setToken('');
          setStep('initial');
          setSuccess('');
          
          // 绑定成功后，引导用户刷新页面或返回个人中心
          // 这样可以确保用户获得新的认证令牌
          if (window && window.location) {
            console.log('[调试-绑定邮箱] 重定向到个人中心页面...');
            window.location.href = '/auth/profile';
          }
        }, 2000);
      } else {
        console.error('[调试-绑定邮箱] 绑定失败:', result.message);
        setError(result.message || '绑定邮箱失败');
      }
    } catch (err) {
      console.error('[调试-绑定邮箱] 捕获到异常:', err);
      setError(err instanceof Error ? err.message : '绑定邮箱失败');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">邮箱绑定</h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
          {user?.email 
            ? '更换您的邮箱地址'
            : '绑定邮箱可用于登录和找回密码'
          }
        </p>
      </div>
      
      <div className="px-4 py-5 sm:p-6">
        {error && <FormError message={error} className="mb-4" />}
        {success && <FormSuccess message={success} className="mb-4" />}
        
        <div className="space-y-4">
          {/* 邮箱地址输入 */}
          <div>
            <FormField
              label="邮箱地址"
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="请输入邮箱地址"
              disabled={isLoading || step === 'bind'}
              required
            />
          </div>
          
          {/* 步骤1：发送验证码 */}
          {step === 'initial' && (
            <div>
              <Button
                type="button"
                variant="primary"
                onClick={handleSendCode}
                isLoading={isLoading}
                disabled={isLoading || !email}
                className="w-full"
              >
                发送验证码
              </Button>
            </div>
          )}
          
          {/* 步骤2：验证验证码 */}
          {step === 'verify' && (
            <>
              <div>
                <FormField
                  label="验证码"
                  type="text"
                  id="code"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  placeholder="请输入验证码"
                  disabled={isLoading}
                  required
                />
              </div>
              
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="primary"
                  onClick={handleVerifyCode}
                  isLoading={isLoading}
                  disabled={isLoading || !code}
                  className="flex-1"
                >
                  验证验证码
                </Button>
                
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleSendCode}
                  disabled={isLoading || countdown > 0}
                  className="whitespace-nowrap"
                >
                  {countdown > 0 ? `${countdown}秒后重新发送` : '重新发送'}
                </Button>
              </div>
            </>
          )}
          
          {/* 步骤3：绑定邮箱 */}
          {step === 'bind' && (
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                验证码已验证成功，点击下方按钮完成邮箱绑定：
              </p>
              
              <Button
                type="button"
                variant="primary"
                onClick={handleBindEmail}
                isLoading={isLoading}
                disabled={isLoading}
                className="w-full"
              >
                绑定邮箱
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BindEmailSection; 