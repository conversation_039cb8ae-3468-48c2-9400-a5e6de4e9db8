"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '../../hooks/useAuth';
import FormField from '../ui/FormField';
import FormError from '../ui/FormError';
import FormSuccess from '../ui/FormSuccess';
import Button from '../ui/Button';
import { useMutation } from '@apollo/client';
import { UPDATE_AVATAR } from '@/lib/graphql/mutations';
import { getAuthToken } from '@/utils/auth-utils';

const ProfileEdit: React.FC = () => {
  const router = useRouter();
  const { user, updateUserProfile, error: authError, clearError, isLoading: authLoading } = useAuth();
  
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    nickname: '',
    email: '',
    description: '',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  
  // GraphQL mutation 用于更新头像
  const [updateAvatar, { loading: avatarLoading, error: avatarError }] = useMutation(UPDATE_AVATAR, {
    onCompleted: (data) => {
      console.log('头像上传成功完成，服务器响应:', data);
      if (data?.updateAvatar?.success) {
        setSuccess('头像上传成功！');
        // 延迟刷新页面以显示新头像
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        setError(data?.updateAvatar?.message || '头像上传失败，请重试');
      }
    },
    onError: (error) => {
      console.error('头像上传失败:', error);
      // 简化错误处理，不再需要特殊处理内部服务器错误
      setError(`头像上传失败: ${error.message}`);
    }
  });
  
  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // 检查文件类型
      if (!file.type.match('image.*')) {
        setError('请选择图片文件');
        return;
      }
      
      // 检查文件大小（限制为2MB）
      if (file.size > 2 * 1024 * 1024) {
        setError('图片大小不能超过2MB');
        return;
      }
      
      setAvatarFile(file);
      
      // 创建预览URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // 上传头像
  const handleAvatarUpload = async () => {
    if (!avatarFile) {
      setError('请先选择一个图片');
      return;
    }
    
    try {
      setIsLoading(true);
      setError(''); // 清除之前的错误
      setSuccess(''); // 清除之前的成功信息
      
      // 检查是否已有预览URL (已经是base64格式)
      if (!previewUrl) {
        throw new Error('图片预览不可用，请重新选择图片');
      }
      
      // 打印调试信息
      console.log('准备上传头像:', {
        fileName: avatarFile.name,
        fileSize: `${(avatarFile.size / 1024).toFixed(2)} KB`,
        fileType: avatarFile.type
      });
      
      // 直接使用GraphQL API通过base64上传头像
      console.log('开始通过GraphQL上传头像...');
      const result = await updateAvatar({
        variables: {
          base64Image: previewUrl
        }
      });
      
      console.log('GraphQL更新结果:', result);
      
      // 注意：onCompleted回调会处理成功逻辑，这里不需要额外处理
    } catch (err) {
      console.error('头像上传失败:', err);
      const errorMsg = err instanceof Error ? err.message : '头像上传失败，请重试';
      setError(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };
  
  // 加载用户数据到表单
  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        nickname: user.nickname || '',
        email: user.email || '',
        description: user.description || '',
      });
    }
  }, [user]);
  
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    // 电子邮箱验证
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = '请输入有效的邮箱地址';
    }
    
    // 昵称验证（可选）
    if (formData.nickname && formData.nickname.length < 2) {
      newErrors.nickname = '昵称至少需要2个字符';
    }
    
    // 个人简介验证（可选）
    if (formData.description && formData.description.length > 500) {
      newErrors.description = '个人简介不能超过500个字符';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 清除该字段的错误
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
    
    // 清除全局错误
    if (error) setError('');
    if (authError) clearError();
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setSuccess('');
    setError('');
    
    if (!validateForm()) return;
    
    setIsLoading(true);
    
    try {
      await updateUserProfile({
        firstName: formData.firstName || undefined,
        lastName: formData.lastName || undefined,
        nickname: formData.nickname || undefined,
        email: formData.email || undefined,
        description: formData.description || undefined,
      });
      
      setSuccess('个人资料已成功更新');
      
      // 延迟跳转回资料页面
      setTimeout(() => {
        router.push('/auth/profile');
      }, 2000);
      
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '更新资料失败，请重试';
      setError(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };
  
  if (authLoading) {
    return (
      <div className="animate-pulse flex flex-col space-y-4">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
        <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
    );
  }
  
  if (!user) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600 dark:text-gray-400 mb-4">未登录或会话已过期</p>
        <Link href="/login">
          <Button variant="primary">请重新登录</Button>
        </Link>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">编辑个人资料</h2>
        <Link 
          href="/profile" 
          className="mt-2 sm:mt-0 inline-flex justify-center items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none"
        >
          返回个人资料
        </Link>
      </div>
      
      <div className="bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">个人资料信息</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">您可以更新以下信息</p>
        </div>
        
        {/* 头像上传区域 */}
        <div className="p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700">
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">个人头像</h4>
          
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-6">
            <div className="relative">
              <div className="w-24 h-24 rounded-full bg-gray-200 dark:bg-gray-700 overflow-hidden">
                {previewUrl ? (
                  <img 
                    src={previewUrl} 
                    alt="预览头像" 
                    className="w-full h-full object-cover"
                  />
                ) : user?.avatar?.url ? (
                  <img 
                    src={user.avatar.url} 
                    alt="当前头像" 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex flex-col space-y-3">
              <div className="text-sm text-gray-700 dark:text-gray-300">
                上传个人头像，建议使用正方形图片，最小尺寸 128x128 像素。
              </div>
              
              <div className="flex flex-wrap gap-2">
                <input
                  type="file"
                  id="avatar-upload"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="sr-only"
                />
                <label
                  htmlFor="avatar-upload"
                  className="px-4 py-2 text-sm font-medium rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600 cursor-pointer flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
                  </svg>
                  {previewUrl ? '更换图片' : '选择图片'}
                </label>
                
                {previewUrl && (
                  <>
                    <Button 
                      type="button" 
                      variant="primary" 
                      onClick={handleAvatarUpload}
                      disabled={avatarLoading}
                      isLoading={avatarLoading}
                    >
                      {avatarLoading ? '上传中...' : '保存头像'}
                    </Button>
                    
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => {
                        setPreviewUrl(null);
                        setAvatarFile(null);
                      }}
                      disabled={avatarLoading}
                    >
                      取消
                    </Button>
                  </>
                )}
              </div>
              
              {error && error.includes('头像') && (
                <p className="text-sm text-red-500">{error}</p>
              )}
              {avatarError && !success && (
                <p className="text-sm text-red-500">{avatarError.message}</p>
              )}
              {success && (
                <p className="text-sm text-green-500 font-semibold">{success}</p>
              )}
            </div>
          </div>
        </div>
        
        <form onSubmit={handleSubmit} className="p-4 sm:p-6">
          {((error && !error.includes('头像')) || authError) && (
            <FormError message={error || authError || ''} />
          )}
          
          {success && (
            <FormSuccess message={success} />
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <FormField
              label="姓氏"
              type="text"
              name="lastName"
              id="lastName"
              value={formData.lastName}
              onChange={handleChange}
              placeholder="请输入姓氏"
              disabled={isLoading || authLoading}
              error={errors.lastName}
            />
            
            <FormField
              label="名字"
              type="text"
              name="firstName"
              id="firstName"
              value={formData.firstName}
              onChange={handleChange}
              placeholder="请输入名字"
              disabled={isLoading || authLoading}
              error={errors.firstName}
            />
          </div>
          
          <div className="mb-6">
            <FormField
              label="显示名称"
              type="text"
              name="nickname"
              id="nickname"
              value={formData.nickname}
              onChange={handleChange}
              placeholder="请输入显示名称"
              disabled={isLoading || authLoading}
              error={errors.nickname}
              description="此名称将显示在您的评论和贡献中"
            />
          </div>
          
          <div className="mb-6">
            <FormField
              label="电子邮箱"
              type="email"
              name="email"
              id="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="请输入电子邮箱"
              disabled={isLoading || authLoading}
              error={errors.email}
              description="此邮箱用于接收通知和重置密码"
            />
          </div>
          
          <div className="mb-6">
            <label 
              htmlFor="description" 
              className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1"
            >
              个人简介
            </label>
            <textarea
              name="description"
              id="description"
              rows={5}
              value={formData.description}
              onChange={handleChange}
              placeholder="请输入个人简介"
              disabled={isLoading || authLoading}
              className={`w-full px-3 py-2 border ${
                errors.description ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } rounded-md shadow-sm focus:border-primary-500 focus:ring-1 focus:ring-primary-500 
              bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100`}
              aria-invalid={errors.description ? "true" : "false"}
            ></textarea>
            
            {errors.description && (
              <p className="mt-1 text-xs text-red-500">
                {errors.description}
              </p>
            )}
            
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              {`${formData.description?.length || 0}/500 字符`}
            </p>
          </div>
          
          <div className="flex justify-end">
            <Button
              type="submit"
              variant="primary"
              isLoading={isLoading || authLoading}
              disabled={isLoading || authLoading}
            >
              保存修改
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProfileEdit; 