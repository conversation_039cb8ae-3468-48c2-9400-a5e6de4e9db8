"use client";

import React, { useState } from 'react';
import { useAuth } from '../../hooks/useAuth';
import Button from '../ui/Button';
import FormField from '../ui/FormField';
import FormError from '../ui/FormError';
import FormSuccess from '../ui/FormSuccess';

/**
 * 手机号绑定组件
 * 提供基于token的两步手机绑定流程
 */
const BindPhoneSection: React.FC = () => {
  const { 
    user, 
    sendPhoneCode, 
    verifyPhoneCodeForBindingAndGetToken, 
    bindPhone
  } = useAuth();
  
  // 步骤状态
  const [step, setStep] = useState<'initial' | 'verify' | 'bind'>('initial');
  
  // 表单状态
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [nationCode, setNationCode] = useState('86');
  const [token, setToken] = useState('');
  
  // UI状态
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  
  // 处理发送验证码
  const handleSendCode = async () => {
    if (!phone) {
      setError('请输入手机号码');
      return;
    }
    
    setError('');
    setIsLoading(true);
    
    try {
      const result = await sendPhoneCode({
        phone,
        nationCode,
        verify: true
      });
      
      if (result.success) {
        setSuccess('验证码已发送，请查收');
        setStep('verify');
        
        // 开始倒计时
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        setError(result.message || '发送验证码失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '发送验证码失败');
    } finally {
      setIsLoading(false);
    }
  };
  
  // 处理验证码验证并获取token
  const handleVerifyCode = async () => {
    if (!phone || !code) {
      setError('请输入手机号码和验证码');
      return;
    }
    
    console.log('[调试-绑定手机] 验证验证码:', { phone, code, nationCode });
    
    setError('');
    setSuccess('');
    setIsLoading(true);
    
    try {
      console.log('[调试-绑定手机] 调用 verifyPhoneCodeForBindingAndGetToken API...');
      const result = await verifyPhoneCodeForBindingAndGetToken(phone, code, nationCode);
      
      console.log('[调试-绑定手机] 验证结果:', {
        success: result.success,
        message: result.message,
        token: result.token ? `${result.token.substring(0, 10)}...${result.token.substring(result.token.length - 10)}` : '未获取到'
      });
      
      if (result.success && result.token) {
        setToken(result.token);
        setSuccess('验证码验证成功，请点击绑定按钮完成绑定');
        setStep('bind');
      } else {
        setError(result.message || '验证码验证失败');
      }
    } catch (err) {
      console.error('[调试-绑定手机] 验证码验证异常:', err);
      setError(err instanceof Error ? err.message : '验证码验证失败');
    } finally {
      setIsLoading(false);
    }
  };
  
  // 处理绑定手机号
  const handleBindPhone = async () => {
    if (!phone || !token) {
      setError('缺少必要参数');
      return;
    }
    
    console.log('[调试-绑定手机] 开始绑定手机号:', { 
      phone, 
      token: token ? `${token.substring(0, 10)}...` : '空', 
      nationCode 
    });
    
    setError('');
    setSuccess('');
    setIsLoading(true);
    
    try {
      console.log('[调试-绑定手机] 调用 bindPhone API...');
      const result = await bindPhone({
        phone,
        token,
        nationCode
      });
      
      console.log('[调试-绑定手机] bindPhone API 返回结果:', result);
      
      if (result.success) {
        console.log('[调试-绑定手机] 手机号绑定成功!');
        setSuccess('手机号绑定成功！');
        
        // 重置表单和状态
        setTimeout(() => {
          setPhone('');
          setCode('');
          setToken('');
          setStep('initial');
          setSuccess('');
        }, 3000);
      } else {
        console.error('[调试-绑定手机] 绑定失败:', result.message);
        setError(result.message || '绑定手机号失败');
      }
    } catch (err) {
      console.error('[调试-绑定手机] 捕获到异常:', err);
      setError(err instanceof Error ? err.message : '绑定手机号失败');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">手机号绑定</h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
          {user?.phone 
            ? '更换您的手机号'
            : '绑定手机号可用于登录和找回密码'
          }
        </p>
      </div>
      
      <div className="px-4 py-5 sm:p-6">
        {error && <FormError message={error} className="mb-4" />}
        {success && <FormSuccess message={success} className="mb-4" />}
        
        <div className="space-y-4">
          {/* 手机号输入 */}
          <div>
            <FormField
              label="手机号码"
              type="tel"
              id="phone"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              placeholder="请输入手机号码"
              disabled={isLoading || step === 'bind'}
              required
            />
          </div>
          
          {/* 国际区号输入 */}
          <div>
            <FormField
              label="国际区号"
              type="text"
              id="nationCode"
              value={nationCode}
              onChange={(e) => setNationCode(e.target.value)}
              placeholder="请输入国际区号（默认86）"
              disabled={isLoading || step === 'bind'}
            />
          </div>
          
          {/* 步骤1：发送验证码 */}
          {step === 'initial' && (
            <div>
              <Button
                type="button"
                variant="primary"
                onClick={handleSendCode}
                isLoading={isLoading}
                disabled={isLoading || !phone}
                className="w-full"
              >
                发送验证码
              </Button>
            </div>
          )}
          
          {/* 步骤2：验证验证码 */}
          {step === 'verify' && (
            <>
              <div>
                <FormField
                  label="验证码"
                  type="text"
                  id="code"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  placeholder="请输入验证码"
                  disabled={isLoading}
                  required
                />
              </div>
              
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="primary"
                  onClick={handleVerifyCode}
                  isLoading={isLoading}
                  disabled={isLoading || !code}
                  className="flex-1"
                >
                  验证验证码
                </Button>
                
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleSendCode}
                  disabled={isLoading || countdown > 0}
                  className="whitespace-nowrap"
                >
                  {countdown > 0 ? `${countdown}秒后重新发送` : '重新发送'}
                </Button>
              </div>
            </>
          )}
          
          {/* 步骤3：绑定手机号 */}
          {step === 'bind' && (
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                验证码已验证成功，点击下方按钮完成手机号绑定：
              </p>
              
              <Button
                type="button"
                variant="primary"
                onClick={handleBindPhone}
                isLoading={isLoading}
                disabled={isLoading}
                className="w-full"
              >
                绑定手机号
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BindPhoneSection; 