'use client';

import React from 'react';

// 定义排序类型
export type SortType = 'hottest' | 'latest';

interface CommentHeaderProps {
  totalCount: number;
  currentSort: SortType; // 新增：接收当前排序状态
  onSortChange: (sortType: SortType) => void; // 修改：确保 onSortChange 始终被提供
}

/**
 * 评论区头部组件 - 知乎风格
 * 显示评论数量和排序选项
 */
const CommentHeader: React.FC<CommentHeaderProps> = ({
  totalCount,
  currentSort,
  onSortChange,
}) => {

  return (
    <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
      {/* 评论数量 */}
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
        {totalCount} 条评论
      </h3>

      {/* 排序选项 */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => onSortChange('hottest')}
          className={`text-sm transition-colors ${
            currentSort === 'hottest'
              ? 'text-blue-500 font-medium'
              : 'text-gray-500 hover:text-blue-500'
          }`}
        >
          最热
        </button>
        <button
          onClick={() => onSortChange('latest')}
          className={`text-sm transition-colors ${
            currentSort === 'latest'
              ? 'text-blue-500 font-medium'
              : 'text-gray-500 hover:text-blue-500'
          }`}
        >
          最新
        </button>
      </div>
    </div>
  );
};

export default CommentHeader;
