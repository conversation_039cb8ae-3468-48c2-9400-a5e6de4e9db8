import React from 'react';

interface CommentListSkeletonProps {
  count?: number;
}

const CommentItemSkeleton: React.FC = () => (
  <div className="animate-pulse flex space-x-3 py-4">
    <div className="w-10 h-10 rounded-full bg-gray-300 dark:bg-gray-700" />
    <div className="flex-1 space-y-2">
      <div className="h-3 w-1/4 bg-gray-300 dark:bg-gray-700 rounded" />
      <div className="h-3 w-full bg-gray-300 dark:bg-gray-700 rounded" />
      <div className="h-3 w-5/6 bg-gray-300 dark:bg-gray-700 rounded" />
    </div>
  </div>
);

const CommentListSkeleton: React.FC<CommentListSkeletonProps> = ({ count = 3 }) => (
  <div className="comment-list-skeleton">
    {Array.from({ length: count }).map((_, i) => (
      <CommentItemSkeleton key={i} />
    ))}
  </div>
);

export default CommentListSkeleton; 