'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';
import { BsEmojiSmile } from 'react-icons/bs';
import { useCreateComment } from '@/hooks/useComment';
import { useAuth } from '@/hooks/useAuth';
import { useDiscussionSettings } from '@/hooks/useSettings';
import ErrorHandler from '@/utils/error-handler';

interface CommentFormProps {
  postId: number;
  parentId?: number;
  onCommentAdded: () => void;
  isCustomType?: boolean;
  isReply?: boolean; // 是否为回复评论
  onCancel?: () => void; // 取消回复的回调
  order?: 'ASC' | 'DESC';
  orderby?: string;
}

const QUICK_EMOJIS = ['😊', '😂', '😎', '😍', '🤔', '👍'];

/**
 * 评论表单组件，允许用户提交评论
 */
const CommentForm: React.FC<CommentFormProps> = ({
  postId,
  parentId,
  onCommentAdded,
  isCustomType = false,
  isReply = false,
  onCancel,
  order,
  orderby,
}) => {
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [countdown, setCountdown] = useState<number>(0); // 用于倒计时
  const [isExpanded, setIsExpanded] = useState(isReply); // 回复时默认展开，主评论默认收起
  const [showPicker, setShowPicker] = useState(false); // 控制表情选择器的显示
  const [loginHref, setLoginHref] = useState('/login'); // 用于动态设置登录链接
  const formRef = useRef<HTMLDivElement>(null);
  const textAreaRef = useRef<HTMLTextAreaElement>(null); // 引用文本输入框
  const pickerRef = useRef<HTMLDivElement>(null); // 引用表情选择器容器
  const lastSubmitTimestampRef = useRef<number>(0); // 用于客户端频率控制
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const { createComment } = useCreateComment();
  const { isAuthenticated, user } = useAuth();
  const { settings: discussion, loading: discussionLoading } =
    useDiscussionSettings();

  const requiresApproval = discussion?.commentModeration;
  const skipOptimisticUpdate = requiresApproval === true;

  // 点击外部区域收起表单
  useEffect(() => {
    if (!isReply && isExpanded) {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          pickerRef.current &&
          pickerRef.current.contains(event.target as Node)
        ) {
          return;
        }
        if (
          formRef.current &&
          !formRef.current.contains(event.target as Node)
        ) {
          if (!content.trim()) {
            setIsExpanded(false);
          }
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [isExpanded, isReply, content]);

  // 处理倒计时
  useEffect(() => {
    if (countdown > 0) {
      countdownIntervalRef.current = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    } else {
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
        countdownIntervalRef.current = null;
        setError(''); // 倒计时结束时清除错误信息
      }
    }

    return () => {
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
    };
  }, [countdown]);

  // 点击外部关闭表情选择器
  useEffect(() => {
    if (!showPicker) return;
    const handleClickOutside = (event: MouseEvent) => {
      const pickerEl = document.querySelector('em-emoji-picker');
      if (
        pickerRef.current &&
        !pickerRef.current.contains(event.target as Node) &&
        (!pickerEl || !pickerEl.contains(event.target as Node))
      ) {
        setShowPicker(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showPicker]);

  // 在客户端挂载时生成回调URL
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const currentUrl = window.location.href;
      const loginUrl = '/login';
      const finalUrl = `${loginUrl}?callbackUrl=${encodeURIComponent(
        currentUrl
      )}`;
      setLoginHref(finalUrl);
    }
  }, []); // 空依赖数组确保仅在挂载时运行一次

  const insertText = (text: string) => {
    const ref = textAreaRef.current;
    if (ref) {
      const start = ref.selectionStart;
      const end = ref.selectionEnd;
      const newContent =
        content.substring(0, start) + text + content.substring(end);

      setContent(newContent);

      setTimeout(() => {
        const newCursorPos = start + text.length;
        ref.focus();
        ref.setSelectionRange(newCursorPos, newCursorPos);
      }, 0);
    } else {
      setContent((prevContent) => prevContent + text);
    }
  };

  // @emoji-mart/react 的事件处理
  const onEmojiSelect = (emojiData: { native: string }) => {
    insertText(emojiData.native);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 清除可能存在的旧倒计时
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
      countdownIntervalRef.current = null;
    }
    setError('');
    setCountdown(0);


    // 客户端频率检查 (前置守卫)
    const now = Date.now();
    const FLOOD_INTERVAL = 15000; // 15秒, 与WordPress默认防灌水时间一致
    if (now - lastSubmitTimestampRef.current < FLOOD_INTERVAL) {
      const remainingSeconds = Math.ceil((lastSubmitTimestampRef.current + FLOOD_INTERVAL - now) / 1000);
      setError(`评论提交过于频繁，请稍后再试`);
      setCountdown(remainingSeconds); // 启动倒计时
      return; // 阻止提交
    }

    setIsSubmitting(true);
    setSuccess(true);

    // 为了避免成功通知一直显示，设定一个自动关闭的定时器
    // 如果后续发生错误，这个通知会被立即清除
    const successTimer = setTimeout(() => {
      setSuccess(false);
    }, 3000); // 3秒后自动消失

    try {
      if (!content.trim()) {
        // 如果内容为空，清除乐观的成功状态并抛出错误
        setSuccess(false);
        clearTimeout(successTimer);
        throw new Error('评论内容不能为空');
      }

      if (!isAuthenticated) {
        setSuccess(false);
        clearTimeout(successTimer);
        throw new Error('请先登录后再发表评论');
      }

      console.log('[CommentForm] Submitting comment:', {
        postId,
        parentId,
        isCustomType,
        requiresApproval,
        skipOptimisticUpdate,
        content: content.substring(0, 50) + '...',
        user: user?.name || user?.nickname || 'Unknown',
      });

      const result = await createComment(
        {
          commentOn: postId,
          content,
          parent: parentId,
        },
        {
          postId,
          requiresApproval,
          isCustomType,
          skipOptimisticUpdate, // 传递跳过乐观更新的标志
          order,
          orderby,
          currentUser: {
            name: user?.name || user?.nickname || 'Me',
            avatarUrl: user?.avatar?.url || '',
          },
        }
      );
      console.log('[DEBUG_OPT_COMMENT] mutation result', result);

      if (result?.success) {
        // 只有在真正成功后，才更新时间戳
        lastSubmitTimestampRef.current = Date.now();

        setContent('');
        setIsExpanded(false); // 提交成功后收起表单

        // 仅当评论需要审核（即跳过了乐观更新）时，才调用 onCommentAdded 来刷新列表
        if (skipOptimisticUpdate) {
          onCommentAdded();
        }

        if (isReply && onCancel) {
          // 成功后直接调用 onCancel，不再需要延迟
            onCancel();
        }
      } else {
        // 如果 mutation 返回 success: false，清除乐观的成功状态并显示错误
        setSuccess(false);
        clearTimeout(successTimer);
        const errorMsg = result?.comment
          ? '评论创建失败，请重试'
          : '评论提交失败，服务器无响应';
        console.error('[CommentForm] Mutation failed with result:', result);
        setError(errorMsg);
        return;
      }
    } catch (err: any) {
      // 如果捕获到错误，立即清除乐观的成功状态
      setSuccess(false);
      clearTimeout(successTimer);

      console.error('[CommentForm] Error:', err);

      console.error('[CommentForm] Error details:', {
        message: err.message,
        graphQLErrors: err.graphQLErrors,
        networkError: err.networkError,
        extraInfo: err.extraInfo,
        stack: err.stack,
      });

      let errorMessage = '评论提交失败，请稍后重试';

      if (err.graphQLErrors && err.graphQLErrors.length > 0) {
        const graphQLError = err.graphQLErrors[0];
        console.error('[CommentForm] GraphQL Error details:', {
          message: graphQLError.message,
          extensions: graphQLError.extensions,
          locations: graphQLError.locations,
          path: graphQLError.path,
        });

        if (graphQLError.message) {
          if (graphQLError.message.includes('duplicate comment')) {
            errorMessage = '请勿重复提交相同的评论';
          } else if (
            graphQLError.message.includes('too quickly') ||
            graphQLError.message.includes('slow down') ||
            graphQLError.message.includes('速度太快')
          ) {
            errorMessage = '评论提交过于频繁，请稍后再试';
          } else if (graphQLError.message.includes('flood')) {
            errorMessage = '评论间隔时间太短，请稍后再试';
          } else if (graphQLError.message.includes('spam')) {
            errorMessage = '评论被识别为垃圾信息，请修改后重试';
          } else if (
            graphQLError.message.includes('not allowed') ||
            graphQLError.message.includes('permission')
          ) {
            errorMessage = '您没有权限发表评论';
          } else if (
            graphQLError.message.includes('closed') ||
            graphQLError.message.includes('disabled')
          ) {
            errorMessage = '该文章已关闭评论';
          } else if (
            graphQLError.message.includes('required') ||
            graphQLError.message.includes('empty') ||
            graphQLError.message.includes('blank')
          ) {
            errorMessage = '请填写必需的字段';
          } else if (
            graphQLError.message.includes('invalid') ||
            graphQLError.message.includes('format')
          ) {
            errorMessage = '提交的数据格式不正确';
          } else if (
            graphQLError.message.includes('expired') ||
            graphQLError.message.includes('token')
          ) {
            errorMessage = '登录已过期，请重新登录后再试';
          } else if (
            graphQLError.message.includes('blocked') ||
            graphQLError.message.includes('banned')
          ) {
            errorMessage = '您的账号或IP已被限制评论';
          } else if (
            graphQLError.message.includes('moderation') ||
            graphQLError.message.includes('approval')
          ) {
            errorMessage = '评论已提交，等待管理员审核';
          } else if (
            graphQLError.message.includes('length') ||
            graphQLError.message.includes('long') ||
            graphQLError.message.includes('short')
          ) {
            errorMessage = '评论内容长度不符合要求';
          } else if (
            graphQLError.message.includes('blacklist') ||
            graphQLError.message.includes('forbidden')
          ) {
            errorMessage = '评论包含被禁止的内容';
          } else if (
            graphQLError.message.includes('network') ||
            graphQLError.message.includes('connection')
          ) {
            errorMessage = '网络连接异常，请稍后重试';
          } else if (
            graphQLError.message.includes('server') ||
            graphQLError.message.includes('internal')
          ) {
            errorMessage = '服务器暂时无法处理请求，请稍后重试';
          } else {
            errorMessage = `提交失败: ${graphQLError.message}`;
          }
        }
      } else if (err.networkError) {
        console.error('[CommentForm] Network Error details:', {
          message: err.networkError.message,
          statusCode: err.networkError.statusCode,
          result: err.networkError.result,
        });
        errorMessage = `网络错误: ${
          err.networkError.message || '连接失败，请检查网络后重试'
        }`;
      } else if (err.message) {
        errorMessage = `错误: ${err.message}`;
      }
      
      setError(errorMessage);
      setCountdown(0); // 确保其他错误发生时，倒计时被重置
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="comment-form mb-6">
        <div
          className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700
                     rounded-lg p-3"
        >
          <div className="flex items-center justify-between">
            <span className="text-gray-500 dark:text-gray-400 text-sm">
              请先登录后发表评论
            </span>
            <Link
              href={loginHref}
              className="px-4 py-1.5 rounded text-sm font-medium transition-colors bg-blue-500 hover:bg-blue-600 text-white"
            >
              登录
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!isExpanded) {
    return (
      <div className="comment-form mb-6" ref={formRef}>
        <div
          className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700
                     rounded-lg p-3 cursor-text hover:bg-gray-100 dark:hover:bg-gray-700
                     transition-colors"
          onClick={() => setIsExpanded(true)}
        >
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
              {user?.avatar?.url ? (
                <img
                  src={user.avatar.url}
                  alt={user.name || user.nickname || 'User'}
                  className="w-8 h-8 rounded-full object-cover"
                />
              ) : (
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {(user?.name || user?.nickname || 'U')
                    .charAt(0)
                    .toUpperCase()}
                </span>
              )}
            </div>

            <span className="text-gray-500 dark:text-gray-400 text-sm flex-1">
              {parentId ? '写下你的回复...' : '写下你的评论...'}
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="comment-form bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg mb-6"
      ref={formRef}
    >
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-3 rounded-t-lg border-b border-red-200 dark:border-red-800">
          {error} {countdown > 0 && `(${countdown}s)`}
        </div>
      )}

      {success && (
        <div
          className={`p-3 rounded-t-lg border-b ${
          requiresApproval
            ? 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-400 border-yellow-200 dark:border-yellow-800'
            : 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 border-green-200 dark:border-green-800'
          }`}
        >
          {requiresApproval ? (
            <div className="flex items-center text-sm">
              <svg
                className="w-4 h-4 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
              <span>评论已提交，等待审核</span>
            </div>
          ) : (
            <div className="flex items-center text-sm">
              <svg
                className="w-4 h-4 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <span>评论发布成功！</span>
            </div>
          )}
        </div>
      )}

      <form onSubmit={handleSubmit} className="p-4">
        <div className="flex space-x-3 mb-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
              {user?.avatar?.url ? (
                <img
                  src={user.avatar.url}
                  alt={user.name || user.nickname || 'User'}
                  className="w-8 h-8 rounded-full object-cover"
                />
              ) : (
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {(user?.name || user?.nickname || 'U')
                    .charAt(0)
                    .toUpperCase()}
                </span>
              )}
            </div>
          </div>

          <div className="flex-1">
            <textarea
              ref={textAreaRef}
              className="w-full border-0 resize-none text-base text-gray-900 dark:text-gray-100
                         bg-transparent placeholder-gray-500 dark:placeholder-gray-400
                         focus:outline-none focus:ring-0"
              rows={3}
              placeholder={parentId ? '写下你的回复...' : '写下你的评论...'}
              value={content}
              onChange={(e) => {
                setContent(e.target.value);
                if (error) setError(''); // 用户开始输入时清除错误
                if (countdown > 0) setCountdown(0); // 用户开始输入时清除倒计时
              }}
              required
              autoFocus
            />
          </div>
        </div>

        <div className="flex items-center justify-between pt-3 border-t border-gray-100 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="relative" ref={pickerRef}>
              <button
                type="button"
                onClick={() => setShowPicker(!showPicker)}
                className="w-8 h-8 flex items-center justify-center rounded text-gray-500 hover:text-blue-500 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                title="添加表情"
              >
                <BsEmojiSmile className="w-5 h-5" />
              </button>
              {showPicker && (
                <div className="absolute bottom-full mb-2 z-10">
                  <Picker
                    data={data}
                    onEmojiSelect={onEmojiSelect}
                    locale="zh"
                    theme={
                      document.documentElement.classList.contains('dark')
                        ? 'dark'
                        : 'light'
                    }
                    set="native"
                    navPosition="bottom"
                    previewPosition="none"
                  />
                </div>
              )}
            </div>

            <div className="items-center space-x-1 hidden sm:flex">
              {QUICK_EMOJIS.map((emoji) => (
                <button
                  key={emoji}
                  type="button"
                  onClick={() => insertText(emoji)}
                  className="w-8 h-8 flex items-center justify-center rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  title={emoji}
                >
                  <span className="text-lg">{emoji}</span>
                </button>
              ))}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {isReply && onCancel && (
              <button
                type="button"
                onClick={onCancel}
                className="px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400
                           hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              >
                取消
              </button>
            )}

            <button
              type="submit"
              disabled={isSubmitting || !content.trim()}
              className={`px-4 py-1.5 rounded text-sm font-medium transition-colors ${
                isSubmitting || !content.trim()
                  ? 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              }`}
            >
              {isSubmitting ? '发布中...' : '发布'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default CommentForm; 