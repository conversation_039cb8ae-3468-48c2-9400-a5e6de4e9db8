'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useComments, useContentNodeComments } from '@/hooks/useComment';
import { useApolloClient } from '@apollo/client';
import { GET_POST_COMMENTS, GET_CONTENT_NODE_COMMENTS } from '@/lib/graphql/queries';
import CommentForm from './CommentForm';
import CommentList from './CommentList';
import CommentListSkeleton from './CommentListSkeleton';
import InfiniteScroll from '@/components/InfiniteScroll';
import { Comment } from '@/types/post';
import CommentHeader from './CommentHeader';
import type { SortType } from './CommentHeader';

interface CommentSectionProps {
  postId: number;
  commentStatus?: string;
  isCustomType?: boolean;
  initialData?: {
    nodes: Comment[];
    pageInfo: {
      hasNextPage: boolean;
      endCursor: string | null;
    };
  };
}

const CommentSection: React.FC<CommentSectionProps> = ({
  postId,
  commentStatus = 'open',
  isCustomType = false,
  initialData,
}) => {
  const [sortType, setSortType] = useState<SortType>('hottest');
  
  const normalizeComments = (nodes: Comment[]): Comment[] => {
    if (!nodes) return [];
    return nodes.map((n: Comment) => ({
      ...n,
      databaseId: (n as any).databaseId ?? 0,
      status: (n as any).status ?? '',
      author: n.author ? {
        ...n.author,
        node: {
          ...n.author.node,
          url: n.author.node?.url ?? '',
        },
      } : n.author,
    }));
  };

  const getSortVariables = (): { order: 'ASC' | 'DESC'; orderby: string } => {
    switch (sortType) {
      case 'latest':
        return { order: 'DESC', orderby: 'COMMENT_DATE' };
      case 'hottest':
        return { order: 'DESC', orderby: 'HOTTEST' };
      default:
        return { order: 'ASC', orderby: 'COMMENT_DATE' };
    }
  };

  const { order, orderby } = getSortVariables();

  const standardCommentsResult = useComments(postId, 20, isCustomType, order, orderby);
  const customCommentsResult = useContentNodeComments(postId, 20, !isCustomType, order, orderby);

  const {
    comments: fetchedComments,
    loading,
    error,
    refetch,
    loadMore,
    pageInfo,
  } = isCustomType ? customCommentsResult : standardCommentsResult;
  
  // Directly use the fetched comments after normalization
  const comments = useMemo(() => normalizeComments(fetchedComments), [fetchedComments]);
  const totalCount = comments.length;
  
  const handleCommentAdded = () => {
    refetch();
  };

  const handleSortChange = (newSortType: SortType) => {
    // 切换排序时，我们希望看到骨架屏，而不是旧数据
    // 虽然 useMemo 会在 order/orderby 变化时重新计算，但数据刷新不是瞬时的
    // 此处可以考虑在切换时清空现有评论，但这会引入更复杂的状态管理
    // 暂时保持原样，依赖loading状态来驱动UI
    setSortType(newSortType);
  };
  
  if (error) {
    return (
      <div className="comment-section my-8">
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded">
          加载评论失败: {error.message}
        </div>
      </div>
    );
  }

  // Remove the debug section
  
  return (
    <div className="comment-section my-8" id="comment-section">
      <h3 className="text-xl font-bold mb-4">评论</h3>

      <div className="mb-6">
        <CommentForm
          postId={postId}
          onCommentAdded={handleCommentAdded}
          isCustomType={isCustomType}
          order={order}
          orderby={orderby}
        />
      </div>

      <CommentHeader
        totalCount={totalCount}
        onSortChange={handleSortChange}
        currentSort={sortType}
      />

      {/* 场景一：初次加载或切换Tab时的骨架屏 */}
      {loading && comments.length === 0 && <CommentListSkeleton />}

      {/* 场景二：加载完成，有评论 */}
      {comments.length > 0 && (
        <InfiniteScroll
          hasMore={pageInfo?.hasNextPage || false}
          loading={loading}
          onLoadMore={loadMore}
        >
          <CommentList
            comments={comments}
            postId={postId}
            onCommentAdded={handleCommentAdded}
            isCustomType={isCustomType}
            order={order}
            orderby={orderby}
          />
          {/* 场景三：分页加载中的追加骨架屏 */}
          {loading && <CommentListSkeleton count={3} />}
        </InfiniteScroll>
      )}
      
      {/* 场景四：加载完成，无评论 */}
      {!loading && comments.length === 0 && (
         <p className='py-8 text-center text-gray-500'>暂无评论，快来抢占沙发吧！</p>
      )}
    </div>
  );
};

export default CommentSection; 