'use client';

import React, { useState } from 'react';
import { Comment } from '@/types/post';
import CommentForm from './CommentForm';

// 定义一个可以递归的评论类型
export interface RecursiveComment extends Comment {
  children: RecursiveComment[];
}

interface CommentItemProps {
  comment: RecursiveComment;
  postId: number;
  onCommentAdded: () => void;
  isCustomType?: boolean;
  level?: number; // 评论层级：1=一级评论，2=二级评论，3=三级评论
  parentAuthor?: string; // 父评论作者名（用于三级评论显示）
  order?: 'ASC' | 'DESC';
  orderby?: string;
}

/**
 * 评论项组件，用于显示单个评论和其回复
 * 支持知乎风格的三级评论系统
 */
const CommentItem: React.FC<CommentItemProps> = ({
  comment,
  postId,
  onCommentAdded,
  isCustomType = false,
  level = 1,
  parentAuthor,
  order,
  orderby
}) => {
  const [isReplying, setIsReplying] = useState(false);
  const [showAllReplies, setShowAllReplies] = useState(false);

  // 提取数字ID用于回复
  const getNumericId = (comment: Comment): number | undefined => {
    // 优先使用databaseId
    if (comment.databaseId && comment.databaseId > 0) {
      return comment.databaseId;
    }

    // 从GraphQL ID中提取数字ID
    try {
      // WordPress GraphQL ID通常是Base64编码的 "comment:123"
      const decoded = atob(comment.id);
      if (decoded.includes(':')) {
        const numericPart = decoded.split(':')[1];
        const numericId = parseInt(numericPart, 10);
        if (!isNaN(numericId) && numericId > 0) {
          return numericId;
        }
      }
    } catch (e) {
      console.warn('[CommentItem] Failed to decode comment ID:', comment.id);
    }

    return undefined;
  };

  const numericId = getNumericId(comment);

  // 格式化日期 - 知乎风格的简化日期显示
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return '刚刚';
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`;
    } else if (diffInHours < 24 * 7) {
      const days = Math.floor(diffInHours / 24);
      return `${days}天前`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit'
      });
    }
  };


  const formattedDate = formatDate(comment.date);
  const authorName = comment.author?.node?.name || '匿名用户';
  
  const replies = comment.children || [];

  // 根据评论层级确定样式
  const getCommentStyles = () => {
    const baseStyles = "comment-item";

    switch (level) {
      case 1:
        return `${baseStyles} mb-6 border-b border-gray-100 dark:border-gray-700 pb-4`;
      case 2:
        // 二级评论有缩进
        return `${baseStyles} mb-3 ml-12`;
      case 3:
        // 三级评论不再增加缩进，保持与二级对齐
        return `${baseStyles} mb-3`;
      default:
        return baseStyles;
    }
  };

  // 显示的回复数量（仅对一级评论下的回复做折叠处理）
  const displayReplies = level === 1 && !showAllReplies ? replies.slice(0, 2) : replies;
  const hasMoreReplies = level === 1 && replies.length > 2 && !showAllReplies;

  return (
    <div className={getCommentStyles()}>
      <div className="flex items-start space-x-3">
        {/* 头像 */}
        <div className="flex-shrink-0">
          {comment.author?.node?.avatar?.url ? (
            <img
              src={comment.author.node.avatar.url}
              alt={authorName}
              className="w-8 h-8 rounded-full"
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
              <span className="text-xs text-gray-600 dark:text-gray-300">
                {authorName.charAt(0)}
              </span>
            </div>
          )}
        </div>

        {/* 评论内容 */}
        <div className="flex-grow min-w-0">
          {/* 用户名和回复关系 */}
          <div className="flex items-center space-x-2 mb-1">
            <span className="font-medium text-gray-900 dark:text-gray-100 text-sm">
              {authorName}
            </span>
            {/* 仅在三级评论中显示回复对象 */}
            {level === 3 && parentAuthor && (
              <>
                <span className="text-gray-400 text-sm mx-1">▸</span>
                <span className="text-gray-600 dark:text-gray-400 text-sm">
                  {parentAuthor}
                </span>
              </>
            )}
          </div>

          {/* 评论内容 */}
          <div
            className="text-gray-700 dark:text-gray-300 text-base leading-relaxed mb-2"
            dangerouslySetInnerHTML={{ __html: comment.content }}
          />

          {/* 时间和操作按钮 */}
          <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
            <span>{formattedDate}</span>
            <button
              className="hover:text-blue-500 transition-colors"
              onClick={() => setIsReplying(!isReplying)}
            >
              {isReplying ? '取消回复' : '回复'}
            </button>
            <button className="hover:text-red-500 transition-colors flex items-center space-x-1">
              <span>♡</span>
              <span>0</span>
            </button>
          </div>
        </div>
      </div>

      {/* 回复表单 */}
      {isReplying && (
        <div className="mt-3 ml-11">
          <CommentForm
            postId={postId}
            parentId={numericId}
            onCommentAdded={() => {
              setIsReplying(false);
              onCommentAdded();
            }}
            isCustomType={isCustomType}
            isReply={true}
            onCancel={() => setIsReplying(false)}
            order={order}
            orderby={orderby}
          />
        </div>
      )}

      {/* 回复列表 (统一的递归渲染) */}
      {displayReplies.length > 0 && (
        <div className="mt-3">
          {displayReplies.map(reply => (
            <CommentItem
              key={reply.id}
              comment={reply}
              postId={postId}
              onCommentAdded={onCommentAdded}
              isCustomType={isCustomType}
              level={level + 1}
              parentAuthor={authorName} // 将当前评论的作者作为父作者传给下一级
              order={order}
              orderby={orderby}
            />
          ))}
        </div>
      )}

      {/* 显示更多回复按钮 (移动到列表下方) */}
      {hasMoreReplies && (
        <div className="mt-3 ml-11">
          <button
            onClick={() => setShowAllReplies(true)}
            className="text-sm text-blue-500 hover:underline flex items-center space-x-1"
          >
            <span>查看全部 {replies.length} 条回复</span>
            <span>▼</span>
          </button>
        </div>
      )}

      {/* 折叠回复按钮 */}
      {showAllReplies && replies.length > 2 && (
        <div className="mt-3 ml-11">
          <button
            onClick={() => setShowAllReplies(false)}
            className="text-sm text-gray-500 hover:text-blue-500 flex items-center space-x-1"
          >
            <span>收起回复</span>
            <span>▲</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default CommentItem; 