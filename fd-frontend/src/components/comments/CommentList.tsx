'use client';

import React from 'react';
import CommentItem, { RecursiveComment } from './CommentItem';
import { Comment } from '@/types/post';

interface CommentListProps {
  comments: Comment[];
  postId: number;
  onCommentAdded: () => void;
  isCustomType?: boolean;
  order?: 'ASC' | 'DESC';
  orderby?: string;
}

/**
 * 评论列表组件 - 知乎风格
 * 1. 负责将扁平化的评论列表转换为三级嵌套结构
 * 2. 渲染评论项
 */
const CommentList: React.FC<CommentListProps> = ({ comments, postId, onCommentAdded, isCustomType = false, order, orderby }) => {
  // 核心逻辑：将扁平列表转换为支持三级的嵌套结构
  const buildCommentTree = (commentList: Comment[]): RecursiveComment[] => {
    const commentMap = new Map<string, RecursiveComment>();

    // 第一次遍历：将所有评论放入 map，并初始化 children 数组
    commentList.forEach(comment => {
      commentMap.set(comment.id, { ...comment, children: [] });
    });

    const topLevelComments: RecursiveComment[] = [];

    // 第二次遍历：构建层级关系
    commentMap.forEach(comment => {
      if (comment.parentId && commentMap.has(comment.parentId)) {
        // 这是一个子评论，找到它的父评论并把自己加进去
        const parent = commentMap.get(comment.parentId);
        if (parent) {
          parent.children.push(comment);
        }
      } else {
        // 这是一个顶级评论
        topLevelComments.push(comment);
      }
    });

    return topLevelComments;
  };

  const topLevelComments = buildCommentTree(comments);

  return (
    <div className="comment-list-container space-y-6">
      {topLevelComments.map(comment => (
        <CommentItem
          key={comment.id}
          comment={comment}
          postId={postId}
          onCommentAdded={onCommentAdded}
          isCustomType={isCustomType}
          level={1}
          order={order}
          orderby={orderby}
        />
      ))}
    </div>
  );
};

export default CommentList; 