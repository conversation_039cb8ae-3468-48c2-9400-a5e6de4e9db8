'use client';

import React, { useState } from 'react';
import { 
  useCommentsByStatus, 
  useUpdateCommentStatus, 
  useDeleteComment, 
  useRestoreComment,
  CommentStatusEnum 
} from '@/hooks/useComment';
import { Comment } from '@/types/post';

interface CommentManagementProps {
  showStatusFilter?: boolean;
}

/**
 * 评论管理组件 - 演示新增的评论管理功能
 * 包括状态过滤、状态转换、恢复评论等功能
 */
const CommentManagement: React.FC<CommentManagementProps> = ({ 
  showStatusFilter = true 
}) => {
  const [selectedStatus, setSelectedStatus] = useState<CommentStatusEnum[]>([CommentStatusEnum.APPROVE]);
  const [selectedComment, setSelectedComment] = useState<Comment | null>(null);

  // 获取评论数据 - 暂时使用模拟数据来测试界面
  const comments: Comment[] = [];
  const loading = false;
  const error: Error | null = null;
  const refetch = () => {};

  // TODO: 启用真实数据获取
  // const {
  //   comments,
  //   loading,
  //   error,
  //   refetch
  // } = useCommentsByStatus(selectedStatus);

  // 状态管理Hooks
  const { updateCommentStatus, loading: statusLoading } = useUpdateCommentStatus();
  const { deleteComment, loading: deleteLoading } = useDeleteComment();
  const { restoreComment, loading: restoreLoading } = useRestoreComment();

  // 处理状态变更
  const handleStatusChange = async (commentId: string, newStatus: CommentStatusEnum) => {
    try {
      await updateCommentStatus(commentId, newStatus);
      refetch(); // 刷新列表
    } catch (error) {
      console.error('状态更新失败:', error);
    }
  };

  // 处理删除评论
  const handleDeleteComment = async (commentId: string) => {
    if (!confirm('确定要删除这条评论吗？')) return;
    
    try {
      await deleteComment(commentId);
      refetch(); // 刷新列表
    } catch (error) {
      console.error('删除评论失败:', error);
    }
  };

  // 处理恢复评论
  const handleRestoreComment = async (commentId: string) => {
    try {
      await restoreComment(commentId);
      refetch(); // 刷新列表
    } catch (error) {
      console.error('恢复评论失败:', error);
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 获取状态显示文本
  const getStatusText = (status: string) => {
    const statusMap = {
      'APPROVE': '已批准',
      'HOLD': '待审核',
      'SPAM': '垃圾评论',
      'TRASH': '已删除'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colorMap = {
      'APPROVE': 'text-green-600 bg-green-100',
      'HOLD': 'text-yellow-600 bg-yellow-100',
      'SPAM': 'text-red-600 bg-red-100',
      'TRASH': 'text-gray-600 bg-gray-100'
    };
    return colorMap[status as keyof typeof colorMap] || 'text-gray-600 bg-gray-100';
  };

  if (loading) {
    return (
      <div className="comment-management p-6">
        <div className="text-center">加载评论管理数据...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="comment-management p-6">
        <div className="text-red-600">加载失败: {String(error)}</div>
      </div>
    );
  }

  return (
    <div className="comment-management p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
      <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100">
        评论管理
      </h2>

      {/* 状态过滤器 */}
      {showStatusFilter && (
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-3 text-gray-900 dark:text-gray-100">
            按状态过滤
          </h3>
          <div className="flex flex-wrap gap-2">
            {Object.values(CommentStatusEnum).map(status => (
              <label key={status} className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedStatus.includes(status)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedStatus([...selectedStatus, status]);
                    } else {
                      setSelectedStatus(selectedStatus.filter(s => s !== status));
                    }
                  }}
                  className="mr-2"
                />
                <span className={`px-2 py-1 rounded text-sm ${getStatusColor(status)}`}>
                  {getStatusText(status)}
                </span>
              </label>
            ))}
          </div>
        </div>
      )}

      {/* 评论列表 */}
      <div className="space-y-4">
        {comments.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            没有找到符合条件的评论
          </div>
        ) : (
          comments.map(comment => (
            <div key={comment.id} className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-700">
              {/* 评论头部信息 */}
              <div className="flex justify-between items-start mb-3">
                <div>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {comment.author?.node?.name || '匿名用户'}
                  </span>
                  <span className="ml-2 text-sm text-gray-500">
                    {formatDate(comment.date)}
                  </span>
                  {comment.parentId && (
                    <span className="ml-2 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                      回复评论
                    </span>
                  )}
                  {comment.commentedOn?.node?.title && (
                    <span className="ml-2 text-xs text-purple-600 bg-purple-100 px-2 py-1 rounded">
                      文章: {comment.commentedOn.node.title}
                    </span>
                  )}
                </div>
                <span className={`px-2 py-1 rounded text-sm ${getStatusColor(comment.status)}`}>
                  {getStatusText(comment.status)}
                </span>
              </div>

              {/* 评论内容 */}
              <div 
                className="text-gray-700 dark:text-gray-300 mb-4 prose prose-sm"
                dangerouslySetInnerHTML={{ __html: comment.content }}
              />

              {/* 操作按钮 */}
              <div className="flex flex-wrap gap-2">
                {/* 状态转换按钮 */}
                {Object.values(CommentStatusEnum).map(status => (
                  status !== comment.status && (
                    <button
                      key={status}
                      onClick={() => handleStatusChange(comment.id, status)}
                      disabled={statusLoading}
                      className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
                    >
                      标记为{getStatusText(status)}
                    </button>
                  )
                ))}

                {/* 删除按钮 */}
                {comment.status !== CommentStatusEnum.TRASH && (
                  <button
                    onClick={() => handleDeleteComment(comment.id)}
                    disabled={deleteLoading}
                    className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
                  >
                    删除
                  </button>
                )}

                {/* 恢复按钮 */}
                {comment.status === CommentStatusEnum.TRASH && (
                  <button
                    onClick={() => handleRestoreComment(comment.id)}
                    disabled={restoreLoading}
                    className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
                  >
                    恢复
                  </button>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* 统计信息 */}
      <div className="mt-6 pt-4 border-t text-sm text-gray-500">
        共找到 {comments.length} 条评论
      </div>
    </div>
  );
};

export default CommentManagement;
