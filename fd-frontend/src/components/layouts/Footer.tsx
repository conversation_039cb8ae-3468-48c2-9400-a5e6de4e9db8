'use client';

import React from 'react';
import Link from 'next/link';
import FooterMenu from '@/components/navigation/FooterMenu';

interface FooterProps {
  className?: string;
}

// 社交媒体链接组件
const SocialLinks: React.FC = () => (
  <div className="flex space-x-4">
    <a 
      href="#" 
      className="text-gray-400 hover:text-primary dark:hover:text-primary-400 transition-colors duration-200"
      aria-label="Twitter"
    >
      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
      </svg>
    </a>
    <a 
      href="#" 
      className="text-gray-400 hover:text-primary dark:hover:text-primary-400 transition-colors duration-200"
      aria-label="GitHub"
    >
      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
      </svg>
    </a>
    <a 
      href="#" 
      className="text-gray-400 hover:text-primary dark:hover:text-primary-400 transition-colors duration-200"
      aria-label="LinkedIn"
    >
      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clipRule="evenodd" />
      </svg>
    </a>
  </div>
);

// 公司信息组件
const CompanyInfo: React.FC = () => (
  <div className="md:col-span-2">
    <h3 className="text-lg font-semibold mb-3 md:mb-4 text-gray-900 dark:text-white font-heading">
      Future Decade
    </h3>
    <p className="text-sm md:text-base text-gray-600 dark:text-gray-300 mb-3 md:mb-4 leading-relaxed">
      探索未来十年的技术趋势，分享前沿科技资讯，构建数字化未来。
    </p>
    <SocialLinks />
  </div>
);

// 联系信息组件
const ContactInfo: React.FC = () => (
  <div>
    <h3 className="text-lg font-semibold mb-3 md:mb-4 text-gray-900 dark:text-white font-heading">
      联系我们
    </h3>
    <ul className="space-y-1.5 md:space-y-2 text-sm md:text-base text-gray-600 dark:text-gray-300">
      <li className="flex items-center">
        <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
        <EMAIL>
      </li>
      <li className="flex items-center">
        <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
        </svg>
        +86 123 4567 8900
      </li>
      <li className="flex items-center">
        <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
        北京市朝阳区科技园
      </li>
    </ul>
  </div>
);

// 版权信息组件
const CopyrightInfo: React.FC = () => (
  <div className="border-t border-gray-200 dark:border-gray-700 mt-6 md:mt-8 pt-4 md:pt-6">
    <div className="flex flex-col md:flex-row justify-between items-center space-y-2 md:space-y-0">
      <p className="text-sm text-gray-500 dark:text-gray-400">
        © 2024 Future Decade. 保留所有权利。
      </p>
      <div className="flex space-x-4 text-sm">
        <Link 
          href="/privacy" 
          className="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-400 transition-colors duration-200"
        >
          隐私政策
        </Link>
        <Link 
          href="/terms" 
          className="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-400 transition-colors duration-200"
        >
          服务条款
        </Link>
        <Link 
          href="/sitemap" 
          className="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-400 transition-colors duration-200"
        >
          网站地图
        </Link>
      </div>
    </div>
  </div>
);

// 主Footer组件
const Footer: React.FC<FooterProps> = ({ className = '' }) => {
  return (
    <footer className={`bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="container mx-auto px-4 py-6 md:py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 md:gap-8">
          
          {/* 公司信息 */}
          <CompanyInfo />
          
          {/* 底部菜单 */}
          <FooterMenu title="快速链接" />
          
          {/* 联系信息 */}
          <ContactInfo />
          
          {/* 额外的菜单或信息区域 */}
          <div>
            <h3 className="text-lg font-semibold mb-3 md:mb-4 text-gray-900 dark:text-white font-heading">
              服务支持
            </h3>
            <ul className="space-y-1.5 md:space-y-2">
              <li>
                <Link 
                  href="/help" 
                  className="text-sm md:text-base text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-400 transition-colors duration-200 block"
                >
                  帮助中心
                </Link>
              </li>
              <li>
                <Link 
                  href="/faq" 
                  className="text-sm md:text-base text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-400 transition-colors duration-200 block"
                >
                  常见问题
                </Link>
              </li>
              <li>
                <Link 
                  href="/feedback" 
                  className="text-sm md:text-base text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-400 transition-colors duration-200 block"
                >
                  意见反馈
                </Link>
              </li>
              <li>
                <Link 
                  href="/api-docs" 
                  className="text-sm md:text-base text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-400 transition-colors duration-200 block"
                >
                  API文档
                </Link>
              </li>
            </ul>
          </div>
        </div>
        
        {/* 版权信息 */}
        <CopyrightInfo />
      </div>
    </footer>
  );
};

export default React.memo(Footer);
