'use client';

import React, { ReactNode } from 'react';
import Header from './Header';
import Footer from './Footer';

// MainLayout组件接口
interface MainLayoutProps {
  children: ReactNode;
  showHeader?: boolean;
  showFooter?: boolean;
}



// 主布局组件
const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  showHeader = true,
  showFooter = true
}) => {
  return (
    <div className="flex flex-col min-h-screen bg-background dark:bg-background-dark">
      {showHeader && <Header />}

      <main className="flex-grow container mx-auto px-3 md:px-4 py-4 md:py-8">
        {children}
      </main>

      {showFooter && <Footer />}
    </div>
  );
};

export default MainLayout;
