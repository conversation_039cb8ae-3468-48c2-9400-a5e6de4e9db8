'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useVITheme } from '@/contexts/VIThemeContext';
import NavigationMenu from '@/components/navigation/NavigationMenu';
import MobileMenu from '@/components/navigation/MobileMenu';
import UserMenu from '@/components/navigation/UserMenu';

interface HeaderProps {
  className?: string;
}

// Logo组件
const Logo: React.FC = () => {
  const { getLogoUrl, settings } = useVITheme();

  return (
    <Link href="/" className="flex items-center">
      {settings.logoUrl ? (
        <Image 
          src={getLogoUrl(false)} 
          alt="Future Decade Logo" 
          width={130} 
          height={35}
          className="dark:hidden"
          style={{ objectFit: 'contain' }}
          priority // 优先加载Logo
        />
      ) : (
        <span className="text-xl md:text-2xl font-bold text-gray-900 dark:text-white font-heading">
          Future Decade
        </span>
      )}
    </Link>
  );
};

// 汉堡菜单按钮组件
const MobileMenuButton: React.FC<{ 
  isOpen: boolean; 
  onClick: () => void; 
}> = ({ isOpen, onClick }) => (
  <button 
    className="md:hidden p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-200 dark:focus:ring-gray-700 transition-colors duration-200"
    onClick={onClick}
    aria-label={isOpen ? "关闭菜单" : "打开菜单"}
    aria-expanded={isOpen}
  >
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      className="h-6 w-6 text-gray-700 dark:text-gray-300 transition-transform duration-200" 
      fill="none" 
      viewBox="0 0 24 24" 
      stroke="currentColor"
    >
      {isOpen ? (
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
      ) : (
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
      )}
    </svg>
  </button>
);

// 主题切换按钮组件
const ThemeToggle: React.FC = () => {
  const { getColor } = useVITheme();
  
  const toggleTheme = () => {
    const html = document.documentElement;
    const isDark = html.classList.contains('dark');
    
    if (isDark) {
      html.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    } else {
      html.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    }
  };

  return (
    <button 
      className="p-1.5 text-gray-600 dark:text-gray-300 hover:text-primary rounded-full transition-colors duration-200"
      onClick={toggleTheme}
      aria-label="切换主题"
    >
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        className="h-5 w-5 dark:hidden" 
        fill="none" 
        viewBox="0 0 24 24" 
        stroke="currentColor"
      >
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth={2} 
          d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" 
        />
      </svg>
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        className="h-5 w-5 hidden dark:block" 
        fill="none" 
        viewBox="0 0 24 24" 
        stroke="currentColor"
      >
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth={2} 
          d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" 
        />
      </svg>
    </button>
  );
};

// 头部组件
const Header: React.FC<HeaderProps> = ({ className = '' }) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(prev => !prev);
  };

  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
  };

  return (
    <header className={`sticky top-0 z-20 bg-white dark:bg-gray-900 shadow-md ${className}`}>
      <div className="container mx-auto px-4 py-3 md:py-4">
        <div className="flex flex-col md:flex-row justify-between items-center">
          
          {/* 左侧 Logo 区域 */}
          <div className="flex items-center justify-between w-full md:w-1/5 md:justify-start mb-2 md:mb-0">
            <Logo />
            <MobileMenuButton 
              isOpen={mobileMenuOpen} 
              onClick={toggleMobileMenu} 
            />
          </div>
          
          {/* 中间 导航区域 - 桌面版 */}
          <div className="hidden md:flex items-center justify-center w-full md:w-3/5">
            <NavigationMenu className="flex-wrap" />
          </div>
          
          {/* 右侧 工具区域 */}
          <div className="hidden md:flex items-center justify-end space-x-2 w-full md:w-1/5">
            <ThemeToggle />
            <UserMenu />
          </div>
        </div>
      </div>
      
      {/* 移动端菜单 */}
      <MobileMenu
        isOpen={mobileMenuOpen}
        onClose={closeMobileMenu}
      />
    </header>
  );
};

export default React.memo(Header);
