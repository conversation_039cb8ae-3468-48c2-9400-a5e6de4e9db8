import React, { ReactNode } from 'react';
import MainLayout from './MainLayout';
import { fetchMenuData } from '@/lib/menu-data';

interface MenuItem {
  id: string;
  title: string;
  label: string;
  url: string;
  target?: string;
  children?: MenuItem[];
}

interface LayoutWithMenuProps {
  children: ReactNode;
  showHeader?: boolean;
  showFooter?: boolean;
  // topMenuItems 和 footerMenuItems 现在通过Context提供，不再需要props
}

// 带菜单数据的布局组件 (现在只是MainLayout的别名，因为菜单数据已经通过Context提供)
const LayoutWithMenu: React.FC<LayoutWithMenuProps> = ({
  children,
  showHeader = true,
  showFooter = true,
  // topMenuItems 和 footerMenuItems 现在通过Context提供，不再需要props
}) => {
  return (
    <MainLayout
      showHeader={showHeader}
      showFooter={showFooter}
    >
      {children}
    </MainLayout>
  );
};

// 服务端预获取菜单数据的工具函数 (已废弃 - 菜单数据现在在根布局中全局获取)
// 保留此函数以保持向后兼容性，但实际上不再需要使用
export async function getMenuData() {
  console.warn('[LayoutWithMenu] getMenuData is deprecated. Menu data is now provided globally via Context.');
  const menuData = await fetchMenuData();

  return {
    topMenuItems: menuData?.topMenu || [],
    footerMenuItems: menuData?.footerMenu || []
  };
}

export default LayoutWithMenu;
