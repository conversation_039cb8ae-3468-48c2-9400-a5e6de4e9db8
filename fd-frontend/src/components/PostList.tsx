'use client';

import { useQuery } from '@apollo/client';
import { GET_LATEST_POSTS } from '../lib/graphql/queries';
import Link from 'next/link';
import { useRoutePrefixes } from '@/hooks';
import { buildPostUrl } from '@/utils/url-builder';

export default function PostList() {
  const { loading, error, data } = useQuery(GET_LATEST_POSTS);
  const { prefixes } = useRoutePrefixes();

  if (loading) return <p>加载中...</p>;
  if (error) return <p>加载出错: {error.message}</p>;

  const posts = data?.posts?.nodes || [];

  return (
    <div className="post-list">
      <h2 className="text-2xl font-bold mb-4">最新文章</h2>
      {posts.length === 0 ? (
        <p>没有找到文章</p>
      ) : (
        <ul className="space-y-4">
          {posts.map((post: any) => (
            <li key={post.id} className="border-b pb-4">
              <Link href={buildPostUrl(post.shortUuid, post.slug, prefixes)}>
                <h3 className="text-xl font-semibold hover:text-blue-600">
                  {post.title}
                </h3>
              </Link>
              <div className="text-sm text-gray-500 mt-1">
                {new Date(post.date).toLocaleDateString('zh-CN')}
              </div>
              <div 
                className="mt-2"
                dangerouslySetInnerHTML={{ __html: post.excerpt || '' }}
              />
            </li>
          ))}
        </ul>
      )}
    </div>
  );
} 