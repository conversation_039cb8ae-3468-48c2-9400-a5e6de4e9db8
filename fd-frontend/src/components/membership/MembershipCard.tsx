"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useMutation } from '@apollo/client';
import { MemberLevel } from '@/types/user-types';
import { CREATE_MEMBER_PAYMENT_ORDER } from '@/lib/graphql/mutations';
import Button from '../ui/Button';

interface MembershipCardProps {
  level: MemberLevel;
  isCurrent: boolean;
  canUpgrade: boolean;
  currentLevel?: MemberLevel | null;
  onUpgradeSuccess?: () => void; // 新增：升级成功回调
  returnUrl?: string | null; // 新增：返回URL
}

const MembershipCard: React.FC<MembershipCardProps> = ({ level, isCurrent, canUpgrade, currentLevel, onUpgradeSuccess, returnUrl }) => {
  const router = useRouter();
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [upgradeMessage, setUpgradeMessage] = useState<string | null>(null);

  // GraphQL mutation for member upgrade
  const [createMemberPaymentOrder] = useMutation(CREATE_MEMBER_PAYMENT_ORDER, {
    onCompleted: (data) => {
      setIsUpgrading(false);
      if (data.createMemberPaymentOrder.status) {
        if (data.createMemberPaymentOrder.paymentUrl) {
          // 需要支付，跳转到支付页面
          window.location.href = data.createMemberPaymentOrder.paymentUrl;
        } else {
          // 免费升级成功，跳转到成功页面
          onUpgradeSuccess?.(); // 调用成功回调
          const successUrl = returnUrl
            ? `/membership/payment-success?returnUrl=${encodeURIComponent(returnUrl)}`
            : '/membership/payment-success';
          router.push(successUrl);
        }
      } else {
        setUpgradeMessage(data.createMemberPaymentOrder.message || '升级失败');
      }
    },
    onError: (error) => {
      setIsUpgrading(false);
      setUpgradeMessage(error.message || '升级过程中发生错误');
    }
  });

  // 获取等级层次描述（优先使用后端返回的tier，否则根据priority计算）
  const getLevelTier = (level: MemberLevel) => {
    if (level.tier) {
      return level.tier;
    }

    const priority = level.priority || 0;
    if (priority >= 100) return '钻石级';
    if (priority >= 80) return '黄金级';
    if (priority >= 50) return '白银级';
    if (priority >= 20) return '青铜级';
    return '基础级';
  };

  // 处理购买/升级
  const handlePurchase = async () => {
    if (isUpgrading) return;

    const price = level.price || 0;

    if (price <= 0) {
      // 免费等级，直接调用GraphQL mutation
      setIsUpgrading(true);
      setUpgradeMessage(null);

      try {
        await createMemberPaymentOrder({
          variables: {
            levelId: level.id
          }
        });
      } catch (error) {
        // 错误处理已在onError回调中处理
      }
    } else {
      // 付费等级，跳转到支付中心页面
      const params = new URLSearchParams({
        title: `会员升级: ${level.name}`,
        amount: String(price),
        description: `升级到 ${level.name}`,
        product_type: 'member_level',
        product_id: String(level.id),
        metadata: JSON.stringify({
          from_level_id: currentLevel?.id || 'none',
          to_level_id: level.id,
          from_priority: currentLevel?.priority || 0,
          to_priority: level.priority || 0,
          returnUrl: returnUrl || null // 添加返回URL到metadata
        }),
      });

      router.push(`/checkout?${params.toString()}`);
    }
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden border-2 ${isCurrent ? 'border-primary-500' : 'border-transparent'}`}>
      <div className="px-6 py-4">
        {isCurrent && (
          <div className="mb-2 text-center">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-800 dark:text-primary-100">
              当前等级
            </span>
          </div>
        )}
        <h3 className="text-xl font-semibold text-center text-gray-900 dark:text-white">{level.name}</h3>

        {/* 等级层次显示 */}
        <div className="text-center mt-1">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
            {getLevelTier(level)} • 优先级 {level.priority || 0}
          </span>
        </div>
        {level.price !== undefined && level.price > 0 ? (
          <div className="mt-2 text-center text-2xl font-bold text-primary-600 dark:text-primary-400">
            ¥{level.price}
            <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
              {level.duration && level.duration > 0 ?
                ` / ${level.duration}${level.durationUnit === 'years' ? '年' :
                   level.durationUnit === 'months' ? '个月' : '天'}` :
                ' / 永久'}
            </span>
          </div>
        ) : (
          <div className="mt-2 text-center text-2xl font-bold text-primary-600 dark:text-primary-400">
            免费
            {level.duration && level.duration > 0 &&
              <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
                {` / ${level.duration}${level.durationUnit === 'years' ? '年' :
                   level.durationUnit === 'months' ? '个月' : '天'}`}
              </span>
            }
          </div>
        )}
      </div>

      <div className="px-6 py-4 bg-gray-50 dark:bg-gray-750">
        {level.description ? (
          <p className="text-gray-600 dark:text-gray-300">{level.description}</p>
        ) : (
          <p className="text-gray-500 dark:text-gray-400">暂无等级描述</p>
        )}
      </div>

      <div className="px-6 py-4">
        {/* 显示升级消息 */}
        {upgradeMessage && (
          <div className={`mb-4 p-3 rounded-lg text-sm text-center ${
            upgradeMessage.includes('成功')
              ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
              : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
          }`}>
            {upgradeMessage}
          </div>
        )}

        {!isCurrent && canUpgrade && level.price !== undefined && level.price > 0 ? (
          <Button
            variant="primary"
            className="w-full"
            onClick={handlePurchase}
            disabled={isUpgrading}
          >
            {isUpgrading ? '处理中...' : `升级到 ${level.name}`}
          </Button>
        ) : !isCurrent && canUpgrade ? (
          <Button
            variant="primary"
            className="w-full"
            onClick={handlePurchase}
            disabled={isUpgrading}
          >
            {isUpgrading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                升级中...
              </div>
            ) : (
              `免费升级到 ${level.name}`
            )}
          </Button>
        ) : isCurrent ? (
          <div className="text-center text-sm text-gray-500 dark:text-gray-400">
            您当前正在使用此等级
          </div>
        ) : !canUpgrade ? (
          <div className="text-center text-sm text-gray-500 dark:text-gray-400">
            {currentLevel && (currentLevel.priority || 0) > (level.priority || 0)
              ? `您已拥有更高等级 (${currentLevel.name})`
              : '无法升级到此等级'
            }
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default MembershipCard; 