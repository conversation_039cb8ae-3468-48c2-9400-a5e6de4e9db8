import React from 'react';
import { Block, getBlockAttributes } from '@/types/block';

// 导入高优先级区块组件
import ParagraphBlock from './ParagraphBlock';
import HeadingBlock from './HeadingBlock';
import ImageBlock from './ImageBlock';
import ListBlock from './ListBlock';
import QuoteBlock from './QuoteBlock';

// 导入中优先级区块组件
import ColumnsBlock from './ColumnsBlock';
import GroupBlock from './GroupBlock';
import ButtonsBlock, { ButtonBlock } from './ButtonsBlock';
import MediaTextBlock from './MediaTextBlock';
import SeparatorBlock from './SeparatorBlock';

interface BlockRendererProps {
  blocks: Block[];
}

/**
 * 区块渲染器
 * 根据区块类型渲染对应的组件
 */
const BlockRenderer: React.FC<BlockRendererProps> = ({ blocks = [] }) => {
  if (!blocks || blocks.length === 0) {
    return null;
  }

  return (
    <>
      {blocks.map((block, index) => {
        // 如果区块为null，则跳过渲染
        if (!block) return null;
        
        const { __typename, name, saveContent, dynamicContent } = block;
        const attributes = getBlockAttributes(block);
        
        // 根据区块类型渲染对应的组件
        switch (__typename) {
          // 高优先级区块 (P0)
          case 'CoreParagraphBlock':
            return <ParagraphBlock key={index} attributes={attributes} />;
          case 'CoreHeadingBlock':
            return <HeadingBlock key={index} attributes={attributes} />;
          case 'CoreImageBlock':
            return <ImageBlock key={index} attributes={attributes} />;
          case 'CoreListBlock':
            return <ListBlock key={index} attributes={attributes} />;
          case 'CoreQuoteBlock':
            return <QuoteBlock key={index} attributes={attributes} />;
          
          // 中优先级区块 (P1)
          case 'CoreColumnsBlock':
            return <ColumnsBlock key={index} attributes={attributes} innerBlocks={block.innerBlocks} />;
          case 'CoreGroupBlock':
            return <GroupBlock key={index} attributes={attributes} innerBlocks={block.innerBlocks} />;
          case 'CoreButtonsBlock':
            return <ButtonsBlock key={index} attributes={attributes} innerBlocks={block.innerBlocks} />;
          case 'CoreButtonBlock':
            return <ButtonBlock key={index} attributes={attributes} />;
          case 'CoreMediaTextBlock':
            return <MediaTextBlock key={index} attributes={attributes} innerBlocks={block.innerBlocks} />;
          case 'CoreSeparatorBlock':
            return <SeparatorBlock key={index} attributes={attributes} />;
            
          // 默认回退渲染：使用HTML内容
          default:
            // 没有对应组件时的混合渲染策略
            console.warn(`未实现组件的区块: ${__typename}`, name);
            
            // 优先使用动态内容，其次使用保存内容
            const htmlContent = dynamicContent || saveContent || '';
            return (
              <div 
                key={index}
                className="fallback-block my-4"
                dangerouslySetInnerHTML={{ __html: htmlContent }}
              />
            );
        }
      })}
    </>
  );
};

export default BlockRenderer; 