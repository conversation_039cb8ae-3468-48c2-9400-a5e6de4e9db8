import React from 'react';
import { Block } from '@/types/block';
import { BlockRenderer } from '.';

interface GroupBlockProps {
  attributes: {
    backgroundColor?: string;
    textColor?: string;
    gradient?: string;
    align?: string;
    tagName?: string;
  };
  innerBlocks?: Block[];
}

/**
 * 渲染Gutenberg的群组区块
 * 群组区块用于将多个区块组合在一起，可以应用统一的样式
 */
const GroupBlock: React.FC<GroupBlockProps> = ({ attributes, innerBlocks = [] }) => {
  const { 
    backgroundColor, 
    textColor, 
    gradient,
    align = 'none', 
    tagName = 'div' 
  } = attributes;
  
  // 构建容器样式
  const style: React.CSSProperties = {};
  
  if (backgroundColor) {
    style.backgroundColor = backgroundColor;
  }
  
  if (textColor) {
    style.color = textColor;
  }
  
  if (gradient) {
    // Gutenberg渐变可能需要特殊处理，这里简化实现
    style.backgroundImage = gradient;
  }
  
  // 根据对齐方式设置类名
  const getAlignmentClass = () => {
    switch (align) {
      case 'wide':
        return 'w-full md:w-[120%] md:-ml-[10%]';
      case 'full':
        return 'w-full md:w-screen md:max-w-none md:ml-[calc(-50vw_+_50%)]';
      case 'center':
        return 'mx-auto';
      default:
        return '';
    }
  };
  
  // 构建群组容器的类名
  const containerClassName = [
    'group-block',
    'p-4',
    'rounded',
    getAlignmentClass()
  ].filter(Boolean).join(' ');
  
  // 根据tagName动态渲染标签类型
  const TagName = tagName as keyof JSX.IntrinsicElements;
  
  return (
    <TagName className={containerClassName} style={style}>
      {innerBlocks && innerBlocks.length > 0 ? (
        <BlockRenderer blocks={innerBlocks} />
      ) : (
        <div className="p-4 text-center text-gray-500 border border-dashed border-gray-300 rounded">
          空群组
        </div>
      )}
    </TagName>
  );
};

export default GroupBlock; 