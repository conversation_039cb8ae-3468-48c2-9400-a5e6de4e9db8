import React from 'react';
import { Block, getBlockAttributes } from '@/types/block';
import { BlockRenderer } from '.';

interface ColumnsBlockProps {
  attributes: {
    verticalAlignment?: 'top' | 'center' | 'bottom';
    isStackedOnMobile?: boolean;
  };
  innerBlocks?: Block[];
}

/**
 * 渲染Gutenberg的多列布局区块
 */
const ColumnsBlock: React.FC<ColumnsBlockProps> = ({ attributes, innerBlocks = [] }) => {
  const { verticalAlignment = 'top', isStackedOnMobile = true } = attributes;
  
  // 设置垂直对齐方式的类名
  const getAlignmentClass = () => {
    switch (verticalAlignment) {
      case 'center':
        return 'items-center';
      case 'bottom':
        return 'items-end';
      default:
        return 'items-start';
    }
  };
  
  // 构建列容器的类名
  const containerClassName = [
    'columns-container',
    'grid',
    'gap-4',
    isStackedOnMobile ? 'grid-cols-1 md:grid-cols-12' : 'grid-cols-12',
    getAlignmentClass()
  ].filter(Boolean).join(' ');
  
  return (
    <div className={containerClassName}>
      {innerBlocks && innerBlocks.length > 0 ? (
        innerBlocks.map((block, index) => (
          <React.Fragment key={index}>
            {block.__typename === 'CoreColumnBlock' ? (
              <ColumnBlock 
                key={index} 
                attributes={getBlockAttributes(block)} 
                innerBlocks={block.innerBlocks}
              />
            ) : (
              // 如果不是列区块，则使用默认的区块渲染器
              <div className="col-span-12">
                <BlockRenderer blocks={[block]} />
              </div>
            )}
          </React.Fragment>
        ))
      ) : (
        <div className="col-span-12 p-4 text-center text-gray-500 border border-dashed border-gray-300 rounded">
          没有列内容
        </div>
      )}
    </div>
  );
};

interface ColumnBlockProps {
  attributes: {
    width?: string;
    verticalAlignment?: string;
  };
  innerBlocks?: Block[];
}

/**
 * 列区块组件
 */
export const ColumnBlock: React.FC<ColumnBlockProps> = ({ attributes, innerBlocks = [] }) => {
  const { width = '50%', verticalAlignment } = attributes;
  
  // 将百分比宽度转换为网格列数(最大12列)
  const getColumnSpan = () => {
    if (!width || width === '100%') return 'col-span-12';
    
    // 移除百分号并转换为数字
    const numericWidth = parseFloat(width.replace('%', ''));
    // 计算大约的列数 (四舍五入)
    const columnsSpan = Math.round((numericWidth / 100) * 12);
    // 确保范围在1-12之间
    const safeColumnsSpan = Math.max(1, Math.min(12, columnsSpan));
    
    return `col-span-12 md:col-span-${safeColumnsSpan}`;
  };
  
  // 获取垂直对齐类名
  const getVerticalAlignClass = () => {
    switch (verticalAlignment) {
      case 'center':
        return 'flex flex-col justify-center';
      case 'bottom':
        return 'flex flex-col justify-end';
      default:
        return '';
    }
  };
  
  // 构建列的类名
  const columnClassName = [
    'column-block',
    getColumnSpan(),
    getVerticalAlignClass()
  ].filter(Boolean).join(' ');
  
  return (
    <div className={columnClassName}>
      {innerBlocks && innerBlocks.length > 0 ? (
        <BlockRenderer blocks={innerBlocks} />
      ) : (
        <div className="p-4 text-center text-gray-500 border border-dashed border-gray-300 rounded">
          空列
        </div>
      )}
    </div>
  );
};

export default ColumnsBlock; 