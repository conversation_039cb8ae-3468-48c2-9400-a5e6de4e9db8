import React from 'react';

interface ListBlockProps {
  attributes: {
    values: string;
    ordered?: boolean;
    start?: number;
  };
}

const ListBlock: React.FC<ListBlockProps> = ({ attributes }) => {
  const { values, ordered = false, start } = attributes;
  
  if (ordered) {
    return (
      <ol 
        className="list-block list-decimal pl-5 my-4"
        start={start}
        dangerouslySetInnerHTML={{ __html: values }}
      />
    );
  }
  
  return (
    <ul 
      className="list-block list-disc pl-5 my-4"
      dangerouslySetInnerHTML={{ __html: values }}
    />
  );
};

export default ListBlock; 