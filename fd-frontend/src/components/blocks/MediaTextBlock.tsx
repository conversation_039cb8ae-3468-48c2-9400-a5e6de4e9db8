import React from 'react';
import { Block } from '@/types/block';
import { BlockRenderer } from '.';
import Image from 'next/image';

interface MediaTextBlockProps {
  attributes: {
    mediaUrl?: string;
    mediaAlt?: string;
    mediaId?: number;
    mediaType?: 'image' | 'video';
    mediaWidth?: number;
    mediaHeight?: number;
    align?: string;
    mediaPosition?: 'left' | 'right';
    verticalAlignment?: 'top' | 'center' | 'bottom';
    isStackedOnMobile?: boolean;
    backgroundColor?: string;
    textColor?: string;
    gradient?: string;
  };
  innerBlocks?: Block[];
}

/**
 * 渲染Gutenberg的媒体与文本区块
 * 该区块将媒体（图片或视频）与文本内容并排放置
 */
const MediaTextBlock: React.FC<MediaTextBlockProps> = ({ attributes, innerBlocks = [] }) => {
  const {
    mediaUrl,
    mediaAlt = '',
    mediaType = 'image',
    mediaWidth,
    mediaHeight,
    align = 'none',
    mediaPosition = 'left',
    verticalAlignment = 'top',
    isStackedOnMobile = true,
    backgroundColor,
    textColor,
    gradient
  } = attributes;
  
  // 构建容器样式
  const style: React.CSSProperties = {};
  
  if (backgroundColor) {
    style.backgroundColor = backgroundColor;
  }
  
  if (textColor) {
    style.color = textColor;
  }
  
  if (gradient) {
    style.backgroundImage = gradient;
  }
  
  // 根据对齐方式设置类名
  const getAlignmentClass = () => {
    switch (align) {
      case 'wide':
        return 'w-full md:w-[120%] md:-ml-[10%]';
      case 'full':
        return 'w-full md:w-screen md:max-w-none md:ml-[calc(-50vw_+_50%)]';
      default:
        return '';
    }
  };
  
  // 设置垂直对齐方式的类名
  const getVerticalAlignmentClass = () => {
    switch (verticalAlignment) {
      case 'center':
        return 'items-center';
      case 'bottom':
        return 'items-end';
      default:
        return 'items-start';
    }
  };
  
  // 构建媒体与文本区块容器的类名
  const containerClassName = [
    'media-text-block',
    'overflow-hidden',
    'rounded',
    getAlignmentClass(),
    isStackedOnMobile ? 'flex flex-col md:grid md:grid-cols-2 gap-4' : 'grid grid-cols-2 gap-4',
    getVerticalAlignmentClass()
  ].filter(Boolean).join(' ');
  
  // 渲染媒体部分
  const renderMedia = () => {
    if (!mediaUrl) {
      return (
        <div className="bg-gray-200 h-48 flex items-center justify-center text-gray-500">
          无媒体内容
        </div>
      );
    }
    
    if (mediaType === 'video') {
      return (
        <video 
          src={mediaUrl} 
          controls 
          className="w-full h-auto" 
        />
      );
    }
    
    // 图片媒体
    if (mediaWidth && mediaHeight) {
      return (
        <Image 
          src={mediaUrl} 
          alt={mediaAlt} 
          width={mediaWidth} 
          height={mediaHeight}
          className="w-full h-auto object-cover"
        />
      );
    }
    
    return (
      <img 
        src={mediaUrl} 
        alt={mediaAlt} 
        className="w-full h-auto object-cover" 
      />
    );
  };

  // 根据媒体位置确定内容顺序
  const isMediaLeft = mediaPosition === 'left';
  const mediaColumn = (
    <div className={`${isStackedOnMobile ? 'order-1' : isMediaLeft ? 'order-1' : 'order-2'}`}>
      {renderMedia()}
    </div>
  );
  
  const contentColumn = (
    <div className={`p-4 ${isStackedOnMobile ? 'order-2' : isMediaLeft ? 'order-2' : 'order-1'}`}>
      {innerBlocks && innerBlocks.length > 0 ? (
        <BlockRenderer blocks={innerBlocks} />
      ) : (
        <div className="p-4 text-center text-gray-500 border border-dashed border-gray-300 rounded">
          无内容
        </div>
      )}
    </div>
  );
  
  return (
    <div className={containerClassName} style={style}>
      {mediaColumn}
      {contentColumn}
    </div>
  );
};

export default MediaTextBlock; 