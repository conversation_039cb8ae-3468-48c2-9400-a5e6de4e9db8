import React from 'react';

interface QuoteBlockProps {
  attributes: {
    value: string;
    citation?: string;
    align?: string;
  };
}

const QuoteBlock: React.FC<QuoteBlockProps> = ({ attributes }) => {
  const { value, citation, align } = attributes;
  
  const className = [
    'quote-block border-l-4 border-gray-300 pl-4 py-2 my-4 italic',
    align ? `text-${align}` : ''
  ].filter(Boolean).join(' ');
  
  return (
    <blockquote className={className}>
      <div dangerouslySetInnerHTML={{ __html: value }} />
      
      {citation && (
        <cite className="block mt-2 text-sm text-gray-600 not-italic">
          — {citation}
        </cite>
      )}
    </blockquote>
  );
};

export default QuoteBlock; 