import React from 'react';
import { Block, getBlockAttributes } from '@/types/block';
import { BlockRenderer } from '.';

interface ButtonsBlockProps {
  attributes: {
    align?: 'left' | 'center' | 'right';
    orientation?: 'horizontal' | 'vertical';
    layout?: {
      justifyContent?: string;
    };
  };
  innerBlocks?: Block[];
}

/**
 * 渲染Gutenberg的按钮组区块
 */
const ButtonsBlock: React.FC<ButtonsBlockProps> = ({ attributes, innerBlocks = [] }) => {
  const { 
    align = 'left', 
    orientation = 'horizontal',
    layout = {}
  } = attributes;
  
  const { justifyContent } = layout;
  
  // 设置对齐方式的类名
  const getAlignmentClass = () => {
    switch (align) {
      case 'center':
        return 'justify-center';
      case 'right':
        return 'justify-end';
      default:
        return 'justify-start';
    }
  };
  
  // 设置布局方向类名
  const getOrientationClass = () => {
    return orientation === 'vertical' ? 'flex-col' : 'flex-row';
  };
  
  // 自定义内联对齐方式
  const getCustomJustify = () => {
    if (!justifyContent) return '';
    
    switch (justifyContent) {
      case 'center':
        return 'justify-center';
      case 'right':
        return 'justify-end';
      case 'space-between':
        return 'justify-between';
      default:
        return '';
    }
  };
  
  // 构建按钮组容器的类名
  const containerClassName = [
    'buttons-block',
    'flex',
    'flex-wrap',
    'gap-2',
    getOrientationClass(),
    getAlignmentClass(),
    getCustomJustify()
  ].filter(Boolean).join(' ');
  
  return (
    <div className={containerClassName}>
      {innerBlocks && innerBlocks.length > 0 ? (
        innerBlocks.map((block, index) => {
          if (block.__typename === 'CoreButtonBlock') {
            return (
              <ButtonBlock 
                key={index} 
                attributes={getBlockAttributes(block)} 
              />
            );
          }
          return <BlockRenderer key={index} blocks={[block]} />;
        })
      ) : (
        <div className="p-4 text-center text-gray-500 border border-dashed border-gray-300 rounded">
          没有按钮
        </div>
      )}
    </div>
  );
};

interface ButtonBlockProps {
  attributes: {
    url?: string;
    text: string;
    backgroundColor?: string;
    textColor?: string;
    gradient?: string;
    width?: string;
    borderRadius?: number;
    style?: {
      border?: {
        width?: string;
        color?: string;
      }
    };
    className?: string;
  };
}

/**
 * 渲染单个按钮区块
 */
export const ButtonBlock: React.FC<ButtonBlockProps> = ({ attributes }) => {
  const { 
    url = '#', 
    text, 
    backgroundColor, 
    textColor,
    gradient,
    width,
    borderRadius,
    style = {},
    className = ''
  } = attributes;
  
  // 构建按钮样式
  const buttonStyle: React.CSSProperties = {};
  
  if (backgroundColor) {
    buttonStyle.backgroundColor = backgroundColor;
  }
  
  if (textColor) {
    buttonStyle.color = textColor;
  }
  
  if (gradient) {
    buttonStyle.backgroundImage = gradient;
  }
  
  if (width) {
    buttonStyle.width = width;
  }
  
  if (borderRadius) {
    buttonStyle.borderRadius = `${borderRadius}px`;
  }
  
  // 处理边框样式
  if (style.border) {
    if (style.border.width) {
      buttonStyle.borderWidth = style.border.width;
    }
    if (style.border.color) {
      buttonStyle.borderColor = style.border.color;
    }
  }
  
  // 处理Gutenberg的自定义类
  const getCustomClasses = () => {
    // 常见的Gutenberg按钮类名处理
    if (className.includes('is-style-outline')) {
      return 'bg-transparent border border-current';
    }
    
    if (className.includes('is-style-fill')) {
      return 'border-0';
    }
    
    return '';
  };
  
  // 构建按钮类名
  const buttonClassName = [
    'button-block',
    'px-4',
    'py-2',
    'rounded',
    'inline-block',
    'text-center',
    'transition-all',
    'duration-200',
    'hover:opacity-90',
    getCustomClasses()
  ].filter(Boolean).join(' ');
  
  return (
    <a 
      href={url} 
      className={buttonClassName} 
      style={buttonStyle}
    >
      {text || '按钮'}
    </a>
  );
};

export default ButtonsBlock; 