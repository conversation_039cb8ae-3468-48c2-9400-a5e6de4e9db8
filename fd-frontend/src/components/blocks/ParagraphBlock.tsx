import React from 'react';

interface ParagraphBlockProps {
  attributes: {
    content: string;
    align?: string;
    dropCap?: boolean;
    backgroundColor?: string;
    textColor?: string;
  };
}

const ParagraphBlock: React.FC<ParagraphBlockProps> = ({ attributes }) => {
  const { content, align, dropCap, backgroundColor, textColor } = attributes;
  
  const className = [
    'paragraph-block',
    align ? `text-${align}` : '',
    dropCap ? 'first-letter:text-4xl first-letter:font-bold first-letter:mr-1 first-letter:float-left' : ''
  ].filter(Boolean).join(' ');
  
  const style: React.CSSProperties = {};
  
  if (backgroundColor) {
    style.backgroundColor = backgroundColor;
  }
  
  if (textColor) {
    style.color = textColor;
  }
  
  return (
    <div 
      className={className}
      style={style}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

export default ParagraphBlock; 