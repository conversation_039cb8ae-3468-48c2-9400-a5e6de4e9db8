import React from 'react';

interface SeparatorBlockProps {
  attributes: {
    backgroundColor?: string;
    className?: string;
    color?: string;
    align?: string;
    style?: {
      color?: {
        background?: string;
      };
    };
  };
}

/**
 * 渲染Gutenberg的分隔线区块
 */
const SeparatorBlock: React.FC<SeparatorBlockProps> = ({ attributes }) => {
  const { 
    backgroundColor, 
    className = '', 
    color, 
    align = 'none',
    style = {}
  } = attributes;
  
  // 构建分隔线样式
  const separatorStyle: React.CSSProperties = {};
  
  // Gutenberg 5.8+使用style.color.background, 而旧版本使用backgroundColor或color
  if (style.color?.background) {
    separatorStyle.backgroundColor = style.color.background;
  } else if (backgroundColor) {
    separatorStyle.backgroundColor = backgroundColor;
  } else if (color) {
    separatorStyle.backgroundColor = color;
  }
  
  // 根据className判断分隔线类型
  const isFull = className.includes('is-style-wide') || align === 'full' || align === 'wide';
  const isDots = className.includes('is-style-dots');
  
  // 根据对齐方式设置类名
  const getAlignmentClass = () => {
    switch (align) {
      case 'left':
        return 'mr-auto';
      case 'right':
        return 'ml-auto';
      case 'center':
        return 'mx-auto';
      case 'wide':
        return 'w-full md:w-[120%] md:-ml-[10%]';
      case 'full':
        return 'w-full';
      default:
        return 'w-full';
    }
  };
  
  if (isDots) {
    return (
      <div 
        className={`separator-block text-center my-4 ${getAlignmentClass()}`}
        style={separatorStyle}
      >
        <div className="inline-block text-4xl tracking-widest">
          ···
        </div>
      </div>
    );
  }
  
  // 构建分隔线容器的类名
  const containerClassName = [
    'separator-block',
    'my-8',
    getAlignmentClass(),
    isFull ? 'h-px' : 'h-0.5 w-1/4',
    className
  ].filter(Boolean).join(' ');
  
  return (
    <hr 
      className={containerClassName}
      style={separatorStyle}
    />
  );
};

export default SeparatorBlock; 