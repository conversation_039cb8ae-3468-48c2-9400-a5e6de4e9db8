import React from 'react';
import Image from 'next/image';

interface ImageBlockProps {
  attributes: {
    url: string;
    alt: string;
    caption?: string;
    width?: number;
    height?: number;
    sizeSlug?: string;
    align?: string;
  };
}

const ImageBlock: React.FC<ImageBlockProps> = ({ attributes }) => {
  const { url, alt, caption, width, height, sizeSlug, align = 'none' } = attributes;
  
  const getContainerClass = () => {
    let className = 'image-block';
    
    switch (align) {
      case 'left': 
        className += ' float-left mr-4'; 
        break;
      case 'right': 
        className += ' float-right ml-4'; 
        break;
      case 'center': 
        className += ' mx-auto'; 
        break;
      case 'wide':
        className += ' w-full md:w-[120%] md:-ml-[10%]';
        break;
      case 'full':
        className += ' w-full md:w-screen md:max-w-none md:ml-[calc(-50vw_+_50%)]';
        break;
    }
    
    return className;
  };
  
  return (
    <figure className={getContainerClass()}>
      {width && height ? (
        <Image 
          src={url}
          alt={alt || ''}
          width={width}
          height={height}
          className="max-w-full h-auto"
        />
      ) : (
        <img 
          src={url}
          alt={alt || ''}
          className="max-w-full h-auto"
        />
      )}
      
      {caption && (
        <figcaption 
          className="text-sm text-gray-600 mt-2 text-center"
          dangerouslySetInnerHTML={{ __html: caption }}
        />
      )}
    </figure>
  );
};

export default ImageBlock; 