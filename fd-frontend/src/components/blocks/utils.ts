/**
 * Gutenberg区块辅助函数集合
 */
import { CSSProperties } from 'react';

/**
 * 将HTML内容中的图片进行响应式处理
 * @param html HTML内容
 * @returns 处理后的HTML内容
 */
export function processResponsiveImages(html: string): string {
  if (!html) return '';
  
  // 为图片添加响应式类
  return html.replace(/<img(.*?)>/g, '<img$1 class="w-full h-auto" />');
}

/**
 * 处理区块的通用样式属性
 * @param attributes 区块属性
 * @returns 样式对象
 */
export function getCommonStyles(attributes: any): CSSProperties {
  const style: CSSProperties = {};
  
  if (attributes?.backgroundColor) {
    style.backgroundColor = attributes.backgroundColor;
  }
  
  if (attributes?.textColor) {
    style.color = attributes.textColor;
  }
  
  if (attributes?.gradient) {
    style.backgroundImage = attributes.gradient;
  }
  
  return style;
}

/**
 * 获取对齐方式CSS类名
 * @param align 对齐方式
 * @returns CSS类名
 */
export function getAlignmentClass(align?: string): string {
  switch (align) {
    case 'left':
      return 'text-left';
    case 'center':
      return 'text-center';
    case 'right':
      return 'text-right';
    case 'wide':
      return 'w-full md:w-[120%] md:-ml-[10%]';
    case 'full':
      return 'w-full md:w-screen md:max-w-none md:ml-[calc(-50vw_+_50%)]';
    default:
      return '';
  }
} 