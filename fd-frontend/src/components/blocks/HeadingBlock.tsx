import React from 'react';

interface HeadingBlockProps {
  attributes: {
    content: string;
    level: number;
    align?: string;
    textColor?: string;
  };
}

const HeadingBlock: React.FC<HeadingBlockProps> = ({ attributes }) => {
  const { content, level = 2, align, textColor } = attributes;
  
  const className = [
    'heading-block',
    align ? `text-${align}` : ''
  ].filter(Boolean).join(' ');
  
  const style: React.CSSProperties = {};
  
  if (textColor) {
    style.color = textColor;
  }
  
  // 动态生成标题标签
  const Tag = `h${level}` as keyof JSX.IntrinsicElements;
  
  return (
    <Tag
      className={className}
      style={style}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

export default HeadingBlock; 