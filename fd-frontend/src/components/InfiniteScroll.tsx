'use client';
import React, { useState, useRef, useEffect } from 'react';

interface InfiniteScrollProps {
  // 是否还有更多数据
  hasMore: boolean;
  // 加载状态
  loading: boolean;
  // 加载更多数据的函数
  onLoadMore: () => void;
  // 子元素
  children: React.ReactNode;
  // 加载中显示的内容
  loadingComponent?: React.ReactNode;
  // 加载完成显示的内容
  endComponent?: React.ReactNode;
  // 没有更多内容时的文本
  endMessage?: string;
  // 观察器配置
  threshold?: number;
  // 结果总数（可选）
  totalCount?: number;
}

/**
 * 无限滚动加载组件
 * 用于实现下拉加载更多功能
 */
const InfiniteScroll: React.FC<InfiniteScrollProps> = ({
  hasMore,
  loading,
  onLoadMore,
  children,
  loadingComponent = <p>Loading...</p>,
  endComponent,
  endMessage = '已加载全部内容',
  threshold = 250,
  totalCount
}) => {
  const triggerRef = useRef<HTMLDivElement>(null);
  const [isIntersecting, setIntersecting] = useState(false);

  // 效果1: 设置和管理IntersectionObserver
  // 它的唯一职责是更新isIntersecting状态
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        setIntersecting(entries[0].isIntersecting);
      },
      { rootMargin: `${threshold}px` }
    );

    const element = triggerRef.current;
    if (element) {
      observer.observe(element);
    }

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, [threshold]);

  // 效果2: 根据状态变化决定是否加载
  // 它的唯一职责是决策
  useEffect(() => {
    if (isIntersecting && hasMore && !loading) {
      onLoadMore();
    }
  }, [isIntersecting, hasMore, loading, onLoadMore]);

  return (
    <div className="infinite-scroll-container">
      {children}
      
      <div ref={triggerRef} className="py-4 text-center">
        {loading && (loadingComponent || (
          <div className="flex justify-center">
            <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        ))}
        
        {!loading && hasMore && (
          <p className="text-gray-500">向下滚动加载更多</p>
        )}
        
        {!loading && !hasMore && (endComponent || (
          <p className="text-gray-500">
            {totalCount ? `已加载全部 ${totalCount} 条内容` : endMessage}
          </p>
        ))}
      </div>
    </div>
  );
};

export default InfiniteScroll; 