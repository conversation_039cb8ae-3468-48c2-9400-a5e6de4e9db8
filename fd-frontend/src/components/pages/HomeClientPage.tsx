'use client';

import React from 'react';
import MainLayout from '../layouts/MainLayout';
import { Card, CardContent } from '../ui/Card';
import { useVITheme } from '../../contexts/VIThemeContext';
import { usePosts } from '../../hooks/usePosts';
import { buildPostUrl } from '../../utils/url-builder';
import Link from 'next/link';
import Loading from '../ui/Loading';
import { ErrorMessage } from '../ui/ErrorMessage';
import InfiniteScroll from '../InfiniteScroll';
import ArticleListSkeleton from '../ArticleListSkeleton';
import { Post } from '../../types/post';
import { RoutePrefixes } from '../../types/routes';

// 定义分类类型
interface Category {
  id: string;
  name: string;
  slug: string;
}

interface HomeClientPageProps {
  routePrefixes: RoutePrefixes;
}

/**
 * 首页客户端组件
 * 处理所有客户端交互逻辑，包括文章列表加载和无限滚动
 */
export default function HomeClientPage({ routePrefixes }: HomeClientPageProps) {
  const { getLogoUrl, getColor, settings, loading: themeLoading } = useVITheme();
  const { posts, pageInfo, loading: postsLoading, error, loadMore } = usePosts({ first: 10 });
  
  // 获取主色
  const primaryColor = getColor('primary');
  const logoUrl = getLogoUrl();
  
  // 处理加载更多
  const handleLoadMore = () => {
    if (pageInfo?.endCursor && !postsLoading) {
      loadMore(pageInfo.endCursor);
    }
  };
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Logo 展示 */}
        {!themeLoading && logoUrl && (
          <div className="flex justify-center mb-8">
            <img 
              src={logoUrl}
              alt="网站Logo"
              className="h-20 object-contain"
            />
          </div>
        )}
        
        <h1 className="text-3xl font-bold mb-8 text-center font-heading">最新文章</h1>
        
        {/* 初始加载状态 */}
        {postsLoading && posts.length === 0 && (
          <Loading />
        )}
        
        {/* 错误状态 */}
        {error && (
          <ErrorMessage 
            title="获取文章失败" 
            message={error.message} 
          />
        )}
        
        {/* 文章列表 */}
        {(!postsLoading || posts.length > 0) && !error && (
          <>
            {posts.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">暂无文章</p>
              </div>
            ) : (
              <InfiniteScroll
                hasMore={!!pageInfo?.hasNextPage}
                loading={postsLoading}
                onLoadMore={handleLoadMore}
                loadingComponent={<ArticleListSkeleton mode="grid" count={3} columns={3} />}
              >
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-10">
                  {posts.map((post: Post) => (
                    <Card key={post.id} hover>
                      {post.shortUuid ? (
                        <Link href={buildPostUrl(post.shortUuid, post.slug, routePrefixes)}>
                          <div className="h-48 overflow-hidden">
                            {post.featuredImage ? (
                              <img 
                                src={post.featuredImage.node.sourceUrl} 
                                alt={post.featuredImage.node.altText || post.title}
                                className="w-full h-full object-cover transition-transform hover:scale-105"
                              />
                            ) : (
                              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                <span className="text-gray-400">无图片</span>
                              </div>
                            )}
                          </div>
                          <CardContent>
                            <h2 className="text-xl font-semibold mb-2 line-clamp-2 font-heading">{post.title}</h2>
                            <p className="text-sm text-gray-500 mb-2">{new Date(post.date).toLocaleDateString('zh-CN')}</p>
                            <div className="text-gray-600 line-clamp-3" dangerouslySetInnerHTML={{ __html: post.excerpt || '' }} />
                            
                            {post.categories?.nodes && post.categories.nodes.length > 0 && (
                              <div className="mt-4 flex flex-wrap gap-2">
                                {post.categories.nodes.map((category: Category) => (
                                  <span key={category.id} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                                    {category.name}
                                  </span>
                                ))}
                              </div>
                            )}
                          </CardContent>
                        </Link>
                      ) : (
                        <div>
                          <div className="h-48 overflow-hidden">
                            {post.featuredImage ? (
                              <img 
                                src={post.featuredImage.node.sourceUrl} 
                                alt={post.featuredImage.node.altText || post.title}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                <span className="text-gray-400">无图片</span>
                              </div>
                            )}
                          </div>
                          <CardContent>
                            <h2 className="text-xl font-semibold mb-2 line-clamp-2 font-heading">{post.title}</h2>
                            <p className="text-sm text-gray-500 mb-2">{new Date(post.date).toLocaleDateString('zh-CN')}</p>
                            <div className="text-gray-600 line-clamp-3" dangerouslySetInnerHTML={{ __html: post.excerpt || '' }} />
                            
                            {post.categories?.nodes && post.categories.nodes.length > 0 && (
                              <div className="mt-4 flex flex-wrap gap-2">
                                {post.categories.nodes.map((category: Category) => (
                                  <span key={category.id} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                                    {category.name}
                                  </span>
                                ))}
                              </div>
                            )}
                          </CardContent>
                        </div>
                      )}
                    </Card>
                  ))}
                </div>
              </InfiniteScroll>
            )}
          </>
        )}
      </div>
    </MainLayout>
  );
}
