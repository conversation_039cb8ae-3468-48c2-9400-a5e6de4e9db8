'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useRoutePrefixes } from '@/hooks';
import MainLayout from '@/components/layouts/MainLayout';
import ArticleListView, { Article } from '@/components/ArticleListView';
import ViewModeSwitcher, { ViewMode, ColumnCount } from '@/components/ViewModeSwitcher';
import InfiniteScroll from '@/components/InfiniteScroll';
import ArticleListSkeleton from '@/components/ArticleListSkeleton';
import TaxonomyBanner from '@/components/ui/TaxonomyBanner';

// 页面信息类型
interface PageInfo {
  hasNextPage: boolean;
  endCursor: string;
}

// 分类法类型
interface Taxonomy {
  id: string;
  name: string;
  label: string;
  description?: string;
  hierarchical: boolean;
  restBase: string;
}

// 术语类型
interface Term {
  __typename: string;
  id: string;
  databaseId: number;
  name: string;
  slug: string;
  description?: string;
  uri: string;
  aiSeoTitle?: string;
  aiSeoDescription?: string;
  aiSeoJsonLd?: string;
  bannerImageUrl?: string;
  bannerImage?: {
    sourceUrl: string;
    altText?: string;
    mediaDetails?: {
      width: number;
      height: number;
    };
  };
  count?: number;
  children?: {
    nodes: any[];
  };
}

// 组件属性类型
interface TaxonomyTermClientPageProps {
  initialTaxonomy: Taxonomy;
  initialTerm: Term;
  initialPosts: Article[];
  initialPageInfo?: PageInfo | null;
}

export default function TaxonomyTermClientPage({
  initialTaxonomy: taxonomy,
  initialTerm: term,
  initialPosts,
  initialPageInfo,
}: TaxonomyTermClientPageProps) {
  const { prefixes } = useRoutePrefixes();

  // 调试信息
  console.log('[TaxonomyTermClientPage] Component initialized:', {
    taxonomyName: taxonomy?.name,
    termName: term?.name,
    termCount: term?.count,
    initialPostsCount: initialPosts.length,
    initialPageInfo
  });

  // 用于合并首屏文章和后续加载的文章
  const [allPosts, setAllPosts] = useState<Article[]>(initialPosts);

  // 当前分页信息状态
  const [currentPageInfo, setCurrentPageInfo] = useState<PageInfo | null>(initialPageInfo || null);

  // 当服务器端获取的初始数据变化时（例如通过router.refresh()），同步更新客户端状态
  useEffect(() => {
    // 使用新的 initialPosts 重置 allPosts 状态
    setAllPosts(initialPosts);
    // 同时，重置分页信息
    setCurrentPageInfo(initialPageInfo || null);
    // 重置加载和错误状态
    setLoadingMore(false);
    setLoadError(null);

    console.log('[TaxonomyTermClientPage] Props updated, state has been reset.', {
      newPostsCount: initialPosts.length,
      newPageInfo: initialPageInfo
    });
  }, [initialPosts, initialPageInfo]);

  // 加载更多状态
  const [loadingMore, setLoadingMore] = useState(false);

  // 错误状态
  const [loadError, setLoadError] = useState<Error | null>(null);

  // 视图模式状态
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [columns, setColumns] = useState<ColumnCount>(3);

  // 初始化时从localStorage获取视图偏好
  useEffect(() => {
    try {
      const savedMode = localStorage.getItem('fd_view_mode') as ViewMode;
      const savedColumns = parseInt(localStorage.getItem('fd_columns_count') || '3') as ColumnCount;
      
      if (savedMode) {
        setViewMode(savedMode);
      }
      
      if (savedColumns) {
        setColumns(savedColumns);
      }
    } catch (e) {
      console.error('无法读取视图偏好设置:', e);
    }
  }, []);

  // 视图模式更改处理函数
  const handleViewModeChange = (mode: ViewMode, cols?: ColumnCount) => {
    setViewMode(mode);
    if (cols) {
      setColumns(cols);
    }
  };

  // 手动加载更多数据的函数 - 使用TaxQuery
  const loadMorePosts = useCallback(async (afterCursor: string): Promise<{
    nodes: Article[];
    pageInfo: PageInfo;
  } | null> => {
    // 首先获取分页设置
    const settingsQuery = `
      query GetPostsPerPageSetting {
        postsPerPageSetting
      }
    `;

    const settingsResponse = await fetch(process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: settingsQuery }),
    });

    const settingsResult = await settingsResponse.json();
    const postsPerPage = settingsResult.data?.postsPerPageSetting || 12;

    console.log('[TaxonomyTermClientPage] loadMorePosts using postsPerPage:', postsPerPage);

    // 根据术语类型构建不同的查询
    const taxonomyEnum = taxonomy.name.toUpperCase();
    const isCategory = term.__typename === 'Category';
    const isTag = term.__typename === 'Tag';

    let query = '';
    let variables: any = {
      first: postsPerPage,
      after: afterCursor,
    };

    if (isCategory) {
      // 对于Category，使用原生的categoryId查询
      query = `
        query GetCategoryPosts($categoryId: Int!, $first: Int, $after: String) {
          posts(first: $first, after: $after, where: { categoryId: $categoryId }) {
            pageInfo {
              hasNextPage
              endCursor
            }
            nodes {
              id
              title
              date
              slug
              shortUuid
              excerpt
              __typename
              featuredImage {
                node {
                  sourceUrl
                  altText
                }
              }
              author {
                node {
                  name
                  slug
                  avatar {
                    url
                  }
                }
              }
              categories {
                nodes {
                  id
                  name
                  slug
                }
              }
            }
          }
        }
      `;
      variables.categoryId = term.databaseId;
    } else if (isTag) {
      // 对于Tag，使用原生的tagId查询
      query = `
        query GetTagPosts($tagId: String!, $first: Int, $after: String) {
          posts(first: $first, after: $after, where: { tagId: $tagId }) {
            pageInfo {
              hasNextPage
              endCursor
            }
            nodes {
              id
              title
              date
              slug
              shortUuid
              excerpt
              __typename
              featuredImage {
                node {
                  sourceUrl
                  altText
                }
              }
              author {
                node {
                  name
                  slug
                  avatar {
                    url
                  }
                }
              }
              categories {
                nodes {
                  id
                  name
                  slug
                }
              }
            }
          }
        }
      `;
      variables.tagId = term.databaseId.toString();
    } else {
      // 对于其他自定义分类法，使用TaxQuery
      query = `
        query GetPostsByTaxQuerySlug($taxonomy: TaxonomyEnum!, $slugs: [String!], $first: Int, $after: String) {
          posts(
            first: $first,
            after: $after,
            where: {
              taxQuery: {
                relation: AND,
                taxArray: [
                  {
                    taxonomy: $taxonomy,
                    operator: IN,
                    terms: $slugs,
                    field: SLUG
                  }
                ]
              }
            }
          ) {
            pageInfo {
              hasNextPage
              endCursor
            }
            nodes {
              id
              title
              date
              slug
              shortUuid
              excerpt
              __typename
              featuredImage {
                node {
                  sourceUrl
                  altText
                }
              }
              author {
                node {
                  name
                  slug
                  avatar {
                    url
                  }
                }
              }
              categories {
                nodes {
                  id
                  name
                  slug
                }
              }
            }
          }
        }
      `;
      variables.taxonomy = taxonomyEnum;
      variables.slugs = [term.slug];
    }

    const response = await fetch(process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query,
        variables,
      }),
    });

    const result = await response.json();
    if (result.errors) {
      throw new Error(result.errors[0]?.message || 'GraphQL query failed');
    }

    return result.data?.posts;
  }, [taxonomy.name, term.__typename, term.databaseId, term.slug]);

  // 加载更多文章
  const handleLoadMore = useCallback(async () => {
    console.log('[TaxonomyTermClientPage] handleLoadMore called:', {
      currentPageInfo,
      hasNextPage: currentPageInfo?.hasNextPage,
      endCursor: currentPageInfo?.endCursor,
      loadingMore,
      allPostsCount: allPosts.length,
      termCount: term?.count
    });
    
    if (!currentPageInfo?.hasNextPage || loadingMore) {
      console.log('[TaxonomyTermClientPage] handleLoadMore early return:', {
        hasNextPage: currentPageInfo?.hasNextPage,
        loadingMore
      });
      return;
    }

    try {
      setLoadingMore(true);
      setLoadError(null);
      
      console.log('[TaxonomyTermClientPage] loading more posts with cursor:', currentPageInfo.endCursor);
      const result = await loadMorePosts(currentPageInfo.endCursor);
      
      if (result) {
        console.log('[TaxonomyTermClientPage] loaded more posts:', {
          newPostsCount: result.nodes.length,
          newPageInfo: result.pageInfo
        });
        
        // 合并新文章到现有列表
        setAllPosts((prev) => {
          const map = new Map<string, Article>();
          prev.forEach((p) => map.set(p.id, p));
          result.nodes.forEach((p: Article) => map.set(p.id, p));
          return Array.from(map.values());
        });
        
        // 更新分页信息
        setCurrentPageInfo(result.pageInfo);
      }
    } catch (error) {
      console.error('加载更多文章出错:', error);
      setLoadError(error as Error);
    } finally {
      setLoadingMore(false);
    }
  }, [currentPageInfo, loadingMore, allPosts.length, term?.count, loadMorePosts]);

  // 错误状态
  if (loadError) {
    return (
      <MainLayout>
        <div className="container mx-auto py-8 px-4">
          <h1 className="text-2xl font-bold mb-6">条目详情</h1>
          <div className="bg-red-50 text-red-500 p-4 rounded-lg">
            <h2 className="text-lg font-medium mb-2">加载出错</h2>
            <p>{loadError.message}</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  const hasMore = currentPageInfo?.hasNextPage ?? false;

  return (
    <MainLayout>
      {/* Hero Banner 区域 */}
      <TaxonomyBanner
        title={term?.name || ''}
        description={term?.description}
        count={term?.count}
        bannerImageUrl={term?.bannerImageUrl}
        bannerImage={term?.bannerImage}
        breadcrumbs={[
          { label: '首页', href: '/' },
          { label: taxonomy.label, href: `/taxonomy/${taxonomy.name}` },
          { label: term?.name || '', href: '#' }
        ]}
        gradientColors="from-green-600 via-teal-600 to-cyan-600"
        icon={
          <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
          </svg>
        }
      />

      {/* 主要内容区域 */}
      <div className="container mx-auto py-8 px-4">

        {/* 视图切换器 */}
        <div className="mb-6 flex justify-end">
          <ViewModeSwitcher
            currentMode={viewMode}
            columns={columns}
            onChange={handleViewModeChange}
          />
        </div>

        {/* 文章列表 */}
        {allPosts.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">该条目下暂无文章</p>
          </div>
        ) : (
          <InfiniteScroll
            hasMore={hasMore}
            loading={loadingMore}
            onLoadMore={handleLoadMore}
            totalCount={term.count}
            loadingComponent={<ArticleListSkeleton mode={viewMode} count={3} columns={columns} />}
          >
            <ArticleListView
              articles={allPosts}
              mode={viewMode}
              columns={columns}
              showFeaturedImage={true}
              showExcerpt={true}
              showDate={true}
              showAuthor={true}
              showCategory={true}
              showReadMore={true}
              routePrefixes={prefixes}
            />
          </InfiniteScroll>
        )}

        {/* 返回链接 */}
        <div className="mt-8">
          <Link 
            href={`/taxonomy/${taxonomy.name}`}
            className="text-blue-600 hover:text-blue-800 flex items-center"
          >
            ← 返回{taxonomy.label}索引
          </Link>
        </div>
      </div>
    </MainLayout>
  );
}
