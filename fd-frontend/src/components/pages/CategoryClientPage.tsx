'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useRoutePrefixes } from '@/hooks';
import MainLayout from '@/components/layouts/MainLayout';
import ArticleListView, { Article } from '@/components/ArticleListView';
import ViewModeSwitcher, { ViewMode, ColumnCount } from '@/components/ViewModeSwitcher';
import InfiniteScroll from '@/components/InfiniteScroll';
import ArticleListSkeleton from '@/components/ArticleListSkeleton';
import TaxonomyBanner from '@/components/ui/TaxonomyBanner';
import Script from 'next/script';
import { Category } from '@/types/post';

interface PageInfo {
  hasNextPage: boolean;
  endCursor: string;
}

interface CategoryClientPageProps {
  initialCategory: Category;
  initialPosts: Article[];
  initialPageInfo?: PageInfo | null;
}

export default function CategoryClientPage({
  initialCategory: category,
  initialPosts,
  initialPageInfo,
}: CategoryClientPageProps) {
  const { prefixes } = useRoutePrefixes();

  // 调试信息
  console.log('[CategoryClientPage] Component initialized:', {
    categoryName: category?.name,
    categoryCount: category?.count,
    initialPostsCount: initialPosts.length,
    initialPageInfo
  });

  // 视图模式状态
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [columns, setColumns] = useState<ColumnCount>(3);

  // 用于合并首屏文章和后续加载的文章
  const [allPosts, setAllPosts] = useState<Article[]>(initialPosts);

  // 当前分页信息状态
  const [currentPageInfo, setCurrentPageInfo] = useState<PageInfo | null>(initialPageInfo || null);

  // 当服务器端获取的初始数据变化时（例如通过router.refresh()），同步更新客户端状态
  useEffect(() => {
    // 使用新的 initialPosts 重置 allPosts 状态
    setAllPosts(initialPosts);
    // 同时，重置分页信息
    setCurrentPageInfo(initialPageInfo || null);
    // 重置加载和错误状态
    setLoadingMore(false);
    setLoadError(null);

    console.log('[CategoryClientPage] Props updated, state has been reset.', {
      newPostsCount: initialPosts.length,
      newPageInfo: initialPageInfo
    });
  }, [initialPosts, initialPageInfo]);


  // 加载更多状态
  const [loadingMore, setLoadingMore] = useState(false);

  // 错误状态
  const [loadError, setLoadError] = useState<Error | null>(null);

  // 手动加载更多数据的函数
  const loadMorePosts = useCallback(async (afterCursor: string): Promise<{
    nodes: Article[];
    pageInfo: PageInfo;
  } | null> => {
    // 首先获取分页设置
    const settingsQuery = `
      query GetPostsPerPageSetting {
        postsPerPageSetting
      }
    `;

    const settingsResponse = await fetch(process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: settingsQuery }),
    });

    const settingsResult = await settingsResponse.json();
    const postsPerPage = settingsResult.data?.postsPerPageSetting || 12;

    console.log('[CategoryClientPage] loadMorePosts using postsPerPage:', postsPerPage);

    const query = `
      query GetPostsByCategory($categoryId: Int!, $first: Int, $after: String) {
        posts(first: $first, after: $after, where: { categoryId: $categoryId }) {
          pageInfo {
            hasNextPage
            endCursor
          }
          nodes {
            id
            title
            date
            slug
            shortUuid
            excerpt
            __typename
            featuredImage {
              node {
                sourceUrl
                altText
              }
            }
            author {
              node {
                name
                slug
                avatar {
                  url
                }
              }
            }
            categories {
              nodes {
                id
                name
                slug
              }
            }
          }
        }
      }
    `;

    const response = await fetch(process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query,
        variables: {
          categoryId: category?.databaseId,
          first: postsPerPage,
          after: afterCursor,
        },
      }),
    });

    const result = await response.json();
    if (result.errors) {
      throw new Error(result.errors[0]?.message || 'GraphQL query failed');
    }

    return result.data?.posts;
  }, [category?.databaseId]);

  // 初始化时读取视图偏好
  useEffect(() => {
    try {
      const savedMode = localStorage.getItem('fd_view_mode') as ViewMode;
      const savedColumns = parseInt(localStorage.getItem('fd_columns_count') || '3') as ColumnCount;

      if (savedMode) {
        setViewMode(savedMode);
      }

      if (savedColumns) {
        setColumns(savedColumns);
      }
    } catch (e) {
      console.error('无法读取视图偏好设置:', e);
    }
  }, []);

  // 视图模式更改处理函数
  const handleViewModeChange = (mode: ViewMode, cols?: ColumnCount) => {
    setViewMode(mode);
    if (cols) {
      setColumns(cols);
    }
  };

  // 加载更多文章
  const handleLoadMore = useCallback(async () => {
    console.log('[CategoryClientPage] handleLoadMore called:', {
      currentPageInfo,
      hasNextPage: currentPageInfo?.hasNextPage,
      endCursor: currentPageInfo?.endCursor,
      loadingMore,
      allPostsCount: allPosts.length,
      categoryCount: category?.count
    });

    if (!currentPageInfo?.hasNextPage || loadingMore) {
      console.log('[CategoryClientPage] handleLoadMore early return:', {
        hasNextPage: currentPageInfo?.hasNextPage,
        loadingMore
      });
      return;
    }

    try {
      setLoadingMore(true);
      setLoadError(null);

      console.log('[CategoryClientPage] loading more posts with cursor:', currentPageInfo.endCursor);
      const result = await loadMorePosts(currentPageInfo.endCursor);

      if (result) {
        console.log('[CategoryClientPage] loaded more posts:', {
          newPostsCount: result.nodes.length,
          newPageInfo: result.pageInfo
        });

        // 合并新文章到现有列表
        setAllPosts((prev) => {
          const map = new Map<string, Article>();
          prev.forEach((p) => map.set(p.id, p));
          result.nodes.forEach((p: Article) => map.set(p.id, p));
          return Array.from(map.values());
        });

        // 更新分页信息
        setCurrentPageInfo(result.pageInfo);
      }
    } catch (error) {
      console.error('加载更多文章出错:', error);
      setLoadError(error as Error);
    } finally {
      setLoadingMore(false);
    }
  }, [currentPageInfo, loadingMore, allPosts.length, category?.count, loadMorePosts]);

  // 错误状态
  if (loadError) {
    return (
      <MainLayout>
        <div className="container mx-auto py-8 px-4">
          <h1 className="text-2xl font-bold mb-6">分类文章</h1>
          <div className="bg-red-50 text-red-500 p-4 rounded-lg">
            <h2 className="text-lg font-medium mb-2">加载出错</h2>
            <p>{loadError.message}</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  const hasMore = currentPageInfo?.hasNextPage ?? false;

  return (
    <MainLayout>
      {/* 注入 JSON-LD */}
      {category?.aiSeoJsonLd && (
        <Script
          id="category-jsonld"
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: category.aiSeoJsonLd }}
        />
      )}

      {/* Hero Banner 区域 */}
      <TaxonomyBanner
        title={category?.name || ''}
        description={category?.description}
        count={category?.count}
        bannerImageUrl={category?.bannerImageUrl}
        bannerImage={category?.bannerImage}
        breadcrumbs={[
          { label: '首页', href: '/' },
          { label: '分类', href: `/${prefixes.categoryIndexRoute}` },
          { label: category?.name || '', href: '#' }
        ]}
        gradientColors="from-blue-600 via-purple-600 to-indigo-700"
        icon={
          <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
            <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
          </svg>
        }
      />

      {/* 主要内容区域 */}
      <div className="container mx-auto py-8 px-4">

        {/* 视图切换器 */}
        <div className="mb-6 flex justify-end items-center">
          <ViewModeSwitcher currentMode={viewMode} columns={columns} onChange={handleViewModeChange} />
        </div>

        {/* 文章列表 */}
        {allPosts.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">该分类下暂无文章</p>
          </div>
        ) : (
          <InfiniteScroll
            hasMore={hasMore}
            loading={loadingMore}
            onLoadMore={handleLoadMore}
            totalCount={category?.count}
            loadingComponent={<ArticleListSkeleton count={3} />}
          >
            <ArticleListView
              articles={allPosts as Article[]}
              mode={viewMode}
              columns={columns}
              showFeaturedImage={true}
              showExcerpt={true}
              showDate={true}
              showAuthor={true}
              showCategory={true}
              showReadMore={true}
              routePrefixes={prefixes}
            />
          </InfiniteScroll>
        )}

        {/* 返回链接 */}
        <div className="mt-8">
          <Link href={`/${prefixes.categoryIndexRoute}`} className="text-blue-600 hover:text-blue-800 flex items-center">
            ← 返回分类索引
          </Link>
        </div>
      </div>
    </MainLayout>
  );
}