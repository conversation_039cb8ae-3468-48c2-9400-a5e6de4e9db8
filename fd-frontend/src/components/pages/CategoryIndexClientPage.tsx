'use client';

import React, { useState, useMemo } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import MainLayout from '@/components/layouts/MainLayout';

// 接口定义
// 共享的分类接口定义
interface Category {
  id: string;
  databaseId: number;
  name: string;
  slug: string;
  description?: string;
  count: number;
  bannerImageUrl?: string;
  bannerImage?: {
    sourceUrl: string;
    altText?: string;
    mediaDetails?: {
      width: number;
      height: number;
    };
  };
}

// 统计信息接口
interface Statistics {
  totalCategories: number;
  totalPosts: number;
  averagePostsPerCategory: number;
}

// SEO设置接口
interface SeoSettings {
  aiSeoTitle?: string;
  aiSeoDescription?: string;
  aiSeoJsonLd?: string;
  enabled?: boolean;
  lastUpdated?: string;
}

interface RoutePrefixes {
  categoryPrefix?: string;
  tagPrefix?: string;
  postPrefix?: string;
  categoryIndexRoute?: string;
  tagIndexRoute?: string;
  customTypePrefix?: string;
}

interface CategoryIndexClientPageProps {
  initialData: {
    seoSettings: SeoSettings;
    categories: Category[];
    statistics: Statistics;
  };
  routePrefixes: RoutePrefixes;
}

type ViewMode = 'grid' | 'list' | 'stats';
type SortBy = 'name' | 'count' | 'recent';

/**
 * 分类索引页面客户端组件
 */
export default function CategoryIndexClientPage({
  initialData,
  routePrefixes
}: CategoryIndexClientPageProps) {
  const { categories, statistics } = initialData;
  
  // 状态管理
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortBy, setSortBy] = useState<SortBy>('name');
  
  // 获取分类URL
  const getCategoryUrl = (slug: string) => {
    return routePrefixes?.categoryPrefix 
      ? `/${routePrefixes.categoryPrefix}/${slug}`
      : `/${slug}`;
  };
  
  // 智能过滤和排序
  const processedCategories = useMemo(() => {
    let filtered = categories.filter(cat => 
      cat.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (cat.description && cat.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    
    switch (sortBy) {
      case 'count':
        return filtered.sort((a, b) => (b.count || 0) - (a.count || 0));
      case 'recent':
        // 这里可以根据最新文章时间排序，暂时按名称排序
        return filtered.sort((a, b) => a.name.localeCompare(b.name, 'zh-CN'));
      default:
        return filtered.sort((a, b) => a.name.localeCompare(b.name, 'zh-CN'));
    }
  }, [categories, searchTerm, sortBy]);
  
  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50">
        {/* 页面头部 */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <div className="container mx-auto px-4 py-16">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">探索所有分类</h1>
              <p className="text-xl opacity-90 mb-8">
                发现 {statistics.totalCategories} 个精彩分类，涵盖 {statistics.totalPosts} 篇优质文章
              </p>
              
              {/* 统计卡片 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                  <div className="text-3xl font-bold mb-2">{statistics.totalCategories}</div>
                  <div className="text-sm opacity-80">总分类数</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                  <div className="text-3xl font-bold mb-2">{statistics.totalPosts}</div>
                  <div className="text-sm opacity-80">总文章数</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                  <div className="text-3xl font-bold mb-2">{Math.round(statistics.averagePostsPerCategory)}</div>
                  <div className="text-sm opacity-80">平均文章数</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* 控制面板 */}
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-xl shadow-sm border p-6 mb-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              {/* 搜索框 */}
              <div className="relative flex-1 max-w-md">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="搜索分类..."
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                {searchTerm && (
                  <button 
                    onClick={() => setSearchTerm('')}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    ✕
                  </button>
                )}
              </div>
              
              {/* 排序和视图控制 */}
              <div className="flex items-center gap-4">
                {/* 排序选择 */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as SortBy)}
                  className="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="name">按名称排序</option>
                  <option value="count">按文章数排序</option>
                  <option value="recent">按最新排序</option>
                </select>
                
                {/* 视图模式切换 */}
                <div className="flex border border-gray-200 rounded-lg overflow-hidden">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`px-4 py-2 text-sm font-medium ${
                      viewMode === 'grid' 
                        ? 'bg-blue-500 text-white' 
                        : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    网格
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`px-4 py-2 text-sm font-medium ${
                      viewMode === 'list' 
                        ? 'bg-blue-500 text-white' 
                        : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    列表
                  </button>
                  <button
                    onClick={() => setViewMode('stats')}
                    className={`px-4 py-2 text-sm font-medium ${
                      viewMode === 'stats' 
                        ? 'bg-blue-500 text-white' 
                        : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    统计
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          {/* 搜索结果提示 */}
          {searchTerm && (
            <div className="mb-6">
              <p className="text-gray-600">
                找到 {processedCategories.length} 个匹配 "{searchTerm}" 的分类
              </p>
            </div>
          )}
          
          {/* 分类展示 */}
          {processedCategories.length === 0 ? (
            <div className="text-center py-16">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 6.306a7.962 7.962 0 00-6 0m6 0V5a2 2 0 00-2-2H9a2 2 0 00-2 2v1.306m8 0V7a2 2 0 012 2v10a2 2 0 01-2 2H7a2 2 0 01-2-2V9a2 2 0 012-2h8a2 2 0 012 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的分类</h3>
              <p className="text-gray-500 mb-4">尝试调整搜索关键词或清除搜索条件</p>
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-100 hover:bg-blue-200"
                >
                  清除搜索
                </button>
              )}
            </div>
          ) : (
            <>
              {viewMode === 'grid' && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {processedCategories.map((category) => (
                    <CategoryCard 
                      key={category.id}
                      category={category}
                      getCategoryUrl={getCategoryUrl}
                    />
                  ))}
                </div>
              )}
              
              {viewMode === 'list' && (
                <div className="space-y-4">
                  {processedCategories.map((category) => (
                    <CategoryListItem 
                      key={category.id}
                      category={category}
                      getCategoryUrl={getCategoryUrl}
                    />
                  ))}
                </div>
              )}
              
              {viewMode === 'stats' && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div className="bg-white rounded-xl shadow-sm border p-6">
                    <h3 className="text-lg font-semibold mb-4">分类分布</h3>
                    <div className="space-y-3">
                      {processedCategories.slice(0, 10).map((category) => (
                        <div key={category.id} className="flex items-center justify-between">
                          <Link 
                            href={getCategoryUrl(category.slug)}
                            className="text-blue-600 hover:text-blue-800 font-medium"
                          >
                            {category.name}
                          </Link>
                          <div className="flex items-center gap-2">
                            <div className="w-20 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-500 h-2 rounded-full" 
                                style={{ 
                                  width: `${Math.min((category.count / Math.max(...processedCategories.map(c => c.count))) * 100, 100)}%` 
                                }}
                              />
                            </div>
                            <span className="text-sm text-gray-600 w-8 text-right">{category.count}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="bg-white rounded-xl shadow-sm border p-6">
                    <h3 className="text-lg font-semibold mb-4">统计概览</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center py-2 border-b border-gray-100">
                        <span className="text-gray-600">总分类数</span>
                        <span className="font-semibold">{statistics.totalCategories}</span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gray-100">
                        <span className="text-gray-600">总文章数</span>
                        <span className="font-semibold">{statistics.totalPosts}</span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gray-100">
                        <span className="text-gray-600">平均每分类文章数</span>
                        <span className="font-semibold">{statistics.averagePostsPerCategory.toFixed(1)}</span>
                      </div>
                      <div className="flex justify-between items-center py-2">
                        <span className="text-gray-600">最活跃分类</span>
                        <Link 
                          href={getCategoryUrl(processedCategories[0]?.slug || '')}
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          {processedCategories[0]?.name}
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </MainLayout>
  );
}

// 分类卡片组件
function CategoryCard({ category, getCategoryUrl }: { 
  category: Category; 
  getCategoryUrl: (slug: string) => string; 
}) {
  return (
    <Link href={getCategoryUrl(category.slug)} className="group">
      <div className="bg-white rounded-xl shadow-sm border hover:shadow-lg transition-all duration-300 overflow-hidden h-full">
        {/* 分类Banner图片 */}
        {category.bannerImageUrl && (
          <div className="relative h-48 overflow-hidden">
            <Image
              src={category.bannerImageUrl}
              alt={category.name}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
          </div>
        )}
        
        {/* 分类信息 */}
        <div className="p-6">
          <div className="flex items-start justify-between mb-3">
            <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
              {category.name}
            </h3>
            <span className="bg-blue-100 text-blue-800 text-sm font-medium px-2.5 py-1 rounded-full">
              {category.count}
            </span>
          </div>
          
          {category.description && (
            <p className="text-gray-600 text-sm mb-4 line-clamp-2">
              {category.description}
            </p>
          )}
          
          <div className="flex items-center justify-between">
            <span className="text-blue-600 text-sm font-medium group-hover:text-blue-700">
              查看文章 →
            </span>
          </div>
        </div>
      </div>
    </Link>
  );
}

// 分类列表项组件
function CategoryListItem({ category, getCategoryUrl }: { 
  category: Category; 
  getCategoryUrl: (slug: string) => string; 
}) {
  return (
    <Link href={getCategoryUrl(category.slug)} className="group">
      <div className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-all duration-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors mb-1">
              {category.name}
            </h3>
            {category.description && (
              <p className="text-gray-600 text-sm line-clamp-1">
                {category.description}
              </p>
            )}
          </div>
          <div className="flex items-center gap-4 ml-4">
            <span className="bg-gray-100 text-gray-800 text-sm font-medium px-3 py-1 rounded-full">
              {category.count} 篇文章
            </span>
            <svg className="w-5 h-5 text-gray-400 group-hover:text-blue-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </div>
      </div>
    </Link>
  );
}
