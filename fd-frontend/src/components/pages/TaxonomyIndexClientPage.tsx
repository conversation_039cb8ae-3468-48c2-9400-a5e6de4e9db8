'use client';

import React, { useState, useMemo } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import MainLayout from '@/components/layouts/MainLayout';

// 接口定义
// 共享的分类法条目接口定义
interface TaxonomyTerm {
  id: string;
  databaseId: number;
  name: string;
  slug: string;
  description?: string;
  count: number;
  bannerImageUrl?: string;
  bannerImage?: {
    sourceUrl: string;
    altText?: string;
    mediaDetails?: {
      width: number;
      height: number;
    };
  };
}

// 统计信息接口
interface Statistics {
  totalTerms: number;
  totalPosts: number;
  averagePostsPerTerm: number;
}

// SEO设置接口
interface SeoSettings {
  aiSeoTitle?: string;
  aiSeoDescription?: string;
  aiSeoJsonLd?: string;
  enabled?: boolean;
  lastUpdated?: string;
}

// 分类法信息接口
interface TaxonomyInfo {
  name: string;
  label: string;
  description?: string;
}

interface RoutePrefixes {
  categoryPrefix?: string;
  tagPrefix?: string;
  postPrefix?: string;
  categoryIndexRoute?: string;
  tagIndexRoute?: string;
  customTypePrefix?: string;
}

// 分类法索引页面数据接口
interface TaxonomyIndexData {
  seoSettings: SeoSettings;
  terms: TaxonomyTerm[];
  statistics: Statistics;
  taxonomy: TaxonomyInfo;
}

interface TaxonomyIndexClientPageProps {
  initialData: TaxonomyIndexData;
  routePrefixes: RoutePrefixes;
  taxonomy: string;
}

// 排序选项
type SortOption = 'name' | 'count' | 'recent';

/**
 * 分类法索引页面客户端组件
 */
export default function TaxonomyIndexClientPage({ 
  initialData, 
  routePrefixes, 
  taxonomy 
}: TaxonomyIndexClientPageProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('name');
  const [showOnlyWithBanner, setShowOnlyWithBanner] = useState(false);
  const [isLoading, setIsLoading] = useState(false);



  // 过滤和排序条目
  const filteredAndSortedTerms = useMemo(() => {
    let filtered = initialData.terms.filter((term: TaxonomyTerm) => {
      const matchesSearch = term.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (term.description && term.description.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesBannerFilter = !showOnlyWithBanner || 
                                 (term.bannerImageUrl || term.bannerImage?.sourceUrl);
      
      return matchesSearch && matchesBannerFilter;
    });

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'count':
          return b.count - a.count;
        case 'recent':
          return b.databaseId - a.databaseId;
        case 'name':
        default:
          return a.name.localeCompare(b.name, 'zh-CN');
      }
    });

    return filtered;
  }, [initialData.terms, searchTerm, sortBy, showOnlyWithBanner]);



  // 获取条目链接
  const getTermUrl = (termSlug: string) => {
    return `/taxonomy/${taxonomy}/${termSlug}`;
  };

  // 获取Banner图片URL
  const getBannerImageUrl = (term: TaxonomyTerm) => {
    return term.bannerImageUrl || term.bannerImage?.sourceUrl;
  };

  // 清除搜索
  const clearSearch = () => {
    setSearchTerm('');
  };

  return (
    <MainLayout>
      {/* Hero区域 */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {initialData.taxonomy.label}索引
            </h1>
            {initialData.taxonomy.description && (
              <div 
                className="text-xl mb-6 opacity-90 max-w-3xl mx-auto"
                dangerouslySetInnerHTML={{ __html: initialData.taxonomy.description }}
              />
            )}
            <div className="flex flex-wrap justify-center gap-6 text-lg">
              <div className="bg-white/20 px-4 py-2 rounded-lg">
                <span className="font-semibold">{initialData.statistics.totalTerms}</span> 个{initialData.taxonomy.label}
              </div>
              <div className="bg-white/20 px-4 py-2 rounded-lg">
                <span className="font-semibold">{initialData.statistics.totalPosts}</span> 篇文章
              </div>
              <div className="bg-white/20 px-4 py-2 rounded-lg">
                平均 <span className="font-semibold">{initialData.statistics.averagePostsPerTerm}</span> 篇/条目
              </div>
            </div>
            

          </div>
        </div>
      </div>

      {/* 控制面板 */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            {/* 搜索框 */}
            <div className="flex-1 max-w-md">
              <div className="relative">
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder={`搜索${initialData.taxonomy.label}...`}
                  className="w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  🔍
                </div>
                {searchTerm && (
                  <button 
                    onClick={clearSearch}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  >
                    ✕
                  </button>
                )}
              </div>
            </div>

            {/* 控制选项 */}
            <div className="flex flex-wrap gap-4 items-center">
              {/* 排序选择 */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortOption)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="name">按名称排序</option>
                <option value="count">按文章数排序</option>
                <option value="recent">按最新排序</option>
              </select>

              {/* Banner筛选 */}
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={showOnlyWithBanner}
                  onChange={(e) => setShowOnlyWithBanner(e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">仅显示有Banner的</span>
              </label>
            </div>
          </div>

          {/* 结果统计 */}
          <div className="mt-4 text-sm text-gray-600">
            {searchTerm || showOnlyWithBanner ? (
              <>
                找到 <span className="font-semibold text-blue-600">{filteredAndSortedTerms.length}</span> 个{initialData.taxonomy.label}
                {searchTerm && (
                  <span className="ml-2">
                    包含 "<span className="font-medium">{searchTerm}</span>"
                  </span>
                )}
              </>
            ) : (
              <>共 <span className="font-semibold text-blue-600">{initialData.statistics.totalTerms}</span> 个{initialData.taxonomy.label}</>
            )}
          </div>
        </div>
      </div>

      {/* 条目展示区域 */}
      <div className="container mx-auto px-4 py-8">
        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">加载中...</span>
          </div>
        ) : filteredAndSortedTerms.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-700 mb-2">
              {searchTerm ? '未找到匹配的条目' : `暂无${initialData.taxonomy.label}`}
            </h3>
            <p className="text-gray-500 mb-6">
              {searchTerm
                ? `没有找到包含 "${searchTerm}" 的${initialData.taxonomy.label}，请尝试其他关键词`
                : `该分类法下暂无${initialData.taxonomy.label}条目`
              }
            </p>
            {searchTerm && (
              <button
                onClick={clearSearch}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                清除搜索
              </button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredAndSortedTerms.map((term: TaxonomyTerm) => {
              const bannerUrl = getBannerImageUrl(term);

              return (
                <div
                  key={term.id}
                  className="bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-300 overflow-hidden group"
                >
                  {/* Banner图片 */}
                  {bannerUrl && (
                    <div className="relative h-48 overflow-hidden">
                      <Image
                        src={bannerUrl}
                        alt={term.bannerImage?.altText || `${term.name} Banner`}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                      <div className="absolute bottom-4 left-4 right-4">
                        <Link href={getTermUrl(term.slug)}>
                          <h2 className="text-xl font-bold text-white mb-1 hover:text-blue-200 transition-colors line-clamp-2">
                            {term.name}
                          </h2>
                        </Link>
                        <div className="text-white/80 text-sm">
                          {term.count} 篇文章
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 内容区域 */}
                  <div className="p-6">
                    {!bannerUrl && (
                      <Link href={getTermUrl(term.slug)}>
                        <h2 className="text-xl font-bold mb-3 hover:text-blue-600 transition-colors line-clamp-2">
                          {term.name}
                        </h2>
                      </Link>
                    )}

                    {term.description && (
                      <div
                        className="text-gray-600 mb-4 line-clamp-3 text-sm leading-relaxed"
                        dangerouslySetInnerHTML={{ __html: term.description }}
                      />
                    )}

                    <div className="flex items-center justify-between">
                      {!bannerUrl && (
                        <div className="text-sm text-gray-500">
                          {term.count} 篇文章
                        </div>
                      )}

                      <Link
                        href={getTermUrl(term.slug)}
                        className="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors"
                      >
                        查看详情
                        <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* 返回链接 */}
        <div className="mt-12 text-center">
          <Link
            href="/"
            className="inline-flex items-center text-gray-600 hover:text-gray-800 transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            返回首页
          </Link>
        </div>
      </div>
    </MainLayout>
  );
}
