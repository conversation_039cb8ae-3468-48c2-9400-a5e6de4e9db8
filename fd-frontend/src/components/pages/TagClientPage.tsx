'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useRoutePrefixes } from '@/hooks';
import MainLayout from '@/components/layouts/MainLayout';
import ArticleListView, { Article } from '@/components/ArticleListView';
import ViewModeSwitcher, { ViewMode, ColumnCount } from '@/components/ViewModeSwitcher';
import InfiniteScroll from '@/components/InfiniteScroll';
import ArticleListSkeleton from '@/components/ArticleListSkeleton';
import TaxonomyBanner from '@/components/ui/TaxonomyBanner';

// 页面信息类型
interface PageInfo {
  hasNextPage: boolean;
  endCursor: string;
}

// 标签类型
interface Tag {
  id: string;
  databaseId: number;
  name: string;
  slug: string;
  count: number;
  description?: string;
  aiSeoTitle?: string;
  aiSeoDescription?: string;
  aiSeoJsonLd?: string;
  bannerImageUrl?: string;
  bannerImage?: {
    sourceUrl: string;
    altText?: string;
    mediaDetails?: {
      width: number;
      height: number;
    };
  };
}

// 组件属性类型
interface TagClientPageProps {
  initialTag: Tag;
  initialPosts: Article[];
  initialPageInfo?: PageInfo | null;
}

export default function TagClientPage({
  initialTag: tag,
  initialPosts,
  initialPageInfo,
}: TagClientPageProps) {
  const { prefixes } = useRoutePrefixes();

  // 调试信息
  console.log('[TagClientPage] Component initialized:', {
    tagName: tag?.name,
    tagCount: tag?.count,
    initialPostsCount: initialPosts.length,
    initialPageInfo
  });

  // 用于合并首屏文章和后续加载的文章
  const [allPosts, setAllPosts] = useState<Article[]>(initialPosts);

  // 当前分页信息状态
  const [currentPageInfo, setCurrentPageInfo] = useState<PageInfo | null>(initialPageInfo || null);

  // 当服务器端获取的初始数据变化时（例如通过router.refresh()），同步更新客户端状态
  useEffect(() => {
    // 使用新的 initialPosts 重置 allPosts 状态
    setAllPosts(initialPosts);
    // 同时，重置分页信息
    setCurrentPageInfo(initialPageInfo || null);
    // 重置加载和错误状态
    setLoadingMore(false);
    setLoadError(null);

    console.log('[TagClientPage] Props updated, state has been reset.', {
      newPostsCount: initialPosts.length,
      newPageInfo: initialPageInfo
    });
  }, [initialPosts, initialPageInfo]);

  // 加载更多状态
  const [loadingMore, setLoadingMore] = useState(false);

  // 错误状态
  const [loadError, setLoadError] = useState<Error | null>(null);

  // 视图模式状态
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [columns, setColumns] = useState<ColumnCount>(3);

  // 初始化时从localStorage获取视图偏好
  useEffect(() => {
    try {
      const savedMode = localStorage.getItem('fd_view_mode') as ViewMode;
      const savedColumns = parseInt(localStorage.getItem('fd_columns_count') || '3') as ColumnCount;
      
      if (savedMode) {
        setViewMode(savedMode);
      }
      
      if (savedColumns) {
        setColumns(savedColumns);
      }
    } catch (e) {
      console.error('无法读取视图偏好设置:', e);
    }
  }, []);

  // 视图模式更改处理函数
  const handleViewModeChange = (mode: ViewMode, cols?: ColumnCount) => {
    setViewMode(mode);
    if (cols) {
      setColumns(cols);
    }
  };

  // 手动加载更多数据的函数
  const loadMorePosts = useCallback(async (afterCursor: string): Promise<{
    nodes: Article[];
    pageInfo: PageInfo;
  } | null> => {
    // 首先获取分页设置
    const settingsQuery = `
      query GetPostsPerPageSetting {
        postsPerPageSetting
      }
    `;
    
    const settingsResponse = await fetch(process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: settingsQuery }),
    });
    
    const settingsResult = await settingsResponse.json();
    const postsPerPage = settingsResult.data?.postsPerPageSetting || 12;
    
    console.log('[TagClientPage] loadMorePosts using postsPerPage:', postsPerPage);

    const query = `
      query GetPostsByTag($tagId: String!, $first: Int, $after: String) {
        posts(first: $first, after: $after, where: { tagId: $tagId }) {
          pageInfo {
            hasNextPage
            endCursor
          }
          nodes {
            id
            title
            date
            slug
            shortUuid
            excerpt
            __typename
            featuredImage {
              node {
                sourceUrl
                altText
              }
            }
            author {
              node {
                name
                slug
                avatar {
                  url
                }
              }
            }
            categories {
              nodes {
                id
                name
                slug
              }
            }
          }
        }
      }
    `;

    const response = await fetch(process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query,
        variables: {
          tagId: tag?.databaseId?.toString(),
          first: postsPerPage,
          after: afterCursor,
        },
      }),
    });

    const result = await response.json();
    if (result.errors) {
      throw new Error(result.errors[0]?.message || 'GraphQL query failed');
    }

    return result.data?.posts;
  }, [tag?.databaseId]);

  // 加载更多文章
  const handleLoadMore = useCallback(async () => {
    console.log('[TagClientPage] handleLoadMore called:', {
      currentPageInfo,
      hasNextPage: currentPageInfo?.hasNextPage,
      endCursor: currentPageInfo?.endCursor,
      loadingMore,
      allPostsCount: allPosts.length,
      tagCount: tag?.count
    });
    
    if (!currentPageInfo?.hasNextPage || loadingMore) {
      console.log('[TagClientPage] handleLoadMore early return:', {
        hasNextPage: currentPageInfo?.hasNextPage,
        loadingMore
      });
      return;
    }

    try {
      setLoadingMore(true);
      setLoadError(null);
      
      console.log('[TagClientPage] loading more posts with cursor:', currentPageInfo.endCursor);
      const result = await loadMorePosts(currentPageInfo.endCursor);
      
      if (result) {
        console.log('[TagClientPage] loaded more posts:', {
          newPostsCount: result.nodes.length,
          newPageInfo: result.pageInfo
        });
        
        // 合并新文章到现有列表
        setAllPosts((prev) => {
          const map = new Map<string, Article>();
          prev.forEach((p) => map.set(p.id, p));
          result.nodes.forEach((p: Article) => map.set(p.id, p));
          return Array.from(map.values());
        });
        
        // 更新分页信息
        setCurrentPageInfo(result.pageInfo);
      }
    } catch (error) {
      console.error('加载更多文章出错:', error);
      setLoadError(error as Error);
    } finally {
      setLoadingMore(false);
    }
  }, [currentPageInfo, loadingMore, allPosts.length, tag?.count, loadMorePosts]);

  // 错误状态
  if (loadError) {
    return (
      <MainLayout>
        <div className="container mx-auto py-8 px-4">
          <h1 className="text-2xl font-bold mb-6">标签文章</h1>
          <div className="bg-red-50 text-red-500 p-4 rounded-lg">
            <h2 className="text-lg font-medium mb-2">加载出错</h2>
            <p>{loadError.message}</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  const hasMore = currentPageInfo?.hasNextPage ?? false;

  return (
    <MainLayout>
      {/* Hero Banner 区域 */}
      <TaxonomyBanner
        title={tag?.name || ''}
        description={tag?.description}
        count={tag?.count}
        bannerImageUrl={tag?.bannerImageUrl}
        bannerImage={tag?.bannerImage}
        breadcrumbs={[
          { label: '首页', href: '/' },
          { label: '标签', href: `/${prefixes.tagIndexRoute}` },
          { label: tag?.name || '', href: '#' }
        ]}
        gradientColors="from-pink-500 via-red-500 to-orange-500"
        titlePrefix="#"
        icon={
          <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.243 3.03a1 1 0 01.727 1.213L9.53 6h2.94l.56-2.243a1 1 0 111.94.486L14.53 6H17a1 1 0 110 2h-2.97l-1 4H15a1 1 0 110 2h-2.47l-.56 2.242a1 1 0 11-1.94-.485L10.47 14H7.53l-.56 2.242a1 1 0 11-1.94-.485L5.47 14H3a1 1 0 110-2h2.97l1-4H5a1 1 0 110-2h2.47l.56-2.243a1 1 0 011.213-.727zM9.03 8l-1 4h2.94l1-4H9.03z" clipRule="evenodd" />
          </svg>
        }
      />

      {/* 主要内容区域 */}
      <div className="container mx-auto py-8 px-4">

        {/* 视图切换器 */}
        <div className="mb-6 flex justify-end">
          <ViewModeSwitcher
            currentMode={viewMode}
            columns={columns}
            onChange={handleViewModeChange}
          />
        </div>

        {/* 文章列表 */}
        {allPosts.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">该标签下暂无文章</p>
          </div>
        ) : (
          <InfiniteScroll
            hasMore={hasMore}
            loading={loadingMore}
            onLoadMore={handleLoadMore}
            totalCount={tag.count}
            loadingComponent={<ArticleListSkeleton mode={viewMode} count={3} />}
          >
            <ArticleListView
              articles={allPosts}
              mode={viewMode}
              columns={columns}
              showFeaturedImage={true}
              showExcerpt={true}
              showDate={true}
              showAuthor={true}
              showCategory={true}
              showReadMore={true}
              routePrefixes={prefixes}
            />
          </InfiniteScroll>
        )}

        {/* 返回链接 */}
        <div className="mt-8">
          <Link 
            href={`/${prefixes.tagIndexRoute}`}
            className="text-blue-600 hover:text-blue-800 flex items-center"
          >
            ← 返回标签索引
          </Link>
        </div>
      </div>
    </MainLayout>
  );
}
