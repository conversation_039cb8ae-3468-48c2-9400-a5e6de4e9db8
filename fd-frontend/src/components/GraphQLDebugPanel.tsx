import React, { useState } from 'react';

interface GraphQLDebugPanelProps {
  type: string;
  data: any;
}

const GraphQLDebugPanel: React.FC<GraphQLDebugPanelProps> = ({ type, data }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  return (
    <div className="mt-8 border border-gray-200 rounded-lg overflow-hidden">
      <div 
        className="bg-gray-100 p-3 flex justify-between items-center cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <h3 className="text-md font-medium">GraphQL 调试信息</h3>
        <span>{isExpanded ? '收起 ▲' : '展开 ▼'}</span>
      </div>
      
      {isExpanded && (
        <div className="p-4">
          <div className="mb-3">
            <p className="text-sm font-medium">查询类型: <span className="font-mono">{type}</span></p>
            <p className="text-sm font-medium mt-1">查询参数: <span className="font-mono">contentTypes: [{type.toUpperCase()}]</span></p>
          </div>
          
          <pre className="bg-gray-50 p-4 rounded overflow-auto text-xs max-h-96">
            {JSON.stringify(data, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default GraphQLDebugPanel; 