'use client';

import React, { useMemo } from 'react';
import PostContentSmart from '@/components/post/PostContentSmart';
import { removeAllImagesFromHTML } from '@/utils/image-utils';

interface NoteContentDisplayProps {
  postId: number;
  initialContent: string;
  postTitle: string;
  unlockPrice?: number;
  requiredMemberLevel?: number;
  isUnlocked?: boolean;
  paywallVariant?: 'default' | 'compact';
  graphqlSingleName?: string;
  initialPaywallInfo?: any;
  removeImages?: boolean; // 是否移除图片
}

const NoteContentDisplay: React.FC<NoteContentDisplayProps> = ({
  postId,
  initialContent,
  postTitle,
  unlockPrice = 0,
  requiredMemberLevel = 0,
  isUnlocked = false,
  paywallVariant = 'default',
  graphqlSingleName = 'Post',
  initialPaywallInfo = null,
  removeImages = false,
}) => {
  // 处理内容，根据需要移除图片
  const processedContent = useMemo(() => {
    if (!removeImages) return initialContent;
    return removeAllImagesFromHTML(initialContent);
  }, [initialContent, removeImages]);

  return (
    <PostContentSmart
      postId={postId}
      initialContent={processedContent}
      postTitle={postTitle}
      unlockPrice={unlockPrice}
      requiredMemberLevel={requiredMemberLevel}
      isUnlocked={isUnlocked}
      paywallVariant={paywallVariant}
      graphqlSingleName={graphqlSingleName}
      initialPaywallInfo={initialPaywallInfo}
    />
  );
};

export default NoteContentDisplay;
