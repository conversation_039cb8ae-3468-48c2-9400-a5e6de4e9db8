'use client';

import React, { useMemo } from 'react';
import ImageCarousel from '@/components/ui/ImageCarousel';
import { extractImagesFromHTML, combineImages, ExtractedImage } from '@/utils/image-utils';

interface NoteImageDisplayProps {
  featuredImage?: {
    sourceUrl: string;
    altText?: string;
  } | null;
  content?: string;
  className?: string;
  showThumbnails?: boolean;
  showCounter?: boolean;
  onImageClick?: (index: number) => void;
}

const NoteImageDisplay: React.FC<NoteImageDisplayProps> = ({
  featuredImage,
  content = '',
  className = '',
  showThumbnails = false,
  showCounter = true,
  onImageClick,
}) => {
  // 提取并合并所有图片
  const allImages = useMemo(() => {
    // 从HTML内容中提取图片
    const contentImages = extractImagesFromHTML(content);
    
    // 合并特色图片和内容图片
    return combineImages(featuredImage, contentImages);
  }, [featuredImage, content]);

  // 如果没有图片，显示占位符
  if (allImages.length === 0) {
    return (
      <div className={`w-full h-full bg-gray-200 flex items-center justify-center ${className}`}>
        <div className="text-center text-gray-500">
          <svg 
            className="w-16 h-16 mx-auto mb-2 text-gray-400" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={1.5} 
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" 
            />
          </svg>
          <p className="text-sm">暂无图片</p>
        </div>
      </div>
    );
  }

  return (
    <ImageCarousel
      images={allImages}
      className={className}
      showThumbnails={showThumbnails}
      showCounter={showCounter}
      onImageClick={onImageClick}
    />
  );
};

export default NoteImageDisplay;
