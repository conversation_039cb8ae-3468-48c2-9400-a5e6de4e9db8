'use client';

import React from 'react';
import PaywallCard from './PaywallCard';
import PaywallCardCompact from './PaywallCardCompact';
import { useAuthContext } from '@/hooks/useAuthContext';
import { useQuery, gql } from '@apollo/client';
import { MemberLevel } from '@/types/user-types';

interface PaywallInfo {
  hasPaywall: boolean;
  previewContent: string;
  loginUrl: string;
  registerUrl: string;
  upgradeUrl: string;
  message: string;
  isLoggedIn: boolean;
}

interface PaywallRendererProps {
  content: string;
  postId: number;
  postTitle: string;
  unlockPrice?: number;
  requiredMemberLevel?: number;
  isUnlocked?: boolean;
  variant?: 'default' | 'compact';
  onUnlock?: () => void;
  // 预处理的付费墙信息
  paywallInfo?: PaywallInfo | null;
  // 新增：是否启用传统模式（向后兼容）
  legacyMode?: boolean;
  // 新增：是否显示加载状态
  showLoading?: boolean;
}

const GET_MEMBER_LEVEL = gql`
  query GetMemberLevel($id: Int!) {
    memberLevel(id: $id) {
      id
      name
      description
      priority
      price
      tier
    }
  }
`;

/**
 * 统一的付费墙渲染器
 * 支持优化模式（使用预处理数据）和传统模式（HTML解析）
 */
const PaywallRenderer: React.FC<PaywallRendererProps> = ({
  content,
  postId,
  postTitle,
  unlockPrice = 0,
  requiredMemberLevel = 0,
  isUnlocked = false,
  variant = 'default',
  onUnlock,
  paywallInfo,
  legacyMode = false,
  showLoading = false
}) => {
  const { user } = useAuthContext();

  // 查询所需会员等级信息
  const { data: levelData, loading: levelLoading } = useQuery(GET_MEMBER_LEVEL, {
    variables: { id: requiredMemberLevel },
    skip: !requiredMemberLevel || requiredMemberLevel === 0,
  });

  // 优化模式：如果有预处理的付费墙信息，优先使用
  if (paywallInfo?.hasPaywall) {
    const PaywallComponent = variant === 'compact' ? PaywallCardCompact : PaywallCard;

    return (
      <div className="article-content prose lg:prose-xl mx-auto">
        {/* 预览内容 */}
        {paywallInfo.previewContent && (
          <div dangerouslySetInnerHTML={{ __html: paywallInfo.previewContent }} />
        )}

        {/* 精致的付费墙卡片 */}
        <PaywallComponent
          postId={postId}
          postTitle={postTitle}
          requiredMemberLevel={requiredMemberLevel}
          requiredMemberLevelInfo={levelData?.memberLevel}
          unlockPrice={unlockPrice}
          isUnlocked={isUnlocked}
          userMemberLevel={user?.memberLevel}
          loginUrl={paywallInfo.loginUrl}
          registerUrl={paywallInfo.registerUrl}
          upgradeUrl={paywallInfo.upgradeUrl}
          onUnlock={onUnlock}
        />
      </div>
    );
  }

  // 传统模式或回退逻辑
  const hasPaywall = legacyMode
    ? (paywallInfo?.hasPaywall ?? content.includes('fd-member-access-denied'))
    : content.includes('fd-member-access-denied');

  const displayContent = legacyMode
    ? (paywallInfo?.previewContent ?? content)
    : content;

  // 显示加载状态
  if (showLoading && levelLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center space-x-2 text-gray-600">
          <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <span>加载中…</span>
        </div>
      </div>
    );
  }

  // 如果没有付费墙，直接返回原内容
  if (!hasPaywall) {
    return (
      <div
        className="article-content prose lg:prose-xl mx-auto"
        dangerouslySetInnerHTML={{ __html: displayContent }}
      />
    );
  }

  // 如果没有预处理信息但内容包含付费墙标记，显示骨架屏（仅在非传统模式下）
  if (!legacyMode && content.includes('fd-member-access-denied')) {
    return (
      <div className="article-content prose lg:prose-xl mx-auto">
        {/* 内容骨架屏 */}
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
        </div>

        {/* 付费墙卡片骨架屏 */}
        <div className="mt-8 p-6 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl border border-blue-200 dark:border-gray-700 animate-pulse">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-blue-200 dark:bg-gray-700 rounded-2xl"></div>
          </div>
          <div className="text-center space-y-4">
            <div className="h-6 bg-blue-200 dark:bg-gray-700 rounded w-1/2 mx-auto"></div>
            <div className="h-4 bg-blue-200 dark:bg-gray-700 rounded w-3/4 mx-auto"></div>
            <div className="flex justify-center space-x-4 mt-6">
              <div className="h-10 bg-blue-200 dark:bg-gray-700 rounded w-24"></div>
              <div className="h-10 bg-blue-200 dark:bg-gray-700 rounded w-24"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 渲染付费墙卡片（传统模式或有付费墙的情况）
  const PaywallComponent = variant === 'compact' ? PaywallCardCompact : PaywallCard;

  return (
    <div className="article-content prose lg:prose-xl mx-auto">
      {/* 预览内容 */}
      {displayContent && (
        <div dangerouslySetInnerHTML={{ __html: displayContent }} />
      )}

      {/* 精致的付费墙卡片 */}
      <PaywallComponent
        postId={postId}
        postTitle={postTitle}
        requiredMemberLevel={requiredMemberLevel}
        requiredMemberLevelInfo={levelData?.memberLevel}
        unlockPrice={unlockPrice}
        isUnlocked={isUnlocked}
        userMemberLevel={user?.memberLevel}
        loginUrl={paywallInfo?.loginUrl || '/auth/login'}
        registerUrl={paywallInfo?.registerUrl || '/auth/register'}
        upgradeUrl={paywallInfo?.upgradeUrl || '/membership/upgrade'}
        onUnlock={onUnlock}
      />
    </div>
  );
};

export default PaywallRenderer;
