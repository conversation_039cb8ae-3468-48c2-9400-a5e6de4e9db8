# 付费墙UI组件

这个目录包含了精致的付费墙UI组件，用于替代后端生成的简陋HTML付费墙。

## 组件概览

### 1. PaywallCard.tsx
完整版付费墙卡片组件，提供最丰富的视觉效果和交互体验。

**特性：**
- 精美的渐变背景和装饰元素
- 动画效果（悬停、脉冲、旋转等）
- 支持会员升级和单篇解锁两种选项
- 完整的响应式设计
- 深色模式支持

**使用场景：**
- 文章详情页的主要付费墙
- 需要强调付费内容价值的场景

### 2. PaywallCardCompact.tsx
紧凑版付费墙卡片组件，适合空间有限的场景。

**特性：**
- 简洁的横向布局
- 保留核心功能和视觉效果
- 更小的占用空间
- 响应式设计

**使用场景：**
- 文章列表中的付费墙提示
- 侧边栏或小部件中的付费墙
- 移动端优化显示

### 3. PaywallRenderer.tsx
智能付费墙渲染器，自动解析后端HTML并渲染为精美卡片。

**特性：**
- 自动检测付费墙HTML标记
- 解析链接和用户状态信息
- 无缝替换原始HTML
- 支持选择不同的卡片样式

**使用场景：**
- PostContentSmart组件中的自动渲染
- 需要兼容现有后端HTML的场景

### 4. PostContentSmart.tsx
智能内容显示组件，集成了付费墙渲染功能。

**特性：**
- 根据用户认证状态动态加载内容
- 自动处理付费墙显示
- 支持GraphQL数据获取
- 集成解锁功能（待实现）

## 使用方法

### 基础使用

```tsx
import PaywallCard from '@/components/post/PaywallCard';

<PaywallCard
  postId={123}
  postTitle="文章标题"
  requiredMemberLevel={2}
  unlockPrice={9.99}
  isUnlocked={false}
  onUnlock={handleUnlock}
/>
```

### 在文章模板中使用

```tsx
import PostContentSmart from '@/components/post/PostContentSmart';

<PostContentSmart 
  postId={post.databaseId} 
  initialContent={post.content}
  postTitle={post.title}
  unlockPrice={post.unlockPrice}
  requiredMemberLevel={post.requiredMemberLevel}
  isUnlocked={post.isUnlockedByCurrentUser}
  paywallVariant="default" // 或 "compact"
/>
```

### 自定义链接

```tsx
<PaywallCard
  // ... 其他属性
  loginUrl="/custom/login"
  registerUrl="/custom/register"
  upgradeUrl="/custom/upgrade"
/>
```

## 属性说明

### PaywallCard / PaywallCardCompact

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| postId | number | - | 文章ID（必需） |
| postTitle | string | - | 文章标题（必需） |
| requiredMemberLevel | number | 0 | 所需会员等级 |
| unlockPrice | number | 0 | 单篇解锁价格 |
| isUnlocked | boolean | false | 是否已解锁 |
| userMemberLevel | object | - | 用户当前会员等级信息 |
| loginUrl | string | '/auth/login' | 登录页面URL |
| registerUrl | string | '/auth/register' | 注册页面URL |
| upgradeUrl | string | '/membership/upgrade' | 会员升级页面URL |
| onUnlock | function | - | 解锁回调函数 |

### PaywallRenderer

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| content | string | - | 包含付费墙的HTML内容 |
| postId | number | - | 文章ID |
| postTitle | string | - | 文章标题 |
| unlockPrice | number | 0 | 单篇解锁价格 |
| requiredMemberLevel | number | 0 | 所需会员等级 |
| isUnlocked | boolean | false | 是否已解锁 |
| variant | 'default' \| 'compact' | 'default' | 卡片样式变体 |
| onUnlock | function | - | 解锁回调函数 |

### PostContentSmart

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| postId | number | - | 文章ID（必需） |
| initialContent | string | - | 初始内容（必需） |
| postTitle | string | '' | 文章标题 |
| unlockPrice | number | 0 | 单篇解锁价格 |
| requiredMemberLevel | number | 0 | 所需会员等级 |
| isUnlocked | boolean | false | 是否已解锁 |
| paywallVariant | 'default' \| 'compact' | 'default' | 付费墙样式 |

## 样式定制

组件使用Tailwind CSS构建，支持通过CSS变量和类名进行定制：

```css
/* 自定义渐变色 */
.paywall-card {
  background: linear-gradient(135deg, var(--paywall-bg-start), var(--paywall-bg-end));
}

/* 自定义动画时长 */
.paywall-card {
  transition-duration: var(--paywall-transition-duration, 300ms);
}
```

## 演示页面

访问 `/demo/paywall` 查看所有组件的演示效果。

## 开发计划

- [ ] 实现createUnlockOrder GraphQL变更
- [ ] 添加支付流程集成
- [ ] 支持更多自定义选项
- [ ] 添加无障碍访问支持
- [ ] 性能优化和代码分割
