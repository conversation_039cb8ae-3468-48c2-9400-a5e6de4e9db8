'use client';

import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import { toast } from 'react-hot-toast';
import { REACTION_MUTATION, REMOVE_REACTION_MUTATION } from '@/lib/graphql/mutations';
import { ReactionType } from '@/types/Reaction';

interface ReactionButtonProps {
  postId: string; // 全局 ID
  initialCount: number;
  initialState: boolean;
  type: ReactionType;
  icon: JSX.Element;
  activeClass: string;
  inactiveClass: string;
  successMessage: string;
  undoMessage: string;
  variant?: 'default' | 'minimal'; // 新增变体支持
  showCount?: boolean; // 是否显示数量
}

export default function ReactionButton({
  postId,
  initialCount,
  initialState,
  type,
  icon,
  activeClass,
  inactiveClass,
  successMessage,
  undoMessage,
  variant = 'default',
  showCount = true,
}: ReactionButtonProps) {
  const [count, setCount] = useState(initialCount);
  const [isActive, setIsActive] = useState(initialState);
  const [isLoading, setIsLoading] = useState(false);

  const [addReaction] = useMutation(REACTION_MUTATION);
  const [removeReaction] = useMutation(REMOVE_REACTION_MUTATION);

  const handleClick = async () => {
    if (isLoading) return;
    setIsLoading(true);

    const originalCount = count;
    const originalState = isActive;

    // Optimistic update
    setIsActive(!originalState);
    setCount(originalState ? originalCount - 1 : originalCount + 1);

    try {
      const mutation = !originalState ? addReaction : removeReaction;
      await mutation({ variables: { postId, type } });
      toast.success(!originalState ? successMessage : undoMessage);
    } catch (error) {
      toast.error('操作失败，请重试');
      // Rollback
      setIsActive(originalState);
      setCount(originalCount);
    } finally {
      setIsLoading(false);
    }
  };

  const getButtonClass = () => {
    const baseClass = 'reaction-button flex items-center transition-colors';
    const loadingClass = isLoading ? 'cursor-not-allowed opacity-70' : '';
    const stateClass = isActive ? activeClass : inactiveClass;

    if (variant === 'minimal') {
      return `${baseClass} space-x-1 ${stateClass} ${loadingClass}`;
    }

    return `${baseClass} px-4 py-2 rounded-full font-medium ${stateClass} ${loadingClass}`;
  };

  return (
    <button
      onClick={handleClick}
      disabled={isLoading}
      className={getButtonClass()}
    >
      {icon}
      {showCount && (
        <span className={variant === 'minimal' ? 'text-sm' : ''}>{count}</span>
      )}
    </button>
  );
} 