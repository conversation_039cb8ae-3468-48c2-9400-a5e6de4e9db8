'use client';

import React, { useState, useRef } from 'react';
import Link from 'next/link';
import { Post } from '@/types/post';
import { useRoutePrefixes } from '@/hooks';
import { buildPostUrl } from '@/utils/url-builder';

interface RelatedPostsProps {
  posts: Post[];
}

/**
 * 相关文章组件
 * 在文章详情页展示相关文章列表，支持轮播滑动
 */
export default function RelatedPosts({ posts }: RelatedPostsProps) {
  const { prefixes } = useRoutePrefixes();
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollRef = useRef<HTMLDivElement>(null);
  
  // 每行显示4篇文章
  const itemsPerView = 4;
  const maxIndex = Math.max(0, Math.ceil(posts.length / itemsPerView) - 1);
  const showNavigation = posts.length > itemsPerView;

  // 如果没有相关文章，不渲染任何内容
  if (!posts || posts.length === 0) {
    return null;
  }

  // 处理向前滑动
  const handlePrev = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  // 处理向后滑动
  const handleNext = () => {
    if (currentIndex < maxIndex) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  return (
    <div className="related-posts max-w-4xl mx-auto px-4 mb-10">
      <div className="flex justify-between items-center mb-6">
        {/* 标题采用更简洁的左侧装饰线风格 */}
        <h3 className="text-xl font-bold">相关推荐</h3>
      </div>
      
      <div className="relative">
        <div 
          ref={scrollRef}
          className="overflow-hidden"
        >
          <div 
            className="flex -mx-2 transition-transform duration-300 ease-in-out"
            style={{ transform: `translateX(-${currentIndex * 100}%)` }}
          >
            {/* 按每页4篇文章分组 */}
            {Array.from({ length: Math.ceil(posts.length / itemsPerView) }).map((_, groupIndex) => (
              <div 
                key={`group-${groupIndex}`} 
                className="w-full flex-shrink-0 grid grid-cols-2 md:grid-cols-4 gap-x-4 px-2"
              >
                {posts.slice(
                  groupIndex * itemsPerView, 
                  groupIndex * itemsPerView + itemsPerView
                ).map((post) => (
                  <div 
                    key={post.id} 
                    className="group"
                  >
                    {post.shortUuid && (
                      <>
                        {post.featuredImage?.node?.sourceUrl && (
                          <Link href={buildPostUrl(post.shortUuid, post.slug, prefixes)}>
                            <div className="relative aspect-[16/10] w-full overflow-hidden rounded-lg">
                              <img
                                src={post.featuredImage.node.sourceUrl}
                                alt={post.featuredImage.node.altText || post.title}
                                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                              />
                            </div>
                          </Link>
                        )}
                        
                        <Link href={buildPostUrl(post.shortUuid, post.slug, prefixes)}>
                          <h4 className="text-base font-medium mt-2 line-clamp-2 text-gray-800 dark:text-gray-200 group-hover:text-blue-600 dark:hover:text-blue-400">
                            {post.title}
                          </h4>
                        </Link>
                      </>
                    )}
                    {!post.shortUuid && (
                      <>
                        {post.featuredImage?.node?.sourceUrl && (
                          <div className="relative aspect-[16/10] w-full overflow-hidden rounded-lg">
                            <img
                              src={post.featuredImage.node.sourceUrl}
                              alt={post.featuredImage.node.altText || post.title}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        )}
                        <h4 className="text-base font-medium mt-2 line-clamp-2 text-gray-800 dark:text-gray-200">
                          {post.title}
                        </h4>
                      </>
                    )}
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>

        {/* 悬浮导航箭头 */}
        {showNavigation && (
          <>
            <button
              onClick={handlePrev}
              disabled={currentIndex === 0}
              className="absolute top-1/2 -translate-y-1/2 -left-4 w-10 h-10 rounded-full bg-white/80 dark:bg-black/50 shadow-lg flex items-center justify-center transition-opacity hover:bg-white disabled:opacity-0"
              aria-label="上一组"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button
              onClick={handleNext}
              disabled={currentIndex === maxIndex}
              className="absolute top-1/2 -translate-y-1/2 -right-4 w-10 h-10 rounded-full bg-white/80 dark:bg-black/50 shadow-lg flex items-center justify-center transition-opacity hover:bg-white disabled:opacity-0"
              aria-label="下一组"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </>
        )}
      </div>
    </div>
  );
} 