'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { buildSearchUrl } from '@/utils/url-builder';

interface SearchBarProps {
  /** 是否使用紧凑模式 */
  compact?: boolean;
  /** 搜索框默认值 */
  defaultValue?: string;
  /** 搜索框占位文本 */
  placeholder?: string;
  /** 定制样式类名 */
  className?: string;
}

/**
 * 全局搜索栏组件
 * 可以在导航条或页面任何位置使用
 */
export default function SearchBar({
  compact = false,
  defaultValue = '',
  placeholder = '搜索内容...',
  className = '',
}: SearchBarProps) {
  const [searchTerm, setSearchTerm] = useState(defaultValue);
  const router = useRouter();
  
  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      // 生成搜索URL并跳转
      router.push(buildSearchUrl(searchTerm));
    }
  };
  
  // 紧凑模式样式
  if (compact) {
    return (
      <form 
        onSubmit={handleSubmit}
        className={`relative flex items-center ${className}`}
      >
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder={placeholder}
          className="w-48 px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
        />
        <button
          type="submit"
          className="absolute right-1 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 p-1"
          aria-label="搜索"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </button>
      </form>
    );
  }
  
  // 标准模式样式
  return (
    <form 
      onSubmit={handleSubmit}
      className={`relative ${className}`}
    >
      <div className="flex items-center">
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder={placeholder}
          className="w-full px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 transition duration-200"
          aria-label="搜索"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </button>
      </div>
      {searchTerm && (
        <button
          type="button"
          onClick={() => setSearchTerm('')}
          className="absolute right-14 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
          aria-label="清除"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </form>
  );
} 