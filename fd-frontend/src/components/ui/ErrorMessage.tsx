import React from 'react';

interface ErrorMessageProps {
  title: string;
  message: string;
}

/**
 * 错误信息展示组件
 */
export const ErrorMessage: React.FC<ErrorMessageProps> = ({ title, message }) => {
  return (
    <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg p-4 my-4">
      <h2 className="text-lg font-semibold mb-2">{title}</h2>
      <p className="text-sm">{message}</p>
    </div>
  );
}; 