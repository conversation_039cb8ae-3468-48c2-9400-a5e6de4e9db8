'use client';

import React, { useEffect, useState } from 'react';

interface Tab {
  id: string;
  label: string;
  content: string; // 内容的路径或标识
}

interface TabNavigationProps {
  tabs: Tab[];
}

/**
 * 标签页导航组件，用于文档页面
 * 直接加载HTML内容
 */
const TabNavigation: React.FC<TabNavigationProps> = ({ tabs }) => {
  const [activeTab, setActiveTab] = useState<string>(tabs[0]?.id || '');
  const [content, setContent] = useState<string>('加载中...');
  
  useEffect(() => {
    // 加载当前活动标签页的内容
    const loadContent = async () => {
      try {
        const currentTab = tabs.find(tab => tab.id === activeTab);
        if (!currentTab) return;
        
        // 加载内容
        const response = await fetch(currentTab.content);
        
        if (!response.ok) {
          throw new Error(`加载失败: ${response.status}`);
        }
        
        const htmlContent = await response.text();
        setContent(htmlContent);
      } catch (error) {
        console.error('加载内容失败:', error);
        setContent('加载内容时出错，请稍后重试。');
      }
    };
    
    loadContent();
  }, [activeTab, tabs]);
  
  return (
    <div className="tab-navigation">
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                py-4 px-1 border-b-2 font-medium text-sm
                ${
                  activeTab === tab.id
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
              aria-current={activeTab === tab.id ? 'page' : undefined}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>
      
      <div className="py-6">
        {/* 渲染HTML内容 */}
        <div 
          className="content prose max-w-none"
          dangerouslySetInnerHTML={{ __html: content }}
        />
      </div>
    </div>
  );
};

export default TabNavigation; 