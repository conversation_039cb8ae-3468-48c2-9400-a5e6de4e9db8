import React from 'react';

interface PageTitleProps {
  title: string;
  description?: string;
}

const PageTitle: React.FC<PageTitleProps> = ({ title, description }) => {
  return (
    <div className="mb-6">
      <h1 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2">
        {title}
      </h1>
      {description && (
        <p className="text-gray-600 dark:text-gray-400 text-sm md:text-base">
          {description}
        </p>
      )}
    </div>
  );
};

export default PageTitle; 