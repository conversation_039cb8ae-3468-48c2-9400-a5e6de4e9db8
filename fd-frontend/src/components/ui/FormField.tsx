"use client";

import React, { forwardRef, InputHTMLAttributes } from 'react';

export interface FormFieldProps extends InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  description?: string;
}

const FormField = forwardRef<HTMLInputElement, FormFieldProps>(
  ({ label, error, description, className = '', ...props }, ref) => {
    const id = props.id || props.name;
    
    return (
      <div className="mb-4">
        <label 
          htmlFor={id} 
          className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1"
        >
          {label}
        </label>
        
        <input
          ref={ref}
          className={`w-full px-3 py-2 h-10 border ${
            error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
          } rounded-md shadow-sm focus:border-primary-500 focus:ring-1 focus:ring-primary-500 
          bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 ${className}`}
          id={id}
          aria-invalid={error ? "true" : "false"}
          aria-describedby={error ? `${id}-error` : description ? `${id}-description` : undefined}
          {...props}
        />
        
        {description && !error && (
          <p id={`${id}-description`} className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            {description}
          </p>
        )}
        
        {error && (
          <p id={`${id}-error`} className="mt-1 text-xs text-red-500">
            {error}
          </p>
        )}
      </div>
    );
  }
);

FormField.displayName = 'FormField';

export default FormField; 