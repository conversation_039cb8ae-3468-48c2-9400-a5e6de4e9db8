"use client";

import React from 'react';

interface FormErrorProps {
  message?: string;
  errors?: string[] | string;
  className?: string;
}

const FormError: React.FC<FormErrorProps> = ({ 
  message, 
  errors,
  className = ''
}) => {
  if (!message && (!errors || (Array.isArray(errors) && errors.length === 0))) {
    return null;
  }

  const errorMessage = message || 
    (typeof errors === 'string' ? errors : errors && errors[0]);

  return (
    <div 
      className={`bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4 dark:bg-red-900/30 dark:border-red-800 dark:text-red-400 ${className}`}
      role="alert"
    >
      <div className="flex items-start">
        <div className="flex-shrink-0 mt-0.5">
          <svg 
            className="h-5 w-5 text-red-500" 
            xmlns="http://www.w3.org/2000/svg" 
            viewBox="0 0 20 20" 
            fill="currentColor" 
            aria-hidden="true"
          >
            <path 
              fillRule="evenodd" 
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" 
              clipRule="evenodd" 
            />
          </svg>
        </div>
        <div className="ml-3">
          <p className="text-sm">
            {errorMessage}
          </p>
          {Array.isArray(errors) && errors.length > 1 && (
            <ul className="mt-1 list-disc list-inside text-xs">
              {errors.slice(1).map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
};

export default FormError; 