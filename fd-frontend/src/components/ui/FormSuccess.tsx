"use client";

import React from 'react';

interface FormSuccessProps {
  message?: string;
  className?: string;
}

const FormSuccess: React.FC<FormSuccessProps> = ({ 
  message,
  className = ''
}) => {
  if (!message) return null;

  return (
    <div 
      className={`bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative mb-4 dark:bg-green-900/30 dark:border-green-800 dark:text-green-400 ${className}`}
      role="alert"
    >
      <div className="flex items-start">
        <div className="flex-shrink-0 mt-0.5">
          <svg 
            className="h-5 w-5 text-green-500" 
            xmlns="http://www.w3.org/2000/svg" 
            viewBox="0 0 20 20" 
            fill="currentColor" 
            aria-hidden="true"
          >
            <path 
              fillRule="evenodd" 
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" 
              clipRule="evenodd" 
            />
          </svg>
        </div>
        <div className="ml-3">
          <p className="text-sm">
            {message}
          </p>
        </div>
      </div>
    </div>
  );
};

export default FormSuccess; 