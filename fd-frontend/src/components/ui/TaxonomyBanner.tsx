'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface BreadcrumbItem {
  label: string;
  href: string;
}

interface TaxonomyBannerProps {
  title: string;
  description?: string;
  count?: number;
  bannerImageUrl?: string;
  bannerImage?: {
    sourceUrl: string;
    altText?: string;
    mediaDetails?: {
      width: number;
      height: number;
    };
  };
  breadcrumbs: BreadcrumbItem[];
  gradientColors?: string;
  titlePrefix?: string;
  icon?: React.ReactNode;
}

export default function TaxonomyBanner({
  title,
  description,
  count,
  bannerImageUrl,
  bannerImage,
  breadcrumbs,
  gradientColors = 'from-blue-600 via-purple-600 to-indigo-700',
  titlePrefix,
  icon
}: TaxonomyBannerProps) {
  const bannerUrl = bannerImageUrl || bannerImage?.sourceUrl;
  const altText = bannerImage?.altText || `${title} Banner`;

  return (
    <div className="relative">
      {/* Banner 图片背景 */}
      {bannerUrl && (
        <div className="relative h-80 md:h-96 lg:h-[28rem] overflow-hidden group">
          <Image
            src={bannerUrl}
            alt={altText}
            fill
            className="object-cover transition-transform duration-700 group-hover:scale-105"
            sizes="100vw"
            priority
          />
          {/* 渐变遮罩 - 增强底部遮罩确保文字可读性 */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-transparent" />
          {/* 额外的底部遮罩确保文字区域有足够对比度 */}
          <div className="absolute bottom-0 left-0 right-0 h-1/2 bg-gradient-to-t from-black/60 to-transparent" />
          {/* 动态光效 */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
        </div>
      )}
      
      {/* 内容覆盖层 */}
      <div className={`${bannerUrl ? 'absolute inset-0' : `relative bg-gradient-to-br ${gradientColors}`} flex items-end overflow-hidden`}>
        {/* 背景装饰元素 */}
        {!bannerUrl && (
          <>
            <div className="absolute top-10 right-10 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse" />
            <div className="absolute bottom-20 left-20 w-24 h-24 bg-white/5 rounded-full blur-lg animate-pulse delay-1000" />
            <div className="absolute top-1/2 right-1/4 w-16 h-16 bg-white/10 rounded-full blur-md animate-pulse delay-500" />
          </>
        )}

        <div className="container mx-auto px-4 pb-12 pt-8 relative z-10">
          <div className="max-w-4xl">
            {/* 面包屑导航 */}
            <nav className="mb-6">
              <div className="flex items-center space-x-2 text-sm">
                {breadcrumbs.map((item, index) => (
                  <React.Fragment key={index}>
                    {index > 0 && (
                      <span className={`${bannerUrl ? 'text-white/60' : 'text-white/60'}`}>›</span>
                    )}
                    {index === breadcrumbs.length - 1 ? (
                      <span className={`${bannerUrl ? 'text-white' : 'text-white'} font-medium`}>
                        {item.label}
                      </span>
                    ) : (
                      <Link 
                        href={item.href}
                        className={`${bannerUrl ? 'text-white/80 hover:text-white' : 'text-white/80 hover:text-white'} transition-colors`}
                      >
                        {item.label}
                      </Link>
                    )}
                  </React.Fragment>
                ))}
              </div>
            </nav>

            {/* 标题区域 */}
            <div className="mb-6 animate-fade-in-up">
              <div className="flex items-center mb-4">
                {titlePrefix && (
                  <span className={`text-3xl md:text-4xl lg:text-5xl font-bold mr-3 ${bannerUrl ? 'text-white drop-shadow-lg' : 'text-white'} animate-fade-in-up delay-100`}>
                    {titlePrefix}
                  </span>
                )}
                {icon && (
                  <div className={`mr-4 ${bannerUrl ? 'text-white' : 'text-white'} animate-fade-in-up delay-200`}>
                    {icon}
                  </div>
                )}
                <h1 className={`text-4xl md:text-5xl lg:text-6xl font-bold ${bannerUrl ? 'text-white drop-shadow-lg' : 'text-white'} leading-tight animate-fade-in-up delay-300`}>
                  {title}
                </h1>
              </div>
              
              {description && (
                <div
                  className={`text-lg md:text-xl leading-relaxed mb-6 ${bannerUrl ? 'text-white/90 drop-shadow-md' : 'text-white/90'} max-w-3xl animate-fade-in-up delay-500`}
                  dangerouslySetInnerHTML={{ __html: description }}
                />
              )}

              {/* 统计信息 */}
              {count !== undefined && (
                <div className="flex items-center space-x-6 animate-fade-in-up delay-700">
                  <div className={`flex items-center space-x-2 ${bannerUrl ? 'text-white/80' : 'text-white/80'} bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20`}>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm font-medium">
                      {count} 篇文章
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
