'use client';

import React, { HTMLAttributes, ReactNode } from 'react';
import { useVITheme } from '@/contexts/VIThemeContext';
import { twMerge } from 'tailwind-merge';

// 定义卡片组件属性接口
export interface CardProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'flat';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  hover?: boolean;
  className?: string;
}

// 卡片头部组件属性接口
export interface CardHeaderProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  className?: string;
}

// 卡片内容组件属性接口
export interface CardContentProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  className?: string;
}

// 卡片底部组件属性接口
export interface CardFooterProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  className?: string;
}

// 卡片组件
const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ children, variant = 'default', padding = 'md', hover = false, className, ...props }, ref) => {
    const { getRadius, getShadow } = useVITheme();
    
    // 基础样式
    const baseStyle = 'overflow-hidden bg-white dark:bg-gray-800 transition-all duration-200';
    
    // 变体样式
    const variantStyles = {
      default: `shadow-sm`,
      elevated: `shadow-md`,
      outlined: 'border border-gray-200 dark:border-gray-700',
      flat: '',
    };
    
    // 内边距样式
    const paddingStyles = {
      none: '',
      sm: 'p-3',
      md: 'p-4',
      lg: 'p-6',
    };
    
    // 悬停效果
    const hoverStyle = hover ? 'hover:-translate-y-1 hover:shadow-md' : '';

    // 获取动态圆角和阴影
    const borderRadius = `rounded-[${getRadius('medium')}]`;
    
    // 根据variant设置阴影
    let shadowStyle = '';
    if (variant === 'default') {
      shadowStyle = `shadow-[${getShadow('small')}]`;
    } else if (variant === 'elevated') {
      shadowStyle = `shadow-[${getShadow('medium')}]`;
    }
    
    // 合并所有样式
    const cardStyle = twMerge(
      baseStyle,
      variantStyles[variant],
      paddingStyles[padding],
      borderRadius,
      shadowStyle,
      hoverStyle,
      className
    );
    
    return (
      <div ref={ref} className={cardStyle} {...props}>
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card';

// 卡片头部组件
const CardHeader = ({ children, className, ...props }: CardHeaderProps) => {
  return (
    <div
      className={twMerge(
        'px-6 py-4 border-b border-gray-200 dark:border-gray-700 font-medium text-lg',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

CardHeader.displayName = 'CardHeader';

// 卡片内容组件
const CardContent = ({ children, className, ...props }: CardContentProps) => {
  return (
    <div className={twMerge('p-6', className)} {...props}>
      {children}
    </div>
  );
};

CardContent.displayName = 'CardContent';

// 卡片底部组件
const CardFooter = ({ children, className, ...props }: CardFooterProps) => {
  return (
    <div
      className={twMerge(
        'px-6 py-4 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

CardFooter.displayName = 'CardFooter';

export { Card, CardHeader, CardContent, CardFooter }; 