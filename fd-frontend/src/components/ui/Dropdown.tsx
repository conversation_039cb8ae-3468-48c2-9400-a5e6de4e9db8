'use client';

import React, { useState, useRef, useEffect } from 'react';
import { twMerge } from 'tailwind-merge';
import { useVITheme } from '@/contexts/VIThemeContext';

export interface DropdownItem {
  id: string | number;
  label: React.ReactNode;
  onClick?: () => void;
  href?: string;
  icon?: React.ReactNode;
  disabled?: boolean;
  divider?: boolean;
  danger?: boolean;
}

export interface DropdownProps {
  trigger: React.ReactNode;
  items: DropdownItem[];
  placement?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
  triggerType?: 'click' | 'hover';
  className?: string;
  dropdownClassName?: string;
  itemClassName?: string;
  disabled?: boolean;
  showArrow?: boolean;
  width?: number | string;
}

/**
 * 下拉菜单组件 - 提供多种选项的下拉列表
 */
const Dropdown: React.FC<DropdownProps> = ({
  trigger,
  items,
  placement = 'bottom-left',
  triggerType = 'click',
  className,
  dropdownClassName,
  itemClassName,
  disabled = false,
  showArrow = true,
  width,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { getRadius } = useVITheme();

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // 处理点击触发器
  const handleTriggerClick = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  // 处理鼠标进入触发器
  const handleTriggerMouseEnter = () => {
    if (!disabled && triggerType === 'hover') {
      setIsOpen(true);
    }
  };

  // 处理鼠标离开触发器和下拉菜单
  const handleMouseLeave = () => {
    if (triggerType === 'hover') {
      setIsOpen(false);
    }
  };

  // 处理项目点击
  const handleItemClick = (item: DropdownItem) => {
    if (!item.disabled && item.onClick) {
      item.onClick();
      setIsOpen(false);
    }
  };

  // 根据放置位置确定样式
  const getPlacementStyles = () => {
    switch (placement) {
      case 'bottom-right':
        return 'bottom-full right-0';
      case 'top-left':
        return 'top-full left-0';
      case 'top-right':
        return 'top-full right-0';
      case 'bottom-left':
      default:
        return 'bottom-full left-0';
    }
  };

  // 触发器事件处理
  const triggerProps = {
    onClick: triggerType === 'click' ? handleTriggerClick : undefined,
    onMouseEnter: handleTriggerMouseEnter,
    onMouseLeave: triggerType === 'hover' ? handleMouseLeave : undefined,
    className: twMerge(
      'cursor-pointer select-none',
      disabled && 'opacity-50 cursor-not-allowed',
      className
    ),
  };

  return (
    <div 
      ref={dropdownRef} 
      className="relative inline-block"
      onMouseLeave={triggerType === 'hover' ? handleMouseLeave : undefined}
    >
      {/* 触发器 */}
      <div {...triggerProps}>
        {trigger}
      </div>

      {/* 下拉菜单 */}
      {isOpen && !disabled && (
        <div
          className={twMerge(
            'absolute z-40 transform translate-y-2 min-w-[180px]',
            getPlacementStyles(),
            'bg-white dark:bg-gray-800 shadow-lg rounded border border-gray-200 dark:border-gray-700 py-1',
            dropdownClassName
          )}
          style={{ 
            borderRadius: getRadius('small'),
            width: width ? (typeof width === 'number' ? `${width}px` : width) : 'auto'
          }}
        >
          {/* 箭头 */}
          {showArrow && (
            <div
              className={`absolute w-3 h-3 bg-white dark:bg-gray-800 transform rotate-45 border ${
                placement.startsWith('bottom') ? 'border-t border-l -top-1.5' : 'border-b border-r -bottom-1.5'
              } ${placement.endsWith('left') ? 'left-3' : 'right-3'} dark:border-gray-700`}
            />
          )}

          {/* 菜单项目 */}
          <div className="relative z-10 py-1">
            {items.map((item, index) => (
              <React.Fragment key={item.id}>
                {item.divider ? (
                  <div className="border-t border-gray-200 dark:border-gray-700 my-1" />
                ) : (
                  <div
                    className={twMerge(
                      'px-4 py-2 text-sm flex items-center cursor-pointer',
                      item.disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700',
                      item.danger && 'text-red-600 dark:text-red-400',
                      itemClassName
                    )}
                    onClick={() => !item.disabled && handleItemClick(item)}
                    role="menuitem"
                  >
                    {item.icon && <span className="mr-2 w-5 h-5">{item.icon}</span>}
                    {item.href ? (
                      <a
                        href={item.disabled ? undefined : item.href}
                        className="block w-full text-left"
                        onClick={(e) => item.disabled && e.preventDefault()}
                      >
                        {item.label}
                      </a>
                    ) : (
                      item.label
                    )}
                  </div>
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Dropdown; 