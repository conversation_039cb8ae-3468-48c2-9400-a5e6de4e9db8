'use client';

import React, { Fragment, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { twMerge } from 'tailwind-merge';
import { useVITheme } from '@/contexts/VIThemeContext';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: React.ReactNode;
  footer?: React.ReactNode;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closeOnClickOutside?: boolean;
  closeOnEsc?: boolean;
  showCloseButton?: boolean;
  className?: string;
  modalClassName?: string;
  headerClassName?: string;
  bodyClassName?: string;
  footerClassName?: string;
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  footer,
  children,
  size = 'md',
  closeOnClickOutside = true,
  closeOnEsc = true,
  showCloseButton = true,
  className,
  modalClassName,
  headerClassName,
  bodyClassName,
  footerClassName,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const { getRadius } = useVITheme();
  
  // 按ESC键关闭模态框
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (closeOnEsc && event.key === 'Escape' && isOpen) {
        onClose();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [closeOnEsc, isOpen, onClose]);
  
  // 禁用背景滚动
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);
  
  // 点击外部区域关闭模态框
  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (closeOnClickOutside && modalRef.current && !modalRef.current.contains(e.target as Node)) {
      onClose();
    }
  };
  
  // 根据size确定宽度类名
  const sizeClassNames = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    full: 'max-w-full'
  };
  
  // 如果模态框未打开，不渲染任何内容
  if (!isOpen) return null;
  
  // 使用createPortal将模态框渲染到body末尾，避免z-index问题
  return createPortal(
    <div
      className={twMerge(
        'fixed inset-0 z-50 flex items-center justify-center overflow-y-auto bg-black bg-opacity-50 transition-opacity',
        className
      )}
      onClick={handleBackdropClick}
      aria-modal="true"
      role="dialog"
      aria-labelledby="modal-title"
    >
      <div
        ref={modalRef}
        className={twMerge(
          `w-full ${sizeClassNames[size]} mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-xl transform transition-all`,
          modalClassName
        )}
        style={{ borderRadius: getRadius('medium') }}
      >
        {/* 模态框头部 */}
        {(title || showCloseButton) && (
          <div
            className={twMerge(
              'px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between',
              headerClassName
            )}
          >
            {title && (
              <h3 id="modal-title" className="text-lg font-medium text-gray-900 dark:text-white">
                {title}
              </h3>
            )}
            {showCloseButton && (
              <button
                type="button"
                className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 focus:outline-none"
                onClick={onClose}
                aria-label="关闭"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        )}
        
        {/* 模态框内容 */}
        <div
          className={twMerge(
            'px-6 py-4 max-h-[calc(100vh-200px)] overflow-y-auto',
            bodyClassName
          )}
        >
          {children}
        </div>
        
        {/* 模态框底部 */}
        {footer && (
          <div
            className={twMerge(
              'px-6 py-4 border-t border-gray-200 dark:border-gray-700',
              footerClassName
            )}
          >
            {footer}
          </div>
        )}
      </div>
    </div>,
    document.body
  );
};

export default Modal; 