'use client';

import React from 'react';
import Modal from './Modal';
import Button from './Button';

export interface DialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string | React.ReactNode;
  confirmLabel?: string;
  cancelLabel?: string;
  onConfirm: () => void;
  onCancel?: () => void;
  type?: 'info' | 'warning' | 'danger' | 'success';
  size?: 'sm' | 'md' | 'lg';
  confirmDisabled?: boolean;
  loading?: boolean;
}

/**
 * 对话框组件 - 用于显示确认/取消操作的对话框
 */
const Dialog: React.FC<DialogProps> = ({
  isOpen,
  onClose,
  title,
  message,
  confirmLabel = '确认',
  cancelLabel = '取消',
  onConfirm,
  onCancel,
  type = 'info',
  size = 'sm',
  confirmDisabled = false,
  loading = false,
}) => {
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    onClose();
  };

  const handleConfirm = () => {
    onConfirm();
    // 注意：根据需求，可能不会立即关闭对话框，特别是在异步操作的情况下
    if (!loading) {
      onClose();
    }
  };

  // 根据类型设置不同的颜色
  const getTypeColor = () => {
    switch (type) {
      case 'warning':
        return 'warning';
      case 'danger':
        return 'error';
      case 'success':
        return 'success';
      case 'info':
      default:
        return 'primary';
    }
  };

  // 对话框图标
  const getTypeIcon = () => {
    switch (type) {
      case 'warning':
        return (
          <svg className="w-12 h-12 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        );
      case 'danger':
        return (
          <svg className="w-12 h-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'success':
        return (
          <svg className="w-12 h-12 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'info':
      default:
        return (
          <svg className="w-12 h-12 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  // 对话框内容
  const dialogContent = (
    <div className="flex flex-col items-center">
      {getTypeIcon()}
      <div className="mt-4 text-center">
        {typeof message === 'string' ? <p className="text-gray-700 dark:text-gray-300">{message}</p> : message}
      </div>
    </div>
  );

  // 对话框底部按钮
  const dialogFooter = (
    <div className="flex justify-end space-x-4">
      <Button variant="outline" onClick={handleCancel} disabled={loading}>
        {cancelLabel}
      </Button>
      <Button 
        variant="primary" 
        color={getTypeColor()} 
        onClick={handleConfirm} 
        disabled={confirmDisabled || loading}
        isLoading={loading}
      >
        {confirmLabel}
      </Button>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size={size}
      closeOnClickOutside={!loading}
      closeOnEsc={!loading}
      showCloseButton={!loading}
      footer={dialogFooter}
    >
      {dialogContent}
    </Modal>
  );
};

export default Dialog; 