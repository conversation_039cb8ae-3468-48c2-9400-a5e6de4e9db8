'use client';

import React, { ButtonHTMLAttributes, ReactNode } from 'react';
import { useVITheme } from '@/contexts/VIThemeContext';
import { twMerge } from 'tailwind-merge';

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'success' | 'error';
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
  isLoading?: boolean;
  loading?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  color,
  size = 'md',
  fullWidth = false,
  icon,
  iconPosition = 'left',
  isLoading = false,
  loading = false,
  className,
  disabled = false,
  ...props
}) => {
  const { getRadius, getColor } = useVITheme();
  
  // 获取VI颜色
  const primaryColor = getColor('primary');
  const secondaryColor = getColor('secondary');
  const successColor = getColor('success');
  const errorColor = getColor('error');
  
  // 按钮基础样式
  const baseStyles = 'inline-flex items-center justify-center font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  // 大小样式
  const sizeStyles = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };
  
  // 禁用样式
  const disabledStyles = {
    primary: 'opacity-50 cursor-not-allowed bg-gray-400 dark:bg-gray-600 text-white',
    secondary: 'opacity-50 cursor-not-allowed bg-gray-400 dark:bg-gray-600 text-white',
    outline: 'opacity-50 cursor-not-allowed bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400 border-gray-300 dark:border-gray-700',
    ghost: 'opacity-50 cursor-not-allowed text-gray-500 dark:text-gray-400',
    success: 'opacity-50 cursor-not-allowed bg-gray-400 dark:bg-gray-600 text-white',
    error: 'opacity-50 cursor-not-allowed bg-gray-400 dark:bg-gray-600 text-white',
  };
  
  // 变体样式
  const variantStyles = {
    primary: `bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 ${disabled ? disabledStyles.primary : ''}`,
    secondary: `bg-secondary-500 text-white hover:bg-opacity-90 focus:ring-secondary-500 ${disabled ? disabledStyles.secondary : ''}`,
    outline: `border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800 focus:ring-primary-500 ${disabled ? disabledStyles.outline : ''}`,
    ghost: `bg-transparent text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800 focus:ring-primary-500 ${disabled ? disabledStyles.ghost : ''}`,
    success: `bg-success-main text-white hover:bg-opacity-90 focus:ring-success-main ${disabled ? disabledStyles.success : ''}`,
    error: `bg-error-main text-white hover:bg-opacity-90 focus:ring-error-main ${disabled ? disabledStyles.error : ''}`,
  };
  
  // 宽度样式
  const widthStyles = fullWidth ? 'w-full' : '';
  
  // 圆角样式，使用VI设置
  const borderRadiusStyle = `rounded-[${getRadius('medium')}]`;
  
  // 合并所有样式
  const buttonStyles = twMerge(
    baseStyles,
    sizeStyles[size],
    variantStyles[variant],
    widthStyles,
    borderRadiusStyle,
    className
  );
  
  // 加载状态指示器
  const LoadingSpinner = () => (
    <svg 
      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" 
      xmlns="http://www.w3.org/2000/svg" 
      fill="none" 
      viewBox="0 0 24 24"
    >
      <circle 
        className="opacity-25" 
        cx="12" 
        cy="12" 
        r="10" 
        stroke="currentColor" 
        strokeWidth="4"
      ></circle>
      <path 
        className="opacity-75" 
        fill="currentColor" 
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
  );
  
  return (
    <button className={buttonStyles} disabled={disabled || isLoading || loading} {...props}>
      {(isLoading || loading) && <LoadingSpinner />}
      {icon && iconPosition === 'left' && !isLoading && !loading && (
        <span className="mr-2">{icon}</span>
      )}
      {children}
      {icon && iconPosition === 'right' && (
        <span className="ml-2">{icon}</span>
      )}
    </button>
  );
};

export default Button; 