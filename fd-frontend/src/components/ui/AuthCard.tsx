"use client";

import React, { ReactNode } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Footer } from './Card';
import Link from 'next/link';

interface AuthCardProps {
  children: ReactNode;
  title: string;
  description?: string;
  footer?: ReactNode;
  backLink?: {
    label: string;
    href: string;
  };
  backLinkText?: string;
}

const AuthCard: React.FC<AuthCardProps> = ({
  children,
  title,
  description,
  footer,
  backLink,
  backLinkText = '返回'
}) => {
  return (
    <div className="flex items-center justify-center min-h-[70vh] px-4 py-8">
      <div className="w-full max-w-md">
        {backLink && (
          <div className="mb-4">
            <Link
              href={backLink.href}
              className="text-sm text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 flex items-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                className="w-4 h-4 mr-1"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
              {backLink.label || backLinkText}
            </Link>
          </div>
        )}
        
        <Card variant="elevated" className="w-full">
          <CardHeader className="text-center">
            <h2 className="text-xl font-bold text-gray-800 dark:text-white">
              {title}
            </h2>
            {description && (
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {description}
              </p>
            )}
          </CardHeader>
          
          <CardContent>
            {children}
          </CardContent>
          
          {footer && (
            <CardFooter className="text-center text-sm">
              {footer}
            </CardFooter>
          )}
        </Card>
      </div>
    </div>
  );
};

export default AuthCard; 