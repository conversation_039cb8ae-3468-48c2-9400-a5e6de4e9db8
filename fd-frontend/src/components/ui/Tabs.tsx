"use client";

import React, { useState } from 'react';

interface TabsProps {
  tabs: {
    id: string;
    label: string;
    icon?: React.ReactNode;
    content: React.ReactNode;
  }[];
  defaultTab?: string;
  fullWidth?: boolean;
  renderInactive?: boolean;
}

const Tabs: React.FC<TabsProps> = ({ tabs, defaultTab, fullWidth = true, renderInactive = true }) => {
  const [activeTab, setActiveTab] = useState(defaultTab || (tabs.length > 0 ? tabs[0].id : ''));

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
  };

  return (
    <div className="w-full">
      <div className="border-b border-gray-200 dark:border-gray-700 mb-4">
        <nav className={`flex ${fullWidth ? 'divide-x divide-gray-200 dark:divide-gray-700' : 'space-x-2'}`} aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => handleTabClick(tab.id)}
              className={`${fullWidth ? 'flex-1' : ''} py-3 px-4 text-sm font-medium transition-colors duration-200 flex items-center justify-center ${
                activeTab === tab.id
                  ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-300 border-b-2 border-primary-600 dark:border-primary-400'
                  : 'text-gray-600 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800'
              }`}
              aria-current={activeTab === tab.id ? 'page' : undefined}
            >
              {tab.icon && <span className="mr-2">{tab.icon}</span>}
              {tab.label}
            </button>
          ))}
        </nav>
      </div>
      <div>
        {tabs.map((tab) => (
          <div key={tab.id} className={activeTab === tab.id ? 'block' : 'hidden'}>
            {(activeTab === tab.id || renderInactive) && tab.content}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Tabs; 