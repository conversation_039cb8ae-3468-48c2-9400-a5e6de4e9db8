'use client';

import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { HiChevronLeft, HiChevronRight } from 'react-icons/hi';
import { ExtractedImage } from '@/utils/image-utils';

interface ImageCarouselProps {
  images: ExtractedImage[];
  className?: string;
  showThumbnails?: boolean;
  showCounter?: boolean;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  onImageClick?: (index: number) => void;
}

const ImageCarousel: React.FC<ImageCarouselProps> = ({
  images,
  className = '',
  showThumbnails = false,
  showCounter = true,
  autoPlay = false,
  autoPlayInterval = 5000,
  onImageClick,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);

  // 如果没有图片，不渲染
  if (!images || images.length === 0) {
    return null;
  }

  const totalImages = images.length;
  const showNavigation = totalImages > 1;

  // 自动播放逻辑
  useEffect(() => {
    if (autoPlay && showNavigation) {
      autoPlayRef.current = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % totalImages);
      }, autoPlayInterval);

      return () => {
        if (autoPlayRef.current) {
          clearInterval(autoPlayRef.current);
        }
      };
    }
  }, [autoPlay, autoPlayInterval, totalImages, showNavigation]);

  // 暂停自动播放
  const pauseAutoPlay = () => {
    if (autoPlayRef.current) {
      clearInterval(autoPlayRef.current);
      autoPlayRef.current = null;
    }
  };

  // 恢复自动播放
  const resumeAutoPlay = () => {
    if (autoPlay && showNavigation && !autoPlayRef.current) {
      autoPlayRef.current = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % totalImages);
      }, autoPlayInterval);
    }
  };

  const goToPrevious = () => {
    pauseAutoPlay();
    setCurrentIndex((prev) => (prev - 1 + totalImages) % totalImages);
  };

  const goToNext = () => {
    pauseAutoPlay();
    setCurrentIndex((prev) => (prev + 1) % totalImages);
  };

  const goToSlide = (index: number) => {
    pauseAutoPlay();
    setCurrentIndex(index);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageClick = () => {
    if (onImageClick) {
      onImageClick(currentIndex);
    }
  };

  const currentImage = images[currentIndex];

  return (
    <div 
      className={`relative w-full h-full bg-gray-100 ${className}`}
      onMouseEnter={pauseAutoPlay}
      onMouseLeave={resumeAutoPlay}
    >
      {/* 主图片区域 */}
      <div className="relative w-full h-full overflow-hidden">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-200">
            <div className="w-8 h-8 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
        
        <div 
          className="relative w-full h-full cursor-pointer"
          onClick={handleImageClick}
        >
          <Image
            src={currentImage.src}
            alt={currentImage.alt || `图片 ${currentIndex + 1}`}
            fill
            className="object-cover transition-opacity duration-300"
            onLoad={handleImageLoad}
            sizes="(max-width: 768px) 100vw, 50vw"
            priority={currentIndex === 0}
          />
        </div>

        {/* 导航按钮 */}
        {showNavigation && (
          <>
            <button
              onClick={goToPrevious}
              className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors z-10"
              aria-label="上一张图片"
            >
              <HiChevronLeft className="w-5 h-5" />
            </button>
            
            <button
              onClick={goToNext}
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors z-10"
              aria-label="下一张图片"
            >
              <HiChevronRight className="w-5 h-5" />
            </button>
          </>
        )}

        {/* 图片计数器 */}
        {showCounter && showNavigation && (
          <div className="absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm z-10">
            {currentIndex + 1} / {totalImages}
          </div>
        )}

        {/* 指示点 */}
        {showNavigation && (
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 z-10">
            {images.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentIndex 
                    ? 'bg-white' 
                    : 'bg-white/50 hover:bg-white/75'
                }`}
                aria-label={`跳转到第 ${index + 1} 张图片`}
              />
            ))}
          </div>
        )}
      </div>

      {/* 缩略图 */}
      {showThumbnails && showNavigation && (
        <div className="absolute bottom-0 left-0 right-0 bg-black/80 p-2">
          <div className="flex space-x-2 overflow-x-auto">
            {images.map((image, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`relative flex-shrink-0 w-16 h-12 rounded overflow-hidden border-2 transition-colors ${
                  index === currentIndex 
                    ? 'border-white' 
                    : 'border-transparent hover:border-white/50'
                }`}
              >
                <Image
                  src={image.src}
                  alt={image.alt || `缩略图 ${index + 1}`}
                  fill
                  className="object-cover"
                  sizes="64px"
                />
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageCarousel;
