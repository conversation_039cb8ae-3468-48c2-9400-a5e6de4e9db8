import React from 'react';
import Link from 'next/link';
import { buildCustomPostUrl } from '@/utils/url-builder';
import { RoutePrefixes } from '@/types/routes';

/**
 * 自定义内容类型卡片组件接口
 */
interface CustomPostCardProps {
  post: {
    id: string;
    title: string;
    slug: string;
    excerpt?: string;
    date: string;
    featuredImage?: {
      node: {
        sourceUrl: string;
        altText?: string;
      }
    };
    databaseId?: number;
    shortUuid?: string;
  };
  type: string;
  prefixes: RoutePrefixes;
}

/**
 * 自定义内容类型卡片组件
 * 用于显示自定义内容类型的列表项
 */
const CustomPostCard: React.FC<CustomPostCardProps> = ({ post, type, prefixes }) => {
  if (!post) return null;
  
  const uuid = post.shortUuid || post.databaseId?.toString() || '';
  
  // 使用URL构建函数生成链接
  const postUrl = buildCustomPostUrl(type, uuid, post.slug, prefixes);
  
  return (
    <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300">
      <Link href={postUrl}>
        <div className="p-4">
          {post.featuredImage?.node?.sourceUrl && (
            <div className="mb-4">
              <img 
                src={post.featuredImage.node.sourceUrl}
                alt={post.featuredImage.node.altText || post.title}
                className="w-full h-48 object-cover rounded-md"
              />
            </div>
          )}
          
          <h3 className="text-xl font-semibold mb-2 text-gray-800 dark:text-white">
            {post.title}
          </h3>
          
          {post.excerpt && (
            <div 
              className="text-gray-600 dark:text-gray-300 mb-3 line-clamp-3"
              dangerouslySetInnerHTML={{ __html: post.excerpt }}
            />
          )}
          
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {new Date(post.date).toLocaleDateString('zh-CN')}
          </div>
        </div>
      </Link>
    </div>
  );
};

export default CustomPostCard; 