import React from 'react';
import ReactionButton from '../post/ReactionButton';
import PostContentSmart from '@/components/post/PostContentSmart';
import { ReactionType } from '@/types/Reaction';
import { Post as PostType } from '@/types/post';
import { HiOutlineHeart, HiOutlineBookmark, HiOutlineThumbUp } from 'react-icons/hi';

/**
 * 封面文章模板组件
 * 显示文章内容，带有全屏封面图片和覆盖式标题
 */
const CoverTemplate: React.FC<{ post: PostType; paywallVariant?: 'default' | 'compact'; }> = ({ post, paywallVariant = 'default' }) => {
  const LikeIcon = (
    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
      <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd"></path>
    </svg>
  );
  const BookmarkIcon = (
    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
      <path d="M10 2a6 6 0 00-6 6c0 7 3.5 9 5 9s5-2 5-9a6 6 0 00-6-6z"></path>
    </svg>
  );
  const RecommendIcon = (
    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
      <path d="M10 2a6 6 0 00-6 6c0 7 3.5 9 5 9s5-2 5-9a6 6 0 00-6-6z"></path>
    </svg>
  );

  return (
    <article className="article-container mx-auto">
      {/* 全宽封面图 */}
      {post.featuredImage?.node?.sourceUrl && (
        <div className="cover-image w-full h-[60vh] relative mb-10">
          <div className="absolute inset-0">
            <img 
              src={post.featuredImage.node.sourceUrl} 
              alt={post.featuredImage.node.altText || post.title} 
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-40"></div>
          </div>
          <div className="absolute inset-0 flex flex-col justify-end p-8 text-white">
            <h1 className="text-4xl md:text-5xl font-bold mb-3">{post.title}</h1>
            {post.postTemplate?.subtitle && (
              <h2 className="text-xl md:text-2xl mb-6 font-light">{post.postTemplate.subtitle}</h2>
            )}
            <div className="flex items-center">
              <span className="mr-4">
                {new Date(post.date).toLocaleDateString('zh-CN')}
              </span>
              {post.postTemplate?.articleAuthor ? (
                <span>{post.postTemplate.articleAuthor}</span>
              ) : post.author?.node && (
                <span>{post.author.node.name}</span>
              )}
            </div>
          </div>
        </div>
      )}
      
      <div className="max-w-4xl mx-auto px-4">
        {/* 文章UUID信息 */}
        {post.shortUuid && (
          <div className="uuid-info text-xs text-gray-500 mb-2">
            文章ID: {post.shortUuid}
          </div>
        )}
        
        {/* 文章头部信息 - 不包括标题，因为已在封面显示 */}
        <div className="article-meta mb-8">
          {/* 来源信息 */}
          {post.postTemplate?.articleSource && (
            <div className="source mb-2 text-gray-600">
              来源: {post.postTemplate.articleSource}
            </div>
          )}
          
          {/* 分类 */}
          {post.categories?.nodes && post.categories.nodes.length > 0 && (
            <div className="categories mb-2">
              <span className="mr-2">分类:</span>
              {post.categories.nodes.map((category) => (
                <span key={category.id} className="mr-2 bg-gray-100 px-2 py-1 rounded text-sm">
                  {category.name}
                </span>
              ))}
            </div>
          )}
          
          {/* 标签 */}
          {post.tags?.nodes && post.tags.nodes.length > 0 && (
            <div className="tags">
              <span className="mr-2">标签:</span>
              {post.tags.nodes.map((tag) => (
                <span key={tag.id} className="mr-2 bg-gray-100 px-2 py-1 rounded text-sm">
                  {tag.name}
                </span>
              ))}
            </div>
          )}
        </div>

        {/* 点赞与推荐按钮容器 */}
        <div className="flex items-center space-x-6 mt-8 pt-6 border-t border-gray-200">
          <ReactionButton
            postId={post.id}
            initialCount={post.likeCount || 0}
            initialState={post.userHasLiked || false}
            type={ReactionType.Like}
            icon={<HiOutlineHeart className="w-5 h-5 mr-2" />}
            activeClass="bg-rose-500 text-white hover:bg-rose-600"
            inactiveClass="bg-gray-100 text-gray-700 hover:bg-gray-200"
            successMessage="感谢点赞！"
            undoMessage="已取消点赞"
          />
          <ReactionButton
            postId={post.id}
            initialCount={post.bookmarkCount || 0}
            initialState={post.userHasBookmarked || false}
            type={ReactionType.Bookmark}
            icon={<HiOutlineBookmark className="w-5 h-5 mr-2" />}
            activeClass="bg-blue-500 text-white hover:bg-blue-600"
            inactiveClass="bg-gray-100 text-gray-700 hover:bg-gray-200"
            successMessage="已收藏"
            undoMessage="已取消收藏"
          />
          <ReactionButton
            postId={post.id}
            initialCount={post.recommendCount || 0}
            initialState={post.userHasRecommended || false}
            type={ReactionType.Recommend}
            icon={<HiOutlineThumbUp className="w-5 h-5 mr-2" />}
            activeClass="bg-green-500 text-white hover:bg-green-600"
            inactiveClass="bg-gray-100 text-gray-700 hover:bg-gray-200"
            successMessage="已推荐"
            undoMessage="已取消推荐"
          />
        </div>

        {/* 文章内容 */}
        {/* PostContentSmart component was removed from imports, so this section is now empty */}
      </div>
    </article>
  );
};

export default CoverTemplate; 