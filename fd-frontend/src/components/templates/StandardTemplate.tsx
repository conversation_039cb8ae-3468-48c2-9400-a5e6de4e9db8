import React from 'react';
import Link from 'next/link';
import ReactionButton from '../post/ReactionButton';
import PostContentSmart from '@/components/post/PostContentSmart';
import { ReactionType } from '@/types/Reaction';
import { Post as PostType } from '@/types/post';
import { HiOutlineHeart, HiOutlineBookmark, HiOutlineThumbUp } from 'react-icons/hi';


interface PostAuthor {
  node?: {
    name: string;
    slug?: string;
  };
}

interface PostCategory {
  id: string;
  name: string;
}

interface PostTag {
  id: string;
  name: string;
}

interface FeaturedImage {
  node?: {
    sourceUrl: string;
    altText?: string;
  };
}

interface PostTemplateType {
  templateType?: string[];
  subtitle?: string;
  articleAuthor?: string;
  articleSource?: string;
  copyrightType?: string[];
  videoUrl?: string;
  additionalImages?: {
    nodes: {
      sourceUrl: string;
      altText?: string;
    }[];
  };
}

interface PaywallInfo {
  hasPaywall: boolean;
  previewContent: string;
  loginUrl: string;
  registerUrl: string;
  upgradeUrl: string;
  message: string;
  isLoggedIn: boolean;
}

/**
 * 标准文章模板组件
 * 显示文章的基本内容，包括标题、作者、日期、分类、标签和正文
 */
const StandardTemplate: React.FC<{ post: PostType; paywallVariant?: 'default' | 'compact'; }> = ({ post, paywallVariant = 'default' }) => {
  return (
    <article className="article-container max-w-4xl mx-auto py-10 px-4">
      <h1 className="text-3xl font-bold mb-6">{post.title}</h1>
      
      {/* 文章UUID信息 */}
      {post.shortUuid && (
        <div className="uuid-info text-xs text-gray-500 mb-2">
          文章ID: {post.shortUuid}
        </div>
      )}
      
      {/* 文章头部信息 */}
      <div className="article-meta mb-8">
        <div className="flex items-center text-gray-600 mb-4">
          <span className="mr-4">
            发布于: {new Date(post.date).toLocaleDateString('zh-CN')}
          </span>
          {post.postTemplate?.articleAuthor ? (
            <span>作者: {post.postTemplate.articleAuthor}</span>
          ) : post.author?.node?.slug ? (
            <span>
              作者: <Link href={`/author/${post.author.node.slug}`} className="text-blue-600 hover:underline">{post.author.node.name}</Link>
            </span>
          ) : post.author?.node?.name && (
            <span>作者: {post.author.node.name}</span>
          )}
        </div>
        
        {/* 来源信息 */}
        {post.postTemplate?.articleSource && (
          <div className="source mb-2 text-gray-600">
            来源: {post.postTemplate.articleSource}
          </div>
        )}
        
        {/* 分类 */}
        {post.categories?.nodes && post.categories.nodes.length > 0 && (
          <div className="categories mb-2">
            <span className="mr-2">分类:</span>
            {post.categories.nodes.map((category) => (
              <span key={category.id} className="mr-2 bg-gray-100 px-2 py-1 rounded text-sm">
                {category.name}
              </span>
            ))}
          </div>
        )}
        
        {/* 标签 */}
        {post.tags?.nodes && post.tags.nodes.length > 0 && (
          <div className="tags">
            <span className="mr-2">标签:</span>
            {post.tags.nodes.map((tag) => (
              <span key={tag.id} className="mr-2 bg-gray-100 px-2 py-1 rounded text-sm">
                {tag.name}
              </span>
            ))}
          </div>
        )}

        {/* 点赞与推荐按钮容器 */}
        <div className="flex items-center space-x-6 mt-6 pt-4 border-t">
          <ReactionButton
            postId={post.id}
            initialCount={post.likeCount || 0}
            initialState={post.userHasLiked || false}
            type={ReactionType.Like}
            icon={<HiOutlineHeart className="w-5 h-5 mr-2" />}
            activeClass="bg-rose-500 text-white hover:bg-rose-600"
            inactiveClass="bg-gray-100 text-gray-700 hover:bg-gray-200"
            successMessage="感谢点赞！"
            undoMessage="已取消点赞"
          />
          <ReactionButton
            postId={post.id}
            initialCount={post.bookmarkCount || 0}
            initialState={post.userHasBookmarked || false}
            type={ReactionType.Bookmark}
            icon={<HiOutlineBookmark className="w-5 h-5 mr-2" />}
            activeClass="bg-blue-500 text-white hover:bg-blue-600"
            inactiveClass="bg-gray-100 text-gray-700 hover:bg-gray-200"
            successMessage="已收藏"
            undoMessage="已取消收藏"
          />
          <ReactionButton
            postId={post.id}
            initialCount={post.recommendCount || 0}
            initialState={post.userHasRecommended || false}
            type={ReactionType.Recommend}
            icon={<HiOutlineThumbUp className="w-5 h-5 mr-2" />}
            activeClass="bg-green-500 text-white hover:bg-green-600"
            inactiveClass="bg-gray-100 text-gray-700 hover:bg-gray-200"
            successMessage="已推荐"
            undoMessage="已取消推荐"
          />
        </div>
      </div>
      
      {/* 特色图片 */}
      {post.featuredImage?.node?.sourceUrl && (
        <div className="featured-image mb-8">
          <img 
            src={post.featuredImage.node.sourceUrl} 
            alt={post.featuredImage.node.altText || post.title} 
            className="w-full rounded-lg shadow-md"
          />
        </div>
      )}
      
      {/* 文章内容（根据用户权限自动刷新） */}
      <PostContentSmart
        postId={post.databaseId}
        initialContent={post.content}
        postTitle={post.title}
        unlockPrice={post.unlockPrice}
        requiredMemberLevel={post.requiredMemberLevel}
        isUnlocked={post.isUnlockedByCurrentUser}
        paywallVariant={paywallVariant}
        initialPaywallInfo={post.paywallInfo}
      />
    </article>
  );
};

export default StandardTemplate; 