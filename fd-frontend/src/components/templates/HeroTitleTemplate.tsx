import React from 'react';
import { Post as PostType } from '@/types/post';
import ReactionButton from '../post/ReactionButton';
import PostContentSmart from '@/components/post/PostContentSmart';
import { ReactionType } from '@/types/Reaction';
import { HiOutlineHeart, HiOutlineBookmark, HiOutlineThumbUp } from 'react-icons/hi';

/**
 * 大标题文章模板组件
 * 显示文章内容，带有突出的大号标题和可选的额外图片
 */
const HeroTitleTemplate: React.FC<{ post: PostType; paywallVariant?: 'default' | 'compact'; }> = ({ post, paywallVariant = 'default' }) => {
  return (
    <article className="article-container mx-auto">
      <div className="hero-header bg-gray-100 py-16 mb-10">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6">{post.title}</h1>
          {post.postTemplate?.subtitle && (
            <h2 className="text-2xl md:text-3xl mb-8 font-light text-gray-600">{post.postTemplate.subtitle}</h2>
          )}
          <div className="flex justify-center items-center text-gray-600">
            <span className="mr-4">
              {new Date(post.date).toLocaleDateString('zh-CN')}
            </span>
            {post.postTemplate?.articleAuthor ? (
              <span>{post.postTemplate.articleAuthor}</span>
            ) : post.author?.node && (
              <span>{post.author.node.name}</span>
            )}
          </div>
        </div>
      </div>
      
      <div className="max-w-4xl mx-auto px-4">
        {/* 文章UUID信息 */}
        {post.shortUuid && (
          <div className="uuid-info text-xs text-gray-500 mb-2">
            文章ID: {post.shortUuid}
          </div>
        )}
        
        {/* 特色图片 */}
        {post.featuredImage?.node?.sourceUrl && (
          <div className="featured-image mb-8">
            <img 
              src={post.featuredImage.node.sourceUrl} 
              alt={post.featuredImage.node.altText || post.title} 
              className="w-full rounded-lg shadow-md"
            />
          </div>
        )}
        
        {/* 文章头部信息 - 不包括标题和副标题，因为已在顶部显示 */}
        <div className="article-meta mb-8">
          {/* 来源信息 */}
          {post.postTemplate?.articleSource && (
            <div className="source mb-2 text-gray-600">
              来源: {post.postTemplate.articleSource}
            </div>
          )}
          
          {/* 分类 */}
          {post.categories?.nodes && post.categories.nodes.length > 0 && (
            <div className="categories mb-2">
              <span className="mr-2">分类:</span>
              {post.categories.nodes.map((category) => (
                <span key={category.id} className="mr-2 bg-gray-100 px-2 py-1 rounded text-sm">
                  {category.name}
                </span>
              ))}
            </div>
          )}
          
          {/* 标签 */}
          {post.tags?.nodes && post.tags.nodes.length > 0 && (
            <div className="tags">
              <span className="mr-2">标签:</span>
              {post.tags.nodes.map((tag) => (
                <span key={tag.id} className="mr-2 bg-gray-100 px-2 py-1 rounded text-sm">
                  {tag.name}
                </span>
              ))}
            </div>
          )}
        </div>

        {/* 交互按钮 */}
        <div className="flex items-center space-x-6 mt-8 pt-6 border-t border-gray-200">
          <ReactionButton
            postId={post.id}
            initialCount={post.likeCount || 0}
            initialState={post.userHasLiked || false}
            type={ReactionType.Like}
            icon={<HiOutlineHeart className="w-5 h-5 mr-2" />}
            activeClass="bg-rose-500 text-white hover:bg-rose-600"
            inactiveClass="bg-gray-100 text-gray-700 hover:bg-gray-200"
            successMessage="感谢点赞！"
            undoMessage="已取消点赞"
          />
          <ReactionButton
            postId={post.id}
            initialCount={post.bookmarkCount || 0}
            initialState={post.userHasBookmarked || false}
            type={ReactionType.Bookmark}
            icon={<HiOutlineBookmark className="w-5 h-5 mr-2" />}
            activeClass="bg-blue-500 text-white hover:bg-blue-600"
            inactiveClass="bg-gray-100 text-gray-700 hover:bg-gray-200"
            successMessage="已收藏"
            undoMessage="已取消收藏"
          />
          <ReactionButton
            postId={post.id}
            initialCount={post.recommendCount || 0}
            initialState={post.userHasRecommended || false}
            type={ReactionType.Recommend}
            icon={<HiOutlineThumbUp className="w-5 h-5 mr-2" />}
            activeClass="bg-green-500 text-white hover:bg-green-600"
            inactiveClass="bg-gray-100 text-gray-700 hover:bg-gray-200"
            successMessage="已推荐"
            undoMessage="已取消推荐"
          />
        </div>
        
        {/* 文章内容 */}
        <PostContentSmart
          postId={post.databaseId}
          initialContent={post.content}
          postTitle={post.title}
          unlockPrice={post.unlockPrice}
          requiredMemberLevel={post.requiredMemberLevel}
          isUnlocked={post.isUnlockedByCurrentUser}
          paywallVariant={paywallVariant}
          initialPaywallInfo={post.paywallInfo}
        />
        
        {/* 额外图片展示 */}
        {post.postTemplate?.additionalImages?.nodes && post.postTemplate.additionalImages.nodes.length > 0 && (
          <div className="additional-images mt-10 grid grid-cols-1 md:grid-cols-2 gap-4">
            {post.postTemplate.additionalImages.nodes.map((image, index) => (
              <div key={index} className="additional-image">
                <img 
                  src={image.sourceUrl} 
                  alt={image.altText || `额外图片 ${index + 1}`} 
                  className="w-full h-auto rounded-lg shadow"
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </article>
  );
};

export default HeroTitleTemplate; 