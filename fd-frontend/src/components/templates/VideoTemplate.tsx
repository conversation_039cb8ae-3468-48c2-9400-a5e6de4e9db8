import React from 'react';
import ReactionButton from '../post/ReactionButton';
import PostContentSmart from '@/components/post/PostContentSmart';
import { ReactionType } from '@/types/Reaction';
import { Post as PostType } from '@/types/post';
import { HiOutlineHeart, HiOutlineBookmark, HiOutlineThumbUp } from 'react-icons/hi';

/**
 * 视频文章模板组件
 * 显示文章内容，带有视频播放功能
 */
const VideoTemplate: React.FC<{ post: PostType; paywallVariant?: 'default' | 'compact'; }> = ({ post, paywallVariant = 'default' }) => {
  const LikeIcon = (
    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
      <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd"></path>
    </svg>
  );
  const BookmarkIcon = (
    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
      <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5-5 2.5V4z"></path>
    </svg>
  );
  const RecommendIcon = (
    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
      <path d="M10 2a8 8 0 00-8 8c0 2.15.8 4 2 5.25V16a2 2 0 002 2h4a2 2 0 002-2v-1.25C13 12 14 10.15 14 8a8 8 0 00-8-8zm0 14a6 6 0 100-12 6 6 0 000 12z"></path>
    </svg>
  );

  return (
    <article className="article-container max-w-4xl mx-auto py-10 px-4">
      <h1 className="text-3xl font-bold mb-6">{post.title}</h1>
      
      {/* 文章UUID信息 */}
      {post.shortUuid && (
        <div className="uuid-info text-xs text-gray-500 mb-2">
          文章ID: {post.shortUuid}
        </div>
      )}
      
      {/* 文章头部信息 */}
      <div className="article-meta max-w-4xl mx-auto px-4 mb-8">
        <div className="flex items-center text-gray-600 mb-4">
          <span className="mr-4">
            发布于: {new Date(post.date).toLocaleDateString('zh-CN')}
          </span>
          {post.postTemplate?.articleAuthor ? (
            <span>作者: {post.postTemplate.articleAuthor}</span>
          ) : post.author?.node && (
            <span>作者: {post.author.node.name}</span>
          )}
        </div>
        
        {/* 来源信息 */}
        {post.postTemplate?.articleSource && (
          <div className="source mb-2 text-gray-600">
            来源: {post.postTemplate.articleSource}
          </div>
        )}
        
        {/* 分类 */}
        {post.categories?.nodes && post.categories.nodes.length > 0 && (
          <div className="categories mb-2">
            <span className="mr-2">分类:</span>
            {post.categories.nodes.map((category) => (
              <span key={category.id} className="mr-2 bg-gray-100 px-2 py-1 rounded text-sm">
                {category.name}
              </span>
            ))}
          </div>
        )}
        
        {/* 标签 */}
        {post.tags?.nodes && post.tags.nodes.length > 0 && (
          <div className="tags">
            <span className="mr-2">标签:</span>
            {post.tags.nodes.map((tag) => (
              <span key={tag.id} className="mr-2 bg-gray-100 px-2 py-1 rounded text-sm">
                {tag.name}
              </span>
            ))}
          </div>
        )}
        
        {/* 点赞与推荐按钮容器 */}
        <div className="flex items-center space-x-6 mt-6 pt-4 border-t">
          <ReactionButton
            postId={post.id}
            initialCount={post.likeCount || 0}
            initialState={post.userHasLiked || false}
            type={ReactionType.Like}
            icon={<HiOutlineHeart className="w-5 h-5 mr-2" />}
            activeClass="bg-rose-500 text-white hover:bg-rose-600"
            inactiveClass="bg-gray-100 text-gray-700 hover:bg-gray-200"
            successMessage="感谢点赞！"
            undoMessage="已取消点赞"
          />
          <ReactionButton
            postId={post.id}
            initialCount={post.bookmarkCount || 0}
            initialState={post.userHasBookmarked || false}
            type={ReactionType.Bookmark}
            icon={<HiOutlineBookmark className="w-5 h-5 mr-2" />}
            activeClass="bg-blue-500 text-white hover:bg-blue-600"
            inactiveClass="bg-gray-100 text-gray-700 hover:bg-gray-200"
            successMessage="已收藏"
            undoMessage="已取消收藏"
          />
          <ReactionButton
            postId={post.id}
            initialCount={post.recommendCount || 0}
            initialState={post.userHasRecommended || false}
            type={ReactionType.Recommend}
            icon={<HiOutlineThumbUp className="w-5 h-5 mr-2" />}
            activeClass="bg-green-500 text-white hover:bg-green-600"
            inactiveClass="bg-gray-100 text-gray-700 hover:bg-gray-200"
            successMessage="已推荐"
            undoMessage="已取消推荐"
          />
        </div>
      </div>
      
      {/* 视频内容 */}
      {post.postTemplate?.videoUrl && (
        <div className="video-container mb-8 aspect-w-16 aspect-h-9">
          <video 
            src={post.postTemplate.videoUrl} 
            controls 
            className="w-full rounded-lg shadow-md"
            poster={post.featuredImage?.node?.sourceUrl}
          >
            您的浏览器不支持视频播放
          </video>
        </div>
      )}
      
      {/* 特色图片（如果没有视频） */}
      {!post.postTemplate?.videoUrl && post.featuredImage?.node?.sourceUrl && (
        <div className="featured-image mb-8">
          <img 
            src={post.featuredImage.node.sourceUrl} 
            alt={post.featuredImage.node.altText || post.title} 
            className="w-full rounded-lg shadow-md"
          />
        </div>
      )}
      
      {/* 文章内容 */}
      <PostContentSmart
        postId={post.databaseId}
        initialContent={post.content}
        postTitle={post.title}
        unlockPrice={post.unlockPrice}
        requiredMemberLevel={post.requiredMemberLevel}
        isUnlocked={post.isUnlockedByCurrentUser}
        paywallVariant={paywallVariant}
        initialPaywallInfo={post.paywallInfo}
      />
    </article>
  );
};

export default VideoTemplate; 