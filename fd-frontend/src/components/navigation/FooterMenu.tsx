'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useMenu } from '@/hooks/useMenu';
import { useMenuContext } from '@/contexts/MenuContext';

interface MenuItem {
  id: string;
  title: string;
  label: string;
  url: string;
  target?: string;
}

interface FooterMenuProps {
  className?: string;
  title?: string;
  menuItems?: MenuItem[]; // 预获取的菜单数据
}

// 底部菜单骨架屏
const FooterMenuSkeleton: React.FC = () => (
  <div className="space-y-1.5 md:space-y-2">
    {[1, 2, 3, 4, 5].map((i) => (
      <div
        key={i}
        className="h-4 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"
        style={{ width: `${60 + Math.random() * 40}%` }}
      />
    ))}
  </div>
);

// 底部菜单组件
const FooterMenu: React.FC<FooterMenuProps> = ({
  className = '',
  title = '快速链接',
  menuItems: preloadedMenuItems
}) => {
  const { menuItems: footerMenuItems, loading: footerMenuLoading } = useMenu('底部菜单');
  const { footerMenuItems: contextFooterMenuItems } = useMenuContext(); // 从Context获取菜单数据
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // 优先级：props传入 > Context > GraphQL查询
  const finalMenuItems = preloadedMenuItems || contextFooterMenuItems || footerMenuItems;
  const isLoading = !preloadedMenuItems && !contextFooterMenuItems.length && (!mounted || footerMenuLoading);

  return (
    <div className={className}>
      <h3 className="text-lg font-semibold mb-3 md:mb-4 text-gray-900 dark:text-white font-heading">
        {title}
      </h3>
      
      {isLoading ? (
        <FooterMenuSkeleton />
      ) : finalMenuItems.length > 0 ? (
        <ul className="space-y-1.5 md:space-y-2">
          {finalMenuItems.map((item: MenuItem) => (
            <li key={item.id}>
              <Link 
                href={item.url}
                target={item.target || '_self'}
                className="text-sm md:text-base text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-400 transition-colors duration-200 block"
              >
                {item.label || item.title}
              </Link>
            </li>
          ))}
        </ul>
      ) : (
        <div className="text-sm text-gray-500 dark:text-gray-400">
          暂无菜单项
        </div>
      )}
    </div>
  );
};

export default React.memo(FooterMenu);

// 导出骨架屏供其他地方使用
export { FooterMenuSkeleton };
