'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useMenu } from '@/hooks/useMenu';
import { useMenuContext } from '@/contexts/MenuContext';

interface MenuItem {
  id: string;
  title: string;
  label: string;
  url: string;
  target?: string;
  children?: MenuItem[];
}

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
  menuItems?: MenuItem[]; // 预获取的菜单数据
}

// 移动端菜单项组件
const MobileMenuItem: React.FC<{
  item: MenuItem;
  pathname: string;
  onSubmenuToggle: (id: string) => void;
  openSubmenu: string | null;
  onClose: () => void;
}> = ({ item, pathname, onSubmenuToggle, openSubmenu, onClose }) => {
  const hasChildren = item.children && item.children.length > 0;
  const isActive = pathname === item.url;
  const isSubmenuOpen = openSubmenu === item.id;

  return (
    <div className="py-2">
      <div className="flex items-center justify-between">
        <Link 
          href={item.url} 
          target={item.target || '_self'}
          className={`text-base font-medium transition-colors duration-200 ${
            isActive
              ? 'text-primary dark:text-primary-400' 
              : 'text-gray-700 dark:text-gray-300'
          }`}
          onClick={onClose}
        >
          {item.label || item.title}
        </Link>
        
        {hasChildren && (
          <button
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors duration-200"
            onClick={() => onSubmenuToggle(item.id)}
            aria-label={`展开 ${item.label || item.title} 子菜单`}
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className={`h-5 w-5 transition-transform duration-200 ${
                isSubmenuOpen ? 'rotate-180' : ''
              }`}
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        )}
      </div>
      
      {/* 移动端子菜单 */}
      {hasChildren && isSubmenuOpen && (
        <div className="mt-2 pl-4 border-l-2 border-gray-200 dark:border-gray-700 animate-in slide-in-from-left-2 duration-200">
          {item.children!.map(child => (
            <Link
              key={child.id}
              href={child.url}
              target={child.target || '_self'}
              className={`block py-2 text-sm transition-colors duration-200 ${
                pathname === child.url
                  ? 'text-primary dark:text-primary-400'
                  : 'text-gray-600 dark:text-gray-300'
              }`}
              onClick={onClose}
            >
              {child.label || child.title}
            </Link>
          ))}
        </div>
      )}
    </div>
  );
};

// 移动端菜单组件
const MobileMenu: React.FC<MobileMenuProps> = ({
  isOpen,
  onClose,
  className = '',
  menuItems: preloadedMenuItems
}) => {
  const pathname = usePathname();
  const { hierarchicalMenuItems, loading } = useMenu('顶部菜单');
  const { topMenuItems } = useMenuContext(); // 从Context获取菜单数据
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  // 优先级：props传入 > Context > GraphQL查询
  const finalMenuItems = preloadedMenuItems || topMenuItems || hierarchicalMenuItems;
  const isLoading = !preloadedMenuItems && !topMenuItems.length && loading;

  // 点击外部区域关闭菜单
  useEffect(() => {
    if (!isOpen) return;
    
    const handleClickOutside = (event: MouseEvent) => {
      if (mobileMenuRef.current && 
          !mobileMenuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, onClose]);

  // ESC键关闭菜单
  useEffect(() => {
    if (!isOpen) return;
    
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    
    document.addEventListener('keydown', handleEscKey);
    return () => document.removeEventListener('keydown', handleEscKey);
  }, [isOpen, onClose]);

  // 菜单打开时禁止页面滚动
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleSubmenuToggle = (id: string) => {
    setOpenSubmenu(prev => prev === id ? null : id);
  };

  if (!isOpen) return null;

  return (
    <>
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden animate-in fade-in-0 duration-200"
        onClick={onClose}
      />
      
      {/* 菜单内容 */}
      <div 
        ref={mobileMenuRef}
        className={`fixed top-16 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 shadow-lg max-h-[calc(100vh-4rem)] overflow-y-auto md:hidden animate-in slide-in-from-top-2 duration-200 ${className}`}
      >
        <nav className="container mx-auto py-3 px-4">
          {isLoading ? (
            // 加载骨架屏
            <div className="space-y-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
              ))}
            </div>
          ) : finalMenuItems.length > 0 ? (
            // 菜单项
            finalMenuItems.map((item) => (
              <MobileMenuItem
                key={item.id}
                item={item}
                pathname={pathname}
                onSubmenuToggle={handleSubmenuToggle}
                openSubmenu={openSubmenu}
                onClose={onClose}
              />
            ))
          ) : (
            // 无菜单数据
            <div className="py-4 text-center text-gray-500 dark:text-gray-400">
              暂无菜单数据
            </div>
          )}
        </nav>
      </div>
    </>
  );
};

export default React.memo(MobileMenu);
