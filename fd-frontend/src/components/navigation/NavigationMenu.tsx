'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useMenu } from '@/hooks/useMenu';
import { useMenuContext } from '@/contexts/MenuContext';

interface MenuItem {
  id: string;
  title: string;
  label: string;
  url: string;
  target?: string;
  children?: MenuItem[];
}

interface NavigationMenuProps {
  className?: string;
  menuItems?: MenuItem[]; // 预获取的菜单数据
}

// 菜单骨架屏组件
const MenuSkeleton: React.FC = () => (
  <nav className="flex items-center space-x-4">
    {[1, 2, 3, 4, 5].map((i) => (
      <div
        key={i}
        className="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"
      />
    ))}
  </nav>
);

// 单个菜单项组件
const MenuItemComponent: React.FC<{
  item: MenuItem;
  pathname: string;
  onSubmenuToggle: (id: string) => void;
  openSubmenu: string | null;
  onSetRef: (id: string, el: HTMLDivElement | null) => void;
}> = ({ item, pathname, onSubmenuToggle, openSubmenu, onSetRef }) => {
  const hasChildren = item.children && item.children.length > 0;
  const isActive = pathname === item.url;
  const isSubmenuOpen = openSubmenu === item.id;

  return (
    <div
      className="relative group"
      ref={(el) => onSetRef(item.id, el)}
    >
      <div className="flex items-center">
        <Link 
          href={item.url} 
          target={item.target || '_self'}
          className={`text-base font-medium whitespace-nowrap transition-colors duration-200 ${
            isActive
              ? 'text-primary hover:text-primary-600 dark:text-primary-400' 
              : 'text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-400'
          }`}
        >
          {item.label || item.title}
        </Link>
        
        {hasChildren && (
          <button
            className="ml-1 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors duration-200"
            onClick={() => onSubmenuToggle(item.id)}
            aria-label={`展开 ${item.label || item.title} 子菜单`}
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className={`h-4 w-4 transition-transform duration-200 ${
                isSubmenuOpen ? 'rotate-180' : ''
              }`}
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        )}
      </div>
      
      {/* 子菜单 */}
      {hasChildren && isSubmenuOpen && (
        <div className="absolute z-10 left-0 mt-2 w-48 bg-white dark:bg-gray-800 border dark:border-gray-700 rounded-md shadow-lg py-1 animate-in fade-in-0 zoom-in-95 duration-200">
          {item.children!.map(child => (
            <Link
              key={child.id}
              href={child.url}
              target={child.target || '_self'}
              className={`block px-4 py-2 text-sm transition-colors duration-200 ${
                pathname === child.url
                  ? 'text-primary bg-gray-50 dark:bg-gray-700'
                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              {child.label || child.title}
            </Link>
          ))}
        </div>
      )}
    </div>
  );
};

// 主导航菜单组件
const NavigationMenu: React.FC<NavigationMenuProps> = ({
  className = '',
  menuItems: preloadedMenuItems
}) => {
  const pathname = usePathname();
  const { hierarchicalMenuItems, loading } = useMenu('顶部菜单');
  const { topMenuItems } = useMenuContext(); // 从Context获取菜单数据
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);
  const menuRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // 优先级：props传入 > Context > GraphQL查询
  const finalMenuItems = preloadedMenuItems || topMenuItems || hierarchicalMenuItems;
  const isLoading = !preloadedMenuItems && !topMenuItems.length && loading;

  // 点击外部区域关闭子菜单
  useEffect(() => {
    if (!openSubmenu) return;
    
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRefs.current[openSubmenu] && 
          menuRefs.current[openSubmenu]?.contains(event.target as Node)) {
        return;
      }
      setOpenSubmenu(null);
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [openSubmenu]);

  const handleSubmenuToggle = (id: string) => {
    setOpenSubmenu(prev => prev === id ? null : id);
  };

  const handleSetRef = (id: string, el: HTMLDivElement | null) => {
    menuRefs.current[id] = el;
  };

  // 加载状态
  if (isLoading) {
    return <MenuSkeleton />;
  }

  // 无菜单数据
  if (!finalMenuItems.length) {
    return null;
  }

  return (
    <nav className={`flex items-center space-x-4 ${className}`}>
      {finalMenuItems.map((item) => (
        <MenuItemComponent
          key={item.id}
          item={item}
          pathname={pathname}
          onSubmenuToggle={handleSubmenuToggle}
          openSubmenu={openSubmenu}
          onSetRef={handleSetRef}
        />
      ))}
    </nav>
  );
};

// 使用React.memo优化重渲染
export default React.memo(NavigationMenu);

// 导出子组件供其他地方使用
export { MenuSkeleton };
