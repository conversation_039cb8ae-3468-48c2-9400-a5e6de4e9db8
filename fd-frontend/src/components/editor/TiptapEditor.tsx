'use client'

import { use<PERSON><PERSON><PERSON>, EditorContent, type Editor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Image from '@tiptap/extension-image'
import {
  FaBold,
  FaItalic,
  FaStrikethrough,
  FaHeading,
  FaListOl,
  FaListUl,
  FaQuoteLeft,
  FaUndo,
  FaRedo
} from 'react-icons/fa'
import type { IconType } from 'react-icons'
import { useEffect } from 'react'

interface MenuItemConfig {
  icon: IconType
  action: () => void
  isActive: boolean
  title: string
}

interface DividerItem {
  type: 'divider'
}

const isDivider = (
  item: MenuItemConfig | DividerItem
): item is DividerItem => {
  return (item as DividerItem).type === 'divider'
}

const MenuBar = ({ editor }: { editor: Editor | null }) => {
  if (!editor) {
    return null
  }

  const menuItems: (MenuItemConfig | DividerItem)[] = [
    {
      icon: FaBold,
      action: () => editor.chain().focus().toggleBold().run(),
      isActive: editor.isActive('bold'),
      title: 'Bold'
    },
    {
      icon: FaItalic,
      action: () => editor.chain().focus().toggleItalic().run(),
      isActive: editor.isActive('italic'),
      title: 'Italic'
    },
    {
      icon: FaStrikethrough,
      action: () => editor.chain().focus().toggleStrike().run(),
      isActive: editor.isActive('strike'),
      title: 'Strike'
    },
    {
      icon: FaHeading,
      action: () => editor.chain().focus().toggleHeading({ level: 2 }).run(),
      isActive: editor.isActive('heading', { level: 2 }),
      title: 'Heading'
    },
    {
      icon: FaListUl,
      action: () => editor.chain().focus().toggleBulletList().run(),
      isActive: editor.isActive('bulletList'),
      title: 'Bullet List'
    },
    {
      icon: FaListOl,
      action: () => editor.chain().focus().toggleOrderedList().run(),
      isActive: editor.isActive('orderedList'),
      title: 'Ordered List'
    },
    {
      icon: FaQuoteLeft,
      action: () => editor.chain().focus().toggleBlockquote().run(),
      isActive: editor.isActive('blockquote'),
      title: 'Blockquote'
    },
    {
      type: 'divider'
    },
    {
      icon: FaUndo,
      action: () => editor.chain().focus().undo().run(),
      isActive: false,
      title: 'Undo'
    },
    {
      icon: FaRedo,
      action: () => editor.chain().focus().redo().run(),
      isActive: false,
      title: 'Redo'
    }
  ]

  return (
    <div className="flex flex-wrap items-center gap-2 p-2 border border-gray-300 bg-gray-50 rounded-t-lg">
      {menuItems.map((item, index) => {
        if (isDivider(item)) {
          return (
            <div key={index} className="w-px h-6 bg-gray-300 mx-1" />
          )
        }

        const { icon: Icon, action, title, isActive } = item

        return (
          <button
            key={index}
            onClick={action}
            title={title}
            className={`p-2 rounded-md hover:bg-gray-200 ${
              isActive ? 'bg-gray-200 ring-2 ring-blue-500' : ''
            }`}
          >
            <Icon className="w-5 h-5 text-gray-700" />
          </button>
        )
      })}
    </div>
  )
}

interface TiptapEditorProps {
  content?: string;
  onUpdate: (payload: { html: string; json: string }) => void;
}

const TiptapEditor = ({
  content,
  onUpdate,
  ...props
}: TiptapEditorProps & Omit<React.ComponentProps<'div'>, 'onUpdate'>) => {
  const editor = useEditor({
    extensions: [
      StarterKit,
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto',
        },
      }),
    ],
    content: content || '<p>在这里开始写作…</p>',
    editorProps: {
      attributes: {
        class:
          'prose dark:prose-invert max-w-none prose-sm sm:prose-base lg:prose-lg focus:outline-none p-4 border border-t-0 border-gray-300 dark:border-gray-700 rounded-b-lg min-h-[300px]'
      }
    },
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      const json = JSON.stringify(editor.getJSON())
      onUpdate({ html, json })
    }
  })

  useEffect(() => {
    if (editor && content !== undefined && content !== editor.getHTML()) {
      editor.commands.setContent(content, false, {
        preserveWhitespace: 'full',
      });
    }
  }, [content, editor])

  return (
    <div {...props}>
      <MenuBar editor={editor} />
      <EditorContent editor={editor} />
    </div>
  )
}

export default TiptapEditor 