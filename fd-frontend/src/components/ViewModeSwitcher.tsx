import React from 'react';

// 定义视图模式类型
export type ViewMode = 'list' | 'grid' | 'compact' | 'magazine';

// 列数选项
export type ColumnCount = 1 | 2 | 3 | 4;

// 组件属性
export interface ViewModeSwitcherProps {
  currentMode: ViewMode;
  columns?: ColumnCount;
  onChange: (mode: ViewMode, columns?: ColumnCount) => void;
  className?: string;
  showColumnsControl?: boolean;
  darkMode?: boolean;
}

/**
 * 视图模式切换器组件
 * 允许用户在不同的视图模式间切换，并可选择设置列数
 */
export default function ViewModeSwitcher({
  currentMode,
  columns = 3,
  onChange,
  className = '',
  showColumnsControl = true,
  darkMode = false
}: ViewModeSwitcherProps) {
  // 视图模式选项
  const viewModes: { id: ViewMode; label: string; icon: React.ReactNode }[] = [
    {
      id: 'list',
      label: '列表视图',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5">
          <line x1="8" y1="6" x2="21" y2="6"></line>
          <line x1="8" y1="12" x2="21" y2="12"></line>
          <line x1="8" y1="18" x2="21" y2="18"></line>
          <line x1="3" y1="6" x2="3.01" y2="6"></line>
          <line x1="3" y1="12" x2="3.01" y2="12"></line>
          <line x1="3" y1="18" x2="3.01" y2="18"></line>
        </svg>
      ),
    },
    {
      id: 'grid',
      label: '网格视图',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5">
          <rect x="3" y="3" width="7" height="7"></rect>
          <rect x="14" y="3" width="7" height="7"></rect>
          <rect x="14" y="14" width="7" height="7"></rect>
          <rect x="3" y="14" width="7" height="7"></rect>
        </svg>
      ),
    },
    {
      id: 'compact',
      label: '紧凑视图',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5">
          <line x1="3" y1="6" x2="21" y2="6"></line>
          <line x1="3" y1="12" x2="21" y2="12"></line>
          <line x1="3" y1="18" x2="21" y2="18"></line>
        </svg>
      ),
    },
    {
      id: 'magazine',
      label: '杂志视图',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-5 h-5">
          <rect x="3" y="3" width="18" height="6" rx="1"></rect>
          <rect x="3" y="15" width="7" height="6" rx="1"></rect>
          <rect x="14" y="15" width="7" height="6" rx="1"></rect>
        </svg>
      ),
    },
  ];

  // 列数选项
  const columnOptions: { value: ColumnCount; label: string }[] = [
    { value: 1, label: '1列' },
    { value: 2, label: '2列' },
    { value: 3, label: '3列' },
    { value: 4, label: '4列' },
  ];
  
  // 处理视图模式变更
  const handleModeChange = (mode: ViewMode) => {
    onChange(mode, columns);
    
    // 本地存储用户偏好
    try {
      localStorage.setItem('fd_view_mode', mode);
    } catch (e) {
      console.error('无法保存视图模式偏好:', e);
    }
  };
  
  // 处理列数变更
  const handleColumnsChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newColumns = parseInt(e.target.value) as ColumnCount;
    onChange(currentMode, newColumns);
    
    // 本地存储用户偏好
    try {
      localStorage.setItem('fd_columns_count', newColumns.toString());
    } catch (e) {
      console.error('无法保存列数偏好:', e);
    }
  };

  // 基础样式和激活状态样式
  const baseButtonClass = `p-2 rounded-md transition-colors ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`;
  const activeButtonClass = darkMode 
    ? 'bg-gray-700 text-blue-400' 
    : 'bg-gray-100 text-blue-600';

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className={`flex ${darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'} rounded-lg shadow-sm border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        {viewModes.map((mode) => (
          <button
            key={mode.id}
            type="button"
            className={`${baseButtonClass} ${currentMode === mode.id ? activeButtonClass : ''}`}
            title={mode.label}
            aria-label={mode.label}
            onClick={() => handleModeChange(mode.id)}
          >
            {mode.icon}
          </button>
        ))}
      </div>

      {showColumnsControl && currentMode === 'grid' && (
        <div className="flex items-center">
          <label className={`text-sm mr-2 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>列数:</label>
          <select
            value={columns}
            onChange={handleColumnsChange}
            className={`text-sm p-1.5 rounded border ${
              darkMode 
                ? 'bg-gray-800 text-white border-gray-700' 
                : 'bg-white text-gray-800 border-gray-200'
            }`}
          >
            {columnOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      )}
    </div>
  );
} 