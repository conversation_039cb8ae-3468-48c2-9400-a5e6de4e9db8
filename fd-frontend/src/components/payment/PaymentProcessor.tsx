import React, { useEffect, useState } from 'react';
import { useMutation } from '@apollo/client';
import { GET_PAYMENT_URL, CHECK_PAYMENT_STATUS } from '../../lib/graphql/mutations';
import Image from 'next/image';
import { QRCodeSVG } from 'qrcode.react'; // 使用QRCodeSVG组件

type PaymentProcessorProps = {
  orderId: string;
  paymentMethod: string;
  onPaymentSuccess: () => void;
  onCancel: () => void;
  productType?: string;
  productId?: string;
  metadata?: string;
};

const PaymentProcessor: React.FC<PaymentProcessorProps> = ({
  orderId,
  paymentMethod,
  onPaymentSuccess,
  onCancel,
  productType,
  productId,
  metadata
}) => {
  const [paymentUrl, setPaymentUrl] = useState('');
  const [paymentHtml, setPaymentHtml] = useState('');
  const [qrcodeUrl, setQrcodeUrl] = useState('');
  const [isMobile, setIsMobile] = useState(false);
  const [isWeixin, setIsWeixin] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [paymentLoaded, setPaymentLoaded] = useState(false);
  
  // 获取支付链接
  const [getPaymentUrl, { loading: loadingUrl, error: urlError }] = useMutation(GET_PAYMENT_URL);
  
  // 检查支付状态
  const [checkPaymentStatus, { loading: loadingStatus, error: statusError }] = 
    useMutation(CHECK_PAYMENT_STATUS);
  
  // 加载支付URL或表单
  useEffect(() => {
    const fetchPaymentUrl = async () => {
      try {
        // 根据支付方式决定返回类型
        const returnType = paymentMethod === 'wxpay' ? 'QRCODE' : 'URL'; // 改为URL以便自动跳转
        
        const { data } = await getPaymentUrl({
          variables: {
            input: {
              orderId,
              returnType,
              paymentMethod
            }
          }
        });
        
        if (data?.getPaymentUrl?.status) {
          const url = data.getPaymentUrl.paymentUrl || '';
          setPaymentUrl(url);
          setPaymentHtml(data.getPaymentUrl.paymentHtml || '');
          setQrcodeUrl(data.getPaymentUrl.qrcodeUrl || '');
          setIsMobile(data.getPaymentUrl.isMobile || false);
          setIsWeixin(data.getPaymentUrl.isWeixin || false);
          setPaymentLoaded(true);
          
          // 对于支付宝支付，直接跳转到支付链接
          if (paymentMethod === 'alipay' && url) {
            window.location.href = url;
            return;
          }
          
          // 如果是移动设备且不在微信中，且有支付URL，直接跳转
          if (data.getPaymentUrl.isMobile && !data.getPaymentUrl.isWeixin && url) {
            window.location.href = url;
          }
        }
      } catch (err) {
        console.error('获取支付链接失败:', err);
      }
    };
    
    fetchPaymentUrl();
  }, [orderId, paymentMethod, getPaymentUrl]);
  
  // 定时检查支付状态 - 只有在支付数据加载成功后才开始检查
  useEffect(() => {
    if (!orderId || !paymentLoaded) return;
    
    let statusTimer: ReturnType<typeof setTimeout>;
    
    // 延迟5秒后开始检查支付状态，以便用户有时间去支付
    const startCheckTimeout = setTimeout(() => {
      const checkStatus = async () => {
        if (isChecking) return;
        
        setIsChecking(true);
        try {
          const { data } = await checkPaymentStatus({
            variables: {
              input: {
                orderId
              }
            }
          });
          
          if (data?.checkPaymentStatus?.isPaid) {
            // 支付成功
            clearInterval(statusTimer);
            onPaymentSuccess();
          }
        } catch (err) {
          console.error('检查支付状态失败:', err);
        } finally {
          setIsChecking(false);
        }
      };
      
      // 开始定时检查
      statusTimer = setInterval(checkStatus, 3000);
    }, 5000); // 延迟5秒开始检查
    
    return () => {
      clearTimeout(startCheckTimeout);
      clearInterval(statusTimer);
    };
  }, [orderId, checkPaymentStatus, isChecking, onPaymentSuccess, paymentLoaded]);
  
  // 渲染支付宝表单
  const renderAlipayForm = () => {
    if (!paymentHtml && !paymentUrl) return null;
    
    // 如果有HTML表单则显示表单
    if (paymentHtml) {
      return (
        <div className="bg-white p-4 rounded-md border border-gray-200">
          <div 
            dangerouslySetInnerHTML={{ __html: paymentHtml }} 
            className="alipay-form"
          />
        </div>
      );
    }
    
    // 否则显示跳转按钮（通常不会进入此逻辑，因为我们已经在获取URL后直接跳转）
    return (
      <div className="bg-white p-4 rounded-md border border-gray-200 text-center">
        <p className="mb-4">正在跳转到支付宝...</p>
        <a 
          href={paymentUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-block px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          如未自动跳转，请点击此处
        </a>
      </div>
    );
  };
  
  // 渲染微信支付二维码
  const renderWxpayQrCode = () => {
    if (!qrcodeUrl) return null;
    
    return (
      <div className="flex flex-col items-center justify-center p-6 bg-white rounded-md border border-gray-200">
        <div className="text-green-600 mb-4 text-xl font-bold">微信扫码支付</div>
        <div className="mb-4 p-3 bg-white border-2 border-green-500 rounded-md">
          <QRCodeSVG value={qrcodeUrl} size={200} />
        </div>
        <div className="text-gray-500 text-sm">
          请使用微信扫描二维码进行支付
        </div>
      </div>
    );
  };
  
  // 直接跳转微信H5支付
  const handleWxH5Redirect = () => {
    if (paymentUrl && isMobile && !isWeixin) {
      window.location.href = paymentUrl;
    }
  };
  
  // 错误处理
  if (urlError || statusError) {
    return (
      <div className="text-red-500 p-4">
        支付处理出错: {urlError?.message || statusError?.message}
      </div>
    );
  }
  
  // 加载状态
  if (loadingUrl) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        <span className="ml-3">准备支付环境...</span>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">订单支付</h2>
        <button 
          onClick={onCancel}
          className="text-gray-500 hover:text-gray-700"
        >
          取消
        </button>
      </div>
      
      {paymentMethod === 'alipay' && renderAlipayForm()}
      {paymentMethod === 'wxpay' && renderWxpayQrCode()}
      {paymentMethod === 'wxpay' && isMobile && !isWeixin && (
        <div className="mt-4">
          <button
            onClick={handleWxH5Redirect}
            className="w-full py-2 px-4 bg-green-500 text-white rounded-md"
          >
            跳转到微信支付
          </button>
        </div>
      )}
      
      <div className="mt-4 text-center text-sm text-gray-500">
        {(loadingStatus || isChecking) && paymentLoaded ? (
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-gray-400 mr-2"></div>
            检查支付状态中...
          </div>
        ) : paymentLoaded ? (
          '支付完成后将自动跳转'
        ) : (
          '正在加载支付界面...'
        )}
      </div>
    </div>
  );
};

export default PaymentProcessor; 