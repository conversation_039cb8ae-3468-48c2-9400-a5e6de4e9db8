import React, { useState } from 'react';
import { useQuery } from '@apollo/client';
import { GET_PAYMENT_METHODS } from '../../lib/graphql/queries';
import Image from 'next/image';

type PaymentMethod = {
  id: string;
  title: string;
  icon: string;
  color: string;
};

type PaymentMethodSelectorProps = {
  selectedMethod: string;
  onSelect: (methodId: string) => void;
  disabled?: boolean;
};

const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  selectedMethod,
  onSelect,
  disabled = false
}) => {
  const { loading, error, data } = useQuery(GET_PAYMENT_METHODS);
  
  if (loading) return <div className="animate-pulse h-16 bg-gray-200 rounded-md"></div>;
  if (error) return <div className="text-red-500">获取支付方式失败: {error.message}</div>;
  
  const methods = data?.paymentMethods || [];
  
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">选择支付方式</h3>
      <div className="grid gap-3">
        {methods.map((method: PaymentMethod) => (
          <button
            key={method.id}
            className={`flex items-center p-3 border rounded-lg transition-colors ${
              selectedMethod === method.id 
              ? `border-2 border-primary-500 bg-primary-50` 
              : 'border-gray-200 hover:bg-gray-50'
            } ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}`}
            onClick={() => !disabled && onSelect(method.id)}
            disabled={disabled}
            style={{
              borderColor: selectedMethod === method.id ? method.color : undefined,
              backgroundColor: selectedMethod === method.id ? `${method.color}10` : undefined
            }}
          >
            <div className="flex-1 flex items-center">
              {method.icon && (
                <div className="w-10 h-10 mr-3 flex-shrink-0">
                  <Image 
                    src={method.icon} 
                    alt={method.title} 
                    width={40} 
                    height={40} 
                    className="object-contain" 
                  />
                </div>
              )}
              <div className="flex-1">
                <div className="font-medium text-gray-900">{method.title}</div>
              </div>
            </div>
            <div className="w-6 h-6 flex-shrink-0 ml-2">
              {selectedMethod === method.id && (
                <div 
                  className="w-5 h-5 rounded-full flex items-center justify-center"
                  style={{ backgroundColor: method.color || '#4F46E5' }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default PaymentMethodSelector; 