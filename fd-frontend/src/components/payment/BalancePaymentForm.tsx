import React, { useState, useEffect } from 'react';
import { useMutation, useQuery } from '@apollo/client';
import { PAY_WITH_BALANCE, PAY_ORDER_WITH_BALANCE } from '../../lib/graphql/mutations';
import { GET_USER_BALANCE_AND_POINTS } from '../../lib/graphql/queries';

type BalancePaymentFormProps = {
  title: string;
  amount: number;
  description?: string;
  orderId: string;
  onPaymentSuccess: (orderId: string, orderNumber: string) => void;
  onError: (error: Error) => void;
  productType?: string;
  productId?: string;
  metadata?: string;
};

const BalancePaymentForm: React.FC<BalancePaymentFormProps> = ({
  title,
  amount,
  description = '',
  orderId,
  onPaymentSuccess,
  onError,
  productType,
  productId,
  metadata
}) => {
  const [userBalance, setUserBalance] = useState(0);
  const [insufficientFunds, setInsufficientFunds] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [paymentCompleted, setPaymentCompleted] = useState(false);
  
  // 获取用户余额
  const { loading: loadingBalance, error: balanceError, data: balanceData } = 
    useQuery(GET_USER_BALANCE_AND_POINTS);
  
  // 根据是否有订单ID选择合适的突变
  const isExistingOrder = !!orderId;
  
  // 余额支付（使用已有订单ID）
  const [payOrderWithBalance, { loading: loadingOrderPayment }] = useMutation(PAY_ORDER_WITH_BALANCE, {
    onCompleted: (data) => {
      if (data?.payOrderWithBalance?.status) {
        setPaymentCompleted(true);
        onPaymentSuccess(
          data.payOrderWithBalance.orderId,
          data.payOrderWithBalance.orderNumber
        );
      }
      setIsSubmitting(false);
    },
    onError: (error) => {
      setIsSubmitting(false);
      onError(error);
    }
  });
  
  // 余额支付（创建新订单）
  const [payWithBalance, { loading: loadingPayment }] = useMutation(PAY_WITH_BALANCE, {
    onCompleted: (data) => {
      if (data?.payWithBalance?.status) {
        setPaymentCompleted(true);
        onPaymentSuccess(
          data.payWithBalance.orderId,
          data.payWithBalance.orderNumber
        );
      }
      setIsSubmitting(false);
    },
    onError: (error) => {
      setIsSubmitting(false);
      onError(error);
    }
  });
  
  // 更新用户余额
  useEffect(() => {
    if (balanceData?.viewer?.balance !== undefined) {
      setUserBalance(parseFloat(balanceData.viewer.balance));
      setInsufficientFunds(parseFloat(balanceData.viewer.balance) < amount);
    }
  }, [balanceData, amount]);
  
  // 处理支付
  const handlePayment = async () => {
    // 防止重复提交
    if (isSubmitting || paymentCompleted || insufficientFunds) return;
    
    setIsSubmitting(true);
    
    try {
      if (isExistingOrder) {
        // 使用已有订单ID进行支付
        await payOrderWithBalance({
          variables: {
            input: {
              orderId
            }
          }
        });
      } else {
        // 创建新订单并支付
        await payWithBalance({
          variables: {
            input: {
              title,       // 必需字段：订单标题
              amount,      // 必需字段：支付金额
              description  // 可选字段：订单描述
            }
          }
        });
      }
    } catch (error) {
      // 错误已在onError回调中处理
      setIsSubmitting(false);
    }
  };
  
  // 计算加载状态
  const isLoading = loadingPayment || loadingOrderPayment;
  
  if (loadingBalance) {
    return <div className="animate-pulse h-20 bg-gray-200 rounded-md"></div>;
  }
  
  if (balanceError) {
    return <div className="text-red-500">获取余额信息失败: {balanceError.message}</div>;
  }
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center p-4 bg-blue-50 border border-blue-200 rounded-md">
        <div>
          <div className="text-sm text-gray-600">当前余额</div>
          <div className="text-xl font-bold text-blue-600">¥{userBalance.toFixed(2)}</div>
        </div>
        <div>
          <div className="text-sm text-gray-600">支付金额</div>
          <div className="text-xl font-bold text-blue-600">¥{amount.toFixed(2)}</div>
        </div>
      </div>
      
      {insufficientFunds && (
        <div className="text-red-500 text-sm p-2 bg-red-50 rounded-md">
          余额不足，请先充值或选择其他支付方式
        </div>
      )}
      
      <button
        onClick={handlePayment}
        disabled={insufficientFunds || isLoading || isSubmitting || paymentCompleted}
        className={`w-full py-3 px-4 rounded-md text-white font-medium ${
          insufficientFunds || isSubmitting || paymentCompleted ? 'bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'
        } ${isLoading || isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
      >
        {isLoading || isSubmitting ? '支付处理中...' : paymentCompleted ? '支付已完成' : '确认支付'}
      </button>
      
      {paymentCompleted && (
        <div className="text-green-500 text-sm p-2 bg-green-50 rounded-md text-center">
          支付已成功，请稍候...
        </div>
      )}
    </div>
  );
};

export default BalancePaymentForm; 