import React, { useState, useEffect } from 'react';
import { useMutation, useQuery } from '@apollo/client';
import { PAY_WITH_POINTS, PAY_ORDER_WITH_POINTS } from '../../lib/graphql/mutations';
import { GET_USER_BALANCE_AND_POINTS } from '../../lib/graphql/queries';

type PointsPaymentFormProps = {
  title: string;
  amount: number; // 积分支付时，amount表示所需积分数
  description?: string;
  orderId: string;
  onPaymentSuccess: (orderId: string, orderNumber: string) => void;
  onError: (error: Error) => void;
  productType?: string;
  productId?: string;
  metadata?: string;
};

const PointsPaymentForm: React.FC<PointsPaymentFormProps> = ({
  title,
  amount,
  description = '',
  orderId,
  onPaymentSuccess,
  onError,
  productType,
  productId,
  metadata
}) => {
  const [userPoints, setUserPoints] = useState(0);
  const [insufficientPoints, setInsufficientPoints] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [paymentCompleted, setPaymentCompleted] = useState(false);
  
  // 获取用户积分
  const { loading: loadingPoints, error: pointsError, data: pointsData } = 
    useQuery(GET_USER_BALANCE_AND_POINTS);
  
  // 根据是否有订单ID选择合适的突变
  const isExistingOrder = !!orderId;
  
  // 积分支付（使用已有订单ID）
  const [payOrderWithPoints, { loading: loadingOrderPayment }] = useMutation(PAY_ORDER_WITH_POINTS, {
    onCompleted: (data) => {
      if (data?.payOrderWithPoints?.status) {
        setPaymentCompleted(true);
        onPaymentSuccess(
          data.payOrderWithPoints.orderId,
          data.payOrderWithPoints.orderNumber
        );
      }
      setIsSubmitting(false);
    },
    onError: (error) => {
      setIsSubmitting(false);
      onError(error);
    }
  });
  
  // 积分支付（创建新订单）
  const [payWithPoints, { loading: loadingPayment }] = useMutation(PAY_WITH_POINTS, {
    onCompleted: (data) => {
      if (data?.payWithPoints?.status) {
        setPaymentCompleted(true);
        onPaymentSuccess(
          data.payWithPoints.orderId,
          data.payWithPoints.orderNumber
        );
      }
      setIsSubmitting(false);
    },
    onError: (error) => {
      setIsSubmitting(false);
      onError(error);
    }
  });
  
  // 更新用户积分
  useEffect(() => {
    if (pointsData?.viewer?.points !== undefined) {
      setUserPoints(parseFloat(pointsData.viewer.points));
      setInsufficientPoints(parseFloat(pointsData.viewer.points) < amount);
    }
  }, [pointsData, amount]);
  
  // 处理支付
  const handlePayment = async () => {
    // 防止重复提交
    if (isSubmitting || paymentCompleted || insufficientPoints) return;
    
    setIsSubmitting(true);
    
    try {
      if (isExistingOrder) {
        // 使用已有订单ID进行支付
        await payOrderWithPoints({
          variables: {
            input: {
              orderId
            }
          }
        });
      } else {
        // 创建新订单并支付
        await payWithPoints({
          variables: {
            input: {
              title,        // 必需字段：订单标题
              points: amount, // amount对应积分数量
              description   // 可选字段：订单描述
            }
          }
        });
      }
    } catch (error) {
      // 错误已在onError回调中处理
      setIsSubmitting(false);
    }
  };
  
  // 计算加载状态
  const isLoading = loadingPayment || loadingOrderPayment;
  
  if (loadingPoints) {
    return <div className="animate-pulse h-20 bg-gray-200 rounded-md"></div>;
  }
  
  if (pointsError) {
    return <div className="text-red-500">获取积分信息失败: {pointsError.message}</div>;
  }
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center p-4 bg-yellow-50 border border-yellow-200 rounded-md">
        <div>
          <div className="text-sm text-gray-600">当前积分</div>
          <div className="text-xl font-bold text-yellow-600">{userPoints.toFixed(0)}</div>
        </div>
        <div>
          <div className="text-sm text-gray-600">支付积分</div>
          <div className="text-xl font-bold text-yellow-600">{amount.toFixed(0)}</div>
        </div>
      </div>
      
      {insufficientPoints && (
        <div className="text-red-500 text-sm p-2 bg-red-50 rounded-md">
          积分不足，请先获取更多积分或选择其他支付方式
        </div>
      )}
      
      <button
        onClick={handlePayment}
        disabled={insufficientPoints || isLoading || isSubmitting || paymentCompleted}
        className={`w-full py-3 px-4 rounded-md text-white font-medium ${
          insufficientPoints || isSubmitting || paymentCompleted ? 'bg-gray-400' : 'bg-yellow-600 hover:bg-yellow-700'
        } ${isLoading || isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
      >
        {isLoading || isSubmitting ? '支付处理中...' : paymentCompleted ? '支付已完成' : '确认支付'}
      </button>
      
      {paymentCompleted && (
        <div className="text-green-500 text-sm p-2 bg-green-50 rounded-md text-center">
          支付已成功，请稍候...
        </div>
      )}
    </div>
  );
};

export default PointsPaymentForm; 