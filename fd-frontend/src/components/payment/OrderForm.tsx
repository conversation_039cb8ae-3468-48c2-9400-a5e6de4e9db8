import React, { useState, useEffect } from 'react';
import { useMutation } from '@apollo/client';
import { CREATE_ORDER } from '../../lib/graphql/mutations';
import PaymentMethodSelector from './PaymentMethodSelector';

type OrderFormProps = {
  onOrderCreated: (orderId: string, paymentMethod: string, orderData?: {
    amount: number;
    title: string;
    description: string;
  }) => void;
  productTitle?: string;
  productAmount?: number;
  productDescription?: string;
  productType?: string;
  productId?: string;
  metadata?: string;
};

const OrderForm: React.FC<OrderFormProps> = ({
  onOrderCreated,
  productTitle = '',
  productAmount = 0,
  productDescription = '',
  productType = '',
  productId = '',
  metadata = ''
}) => {
  const [title, setTitle] = useState(productTitle);
  const [amount, setAmount] = useState(productAmount);
  const [description, setDescription] = useState(productDescription);
  const [paymentMethod, setPaymentMethod] = useState('');
  
  // 当props变化时更新状态
  useEffect(() => {
    if (productTitle) setTitle(productTitle);
    if (productAmount) setAmount(productAmount);
    if (productDescription) setDescription(productDescription);
  }, [productTitle, productAmount, productDescription]);
  
  const [createOrder, { loading, error }] = useMutation(CREATE_ORDER);
  
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!title || amount <= 0 || !paymentMethod) {
      return;
    }
    
    try {
      // 准备变量对象
      const variables: any = {
          input: {
            title,
            amount,
            paymentMethod,
            description
          }
      };
      
      // 如果有产品类型和ID，添加到变量中
      if (productType) {
        variables.input.productType = productType;
      }
      
      if (productId) {
        variables.input.productId = productId;
      }
      
      // 如果有元数据，解析并添加到变量中
      if (metadata) {
        try {
          // 确保metadata是字符串
          if (typeof metadata === 'string') {
            // 直接使用字符串形式的metadata
            variables.input.metadata = metadata;
          } else {
            // 如果不是字符串，则转换为字符串
            variables.input.metadata = JSON.stringify(metadata);
          }
        } catch (e) {
          console.error('处理元数据失败:', e);
        }
      }
      
      const { data } = await createOrder({
        variables
      });
      
      if (data?.createOrder?.status) {
        onOrderCreated(
          data.createOrder.order.id, 
          data.createOrder.order.paymentMethod,
          {
            amount: data.createOrder.order.amount || amount,
            title: data.createOrder.order.title || title,
            description: description
          }
        );
      }
    } catch (err) {
      console.error('创建订单失败:', err);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label htmlFor="title" className="block text-sm font-medium text-gray-700">商品名称</label>
        <input
          type="text"
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          required
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          placeholder="请输入商品名称"
          disabled={loading}
        />
      </div>
      
      <div>
        <label htmlFor="amount" className="block text-sm font-medium text-gray-700">支付金额</label>
        <div className="mt-1 relative rounded-md shadow-sm">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className="text-gray-500 sm:text-sm">¥</span>
          </div>
          <input
            type="number"
            id="amount"
            value={amount || ''}
            onChange={(e) => setAmount(parseFloat(e.target.value))}
            required
            min="0.01"
            step="0.01"
            className="block w-full pl-7 pr-12 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            placeholder="0.00"
            disabled={loading}
          />
        </div>
      </div>
      
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">商品描述</label>
        <textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          rows={3}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          placeholder="请输入商品描述"
          disabled={loading}
        />
      </div>
      
      {productType && (
        <div className="bg-gray-50 p-3 rounded-md">
          <p className="text-sm text-gray-600">商品类型: <strong>{productType}</strong></p>
          {productId && <p className="text-sm text-gray-600">商品ID: <strong>{productId}</strong></p>}
        </div>
      )}
      
      <PaymentMethodSelector
        selectedMethod={paymentMethod}
        onSelect={setPaymentMethod}
        disabled={loading}
      />
      
      {error && (
        <div className="text-red-500 text-sm">
          创建订单失败: {error.message}
        </div>
      )}
      
      <div>
        <button
          type="submit"
          disabled={loading || !title || amount <= 0 || !paymentMethod}
          className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
            loading || !title || amount <= 0 || !paymentMethod
              ? 'opacity-60 cursor-not-allowed'
              : ''
          }`}
        >
          {loading ? '处理中...' : '提交订单'}
        </button>
      </div>
    </form>
  );
};

export default OrderForm; 