"use client";

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '../../hooks/useAuth';
import { useAccountValidation } from '../../hooks/useAccountValidation';
import FormField from '../ui/FormField';
import PasswordInput from '../ui/PasswordInput';
import FormError from '../ui/FormError';
import FormSuccess from '../ui/FormSuccess';
import Button from '../ui/Button';

const PhoneResetPasswordForm: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { 
    sendPhoneCode, 
    verifyPhoneCodeAndGetToken, 
    resetPasswordWithPhoneCode,
    error: authError, 
    clearError, 
    isLoading: authLoading 
  } = useAuth();
  
  const [formData, setFormData] = useState({
    phone: '',
    nationCode: '86',
    code: '',
    newPassword: '',
    confirmPassword: '',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  // 倒计时状态
  const [countdown, setCountdown] = useState(0);
  
  // 验证码验证状态
  const [isVerifyingCode, setIsVerifyingCode] = useState(false);
  const [isCodeValid, setIsCodeValid] = useState<boolean | null>(null);
  const [verificationSuccess, setVerificationSuccess] = useState(false);
  const [verificationToken, setVerificationToken] = useState<string | null>(null);
  // 验证码发送加载状态
  const [verificationLoading, setVerificationLoading] = useState(false);
  
  // 从URL参数中获取手机号（如果有）
  useEffect(() => {
    const phone = searchParams.get('phone');
    if (phone) {
      setFormData(prev => ({ ...prev, phone }));
    }
  }, [searchParams]);
  
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.phone.trim()) {
      newErrors.phone = '请输入手机号';
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone.trim())) {
      newErrors.phone = '请输入有效的手机号';
    }
    
    if (!formData.code.trim()) {
      newErrors.code = '请输入验证码';
    } else if (!/^\d{6}$/.test(formData.code)) {
      newErrors.code = '验证码应为6位数字';
    }
    
    if (!formData.newPassword) {
      newErrors.newPassword = '请输入新密码';
    } else if (formData.newPassword.length < 8) {
      newErrors.newPassword = '密码至少需要8个字符';
    }
    
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = '请确认密码';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 清除该字段的错误
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
    
    // 清除全局错误
    if (error) setError('');
    if (authError) clearError();
    
    // 手机号或验证码变更时，重置验证状态
    if (name === 'phone' || name === 'nationCode' || name === 'code') {
      setIsCodeValid(null);
      setVerificationSuccess(false);
      setVerificationToken(null);
    }
  };
  
  // 处理发送验证码
  const handleSendCode = async () => {
    // 验证手机号格式
    if (!formData.phone || !/^1[3-9]\d{9}$/.test(formData.phone)) {
      setErrors(prev => ({ ...prev, phone: '请输入有效的手机号' }));
      return;
    }
    
    try {
      setVerificationLoading(true);
      console.log('开始发送验证码到手机:', formData.phone);
      const result = await sendPhoneCode({
        phone: formData.phone,
        nationCode: formData.nationCode
      });
      console.log('验证码发送结果:', result);
      
      if (result.success) {
        setSuccess('验证码已发送到您的手机，请查收');
        
        // 开始倒计时
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
        
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(result.message || '发送验证码失败');
      }
    } catch (err) {
      console.error('发送验证码错误:', err);
      const errorMsg = err instanceof Error ? err.message : '发送验证码失败';
      setError(errorMsg);
    } finally {
      setVerificationLoading(false);
    }
  };
  
  // 验证码输入完成后自动验证并获取令牌
  const handleCodeBlur = async () => {
    if (formData.code.length === 6 && formData.phone) {
      setIsVerifyingCode(true);
      
      try {
        // 使用verifyPhoneCodeAndGetToken方法验证验证码
        const result = await verifyPhoneCodeAndGetToken(
          formData.phone, 
          formData.code, 
          formData.nationCode
        );
        
        setIsCodeValid(result.success);
        
        if (result.success) {
          setVerificationSuccess(true);
          setErrors(prev => ({ ...prev, code: '' }));
          
          // 保存验证令牌
          setVerificationToken(result.token || null);
        } else {
          setVerificationSuccess(false);
          setVerificationToken(null);
          setErrors(prev => ({ ...prev, code: result.message || '验证码无效' }));
        }
      } catch (err) {
        setIsCodeValid(false);
        setVerificationSuccess(false);
        setVerificationToken(null);
        const errorMsg = err instanceof Error ? err.message : '验证码验证失败';
        setErrors(prev => ({ ...prev, code: errorMsg }));
      } finally {
        setIsVerifyingCode(false);
      }
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setSuccess('');
    setError('');
    
    // 验证表单
    if (!validateForm()) return;
    
    // 验证码未验证或验证失败
    if (!verificationSuccess || !verificationToken) {
      setError('请先完成手机验证');
      return;
    }
    
    setIsLoading(true);
    
    try {
      // 使用手机号、验证码和新密码重置密码
      const result = await resetPasswordWithPhoneCode(
        formData.phone,
        formData.code,
        formData.newPassword,
        formData.nationCode
      );
      
      if (result.success) {
        setSuccess(result.message || '密码重置成功，请使用新密码登录');
        
        // 延迟跳转到登录页面
        setTimeout(() => {
          router.push('/auth/login');
        }, 2000);
      } else {
        setError(result.message || '密码重置失败，请重试');
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '发生错误，请重试';
      setError(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {(error || authError) && (
        <div className="animate-shake">
          <FormError message={error || authError || ''} />
        </div>
      )}
      
      {success && (
        <div className="animate-fadeIn">
          <FormSuccess message={success} />
        </div>
      )}
      
      <div className="mb-4">
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
          请输入您的手机号和收到的验证码来完成密码重置。
        </p>
      </div>
      
      {/* 手机号字段 */}
      <div className="flex items-center mb-4 gap-3">
        <div className="flex-grow">
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
            手机号
          </label>
          <div className="flex rounded-md shadow-sm relative">
            <span className="inline-flex items-center px-3 py-2 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300">
              +{formData.nationCode}
            </span>
            <input
              type="text"
              name="phone"
              id="phone"
              value={formData.phone}
              onChange={handleChange}
              autoComplete="tel"
              placeholder="请输入手机号"
              pattern="^1[3-9]\d{9}$"
              disabled={isLoading || authLoading || isCodeValid === true || !!searchParams.get('phone')}
              required
              className={`flex-1 min-w-0 block w-full px-3 py-2 h-10 rounded-none rounded-r-md border ${
                errors.phone ? 'border-red-500' : 
                isCodeValid === true ? 'border-green-500' : 
                'border-gray-300 dark:border-gray-600'
              } shadow-sm 
              focus:border-primary-500 focus:ring-primary-500
              bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
              disabled:bg-gray-100 dark:disabled:bg-gray-800 disabled:text-gray-500 dark:disabled:text-gray-400
              disabled:cursor-not-allowed`}
            />
          </div>
          {errors.phone && (
            <p className="mt-1 text-xs text-red-500">{errors.phone}</p>
          )}
        </div>
        <div className="w-32 self-end mb-[1px]">
          <Button
            type="button"
            onClick={handleSendCode}
            disabled={
              isLoading || 
              authLoading || 
              !formData.phone || 
              countdown > 0 || 
              isCodeValid === true
            }
            variant="primary"
            className="w-full h-10 text-sm"
            isLoading={verificationLoading}
          >
            {countdown > 0 ? `${countdown}秒` : '获取验证码'}
          </Button>
        </div>
      </div>
      
      {/* 验证码字段 */}
      <div className="space-y-1">
        <div className="flex justify-between">
          <label htmlFor="phone-code" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
            验证码 {isVerifyingCode && <span className="ml-2 text-xs text-gray-500 animate-pulse">验证中...</span>}
          </label>
        </div>
        <div className="relative">
          <input
            type="text"
            name="code"
            id="phone-code"
            value={formData.code}
            onChange={handleChange}
            onBlur={handleCodeBlur}
            placeholder="请输入6位数字验证码"
            disabled={isLoading || authLoading || !formData.phone || isCodeValid === true}
            required
            className={`block w-full px-3 py-2 h-10 border ${
              errors.code ? 'border-red-500' : isCodeValid === true ? 'border-green-500' : isCodeValid === false ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 
            ${isCodeValid === true ? 'focus:border-green-500 focus:ring-green-500' : 'focus:border-primary-500 focus:ring-primary-500'}
            ${(isLoading || authLoading || !formData.phone) ? 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400' : 'bg-white dark:bg-gray-900 text-gray-900 dark:text-white'}
            disabled:cursor-not-allowed`}
            maxLength={6}
          />
          {isCodeValid !== null && !isVerifyingCode && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              {isCodeValid ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              )}
            </div>
          )}
        </div>
        {errors.code && (
          <p className="mt-1 text-xs text-red-500">{errors.code}</p>
        )}
      </div>
      
      {/* 仅当验证码验证成功后显示密码字段 */}
      {verificationSuccess && (
        <>
          <PasswordInput
            label="新密码"
            name="newPassword"
            id="newPassword"
            value={formData.newPassword}
            onChange={handleChange}
            placeholder="请输入新密码"
            disabled={isLoading || authLoading}
            error={errors.newPassword}
            required
            description="密码至少需要8个字符"
          />
          
          <PasswordInput
            label="确认密码"
            name="confirmPassword"
            id="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleChange}
            placeholder="请再次输入新密码"
            disabled={isLoading || authLoading}
            error={errors.confirmPassword}
            required
          />
        </>
      )}
      
      <div>
        <Button
          type="submit"
          className="w-full"
          variant="primary"
          isLoading={isLoading || authLoading}
          disabled={isLoading || authLoading || !verificationSuccess}
        >
          重置密码
        </Button>
      </div>
      
      <div className="text-center">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          没有收到验证码？{' '}
          <Link
            href="/forgot-password"
            className="text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
          >
            重新发送
          </Link>
        </p>
      </div>
    </form>
  );
};

export default PhoneResetPasswordForm; 