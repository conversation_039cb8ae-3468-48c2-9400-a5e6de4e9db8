"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '../../hooks/useAuth';
import FormField from '../ui/FormField';
import PasswordInput from '../ui/PasswordInput';
import FormSuccess from '../ui/FormSuccess';
import Button from '../ui/Button';

// 扩展 Window 接口
declare global {
  interface Window {
    __LOGIN_REDIRECT_IN_PROGRESS__?: boolean;
  }
}

interface LoginFormProps {
  callbackUrl?: string;
}

const LoginForm: React.FC<LoginFormProps> = ({ callbackUrl = '/' }) => {
  const router = useRouter();
  const { login, error: authError, clearError, isLoading: authLoading } = useAuth();

  // 添加调试日志
  console.log('LoginForm - callbackUrl:', callbackUrl);
  
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');
  const errorTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // 清除先前的错误定时器
  const clearErrorTimer = () => {
    if (errorTimerRef.current) {
      clearTimeout(errorTimerRef.current);
      errorTimerRef.current = null;
    }
  };
  
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.username.trim()) {
      newErrors.username = '请输入账号';
    } else {
      // 简单验证格式：
      // 1. 用户名: 字母、数字、下划线的组合
      // 2. 邮箱: 包含@符号
      // 3. 手机号: 11位数字，以1开头
      const isEmail = formData.username.includes('@');
      const isPhone = /^1[3-9]\d{9}$/.test(formData.username);
      const isUsername = /^[a-zA-Z0-9_]+$/.test(formData.username);
      
      if (!isEmail && !isPhone && !isUsername) {
        newErrors.username = '请输入有效的用户名、邮箱或手机号';
      }
    }
    
    if (!formData.password) {
      newErrors.password = '请输入密码';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 清除该字段的错误
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
    
    // 不要在输入时立即清除错误消息
    // 而是在提交时清除
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 清除之前的消息和定时器
    setSuccess('');
    clearErrorTimer();
    clearError();
    setError('');
    
    // 表单验证
    if (!validateForm()) return;
    
    setIsLoading(true);
    
    try {
      await login({
        username: formData.username,
        password: formData.password
      });
      
      // 登录成功
      setSuccess('登录成功，正在跳转...');

      // 添加调试日志
      console.log('登录成功，准备跳转到:', callbackUrl);

      // 使用 router.push 进行跳转，避免页面刷新
      // 设置一个标记，防止 ProtectedRoute 重复跳转
      if (typeof window !== 'undefined') {
        window.__LOGIN_REDIRECT_IN_PROGRESS__ = true;
      }

      setTimeout(() => {
        console.log('执行跳转到:', callbackUrl);
        router.push(callbackUrl);

        // 清除标记
        setTimeout(() => {
          if (typeof window !== 'undefined') {
            window.__LOGIN_REDIRECT_IN_PROGRESS__ = false;
          }
        }, 1000);
      }, 100); // 短暂延迟确保状态更新完成
      
    } catch (err) {
      // 确保密码字段被清空，增加安全性
      setFormData(prev => ({ ...prev, password: '' }));
      
      // 0. 优先检查全局GraphQL错误变量
      if (typeof window !== 'undefined' && window.__LAST_GRAPHQL_ERROR__) {
        const graphQLError = window.__LAST_GRAPHQL_ERROR__;
        
        // 使用GraphQL错误消息
        if (graphQLError.message) {
          // 先清除HTML标签
          let errorMsg = graphQLError.message.replace(/<\/?[^>]+(>|$)/g, "");
          
          // 替换特定错误消息
          if (errorMsg.includes('未知用户名') || errorMsg.includes('请再次检查或改用您的邮箱地址')) {
            errorMsg = '您输入的账号不存在，请重新输入';
          } else if (errorMsg.includes('用户不存在') || errorMsg.includes('does not exist')) {
            errorMsg = '您输入的账号不存在，请重新输入';
          } else if (errorMsg.includes('密码错误') || errorMsg.includes('incorrect password')) {
            errorMsg = '密码不正确，请重新输入';
          } else if (errorMsg.includes('invalid_credentials') || errorMsg.includes('不正确的凭据')) {
            errorMsg = '账号或密码不正确，请重新输入';
          } else if (errorMsg.includes('application password')) {
            errorMsg = '登录验证失败，请稍后重试';
          } else if (errorMsg.includes('该手机号未注册')) {
            errorMsg = '该手机号未注册，请确认后重试';
          }
          
          setError(errorMsg);
          
          // 使用后清除全局变量
          window.__LAST_GRAPHQL_ERROR__ = null;
          
          // 设置定时器，延迟清除错误
          errorTimerRef.current = setTimeout(() => {
            setError('');
            clearError();
          }, 5000);
          return;
        }
      }
      
      // 1. 检查是否有增强的错误对象（由AuthContext添加）
      if (err && typeof err === 'object' && (('originalGraphQLError' in err) || ('originalNetworkError' in err))) {
        // 使用增强的错误消息
        const errorMsg = err instanceof Error ? err.message : '登录失败，请重试';
        setError(errorMsg);
        
        // 设置定时器，延迟清除错误
        errorTimerRef.current = setTimeout(() => {
          setError('');
          clearError();
        }, 5000);
        return;
      }
      
      // 2. 继续尝试其他方法提取错误
      // 首先尝试从Apollo错误中直接提取GraphQL错误信息
      if (err && typeof err === 'object' && 'graphQLErrors' in err && Array.isArray(err.graphQLErrors) && err.graphQLErrors.length > 0) {
        const graphQLError = err.graphQLErrors[0];
        if (graphQLError && typeof graphQLError === 'object' && 'message' in graphQLError) {
          // 清除HTML标签
          let errorMsg = graphQLError.message.replace(/<\/?[^>]+(>|$)/g, "");
          
          // 替换特定错误消息
          if (errorMsg.includes('未知用户名') || errorMsg.includes('请再次检查或改用您的邮箱地址')) {
            errorMsg = '您输入的账号不存在，请重新输入';
          } else if (errorMsg.includes('用户不存在') || errorMsg.includes('does not exist')) {
            errorMsg = '您输入的账号不存在，请重新输入';
          } else if (errorMsg.includes('密码错误') || errorMsg.includes('incorrect password')) {
            errorMsg = '密码不正确，请重新输入';
          } else if (errorMsg.includes('invalid_credentials') || errorMsg.includes('不正确的凭据')) {
            errorMsg = '用户名或密码不正确，请重新输入';
          } else if (errorMsg.includes('application password')) {
            errorMsg = '登录验证失败，请稍后重试';
          }
          
          setError(errorMsg);
          
          // 设置定时器，延迟清除错误
          errorTimerRef.current = setTimeout(() => {
            setError('');
            clearError();
          }, 5000);
          return;
        }
      }
      
      // 3. 从AuthContext获取错误信息，如果存在的话
      if (authError) {
        // 清除HTML标签
        let errorMsg = authError.replace(/<\/?[^>]+(>|$)/g, "");
        
        // 替换特定错误消息
        if (errorMsg.includes('未知用户名') || errorMsg.includes('请再次检查或改用您的邮箱地址')) {
          errorMsg = '您输入的账号不存在，请重新输入';
        } else if (errorMsg.includes('用户不存在') || errorMsg.includes('does not exist')) {
          errorMsg = '您输入的账号不存在，请重新输入';
        } else if (errorMsg.includes('密码错误') || errorMsg.includes('incorrect password')) {
          errorMsg = '密码不正确，请重新输入';
        } else if (errorMsg.includes('invalid_credentials') || errorMsg.includes('不正确的凭据')) {
          errorMsg = '账号或密码不正确，请重新输入';
        } else if (errorMsg.includes('application password')) {
          errorMsg = '登录验证失败，请稍后重试';
        }
        
        setError(errorMsg);
      } else {
        // 获取错误消息并清除HTML标签
        let errorMsg = err instanceof Error ? err.message.replace(/<\/?[^>]+(>|$)/g, "") : '登录失败，请重试';
        
        // 格式化常见错误信息，使其更友好
        if (errorMsg.includes('未知用户名') || errorMsg.includes('请再次检查或改用您的邮箱地址')) {
          errorMsg = '您输入的账号不存在，请重新输入';
        } else if (errorMsg.includes('incorrect password') || errorMsg.includes('密码不正确')) {
          errorMsg = '密码不正确，请重新输入';
        } else if (errorMsg.includes('not found') || errorMsg.includes('未找到')) {
          errorMsg = '您输入的账号不存在，请重新输入';
        } else if (errorMsg.includes('invalid_credentials') || errorMsg.includes('不正确的凭据')) {
          errorMsg = '账号或密码不正确，请重新输入';
        } else if (errorMsg.includes('application password')) {
          errorMsg = '登录验证失败，请稍后重试';
        } else if (errorMsg.includes('该手机号未注册')) {
          errorMsg = '该手机号未注册，请确认后重试';
        } else if (errorMsg === '登录失败') {
          // 这是通用错误，尝试查找更具体的原因
          if (err && typeof err === 'object' && 'networkError' in err) {
            errorMsg = '网络连接错误，请检查您的网络连接并重试';
          }
        }
        
        // 设置错误消息状态
        setError(errorMsg);
      }
      
      // 设置定时器，延迟清除错误
      errorTimerRef.current = setTimeout(() => {
        setError('');
        clearError();
      }, 5000); // 延长到5秒，确保用户能看到
    } finally {
      setIsLoading(false);
    }
  };
  
  // 在组件卸载时清除定时器
  useEffect(() => {
    return () => clearErrorTimer();
  }, []);
  
  // 从AuthContext同步错误状态
  useEffect(() => {
    if (authError && !error) {
      setError(authError);
      
      // 设置定时器，延迟清除错误
      clearErrorTimer();
      errorTimerRef.current = setTimeout(() => {
        setError('');
        clearError();
      }, 5000); // 延长到5秒
    }
  }, [authError, error, clearError]);
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 成功提示 - 添加淡入效果 */}
      {success && (
        <div className="animate-fadeIn">
          <FormSuccess message={success} />
        </div>
      )}
      
      {/* 用户名/邮箱/手机号字段 */}
      <FormField
        label="账号"
        type="text"
        name="username"
        id="username"
        value={formData.username}
        onChange={handleChange}
        autoComplete="username"
        placeholder="请输入用户名、邮箱或手机号"
        error={errors.username}
        disabled={isLoading || authLoading}
        required
      />
      
      {/* 密码字段 */}
      <PasswordInput
        label="密码"
        name="password"
        id="password"
        value={formData.password}
        onChange={handleChange}
        autoComplete="current-password"
        placeholder="请输入密码"
        error={errors.password}
        disabled={isLoading || authLoading}
        required
      />
      
      {/* 忘记密码链接 */}
      <div className="text-right">
        <Link
          href="/forgot-password"
          className="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
        >
          忘记密码？
        </Link>
      </div>
      
      {/* 错误提示 - 位于登录按钮正上方，优化样式和过渡效果 */}
      {(error || authError) && (
        <div className="animate-shake mt-4 p-3 bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 dark:border-red-500 rounded-md shadow-md transition-all duration-300 ease-in-out">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <p className="text-sm text-red-700 dark:text-red-200 font-medium">
              {error || authError || ''}
            </p>
          </div>
        </div>
      )}
      
      {/* 登录按钮 */}
      <div>
        <Button
          type="submit"
          className="w-full"
          variant="primary"
          isLoading={isLoading || authLoading}
          disabled={isLoading || authLoading}
        >
          登录
        </Button>
      </div>
      
      {/* 注册链接 */}
      <div className="text-center">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          还没有账号？{' '}
          <Link
            href="/register"
            className="text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
          >
            立即注册
          </Link>
        </p>
      </div>
    </form>
  );
};

export default LoginForm; 