"use client";

// @ts-nocheck
import React, { useEffect } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { useAuth } from '../../hooks/useAuth';

// 扩展 Window 接口
declare global {
  interface Window {
    __LOGIN_REDIRECT_IN_PROGRESS__?: boolean;
  }
}

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
  requireAuth?: boolean;
  requireGuest?: boolean;
  callbackForGuest?: string; // 新增：已登录用户访问guest页面时的重定向地址
}

/**
 * 保护路由组件
 * 
 * 该组件用于控制页面访问权限：
 * - requireAuth=true: 只允许已登录用户访问，未登录用户将被重定向到登录页面
 * - requireGuest=true: 只允许未登录用户访问，已登录用户将被重定向到主页
 * - 两者都为false: 不限制访问
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  redirectTo = '/login',
  requireAuth = false,
  requireGuest = false,
  callbackForGuest,
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const callbackFromUrl = searchParams?.get('callbackUrl');

  // 优先使用URL参数中的callbackUrl，只有当它不存在时才使用props传入的callbackForGuest
  // 但如果callbackForGuest是默认值'/'，则忽略它
  const finalCallbackForGuest = callbackFromUrl || (callbackForGuest && callbackForGuest !== '/' ? callbackForGuest : null);

  // 添加调试日志
  console.log('ProtectedRoute - callbackForGuest:', callbackForGuest);
  console.log('ProtectedRoute - callbackFromUrl:', callbackFromUrl);
  console.log('ProtectedRoute - finalCallbackForGuest:', finalCallbackForGuest);
  console.log('ProtectedRoute - isAuthenticated:', isAuthenticated);
  console.log('ProtectedRoute - requireGuest:', requireGuest);

  useEffect(() => {
    // 等待认证状态加载完成
    if (isLoading) return;

    // 处理需要认证的路由
    if (requireAuth && !isAuthenticated) {
      // 保存当前URL作为登录后的回调地址
      router.push(`${redirectTo}?callbackUrl=${encodeURIComponent(pathname)}`);
      return;
    }

    // 处理只允许未登录用户访问的路由（如登录、注册页面）
    if (requireGuest && isAuthenticated) {
      // 检查是否有登录跳转正在进行，避免重复跳转
      if (typeof window !== 'undefined' && window.__LOGIN_REDIRECT_IN_PROGRESS__) {
        console.log('ProtectedRoute - 登录跳转正在进行，跳过重复跳转');
        return;
      }

      console.log('ProtectedRoute - 已登录用户访问guest页面，准备跳转到:', finalCallbackForGuest || '/');
      if (finalCallbackForGuest) {
        router.push(finalCallbackForGuest);
      } else {
        router.push('/');
      }
      return;
    }
  }, [isAuthenticated, isLoading, requireAuth, requireGuest, redirectTo, router, pathname, finalCallbackForGuest]);

  // 如果认证状态仍在加载中，可以显示加载状态
  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 如果需要认证但用户未登录，或者需要未登录但用户已登录，则不渲染内容
  // (实际上重定向会在useEffect中触发)
  if ((requireAuth && !isAuthenticated) || (requireGuest && isAuthenticated)) {
    return null;
  }

  // 正常渲染内容
  return <>{children}</>;
};

export default ProtectedRoute; 