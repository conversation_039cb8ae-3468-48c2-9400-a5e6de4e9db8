"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '../../hooks/useAuth';
import FormError from '../ui/FormError';
import Button from '../ui/Button';

type ResetMethod = 'email' | 'phone' | 'choice';

const ForgotPasswordForm: React.FC = () => {
  const router = useRouter();
  const { error: authError, clearError } = useAuth();
  
  const [error, setError] = useState('');
  
  const handleChooseMethod = (method: ResetMethod) => {
    setError('');
    if (method === 'phone') {
      router.push('/auth/phone-reset-password');
    } else if (method === 'email') {
      // 直接跳转到邮箱重置密码表单页面
      router.push('/auth/reset-password');
    }
  };
  
  return (
    <div className="space-y-6">
      {(error || authError) && (
        <div className="animate-shake">
          <FormError message={error || authError || ''} />
        </div>
      )}
      
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          选择密码找回方式
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          请选择您想要使用的密码重置验证方式
        </p>
      </div>
      
      <div className="grid grid-cols-1 gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => handleChooseMethod('email')}
          className="flex items-center justify-center gap-3 py-3"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
          </svg>
          <span>使用邮箱地址验证</span>
        </Button>
        
        <Button
          type="button"
          variant="outline"
          onClick={() => handleChooseMethod('phone')}
          className="flex items-center justify-center gap-3 py-3"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
          </svg>
          <span>使用手机号验证</span>
        </Button>
      </div>
      
      <div className="text-center">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          记起密码了？{' '}
          <Link
            href="/login"
            className="text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
          >
            返回登录
          </Link>
        </p>
      </div>
    </div>
  );
};

export default ForgotPasswordForm; 