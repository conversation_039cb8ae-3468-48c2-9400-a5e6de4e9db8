"use client";

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { useAuth } from '../../hooks/useAuth';
import FormField from '../ui/FormField';
import FormError from '../ui/FormError';
import FormSuccess from '../ui/FormSuccess';
import Button from '../ui/Button';
import { useRouter } from 'next/navigation';

const ChangePasswordForm: React.FC = () => {
  const router = useRouter();
  const { changePassword, error: authError, clearError, isLoading: authLoading } = useAuth();
  
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    // 当前密码不能为空
    if (!formData.currentPassword) {
      newErrors.currentPassword = '请输入当前密码';
    }
    
    // 新密码验证
    if (!formData.newPassword) {
      newErrors.newPassword = '请输入新密码';
    } else if (formData.newPassword.length < 8) {
      newErrors.newPassword = '密码必须至少包含8个字符';
    }
    
    // 确认密码验证
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = '请确认新密码';
    } else if (formData.confirmPassword !== formData.newPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 清除该字段的错误
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
    
    // 清除全局错误
    if (error) setError('');
    if (authError) clearError();
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setSuccess('');
    setError('');
    
    if (!validateForm()) return;
    
    setIsLoading(true);
    
    try {
      await changePassword(formData.currentPassword, formData.newPassword);
      
      setSuccess('密码已成功修改');
      setFormData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
      
      // 延迟跳转回资料页面
      setTimeout(() => {
        router.push('/auth/profile');
      }, 2000);
      
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '密码修改失败，请重试';
      setError(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">修改密码</h2>
        <Link 
          href="/profile" 
          className="mt-2 sm:mt-0 inline-flex justify-center items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none"
        >
          返回个人资料
        </Link>
      </div>
      
      <div className="bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">密码设置</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">请填写以下表单修改您的密码</p>
        </div>
        
        <form onSubmit={handleSubmit} className="p-4 sm:p-6">
          {(error || authError) && (
            <FormError message={error || authError || ''} />
          )}
          
          {success && (
            <FormSuccess message={success} />
          )}
          
          <div className="mb-6">
            <FormField
              label="当前密码"
              type="password"
              name="currentPassword"
              id="currentPassword"
              value={formData.currentPassword}
              onChange={handleChange}
              placeholder="请输入当前密码"
              required
              disabled={isLoading || authLoading}
              error={errors.currentPassword}
            />
          </div>
          
          <div className="mb-6">
            <FormField
              label="新密码"
              type="password"
              name="newPassword"
              id="newPassword"
              value={formData.newPassword}
              onChange={handleChange}
              placeholder="请输入新密码"
              required
              disabled={isLoading || authLoading}
              error={errors.newPassword}
              description="密码必须至少包含8个字符"
            />
          </div>
          
          <div className="mb-6">
            <FormField
              label="确认新密码"
              type="password"
              name="confirmPassword"
              id="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleChange}
              placeholder="请再次输入新密码"
              required
              disabled={isLoading || authLoading}
              error={errors.confirmPassword}
            />
          </div>
          
          <div className="flex justify-end">
            <Button
              type="submit"
              variant="primary"
              isLoading={isLoading || authLoading}
              disabled={isLoading || authLoading}
            >
              修改密码
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ChangePasswordForm; 