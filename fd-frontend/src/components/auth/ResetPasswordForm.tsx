"use client";

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '../../hooks/useAuth';
import { useVerificationCode } from '../../hooks/useVerificationCode';
import PasswordInput from '../ui/PasswordInput';
import FormError from '../ui/FormError';
import FormSuccess from '../ui/FormSuccess';
import Button from '../ui/Button';

// 验证码输入组件
const VerificationCodeInput = ({ 
  value, 
  onChange, 
  disabled, 
  error, 
  isValid,
  isVerifying,
  onBlur
}: { 
  value: string, 
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void, 
  disabled?: boolean, 
  error?: string,
  isValid: boolean | null,
  isVerifying: boolean,
  onBlur: () => void
}) => {
  return (
    <div className="space-y-1">
      <div className="flex items-center justify-between">
        <label htmlFor="code" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
          验证码 {isVerifying && <span className="ml-2 text-xs text-gray-500 animate-pulse">验证中...</span>}
        </label>
      </div>
      <div className="relative">
        <input
          type="text"
          name="code"
          id="code"
          value={value}
          onChange={onChange}
          onBlur={onBlur}
          disabled={disabled}
          placeholder="请输入6位数字验证码"
          required
          className={`block w-full px-3 py-2 border ${
            error ? 'border-red-500' : isValid === true ? 'border-green-500' : isValid === false ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
          } rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 
          ${isValid === true ? 'focus:border-green-500 focus:ring-green-500' : 'focus:border-primary-500 focus:ring-primary-500'}
          ${disabled ? 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400' : 'bg-white dark:bg-gray-900 text-gray-900 dark:text-white'}
          disabled:cursor-not-allowed`}
          aria-invalid={error ? "true" : "false"}
        />
        {isValid !== null && !isVerifying && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            {isValid ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            )}
          </div>
        )}
      </div>
      {error && (
        <p className="mt-1 text-xs text-red-500">{error}</p>
      )}
    </div>
  );
};

const ResetPasswordForm: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { 
    resetPasswordWithCode, 
    verifyResetCode, 
    error: authError, 
    clearError, 
    isLoading: authLoading 
  } = useAuth();
  
  // 使用验证码钩子来管理验证码发送和计时
  const { 
    sendVerificationCode, 
    verifyVerificationCode, 
    isLoading: verificationLoading, 
    error: verificationError, 
    countdown,
    clearError: clearVerificationError 
  } = useVerificationCode({ defaultType: 'password_reset' });
  
  const [formData, setFormData] = useState({
    email: '',
    code: '',
    newPassword: '',
    confirmPassword: '',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isVerifyingCode, setIsVerifyingCode] = useState(false);
  const [isCodeValid, setIsCodeValid] = useState<boolean | null>(null);
  
  // 从URL参数中获取邮箱地址（如果有）
  useEffect(() => {
    const email = searchParams.get('email');
    if (email) {
      setFormData(prev => ({ ...prev, email }));
    }
  }, [searchParams]);
  
  // 验证验证码
  const validateCode = async () => {
    if (!formData.email || !formData.code || !/^\d{6}$/.test(formData.code)) {
      return;
    }
    
    setIsVerifyingCode(true);
    
    try {
      const result = await verifyResetCode(formData.email, formData.code);
      setIsCodeValid(result.isValid);
      
      if (!result.isValid) {
        setErrors(prev => ({ ...prev, code: result.message || '验证码无效' }));
      } else {
        setErrors(prev => ({ ...prev, code: '' }));
      }
    } catch (err) {
      setIsCodeValid(false);
      const errorMsg = err instanceof Error ? err.message : '验证码验证失败';
      setErrors(prev => ({ ...prev, code: errorMsg }));
    } finally {
      setIsVerifyingCode(false);
    }
  };
  
  // 当验证码和邮箱变化时清除验证状态
  useEffect(() => {
    setIsCodeValid(null);
  }, [formData.code, formData.email]);
  
  // 验证表单
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.email.trim()) {
      newErrors.email = '请输入邮箱地址';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = '请输入有效的邮箱地址';
    }
    
    if (!formData.code.trim()) {
      newErrors.code = '请输入验证码';
    } else if (!/^\d{6}$/.test(formData.code)) {
      newErrors.code = '验证码应为6位数字';
    }
    
    if (!formData.newPassword) {
      newErrors.newPassword = '请输入新密码';
    } else if (formData.newPassword.length < 8) {
      newErrors.newPassword = '密码至少需要8个字符';
    }
    
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = '请确认密码';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // 处理表单字段变更
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 清除该字段的错误
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
    
    // 清除全局错误
    if (error) setError('');
    if (authError) clearError();
    if (verificationError) clearVerificationError();
  };
  
  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setSuccess('');
    setError('');
    
    // 验证表单
    if (!validateForm()) return;
    
    // 如果验证码未验证或无效，先验证验证码
    if (isCodeValid !== true) {
      await validateCode();
      
      // 如果验证码验证失败，则不提交表单
      if (isCodeValid === false) {
        alert('验证码无效，请重新输入');
        return;
      }
    }
    
    setIsLoading(true);
    
    try {
      const result = await resetPasswordWithCode(
        formData.email,
        formData.code,
        formData.newPassword
      );
      
      if (result.success) {
        setSuccess(result.message || '密码重置成功，请使用新密码登录');
        
        // 延迟跳转到登录页面
        setTimeout(() => {
          router.push('/auth/login');
        }, 2000);
      } else {
        setError(result.message || '密码重置失败，请重试');
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '发生错误，请重试';
      setError(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };
  
  // 处理验证码输入框失去焦点事件
  const handleCodeBlur = () => {
    if (formData.code.length === 6 && formData.email) {
      validateCode();
    }
  };
  
  // 发送验证码
  const handleSendCode = async (e: React.MouseEvent) => {
    e.preventDefault();
    
    // 验证邮箱格式
    if (!formData.email || !/\S+@\S+\.\S+/.test(formData.email)) {
      setErrors(prev => ({ ...prev, email: '请输入有效的邮箱地址' }));
      return;
    }
    
    try {
      const templateData = {
        subject: '密码重置验证码',
        title: '重置密码',
        intro: '您正在重置您的账户密码，请使用以下验证码完成验证。',
        expire_notice: '此验证码将在10分钟内有效。'
      };
      
      const result = await sendVerificationCode(formData.email, 'password_reset', templateData);
      
      if (result.success) {
        setSuccess('验证码已发送到您的邮箱，请查收');
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(result.message || '发送验证码失败');
      }
    } catch (err) {
      console.error('发送验证码错误:', err);
      const errorMsg = err instanceof Error ? err.message : '发送验证码失败，请重试';
      setError(errorMsg);
    }
  };
  
  // 当验证错误出现时，自动清除
  useEffect(() => {
    if (verificationError) {
      const timer = setTimeout(() => {
        clearVerificationError();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [verificationError, clearVerificationError]);
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 错误提示 */}
      {(error || authError || verificationError) && (
        <FormError message={error || authError || verificationError || ''} />
      )}
      
      {/* 成功提示 */}
      {success && (
        <FormSuccess message={success} />
      )}
      
      <div className="mb-4">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          请输入您的邮箱地址获取验证码，然后输入新密码来完成重置。
        </p>
      </div>
      
      {/* 邮箱字段 */}
      <div className="flex items-center mb-4 gap-3">
        <div className="flex-grow">
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
            邮箱地址
          </label>
          <div className="relative">
            <input
              type="email"
              name="email"
              id="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="请输入邮箱地址"
              disabled={isLoading || authLoading || verificationLoading || isCodeValid === true || !!searchParams.get('email')}
              required
              className={`w-full px-3 py-2 h-10 border ${
                errors.email ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } rounded-md shadow-sm 
              focus:border-primary-500 focus:ring-primary-500
              bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
              disabled:bg-gray-100 dark:disabled:bg-gray-800 disabled:text-gray-500 dark:disabled:text-gray-400
              disabled:cursor-not-allowed`}
            />
          </div>
          {errors.email && (
            <p className="mt-1 text-xs text-red-500">{errors.email}</p>
          )}
        </div>
        <div className="w-32 self-end mb-[1px]">
          <Button
            type="button"
            onClick={handleSendCode}
            disabled={
              isLoading || 
              authLoading || 
              verificationLoading || 
              !formData.email || 
              countdown > 0 || 
              isCodeValid === true
            }
            variant="primary"
            className="w-full h-10 text-sm"
            isLoading={verificationLoading}
          >
            {countdown > 0 ? `${countdown}秒` : '获取验证码'}
          </Button>
        </div>
      </div>
      
      {/* 验证码字段 */}
      <VerificationCodeInput
        value={formData.code}
        onChange={handleChange}
        onBlur={handleCodeBlur}
        disabled={isLoading || authLoading || !formData.email || isCodeValid === true}
        error={errors.code}
        isValid={isCodeValid}
        isVerifying={isVerifyingCode}
      />
      
      <PasswordInput
        label="新密码"
        name="newPassword"
        id="newPassword"
        value={formData.newPassword}
        onChange={handleChange}
        placeholder="请输入新密码"
        disabled={isLoading || authLoading || isCodeValid !== true}
        error={errors.newPassword}
        required
        description="密码至少需要8个字符"
      />
      
      <PasswordInput
        label="确认密码"
        name="confirmPassword"
        id="confirmPassword"
        value={formData.confirmPassword}
        onChange={handleChange}
        placeholder="请再次输入新密码"
        disabled={isLoading || authLoading || isCodeValid !== true}
        error={errors.confirmPassword}
        required
      />
      
      <div>
        <Button
          type="submit"
          className="w-full"
          variant="primary"
          isLoading={isLoading || authLoading}
          disabled={isLoading || authLoading || isCodeValid !== true}
        >
          重置密码
        </Button>
      </div>
      
      <div className="text-center">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          记起密码了？{' '}
          <Link
            href="/login"
            className="text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
          >
            返回登录
          </Link>
        </p>
      </div>
    </form>
  );
};

export default ResetPasswordForm; 