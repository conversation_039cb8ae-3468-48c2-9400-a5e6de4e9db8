"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '../../hooks/useAuth';
import { useAccountValidation } from '../../hooks/useAccountValidation';
import FormField from '../ui/FormField';
import PasswordInput from '../ui/PasswordInput';
import FormError from '../ui/FormError';
import FormSuccess from '../ui/FormSuccess';
import Button from '../ui/Button';

interface PhoneRegisterFormProps {
  callbackUrl?: string;
  onSwitchMethod?: () => void;
}

const PhoneRegisterForm: React.FC<PhoneRegisterFormProps> = ({ 
  callbackUrl = '/'
}) => {
  const router = useRouter();
  const { 
    phoneRegister, 
    sendPhoneCode, 
    error: authError, 
    clearError, 
    verifyPhoneCodeAndGetToken, 
    phoneRegisterWithToken 
  } = useAuth();
  
  const { validateAccount, validationState } = useAccountValidation();
  
  const [formData, setFormData] = useState({
    username: '',
    phone: '',
    nationCode: '86',
    code: '',
    password: '',
    confirmPassword: '',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');
  
  // 倒计时状态
  const [countdown, setCountdown] = useState(0);
  
  // 验证码验证状态
  const [isCodeVerifying, setIsCodeVerifying] = useState(false);
  const [isCodeValid, setIsCodeValid] = useState<boolean | null>(null);
  const [verificationSuccess, setVerificationSuccess] = useState(false);
  const [verificationToken, setVerificationToken] = useState<string | null>(null);
  // 添加验证码发送加载状态
  const [verificationLoading, setVerificationLoading] = useState(false);
  
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    // 手机号验证
    if (!formData.phone.trim()) {
      newErrors.phone = '请输入手机号';
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone.trim())) {
      newErrors.phone = '请输入有效的手机号';
    }
    
    // 验证码验证
    if (!formData.code.trim()) {
      newErrors.code = '请输入验证码';
    } else if (!/^\d{6}$/.test(formData.code)) {
      newErrors.code = '验证码应为6位数字';
    }
    
    // 用户名验证 - 如果填写了才验证
    if (formData.username.trim()) {
      if (formData.username.length < 3) {
        newErrors.username = '用户名至少需要3个字符';
      } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
        newErrors.username = '用户名只能包含字母、数字和下划线';
      }
    }
    
    // 密码验证 - 如果填写了才验证
    if (formData.password) {
      if (formData.password.length < 6) {
        newErrors.password = '密码至少需要6个字符';
      } else if (!/[A-Z]/.test(formData.password)) {
        newErrors.password = '密码至少需要包含一个大写字母';
      } else if (!/[a-z]/.test(formData.password)) {
        newErrors.password = '密码至少需要包含一个小写字母';
      } else if (!/[0-9]/.test(formData.password)) {
        newErrors.password = '密码至少需要包含一个数字';
      }
      
      // 确认密码验证 - 只在有密码时验证
      if (!formData.confirmPassword) {
        newErrors.confirmPassword = '请确认密码';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = '两次输入的密码不一致';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 清除该字段的错误
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
    
    // 清除全局错误
    if (error) setError('');
    if (authError) clearError();
    
    // 手机号或验证码变更时，重置验证状态
    if (name === 'phone' || name === 'nationCode' || name === 'code') {
      setIsCodeValid(null);
      setVerificationSuccess(false);
      setVerificationToken(null);
    }
  };
  
  // 处理字段失去焦点事件，触发实时验证
  const handleBlur = async (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    // 如果字段为空，不进行验证
    if (!value.trim()) return;
    
    // 根据字段类型执行相应验证
    if (name === 'phone') {
      // 手机号基本格式验证
      if (!/^1[3-9]\d{9}$/.test(value.trim())) {
        setErrors(prev => ({ ...prev, phone: '请输入有效的手机号' }));
        return;
      }
      
      console.log('开始验证手机号是否已存在:', value);
      // 调用API验证手机号是否已存在
      const result = await validateAccount('phone', value);
      console.log('手机号验证结果:', result);
    } else if (name === 'username') {
      // 用户名基本格式验证
      if (value.length < 3) {
        setErrors(prev => ({ ...prev, username: '用户名至少需要3个字符' }));
        return;
      }
      
      if (!/^[a-zA-Z0-9_]+$/.test(value)) {
        setErrors(prev => ({ ...prev, username: '用户名只能包含字母、数字和下划线' }));
        return;
      }
      
      // 调用API验证用户名是否已存在
      await validateAccount('username', value);
    }
  };
  
  // 处理发送验证码
  const handleSendCode = async () => {
    // 验证手机号格式
    if (!formData.phone || !/^1[3-9]\d{9}$/.test(formData.phone)) {
      setErrors(prev => ({ ...prev, phone: '请输入有效的手机号' }));
      return;
    }
    
    // 确认手机号未被使用
    if (validationState.phone.isValid !== true) {
      // 如果还未验证，先验证手机号
      if (validationState.phone.isValid === null) {
        console.log('手机号尚未验证，先进行验证');
        const validationResult = await validateAccount('phone', formData.phone);
        if (!validationResult.isValid) {
          setError(validationResult.message || '该手机号已被注册');
          return;
        }
      } else if (validationState.phone.isValid === false) {
        // 如果已验证为无效
        setError(validationState.phone.message || '该手机号已被注册');
        return;
      }
    }
    
    try {
      setVerificationLoading(true);
      console.log('开始发送验证码到手机:', formData.phone);
      const result = await sendPhoneCode({
        phone: formData.phone,
        nationCode: formData.nationCode
      });
      console.log('验证码发送结果:', result);
      
      if (result.success) {
        setSuccess('验证码已发送到您的手机，请查收');
        
        // 开始倒计时
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
        
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(result.message || '发送验证码失败');
      }
    } catch (err) {
      console.error('发送验证码错误:', err);
      const errorMsg = err instanceof Error ? err.message : '发送验证码失败';
      setError(errorMsg);
    } finally {
      setVerificationLoading(false);
    }
  };
  
  // 验证码输入完成后自动验证并获取令牌
  const handleCodeBlur = async () => {
    if (formData.code.length === 6 && formData.phone) {
      setIsCodeVerifying(true);
      
      try {
        // 使用新的verifyPhoneCodeAndGetToken方法
        const result = await verifyPhoneCodeAndGetToken(
          formData.phone, 
          formData.code, 
          formData.nationCode
        );
        
        setIsCodeValid(result.success);
        
        if (result.success) {
          setVerificationSuccess(true);
          setErrors(prev => ({ ...prev, code: '' }));
          
          // 保存验证令牌
          setVerificationToken(result.token || null);
        } else {
          setVerificationSuccess(false);
          setVerificationToken(null);
          setErrors(prev => ({ ...prev, code: result.message || '验证码无效' }));
        }
      } catch (err) {
        setIsCodeValid(false);
        setVerificationSuccess(false);
        setVerificationToken(null);
        const errorMsg = err instanceof Error ? err.message : '验证码验证失败';
        setErrors(prev => ({ ...prev, code: errorMsg }));
      } finally {
        setIsCodeVerifying(false);
      }
    }
  };
  
  // 新增：验证手机号是否已被注册的专用函数
  const checkPhoneExists = async (phone: string): Promise<boolean> => {
    console.log('[调试] 检查手机号是否已被注册:', phone);
    try {
      const result = await validateAccount('phone', phone);
      console.log('[调试] 手机号检查结果:', result);
      return !result.isValid; // isValid为true表示手机号可用（未被注册）
    } catch (err) {
      console.error('[调试] 检查手机号过程中出错:', err);
      return false; // 出错时默认为不存在，允许继续流程
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    console.log('[调试] 提交注册表单:', formData);
    console.log('[调试] 注册前验证状态:', {
      isCodeValid,
      verificationSuccess,
      verificationToken,
      phoneValidation: validationState.phone
    });
    
    // 清除之前的消息
    setSuccess('');
    setError('');
    
    // 表单验证
    if (!validateForm()) {
      console.log('[调试] 表单验证失败，错误:', errors);
      return;
    }
    
    // 验证码未验证或验证失败，或者没有验证令牌
    if (!isCodeValid || !verificationToken) {
      console.log('[调试] 验证码未验证或验证失败，或者没有验证令牌');
      setError('请先验证手机验证码');
      return;
    }
    
    setIsLoading(true);
    
    try {
      console.log('[调试] 开始使用令牌注册:', {
        phone: formData.phone,
        username: formData.username,
        token: verificationToken ? verificationToken.substring(0, 10) + '...' : 'null'
      });
      
      // 使用令牌注册
      const registerResult = await phoneRegisterWithToken({
        phone: formData.phone,
        username: formData.username,
        password: formData.password,
        displayName: formData.username,
        token: verificationToken,
        nationCode: formData.nationCode
      });
      
      console.log('[调试] 注册结果:', registerResult);
      
      // 注册成功
      setSuccess('注册成功，正在跳转...');
      
      // 延迟跳转，让用户看到成功消息
      setTimeout(() => {
        router.push(callbackUrl);
      }, 1000);
      
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '注册失败，请重试';
      console.error('[调试] 注册错误:', err);
      console.log('[调试] 错误消息:', errorMsg);
      setError(errorMsg);
      
      // 检查错误消息是否包含"已注册"信息
      if (errorMsg.includes('已注册') || errorMsg.includes('已被使用')) {
        console.log('[调试] 检测到"已注册"相关错误');
        
        // 更新手机号验证状态
        try {
          const revalidateResult = await validateAccount('phone', formData.phone);
          console.log('[调试] 重新验证手机号结果:', revalidateResult);
        } catch (validationErr) {
          console.error('[调试] 重新验证手机号失败:', validationErr);
        }
      }
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 错误提示 */}
      {(error || authError) && (
        <FormError message={error || authError || ''} />
      )}
      
      {/* 成功提示 */}
      {success && (
        <FormSuccess message={success} />
      )}
      
      <div className="mb-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            请先验证您的手机号，然后完成注册信息。
          </p>
      </div>
      
      {/* 手机号字段 */}
      <div className="flex items-center mb-4 gap-3">
        <div className="flex-grow">
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
            手机号
          </label>
          <div className="flex rounded-md shadow-sm relative">
            <span className="inline-flex items-center px-3 py-2 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300">
              +{formData.nationCode}
            </span>
            <input
              type="text"
              name="phone"
              id="phone"
              value={formData.phone}
              onChange={handleChange}
              onBlur={handleBlur}
              autoComplete="tel"
              placeholder="请输入手机号"
              pattern="^1[3-9]\d{9}$"
              disabled={isLoading || isCodeValid === true}
              required
              className={`flex-1 min-w-0 block w-full px-3 py-2 h-10 rounded-none rounded-r-md border ${
                errors.phone ? 'border-red-500' : 
                validationState.phone.isValid === true ? 'border-green-500' : 
                validationState.phone.isValid === false ? 'border-red-500' : 
                'border-gray-300 dark:border-gray-600'
              } shadow-sm 
              ${validationState.phone.isValid === true ? 'focus:border-green-500 focus:ring-green-500' : 'focus:border-primary-500 focus:ring-primary-500'}
              bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100`}
            />
            {/* 验证状态图标 */}
            {validationState.phone.isChecking && (
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg className="animate-spin h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            )}
            {!validationState.phone.isChecking && validationState.phone.isValid !== null && (
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                {validationState.phone.isValid ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
            )}
          </div>
          {errors.phone && (
            <p className="mt-1 text-xs text-red-500">{errors.phone}</p>
          )}
          {!errors.phone && validationState.phone.message && (
            <p className={`mt-1 text-xs ${validationState.phone.isValid ? 'text-green-500' : 'text-red-500'}`}>
              {validationState.phone.message}
            </p>
          )}
        </div>
        <div className="w-32 self-end mb-[1px]">
          <Button
            type="button"
            onClick={handleSendCode}
            disabled={
              isLoading || 
              !formData.phone || 
              countdown > 0 || 
              isCodeValid === true ||
              validationState.phone.isValid !== true // 只有当手机号验证有效时才启用按钮
            }
            variant="primary"
            className="w-full h-10 text-sm"
            isLoading={verificationLoading}
          >
            {countdown > 0 ? `${countdown}秒` : '获取验证码'}
          </Button>
        </div>
      </div>
      
      {/* 验证码字段 */}
      <div className="space-y-1">
        <div className="flex justify-between">
          <label htmlFor="phone-code" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
            验证码
          </label>
        </div>
        <div className="relative">
          <input
            type="text"
            name="code"
            id="phone-code"
            value={formData.code}
            onChange={handleChange}
            onBlur={handleCodeBlur}
            placeholder="请输入6位数字验证码"
            disabled={isLoading || !formData.phone || isCodeValid === true}
            required
            className={`block w-full px-3 py-2 h-10 border ${
              errors.code ? 'border-red-500' : isCodeValid === true ? 'border-green-500' : isCodeValid === false ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 
            ${isCodeValid === true ? 'focus:border-green-500 focus:ring-green-500' : 'focus:border-primary-500 focus:ring-primary-500'}
            ${(isLoading || !formData.phone) ? 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400' : 'bg-white dark:bg-gray-900 text-gray-900 dark:text-white'}
            disabled:cursor-not-allowed`}
            maxLength={6}
          />
          {isCodeValid !== null && !isCodeVerifying && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              {isCodeValid ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              )}
            </div>
          )}
        </div>
        {errors.code && (
          <p className="mt-1 text-xs text-red-500">{errors.code}</p>
        )}
      </div>
      
      {verificationSuccess && (
        <>
          {/* 用户名字段 */}
          <div className="space-y-1">
            <label htmlFor="username" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
              用户名 {validationState.username.isChecking && <span className="ml-2 text-xs text-gray-500 animate-pulse">验证中...</span>}
            </label>
            <div className="relative">
              <input
            type="text"
            name="username"
            id="username"
            value={formData.username}
            onChange={handleChange}
                onBlur={handleBlur}
            autoComplete="username"
            placeholder="请输入用户名（可选）"
            disabled={isLoading || isCodeValid !== true}
                className={`block w-full px-3 py-2 h-10 border ${
                  errors.username ? 'border-red-500' : 
                  validationState.username.isValid === true ? 'border-green-500' : 
                  validationState.username.isValid === false ? 'border-red-500' : 
                  'border-gray-300 dark:border-gray-600'
                } rounded-md shadow-sm 
                ${validationState.username.isValid === true ? 'focus:border-green-500 focus:ring-green-500' : 'focus:border-primary-500 focus:ring-primary-500'}
                bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                disabled:bg-gray-100 dark:disabled:bg-gray-800 disabled:text-gray-500 dark:disabled:text-gray-400
                disabled:cursor-not-allowed`}
              />
              {!validationState.username.isChecking && validationState.username.isValid !== null && (
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  {validationState.username.isValid ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              )}
            </div>
            {errors.username && (
              <p className="mt-1 text-xs text-red-500">{errors.username}</p>
            )}
            {!errors.username && validationState.username.message && (
              <p className={`mt-1 text-xs ${validationState.username.isValid ? 'text-green-500' : 'text-red-500'}`}>
                {validationState.username.message}
              </p>
            )}
            <p className="mt-1 text-xs text-gray-500">用户名将用于登录，不填写时系统会自动生成</p>
          </div>
          
          {/* 密码字段 */}
          <PasswordInput
            label="密码"
            name="password"
            id="password"
            value={formData.password}
            onChange={handleChange}
            autoComplete="new-password"
            placeholder="请输入密码（可选）"
            error={errors.password}
            disabled={isLoading || isCodeValid !== true}
            description="密码至少需要6个字符，包含大小写字母和数字"
          />
          
          {/* 确认密码字段 */}
          <PasswordInput
            label="确认密码"
            name="confirmPassword"
            id="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleChange}
            autoComplete="new-password"
            placeholder="请再次输入密码（可选）"
            error={errors.confirmPassword}
            disabled={isLoading || isCodeValid !== true || !formData.password}
          />
        </>
      )}
      
      {/* 注册按钮 */}
      <div>
        <Button
          type="submit"
          className="w-full"
          variant="primary"
          isLoading={isLoading}
          disabled={isLoading || isCodeValid !== true || !verificationSuccess}
        >
          注册
        </Button>
      </div>
      
      {/* 登录链接 */}
      <div className="text-center">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          已有账号？{' '}
          <Link
            href="/login"
            className="text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
          >
            立即登录
          </Link>
        </p>
      </div>
    </form>
  );
};

export default PhoneRegisterForm; 