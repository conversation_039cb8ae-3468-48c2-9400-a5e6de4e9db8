"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../hooks/useAuth';
import { useAccountValidation } from '../../hooks/useAccountValidation';
import FormField from '../ui/FormField';
import FormError from '../ui/FormError';
import FormSuccess from '../ui/FormSuccess';
import Button from '../ui/Button';

interface PhoneLoginFormProps {
  callbackUrl?: string;
}

const PhoneLoginForm: React.FC<PhoneLoginFormProps> = ({ callbackUrl = '/' }) => {
  const router = useRouter();
  const { 
    phoneLogin, 
    phoneRegister, 
    sendPhoneCode,
    verifyPhoneCodeAndGetToken,
    phoneRegisterWithToken,
    phoneLoginWithToken,
    clearError 
  } = useAuth();
  
  const { validateAccount, validationState } = useAccountValidation();
  
  // 组件级状态管理，避免全局状态导致的表单重置
  const [formData, setFormData] = useState({
    phone: '',
    nationCode: '86',
    code: '',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [sendingCode, setSendingCode] = useState(false);
  
  // 手机号是否存在的状态
  const [phoneExists, setPhoneExists] = useState<boolean | null>(null);
  
  // 验证码验证状态
  const [isCodeVerifying, setIsCodeVerifying] = useState(false);
  const [isCodeValid, setIsCodeValid] = useState<boolean | null>(null);
  
  // 添加验证令牌状态
  const [verificationToken, setVerificationToken] = useState<string | null>(null);
  
  // 处理输入变更
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 清除该字段的错误
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
    
    // 如果手机号变化，重置存在状态
    if (name === 'phone') {
      setPhoneExists(null);
    }
    
    // 如果验证码变化，重置验证状态
    if (name === 'code') {
      setIsCodeValid(null);
      // 重置验证令牌
      setVerificationToken(null);
    }
  };
  
  // 验证表单
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    // 手机号验证
    if (!formData.phone.trim()) {
      newErrors.phone = '请输入手机号';
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone.trim())) {
      newErrors.phone = '请输入有效的手机号';
    }
    
    // 验证码验证
    if (!formData.code.trim()) {
      newErrors.code = '请输入验证码';
    } else if (!/^\d{6}$/.test(formData.code.trim())) {
      newErrors.code = '验证码应为6位数字';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // 验证码输入后自动验证并获取令牌
  const handleCodeBlur = async () => {
    if (formData.code.length === 6 && formData.phone) {
      setIsCodeVerifying(true);
      
      try {
        // 使用verifyPhoneCodeAndGetToken方法与后端通信验证并获取令牌
        const result = await verifyPhoneCodeAndGetToken(
          formData.phone, 
          formData.code, 
          formData.nationCode
        );
        
        setIsCodeValid(result.success);
        
        if (result.success) {
          // 保存验证令牌
          setVerificationToken(result.token || null);
          setErrors(prev => ({ ...prev, code: '' }));
        } else {
          setErrors(prev => ({ ...prev, code: result.message || '验证码无效' }));
        }
      } catch (err) {
        setIsCodeValid(false);
        const errorMsg = err instanceof Error ? err.message : '验证码验证失败';
        setErrors(prev => ({ ...prev, code: errorMsg }));
      } finally {
        setIsCodeVerifying(false);
      }
    }
  };
  
  // 检查手机号是否存在
  const checkPhoneExists = async () => {
    if (!formData.phone || !/^1[3-9]\d{9}$/.test(formData.phone)) {
      setErrors(prev => ({ ...prev, phone: '请输入有效的手机号' }));
      return false;
    }
    
    try {
      const result = await validateAccount('phone', formData.phone);
      // 注意: isValid为true表示手机号可用（未被注册）
      setPhoneExists(!result.isValid);
      return true;
    } catch (err) {
      console.error('检查手机号错误:', err);
      setError('验证手机号失败，请重试');
      return false;
    }
  };
  
  // 发送验证码
  const handleSendCode = async () => {
    setError('');
    setSuccess('');
    
    // 先检查手机号是否有效
    if (!formData.phone || !/^1[3-9]\d{9}$/.test(formData.phone)) {
      setErrors(prev => ({ ...prev, phone: '请输入有效的手机号' }));
      return;
    }
    
    // 先检查手机号是否已注册
    setSendingCode(true);
    const checkResult = await checkPhoneExists();
    if (!checkResult) {
      setSendingCode(false);
      return;
    }
    
    try {
      const result = await sendPhoneCode({
        phone: formData.phone,
        nationCode: formData.nationCode
      });
      
      if (result.success) {
        setSuccess('验证码已发送到您的手机，请查收');
        
        // 开始倒计时
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
        
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(result.message || '发送验证码失败');
      }
    } catch (err) {
      console.error('发送验证码错误:', err);
      const errorMsg = err instanceof Error ? err.message : '发送验证码失败';
      setError(errorMsg);
    } finally {
      setSendingCode(false);
    }
  };
  
  // 处理提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 清除之前的消息
    setSuccess('');
    setError('');
    clearError();
    
    if (!validateForm()) {
      return;
    }
    
    // 验证码验证状态及令牌检查
    if (!isCodeValid || !verificationToken) {
      setError('请输入并验证正确的验证码');
      return;
    }
    
    setIsLoading(true);
    
    try {
      // 如果手机号尚未验证存在性，先验证
      if (phoneExists === null) {
        await checkPhoneExists();
      }
      
      if (phoneExists) {
        // 手机号已存在，使用令牌执行登录流程
        console.log('执行手机号令牌登录流程');
        await phoneLoginWithToken({
          phone: formData.phone,
          token: verificationToken,
          nationCode: formData.nationCode
        });
        
        setSuccess('登录成功，正在跳转...');
      } else {
        // 手机号不存在，使用令牌执行注册流程
        console.log('执行手机号令牌注册流程');
        await phoneRegisterWithToken({
          phone: formData.phone,
          token: verificationToken,
          nationCode: formData.nationCode
        });
        
        setSuccess('注册并登录成功，正在跳转...');
      }
      
      // 延迟跳转，让用户看到成功消息
      setTimeout(() => {
        // 添加额外延迟确保状态同步完成
        setTimeout(() => {
          router.refresh(); // 刷新当前路由数据
          router.push(callbackUrl);
        }, 500);
      }, 1500);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '操作失败，请重试';
      setError(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 错误提示 */}
      {error && <FormError message={error} />}
      
      {/* 成功提示 */}
      {success && <FormSuccess message={success} />}
      
      {/* 手机号字段 */}
      <div className="flex items-center gap-3">
        <div className="flex-grow">
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
            手机号
          </label>
          <div className="flex rounded-md shadow-sm relative">
            <span className="inline-flex items-center px-3 py-2 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300">
              +{formData.nationCode}
            </span>
            <input
              type="text"
              name="phone"
              id="phone"
              value={formData.phone}
              onChange={handleChange}
              autoComplete="tel"
              placeholder="请输入手机号"
              pattern="^1[3-9]\d{9}$"
              disabled={isLoading}
              required
              className={`flex-1 min-w-0 block w-full px-3 py-2 h-10 rounded-none rounded-r-md border ${
                errors.phone ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } shadow-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100`}
            />
          </div>
          {errors.phone && (
            <p className="mt-1 text-xs text-red-500">{errors.phone}</p>
          )}
        </div>
        <div className="w-32 self-end mb-[1px]">
          <Button
            type="button"
            onClick={handleSendCode}
            disabled={isLoading || !formData.phone || countdown > 0}
            variant="primary"
            className="w-full h-10 text-sm"
            isLoading={sendingCode}
          >
            {countdown > 0 ? `${countdown}秒` : '获取验证码'}
          </Button>
        </div>
      </div>
      
      {/* 验证码字段 */}
      <div className="space-y-1">
        <label htmlFor="login-code" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
          验证码 {isCodeVerifying && <span className="ml-2 text-xs text-gray-500 animate-pulse">验证中...</span>}
        </label>
        <div className="relative">
          <input
            type="text"
            name="code"
            id="login-code"
            value={formData.code}
            onChange={handleChange}
            onBlur={handleCodeBlur}
            placeholder="请输入6位数字验证码"
            disabled={isLoading}
            required
            className={`block w-full px-3 py-2 h-10 border ${
              errors.code ? 'border-red-500' : isCodeValid === true ? 'border-green-500' : isCodeValid === false ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } rounded-md shadow-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100`}
            maxLength={6}
          />
          {isCodeValid !== null && !isCodeVerifying && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              {isCodeValid ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              )}
            </div>
          )}
        </div>
        {errors.code && (
          <p className="mt-1 text-xs text-red-500">{errors.code}</p>
        )}
      </div>
      
      {/* 登录按钮 */}
      <div>
        <Button
          type="submit"
          className="w-full"
          variant="primary"
          isLoading={isLoading}
          disabled={isLoading}
        >
          {phoneExists === false ? '注册并登录' : '登录'}
        </Button>
      </div>
    </form>
  );
};

export default PhoneLoginForm; 