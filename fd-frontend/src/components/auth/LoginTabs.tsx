"use client";

import React, { useState } from 'react';
import LoginForm from './LoginForm';
import PhoneLoginForm from './PhoneLoginForm';

interface LoginTabsProps {
  callbackUrl?: string;
}

const LoginTabs: React.FC<LoginTabsProps> = ({ callbackUrl = '/' }) => {
  const [activeTab, setActiveTab] = useState<'password' | 'phone'>('password');

  return (
    <div className="space-y-6">
      {/* 选项卡导航 */}
      <div className="flex border-b border-gray-200 dark:border-gray-700">
        <button
          className={`py-2 px-4 font-medium text-sm ${
            activeTab === 'password'
              ? 'border-b-2 border-primary-500 text-primary-600 dark:text-primary-400'
              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
          }`}
          onClick={() => setActiveTab('password')}
        >
          账号密码登录
        </button>
        <button
          className={`py-2 px-4 font-medium text-sm ${
            activeTab === 'phone'
              ? 'border-b-2 border-primary-500 text-primary-600 dark:text-primary-400'
              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
          }`}
          onClick={() => setActiveTab('phone')}
        >
          手机快捷登录
        </button>
      </div>
      
      {/* 选项卡内容 */}
      <div className="mt-6">
        {activeTab === 'password' && <LoginForm callbackUrl={callbackUrl} />}
        {activeTab === 'phone' && <PhoneLoginForm callbackUrl={callbackUrl} />}
      </div>
    </div>
  );
};

export default LoginTabs; 