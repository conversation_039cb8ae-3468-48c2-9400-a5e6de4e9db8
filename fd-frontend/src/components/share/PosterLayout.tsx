import React, { useEffect } from 'react';
import { QRCodeSVG } from 'qrcode.react';

// 定义海报数据的类型
export interface PosterData {
  title?: string | null;
  excerpt?: string | null;
  authorName?: string | null;
  authorAvatar?: string | null;
  featuredImage?: string | null;
  posterLogo?: string | null;
}

interface PosterLayoutProps {
  data: PosterData;
  postUrl: string;
  posterRef: React.RefObject<HTMLDivElement>;
  onImagesLoaded: () => void;
}

const PosterLayout: React.FC<PosterLayoutProps> = ({ data, postUrl, posterRef, onImagesLoaded }) => {
  useEffect(() => {
    if (!data) return;

    const imagesToLoad = [
      data.featuredImage,
      data.authorAvatar,
      data.posterLogo,
    ].filter(Boolean) as string[];

    const imagePromises = imagesToLoad.map(src => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.src = src;
        img.onload = resolve;
        img.onerror = reject;
      });
    });

    Promise.all(imagePromises)
      .then(() => {
        console.log('所有海报图片加载完成');
        onImagesLoaded();
      })
      .catch(error => {
        console.error('海报图片加载失败:', error);
        // 即使一张图片加载失败，也尝试生成海报
        onImagesLoaded();
      });

  }, [data, onImagesLoaded]);

  // 防止在服务器端渲染或数据不完整时出错
  if (!data) {
    return null;
  }

  return (
    <div 
      ref={posterRef} 
      className="absolute -left-[9999px] top-0 w-[375px] bg-white p-4 font-sans"
    >
      {/* 顶部特色图 */}
      {data.featuredImage && (
        <div className="w-full h-[210px] bg-cover bg-center" style={{ backgroundImage: `url(${data.featuredImage})` }}></div>
      )}

      {/* 标题 */}
      <h1 className="mt-4 text-xl font-bold text-gray-800 break-words">
        {data.title}
      </h1>

      {/* 作者信息 */}
      <div className="flex items-center mt-3">
        {data.authorAvatar && (
          <img src={data.authorAvatar} alt={data.authorName || ''} className="w-8 h-8 rounded-full" />
        )}
        <span className="ml-2 text-sm text-gray-600">{data.authorName}</span>
      </div>
      
      {/* 摘要 */}
      <p className="mt-3 text-sm text-gray-700 leading-relaxed text-justify break-words">
        {data.excerpt}
      </p>

      <div className="mt-4 border-t border-gray-200 pt-4 flex justify-between items-center">
        {/* 左侧Logo和文字 */}
        <div className="flex items-center">
          {data.posterLogo && (
            <img src={data.posterLogo} alt="Logo" className="h-10 w-auto" />
          )}
          <div className="ml-3 text-xs text-gray-500">
            <p className="font-semibold">扫码阅读全文</p>
            <p>长按识别二维码</p>
          </div>
        </div>

        {/* 右侧二维码 */}
        <div className="p-1 border border-gray-200">
          <QRCodeSVG value={postUrl} size={64} level="M" />
        </div>
      </div>
    </div>
  );
};

export default PosterLayout; 