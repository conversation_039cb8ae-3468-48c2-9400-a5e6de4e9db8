'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useMutation } from '@apollo/client';
import html2canvas from 'html2canvas';
import { Loader2, X, Download } from 'lucide-react';
import { GENERATE_POSTER_MUTATION } from '@/lib/graphql/share';
import PosterLayout, { PosterData } from './PosterLayout';

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  postId: string;
  postUrl: string;
}

// 定义变更返回的数据结构类型
interface GeneratePosterData {
  generatePostPoster: {
    posterData: PosterData;
  };
}

const ShareModal: React.FC<ShareModalProps> = ({ isOpen, onClose, postId, postUrl }) => {
  const [posterImage, setPosterImage] = useState<string | null>(null);
  const [fetchStarted, setFetchStarted] = useState(false);
  const posterRef = useRef<HTMLDivElement>(null);

  const [generatePoster, { loading, error, data }] = useMutation<GeneratePosterData>(GENERATE_POSTER_MUTATION, {
    variables: { postId },
  });

  useEffect(() => {
    // 模态框打开时，如果还没有开始获取，则触发
    if (isOpen && !fetchStarted) {
      setFetchStarted(true);
      generatePoster();
    }
    // 模态框关闭时，重置状态
    if (!isOpen) {
      setPosterImage(null);
      setFetchStarted(false);
    }
  }, [isOpen, fetchStarted, generatePoster]);
  
  const generateImage = async () => {
    if (posterRef.current) {
      try {
        const canvas = await html2canvas(posterRef.current, {
          useCORS: true, // 允许加载跨域图片
          scale: 2, // 提高清晰度
          backgroundColor: null, // 使用DOM元素的背景
        });
        setPosterImage(canvas.toDataURL('image/png'));
      } catch (e) {
        console.error('海报生成失败:', e);
      }
    }
  };

  const handleDownload = () => {
    if (posterImage) {
      const link = document.createElement('a');
      link.href = posterImage;
      link.download = `poster-${postId}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  if (!isOpen) {
    return null;
  }

  const posterData = data?.generatePostPoster?.posterData;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-sm w-full relative">
        <button onClick={onClose} className="absolute -top-4 -right-4 bg-gray-700 text-white rounded-full p-1.5 z-10">
          <X size={20} />
        </button>

        <div className="p-6 text-center">
          <h3 className="text-lg font-semibold text-gray-800">分享海报</h3>
          <p className="text-sm text-gray-500 mt-1">长按或点击下方按钮保存图片</p>
          
          <div className="mt-4 aspect-[9/16] w-full bg-gray-100 flex items-center justify-center rounded-md overflow-hidden">
            {posterImage ? (
              <img src={posterImage} alt="分享海报" className="w-full h-full object-contain" />
            ) : (
              <div className="flex flex-col items-center text-gray-500">
                <Loader2 className="animate-spin" size={40} />
                <span className="mt-2 text-sm">海报生成中...</span>
                {error && <p className="text-xs text-red-500 mt-1">生成失败，请重试</p>}
              </div>
            )}
          </div>

          {posterImage && (
            <button
              onClick={handleDownload}
              className="mt-4 w-full bg-blue-600 text-white font-bold py-2 px-4 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors"
            >
              <Download size={18} className="mr-2" />
              保存图片
            </button>
          )}
        </div>
      </div>

      {/* 这个组件仅用于在后台渲染并被html2canvas捕捉，它本身是不可见的 */}
      {posterData && !posterImage && (
        <PosterLayout 
          data={posterData}
          postUrl={postUrl} 
          posterRef={posterRef} 
          onImagesLoaded={generateImage}
        />
      )}
    </div>
  );
};

export default ShareModal; 