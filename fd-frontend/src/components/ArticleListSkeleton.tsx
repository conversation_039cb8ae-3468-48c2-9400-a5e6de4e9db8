import React from 'react';
import { ViewMode } from './ViewModeSwitcher';

/**
 * 列表模式骨架屏项
 */
const ListItemSkeleton = () => (
  <div className="animate-pulse flex flex-col md:flex-row bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
    {/* 图片占位符 */}
    <div className="md:w-1/3 h-48 md:h-auto bg-gray-300"></div>
    <div className="p-6 flex-1 space-y-4">
      {/* 分类占位符 */}
      <div className="h-5 bg-gray-300 rounded w-16"></div>
      {/* 标题占位符 */}
      <div className="h-6 bg-gray-300 rounded w-3/4"></div>
      {/* 元信息占位符 */}
      <div className="flex space-x-3">
        <div className="h-4 bg-gray-300 rounded w-20"></div>
        <div className="h-4 bg-gray-300 rounded w-24"></div>
      </div>
      {/* 摘要占位符 */}
      <div className="space-y-2">
        <div className="h-4 bg-gray-300 rounded"></div>
        <div className="h-4 bg-gray-300 rounded w-5/6"></div>
      </div>
      {/* 阅读更多占位符 */}
      <div className="h-4 bg-gray-300 rounded w-20"></div>
    </div>
  </div>
);

/**
 * 网格模式骨架屏项
 */
const GridItemSkeleton = () => (
  <div className="animate-pulse bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
    {/* 图片占位符 */}
    <div className="h-48 bg-gray-300"></div>
    <div className="p-4 space-y-3">
      {/* 分类占位符 */}
      <div className="h-5 bg-gray-300 rounded w-16"></div>
      {/* 标题占位符 */}
      <div className="h-5 bg-gray-300 rounded w-full"></div>
      {/* 元信息占位符 */}
      <div className="flex space-x-2">
        <div className="h-3 bg-gray-300 rounded w-16"></div>
        <div className="h-3 bg-gray-300 rounded w-20"></div>
      </div>
      {/* 摘要占位符 */}
      <div className="h-4 bg-gray-300 rounded"></div>
      {/* 阅读更多占位符 */}
      <div className="h-3 bg-gray-300 rounded w-16"></div>
    </div>
  </div>
);

/**
 * 紧凑模式骨架屏项
 */
const CompactItemSkeleton = () => (
  <div className="animate-pulse py-3 border-b border-gray-100">
    <div className="flex">
      {/* 小图占位符 */}
      <div className="flex-shrink-0 w-16 h-16 bg-gray-300 rounded mr-4"></div>
      <div className="flex-1 space-y-2">
        {/* 标题占位符 */}
        <div className="h-4 bg-gray-300 rounded w-3/4"></div>
        {/* 元信息占位符 */}
        <div className="h-3 bg-gray-300 rounded w-1/3"></div>
      </div>
    </div>
  </div>
);

/**
 * 文章列表骨架屏
 * @param mode 显示模式
 * @param count 要显示的骨架项数量
 */
interface ArticleListSkeletonProps {
  mode?: ViewMode;
  count?: number;
  columns?: 1 | 2 | 3 | 4;
}

const ArticleListSkeleton = ({ mode = 'list', count = 3, columns = 3 }: ArticleListSkeletonProps) => {
  if (mode === 'list') {
    return (
      <div className="space-y-6">
        {Array.from({ length: count }).map((_, i) => (
          <ListItemSkeleton key={i} />
        ))}
      </div>
    );
  }
  
  if (mode === 'grid') {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-${columns} gap-6`}>
        {Array.from({ length: count }).map((_, i) => (
          <GridItemSkeleton key={i} />
        ))}
      </div>
    );
  }
  
  if (mode === 'compact') {
    return (
      <div>
        {Array.from({ length: count }).map((_, i) => (
          <CompactItemSkeleton key={i} />
        ))}
      </div>
    );
  }
  
  // 杂志模式
  return (
    <div className="space-y-6">
      <ListItemSkeleton /> {/* 首个大项 */}
      <div className={`grid grid-cols-1 md:grid-cols-${columns} gap-6`}>
        {Array.from({ length: count - 1 }).map((_, i) => (
          <GridItemSkeleton key={i} />
        ))}
      </div>
    </div>
  );
};

export default ArticleListSkeleton; 