'use client';

import { FC, useEffect, useRef, useState } from 'react';
import NotificationIcon from './NotificationIcon';
import NotificationList from './NotificationList';
import { 
  useNotifications, 
  useUnreadNotificationCount, 
  useMarkNotificationRead, 
  useMarkAllNotificationsRead, 
  useDeleteNotification 
} from '../../hooks/useNotifications';
import { useRouter } from 'next/navigation';

interface NotificationDropdownProps {
  className?: string;
}

/**
 * 通知下拉菜单组件
 */
const NotificationDropdown: FC<NotificationDropdownProps> = ({
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  // 通知相关 hooks
  const { notifications, loading, error, refetch } = useNotifications({
    perPage: 10
  });
  const { unreadCount, refetch: refetchCount } = useUnreadNotificationCount();
  const { markNotificationRead } = useMarkNotificationRead();
  const { markAllNotificationsRead } = useMarkAllNotificationsRead();
  const { deleteNotification } = useDeleteNotification();

  // 切换下拉菜单
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      // 打开下拉菜单时刷新数据
      refetch();
      refetchCount();
    }
  };

  // 处理标记已读
  const handleMarkAsRead = async (id: string) => {
    try {
      await markNotificationRead(id);
      refetch();
      refetchCount();
    } catch (err) {
      console.error('标记已读失败', err);
    }
  };

  // 处理标记所有已读
  const handleMarkAllAsRead = async () => {
    try {
      await markAllNotificationsRead();
      refetch();
      refetchCount();
    } catch (err) {
      console.error('标记所有已读失败', err);
    }
  };

  // 处理删除通知
  const handleDelete = async (id: string) => {
    try {
      const result = await deleteNotification(id);
      if (result && result.success) {
        refetch();
        refetchCount();
      }
    } catch (err) {
      console.error('删除通知失败', err);
    }
  };

  // 查看所有通知
  const handleViewAll = () => {
    setIsOpen(false);
    router.push('/notification-center');
  };

  // 刷新通知数据
  const handleRefresh = async () => {
    try {
      await Promise.all([refetch(), refetchCount()]);
    } catch (err) {
      console.error('刷新通知失败', err);
    }
  };

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 定期轮询未读通知数量
  useEffect(() => {
    const interval = setInterval(() => {
      refetchCount();
    }, 60000); // 每分钟轮询一次

    return () => clearInterval(interval);
  }, [refetchCount]);

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* 通知图标 */}
      <NotificationIcon
        count={unreadCount}
        onClick={toggleDropdown}
      />
      
      {/* 通知下拉菜单 */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 sm:w-96 z-50">
          <div className="rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 overflow-hidden">
            <NotificationList
              notifications={notifications}
              loading={loading}
              error={error}
              onMarkAsRead={handleMarkAsRead}
              onDelete={handleDelete}
              onRefresh={handleRefresh}
              onMarkAllAsRead={handleMarkAllAsRead}
              maxHeight="300px"
            />
            
            {/* 查看全部按钮 */}
            <div className="p-2 bg-gray-50 text-center border-t">
              <button
                className="text-sm text-blue-600 hover:text-blue-800"
                onClick={handleViewAll}
              >
                查看全部通知
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationDropdown; 