'use client';

import { FC } from 'react';
import { Notification, NotificationStatus } from '../../types/notification';

interface NotificationDetailProps {
  notification: Notification;
  onClose: () => void;
  onMarkAsRead?: (id: string) => void;
}

/**
 * 通知详情组件
 */
const NotificationDetail: FC<NotificationDetailProps> = ({
  notification,
  onClose,
  onMarkAsRead
}) => {
  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', { 
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (err) {
      return dateString;
    }
  };

  // 处理标记已读按钮点击
  const handleMarkAsRead = () => {
    if (onMarkAsRead) {
      onMarkAsRead(notification.id);
    }
  };
  
  const isUnread = notification.status === NotificationStatus.UNREAD;

  return (
    <div className="border rounded-lg overflow-hidden">
      <div className="bg-gray-100 p-4 flex justify-between items-center">
        <h2 className="text-lg font-semibold">通知详情</h2>
        <button 
          className="text-gray-600 hover:text-gray-800"
          onClick={onClose}
          aria-label="关闭"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      <div className="p-6">
        <h3 className="text-xl font-medium mb-2">{notification.title}</h3>
        <div className="flex items-center text-sm text-gray-500 mb-6">
          <span className="mr-4">{formatDate(notification.createdAt)}</span>
          <span>{notification.typeName}</span>
          {isUnread && <span className="ml-2 px-1.5 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs">未读</span>}
        </div>
        <div 
          className="prose prose-sm max-w-none"
          dangerouslySetInnerHTML={{ __html: notification.content }}
        />
      </div>
    </div>
  );
};

export default NotificationDetail; 