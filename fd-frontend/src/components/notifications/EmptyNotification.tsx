import { FC } from 'react';

interface EmptyNotificationProps {
  message?: string;
  loading?: boolean;
}

/**
 * 空通知状态组件
 */
const EmptyNotification: FC<EmptyNotificationProps> = ({
  message = '暂无通知',
  loading = false
}) => {
  return (
    <div className="flex flex-col items-center justify-center py-10 px-4 text-center">
      {loading ? (
        <>
          <div className="w-8 h-8 border-t-2 border-b-2 border-gray-400 rounded-full animate-spin mb-4"></div>
          <p className="text-gray-500">加载中...</p>
        </>
      ) : (
        <>
          <svg 
            className="w-16 h-16 text-gray-300 mb-4" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth="2" 
              d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
            ></path>
          </svg>
          <p className="text-gray-500">{message}</p>
        </>
      )}
    </div>
  );
};

export default EmptyNotification; 