'use client';

import { FC } from 'react';
import { Notification, NotificationStatus, NotificationType } from '../../types/notification';

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
  onClick: (notification: Notification) => void;
}

/**
 * 通知项组件
 */
const NotificationItem: FC<NotificationItemProps> = ({
  notification,
  onMarkAsRead,
  onDelete,
  onClick,
}) => {
  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      
      // 计算时间差
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      
      // 根据时间差显示不同格式
      if (diffMinutes < 60) {
        return `${diffMinutes} 分钟前`;
      } else if (diffHours < 24) {
        return `${diffHours} 小时前`;
      } else if (diffDays < 7) {
        return `${diffDays} 天前`;
      } else {
        // 超过7天显示具体日期
        return date.toLocaleDateString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        });
      }
    } catch (err) {
      return dateString;
    }
  };

  // 根据通知类型返回不同的样式类
  const getTypeColor = (type: NotificationType) => {
    switch (type) {
      case NotificationType.SYSTEM:
        return 'bg-purple-100 text-purple-800';
      case NotificationType.ACCOUNT:
        return 'bg-green-100 text-green-800';
      case NotificationType.PAYMENT:
        return 'bg-orange-100 text-orange-800';
      case NotificationType.MEMBER:
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 防止点击操作按钮时触发整个通知项的点击事件
  const handleActionClick = (e: React.MouseEvent, action: () => void) => {
    e.stopPropagation();
    action();
  };
  
  const isUnread = notification.status === NotificationStatus.UNREAD;

  return (
    <div 
      className={`p-4 border-b cursor-pointer hover:bg-gray-50 transition-colors ${isUnread ? 'bg-blue-50' : 'bg-white'}`}
      onClick={() => onClick(notification)}
    >
      <div className="flex justify-between items-start">
        <div className="flex-1 min-w-0 pr-4">
          <h3 className={`text-sm font-medium truncate ${isUnread ? 'text-black' : 'text-gray-600'}`}>
            {notification.title}
          </h3>
          <p className="mt-1 text-xs text-gray-500 line-clamp-2">
            {notification.content.replace(/<[^>]*>/g, '')}
          </p>
          <div className="mt-2 flex items-center space-x-3">
            <span className={`text-xs px-2 py-0.5 rounded ${getTypeColor(notification.type)}`}>
              {notification.typeName}
            </span>
            <span className="text-xs text-gray-500">{formatDate(notification.createdAt)}</span>
          </div>
        </div>
        
        <div className="flex items-center space-x-1">
          {isUnread && (
            <button 
              className="text-xs px-2 py-1 text-green-600 hover:bg-green-50 rounded"
              onClick={(e) => handleActionClick(e, () => onMarkAsRead(notification.id))}
              aria-label="标记为已读"
            >
              未读
            </button>
          )}
          <button 
            className="text-xs px-2 py-1 text-red-600 hover:bg-red-50 rounded"
            onClick={(e) => handleActionClick(e, () => onDelete(notification.id))}
            aria-label="删除"
          >
            删除
          </button>
        </div>
      </div>
      
      {isUnread && (
        <div className="absolute left-1 top-1/2 -translate-y-1/2 w-2 h-2 rounded-full bg-blue-500"></div>
      )}
    </div>
  );
};

export default NotificationItem; 