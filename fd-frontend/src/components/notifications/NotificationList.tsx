'use client';

import { FC, useState } from 'react';
import { Notification, NotificationStatus, NotificationType } from '../../types/notification';
import NotificationItem from './NotificationItem';
import EmptyNotification from './EmptyNotification';
import NotificationDetail from './NotificationDetail';

interface NotificationListProps {
  notifications: Notification[];
  loading: boolean;
  error?: Error;
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
  onRefresh: () => void;
  onMarkAllAsRead: () => void;
  showFilters?: boolean;
  showActions?: boolean;
  className?: string;
  maxHeight?: string;
}

/**
 * 通知列表组件
 */
const NotificationList: FC<NotificationListProps> = ({
  notifications,
  loading,
  error,
  onMarkAsRead,
  onDelete,
  onRefresh,
  onMarkAllAsRead,
  showFilters = false,
  showActions = true,
  className = '',
  maxHeight = '400px'
}) => {
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);
  const [status, setStatus] = useState<NotificationStatus | ''>('');
  const [type, setType] = useState<NotificationType | ''>('');

  // 筛选通知
  const filteredNotifications = notifications.filter(notification => {
    if (status && notification.status !== status) return false;
    if (type && notification.type !== type) return false;
    return true;
  });

  // 处理查看通知详情
  const handleNotificationClick = (notification: Notification) => {
    setSelectedNotification(notification);
    
    // 移除自动标记为已读的逻辑，仅显示详情
    // 当用户查看详情时，由详情组件处理已读标记
  };

  // 关闭通知详情并标记为已读
  const handleCloseDetail = () => {
    // 如果通知是未读状态，关闭时标记为已读
    if (selectedNotification && selectedNotification.status === NotificationStatus.UNREAD) {
      onMarkAsRead(selectedNotification.id);
    }
    setSelectedNotification(null);
  };

  // 清除筛选条件
  const handleClearFilters = () => {
    setStatus('');
    setType('');
  };

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      {/* 列表标题和操作按钮 */}
      {showActions && (
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-lg font-medium">通知中心</h2>
          <div className="flex space-x-2">
            <button 
              onClick={onMarkAllAsRead}
              className="text-xs px-3 py-1 rounded bg-blue-50 text-blue-600 hover:bg-blue-100"
              disabled={loading}
            >
              全部已读
            </button>
            <button 
              onClick={onRefresh}
              className="text-xs px-3 py-1 rounded bg-gray-50 text-gray-600 hover:bg-gray-100"
              disabled={loading}
            >
              刷新
            </button>
          </div>
        </div>
      )}
      
      {/* 筛选栏 */}
      {showFilters && (
        <div className="p-4 border-b bg-gray-50">
          <div className="flex flex-wrap gap-3">
            <div>
              <select
                className="text-sm border rounded p-1"
                value={status}
                onChange={(e) => setStatus(e.target.value as NotificationStatus | '')}
              >
                <option value="">全部状态</option>
                <option value={NotificationStatus.UNREAD}>未读</option>
                <option value={NotificationStatus.READ}>已读</option>
              </select>
            </div>
            
            <div>
              <select
                className="text-sm border rounded p-1"
                value={type}
                onChange={(e) => setType(e.target.value as NotificationType | '')}
              >
                <option value="">全部类型</option>
                <option value={NotificationType.SYSTEM}>系统通知</option>
                <option value={NotificationType.ACCOUNT}>账户通知</option>
                <option value={NotificationType.PAYMENT}>支付通知</option>
                <option value={NotificationType.MEMBER}>会员通知</option>
              </select>
            </div>
            
            {(status || type) && (
              <button 
                className="text-xs px-2 py-1 text-gray-600"
                onClick={handleClearFilters}
              >
                清除筛选
              </button>
            )}
          </div>
        </div>
      )}
      
      {/* 错误提示 */}
      {error && (
        <div className="p-4 text-red-600 text-sm border-b">
          加载失败: {error.message}
        </div>
      )}
      
      {/* 列表内容 */}
      <div className={`overflow-y-auto ${maxHeight ? `max-h-[${maxHeight}]` : ''}`} style={{ maxHeight }}>
        {loading && notifications.length === 0 ? (
          <EmptyNotification loading={true} />
        ) : filteredNotifications.length === 0 ? (
          <EmptyNotification message={status || type ? '没有符合条件的通知' : '暂无通知'} />
        ) : (
          <div className="relative">
            {filteredNotifications.map(notification => (
              <NotificationItem
                key={notification.id}
                notification={notification}
                onMarkAsRead={onMarkAsRead}
                onDelete={onDelete}
                onClick={handleNotificationClick}
              />
            ))}
          </div>
        )}
      </div>
      
      {/* 通知详情 */}
      {selectedNotification && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <NotificationDetail
              notification={selectedNotification}
              onClose={handleCloseDetail}
              onMarkAsRead={onMarkAsRead}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationList; 