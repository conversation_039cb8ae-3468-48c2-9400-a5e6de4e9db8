'use client';

import { FC } from 'react';
import NotificationList from './NotificationList';
import { 
  useNotifications, 
  useUnreadNotificationCount, 
  useMarkNotificationRead, 
  useMarkAllNotificationsRead, 
  useDeleteNotification 
} from '../../hooks/useNotifications';

interface NotificationCenterProps {
  className?: string;
}

/**
 * 通知中心页面组件
 */
const NotificationCenter: FC<NotificationCenterProps> = ({
  className = ''
}) => {
  // 通知相关 hooks
  const { notifications, loading, error, refetch } = useNotifications({
    perPage: 50 // 在中心页面显示更多通知
  });
  const { refetch: refetchCount } = useUnreadNotificationCount();
  const { markNotificationRead } = useMarkNotificationRead();
  const { markAllNotificationsRead } = useMarkAllNotificationsRead();
  const { deleteNotification } = useDeleteNotification();

  // 处理标记已读
  const handleMarkAsRead = async (id: string) => {
    try {
      await markNotificationRead(id);
      refetch();
      refetchCount();
    } catch (err) {
      console.error('标记已读失败', err);
    }
  };

  // 刷新通知数据
  const handleRefresh = async () => {
    try {
      await Promise.all([refetch(), refetchCount()]);
    } catch (err) {
      console.error('刷新通知失败', err);
    }
  };

  // 处理标记所有已读
  const handleMarkAllAsRead = async () => {
    try {
      await markAllNotificationsRead();
      refetch();
      refetchCount();
    } catch (err) {
      console.error('标记所有已读失败', err);
    }
  };

  // 处理删除通知
  const handleDelete = async (id: string) => {
    try {
      const result = await deleteNotification(id);
      if (result && result.success) {
        refetch();
        refetchCount();
      }
    } catch (err) {
      console.error('删除通知失败', err);
    }
  };

  return (
    <div className={`container mx-auto p-4 ${className}`}>
      <div className="mb-6">
        <h1 className="text-2xl font-bold">通知中心</h1>
        <p className="text-gray-600 mt-2">查看您的所有通知</p>
      </div>
      
      <div className="bg-white rounded-lg shadow">
        <NotificationList
          notifications={notifications}
          loading={loading}
          error={error}
          onMarkAsRead={handleMarkAsRead}
          onDelete={handleDelete}
          onRefresh={handleRefresh}
          onMarkAllAsRead={handleMarkAllAsRead}
          showFilters={true}
          maxHeight="none"
        />
      </div>
    </div>
  );
};

export default NotificationCenter; 