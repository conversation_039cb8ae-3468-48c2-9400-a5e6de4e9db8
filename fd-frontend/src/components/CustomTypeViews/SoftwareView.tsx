import React from 'react';
import Link from 'next/link';
import { CustomTypeViewProps, CustomTypePost } from './BaseTypeView';
import { buildCustomPostUrl } from '@/utils/url-builder';

/**
 * 软件视图组件
 * 设计类似应用商店的风格，展示软件图标、版本等信息
 */
const SoftwareView: React.FC<CustomTypeViewProps> = ({ posts, type, routePrefixes, className = '' }) => {
  if (!posts || posts.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">暂无软件内容</p>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      {/* 筛选工具栏 */}
      <div className="mb-8 bg-gray-50 rounded-lg p-4">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center">
            <span className="text-sm font-medium mr-2">平台:</span>
            <select className="text-sm border border-gray-300 rounded py-1 px-2">
              <option value="all">全部</option>
              <option value="windows">Windows</option>
              <option value="macos">macOS</option>
              <option value="linux">Linux</option>
              <option value="ios">iOS</option>
              <option value="android">Android</option>
            </select>
          </div>
          
          <div className="flex items-center">
            <span className="text-sm font-medium mr-2">分类:</span>
            <select className="text-sm border border-gray-300 rounded py-1 px-2">
              <option value="all">全部分类</option>
              <option value="productivity">效率工具</option>
              <option value="development">开发工具</option>
              <option value="design">设计工具</option>
              <option value="entertainment">娱乐应用</option>
            </select>
          </div>
          
          <div className="flex items-center">
            <span className="text-sm font-medium mr-2">排序:</span>
            <select className="text-sm border border-gray-300 rounded py-1 px-2">
              <option value="newest">最新发布</option>
              <option value="downloads">下载量</option>
              <option value="name">名称</option>
            </select>
          </div>
        </div>
      </div>

      {/* 软件列表 */}
      <div className="space-y-6">
        {posts.map((post: CustomTypePost) => {
          const uuid = post.shortUuid || post.databaseId?.toString() || '';
          const postUrl = buildCustomPostUrl(type, uuid, post.slug, routePrefixes);
          
          return (
            <div 
              key={post.id} 
              className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow"
            >
              <div className="flex flex-col sm:flex-row">
                {/* 软件图标/封面 */}
                <div className="sm:w-40 h-40 sm:h-auto flex-shrink-0 bg-gradient-to-br from-blue-50 to-indigo-50 flex items-center justify-center p-4">
                  {post.featuredImage?.node?.sourceUrl ? (
                    <img 
                      src={post.featuredImage.node.sourceUrl}
                      alt={post.title}
                      className="max-w-full max-h-full object-contain"
                    />
                  ) : (
                    <div className="w-24 h-24 rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center text-2xl font-bold text-white">
                      {post.title.charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>
                
                {/* 软件信息 */}
                <div className="p-5 flex-grow">
                  <div className="flex justify-between items-start">
                    <div>
                      <Link href={postUrl}>
                        <h2 className="text-xl font-bold text-gray-900 hover:text-blue-600">
                          {post.title}
                        </h2>
                      </Link>
                      
                      <div className="mt-1 flex flex-wrap gap-2 mb-3">
                        {post.platforms && post.platforms.map((platform, index) => (
                          <span 
                            key={index} 
                            className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800"
                          >
                            {platform}
                          </span>
                        ))}
                        
                        {post.version && (
                          <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800">
                            v{post.version}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    {post.downloadUrl && (
                      <Link 
                        href={post.downloadUrl}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
                      >
                        下载
                      </Link>
                    )}
                  </div>
                  
                  {post.excerpt && (
                    <div 
                      className="text-gray-600 text-sm mb-4 line-clamp-2"
                      dangerouslySetInnerHTML={{ __html: post.excerpt }}
                    />
                  )}
                  
                  <div className="flex items-center text-xs text-gray-500 mt-4">
                    <span>更新于: {new Date(post.date).toLocaleDateString('zh-CN')}</span>
                  </div>
                </div>
              </div>
              
              {/* 软件截图预览 - 横向滚动 */}
              {post.screenshots && post.screenshots.length > 0 && (
                <div className="p-4 border-t border-gray-100">
                  <h3 className="text-sm font-medium text-gray-900 mb-3">应用截图</h3>
                  <div className="flex space-x-4 overflow-x-auto pb-2">
                    {post.screenshots.map((screenshot, index) => (
                      <div key={index} className="flex-shrink-0 w-48 h-32">
                        <img 
                          src={screenshot.url}
                          alt={screenshot.alt || `截图 ${index + 1}`}
                          className="rounded w-full h-full object-cover"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default SoftwareView; 