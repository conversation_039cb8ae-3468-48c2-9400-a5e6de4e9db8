import React from 'react';
import Link from 'next/link';
import { CustomTypeViewProps, CustomTypePost } from './BaseTypeView';
import { buildCustomPostUrl } from '@/utils/url-builder';

/**
 * 书架风格的星级评分组件
 */
const StarRating = ({ rating = 0 }: { rating?: number }) => {
  const stars = [];
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating - fullStars >= 0.5;
  
  // 填充完整星星
  for (let i = 0; i < fullStars; i++) {
    stars.push(
      <svg key={`full-${i}`} className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.462a1 1 0 00.95-.69l1.07-3.292z" />
      </svg>
    );
  }
  
  // 添加半颗星
  if (hasHalfStar) {
    stars.push(
      <svg key="half" className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
        <defs>
          <linearGradient id="halfGradient">
            <stop offset="50%" stopColor="currentColor" />
            <stop offset="50%" stopColor="#e5e7eb" />
          </linearGradient>
        </defs>
        <path fill="url(#halfGradient)" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.462a1 1 0 00.95-.69l1.07-3.292z" />
      </svg>
    );
  }
  
  // 添加空星星
  for (let i = stars.length; i < 5; i++) {
    stars.push(
      <svg key={`empty-${i}`} className="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.462a1 1 0 00.95-.69l1.07-3.292z" />
      </svg>
    );
  }
  
  return <div className="flex">{stars}</div>;
};

/**
 * 书籍视图组件
 * 设计类似图书馆书架的风格，展示书籍封面、作者信息和评分
 */
const BooksView: React.FC<CustomTypeViewProps> = ({ posts, type, routePrefixes, className = '' }) => {
  if (!posts || posts.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">暂无书籍内容</p>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      {/* 筛选区域 */}
      <div className="mb-8 p-4 bg-gray-50 rounded-lg">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center">
            <span className="text-sm font-medium mr-2">排序:</span>
            <select className="text-sm border border-gray-300 rounded py-1 px-2">
              <option value="newest">最新上架</option>
              <option value="rating">评分最高</option>
              <option value="title">书名排序</option>
            </select>
          </div>
          
          <div className="flex items-center">
            <span className="text-sm font-medium mr-2">显示:</span>
            <div className="flex border border-gray-300 rounded overflow-hidden">
              <button className="py-1 px-3 bg-blue-500 text-white text-sm">
                书架
              </button>
              <button className="py-1 px-3 bg-white text-gray-700 text-sm">
                列表
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 书籍书架展示 */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6 md:gap-8">
        {posts.map((post: CustomTypePost) => {
          const uuid = post.shortUuid || post.databaseId?.toString() || '';
          const postUrl = buildCustomPostUrl(type, uuid, post.slug, routePrefixes);
          
          return (
            <div key={post.id} className="group perspective-1000">
              <Link href={postUrl}>
                <div className="relative preserve-3d transition-transform duration-500 transform-style-preserve-3d hover:rotate-y-15">
                  {/* 封面 */}
                  <div className="relative w-full h-[220px] rounded shadow-lg overflow-hidden">
                    {post.featuredImage?.node?.sourceUrl ? (
                      <img 
                        src={post.featuredImage.node.sourceUrl}
                        alt={post.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-b from-blue-500 to-blue-700 flex items-center justify-center p-4">
                        <span className="text-white font-bold text-center">{post.title}</span>
                      </div>
                    )}
                    
                    {/* 书脊效果 */}
                    <div className="absolute left-0 top-0 bottom-0 w-[6px] bg-gradient-to-r from-gray-800/20 to-transparent"></div>
                    
                    {/* 书本阴影效果 */}
                    <div className="absolute left-0 right-0 bottom-0 h-8 bg-gradient-to-t from-black/30 to-transparent"></div>
                  </div>
                  
                  {/* 书籍信息 */}
                  <div className="mt-3">
                    <h3 className="text-sm font-bold text-gray-800 line-clamp-1">
                      {post.title}
                    </h3>
                    
                    {post.bookAuthor && (
                      <p className="text-xs text-gray-500 mt-1">{post.bookAuthor}</p>
                    )}
                    
                    <div className="mt-2 flex items-center justify-between">
                      <StarRating rating={post.rating} />
                      {post.pageCount && (
                        <span className="text-xs text-gray-500">{post.pageCount}页</span>
                      )}
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default BooksView; 