import React from 'react';

/**
 * 通用卡片骨架屏
 */
const CardSkeleton = () => {
  return (
    <div className="rounded-lg overflow-hidden animate-pulse break-inside-avoid">
      {/* 图片占位符 */}
      <div className="relative w-full bg-gray-300" style={{ paddingBottom: '125%' }}></div>
      
      <div className="p-2">
          {/* 标题占位符 */}
        <div className="h-4 bg-gray-300 rounded w-full mb-3"></div>
        
        {/* 作者和点赞占位符 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-5 h-5 bg-gray-300 rounded-full mr-2"></div>
            <div className="h-4 bg-gray-300 rounded w-16"></div>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-gray-300 rounded-sm"></div>
            <div className="h-4 bg-gray-300 rounded w-8 ml-1"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

interface SkeletonProps {
  count?: number;
}

/**
 * 默认视图的骨架屏组件
 */
const DefaultViewSkeleton: React.FC<SkeletonProps> = ({ count = 8 }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {Array.from({ length: count }).map((_, i) => (
        <CardSkeleton key={i} />
        ))}
    </div>
  );
};

export default DefaultViewSkeleton; 