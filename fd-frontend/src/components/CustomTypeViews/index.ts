import React from 'react';
import { CustomTypeViewProps, TypeViewRegistry } from './BaseTypeView';
import DefaultView from './DefaultView';  // 先假设已经创建了这个文件

// 定义视图组件映射
const viewComponentMap: {[key: string]: React.ComponentType<CustomTypeViewProps>} = {
  default: DefaultView
};

// 实现异步加载视图组件的函数
export const loadViewComponent = async (viewName: string): Promise<React.ComponentType<CustomTypeViewProps>> => {
  // 如果组件已经加载，则直接返回
  if (viewComponentMap[viewName]) {
    return viewComponentMap[viewName];
  }

  try {
    // 根据视图名称动态导入组件
    // 注意：这里假设每个视图组件都导出为default
    let component;
    switch (viewName) {
      case 'notes':
        component = (await import('./NotesView')).default;
        break;
      case 'books':
        component = (await import('./BooksView')).default;
        break;
      case 'software':
        component = (await import('./SoftwareView')).default;
        break;
      case 'products':
        component = (await import('./ProductsView')).default;
        break;
      default:
        component = DefaultView;
    }
    
    // 缓存组件
    viewComponentMap[viewName] = component;
    return component;
  } catch (error) {
    console.error(`Failed to load component ${viewName}View:`, error);
    return DefaultView;
  }
};

/**
 * 获取视图组件
 * @param viewName 视图名称
 * @returns 对应的视图组件或默认组件
 */
export function getTypeViewComponent(viewName: string) {
  return async () => {
    return await loadViewComponent(viewName || 'default');
  };
} 