import React from 'react';
import Link from 'next/link';
import { CustomTypeViewProps, CustomTypePost } from './BaseTypeView';
import { buildCustomPostUrl } from '@/utils/url-builder';

/**
 * 默认视图组件 - 网格布局
 */
const DefaultView: React.FC<CustomTypeViewProps> = ({ posts, type, routePrefixes, className = '' }) => {
  if (!posts || posts.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">暂无{type}内容</p>
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>
      {posts.map((post: CustomTypePost) => {
        const uuid = post.shortUuid || post.databaseId?.toString() || '';
        const postUrl = buildCustomPostUrl(type, uuid, post.slug, routePrefixes);
        
        return (
          <div key={post.id} className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <Link href={postUrl}>
              <div className="p-4">
                {post.featuredImage?.node?.sourceUrl && (
                  <div className="mb-4">
                    <img 
                      src={post.featuredImage.node.sourceUrl}
                      alt={post.featuredImage.node.altText || post.title}
                      className="w-full h-48 object-cover rounded-md"
                    />
                  </div>
                )}
                
                <h3 className="text-xl font-semibold mb-2 text-gray-800 dark:text-white">
                  {post.title}
                </h3>
                
                {post.excerpt && (
                  <div 
                    className="text-gray-600 dark:text-gray-300 mb-3 line-clamp-3"
                    dangerouslySetInnerHTML={{ __html: post.excerpt }}
                  />
                )}
                
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {new Date(post.date).toLocaleDateString('zh-CN')}
                </div>
              </div>
            </Link>
          </div>
        );
      })}
    </div>
  );
};

export default DefaultView; 