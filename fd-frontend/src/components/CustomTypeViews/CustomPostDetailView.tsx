'use client';

import React from 'react';
import ReactionButton from '../post/ReactionButton';
import { ReactionType } from '@/types/Reaction';
import { Post as PostType } from '@/types/post'; // Assuming CustomPost can be represented by PostType
import { HiOutlineHeart } from 'react-icons/hi';


interface CustomPostDetailViewProps {
  customPost: PostType;
}

/**
 * 自定义文章类型详情页组件
 * 展示自定义文章类型的详情，并包含点赞功能
 */
const CustomPostDetailView: React.FC<CustomPostDetailViewProps> = ({ customPost }) => {
  return (
    <article className="custom-post-container max-w-4xl mx-auto py-10 px-4">
      <h1 className="text-3xl font-bold mb-6">{customPost.title}</h1>
      
      {/* 文章内容 */}
      <div 
        className="custom-post-content prose lg:prose-xl mx-auto mb-8"
        dangerouslySetInnerHTML={{ __html: customPost.content || '' }}
      />
      
      {/* 点赞按钮 - 使用改造后的LikeButton组件 */}
      <div className="like-section mt-6 pt-4 border-t">
        <ReactionButton
          postId={customPost.id}
          initialCount={customPost.likeCount || 0}
          initialState={customPost.userHasLiked || false}
          type={ReactionType.Like}
          icon={<HiOutlineHeart className="w-5 h-5 mr-2" />}
          activeClass="bg-rose-500 text-white hover:bg-rose-600"
          inactiveClass="bg-gray-100 text-gray-700 hover:bg-gray-200"
          successMessage="感谢点赞！"
          undoMessage="已取消点赞"
        />
        {/* You can add Bookmark and Recommend buttons here as well if needed */}
      </div>
    </article>
  );
};

export default CustomPostDetailView; 