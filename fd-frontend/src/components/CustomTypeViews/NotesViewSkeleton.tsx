import React from 'react';

/**
 * 新的小红书风格卡片骨架屏
 */
const NoteCardSkeleton = () => {
  return (
    <div className="rounded-lg overflow-hidden animate-pulse break-inside-avoid">
      {/* 图片占位符 */}
      <div className="relative w-full bg-gray-300" style={{ paddingBottom: '125%' }}></div>
      
      <div className="p-2">
          {/* 标题占位符 */}
        <div className="h-4 bg-gray-300 rounded w-full mb-3"></div>
        
        {/* 作者和点赞占位符 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-5 h-5 bg-gray-300 rounded-full mr-2"></div>
            <div className="h-4 bg-gray-300 rounded w-16"></div>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-gray-300 rounded-sm"></div>
            <div className="h-4 bg-gray-300 rounded w-8 ml-1"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

interface NotesViewSkeletonProps {
  count?: number;
}

/**
 * 笔记视图的骨架屏组件 - 匹配小红书UI
 */
const NotesViewSkeleton: React.FC<NotesViewSkeletonProps> = ({ count = 8 }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {Array.from({ length: count }).map((_, i) => (
        <NoteCardSkeleton key={i} />
        ))}
    </div>
  );
};

export default NotesViewSkeleton; 