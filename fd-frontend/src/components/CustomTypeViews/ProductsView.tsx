import React from 'react';
import Link from 'next/link';
import { CustomTypeViewProps, CustomTypePost } from './BaseTypeView';
import { buildCustomPostUrl } from '@/utils/url-builder';

/**
 * 产品视图组件
 * 设计类似电商网站的产品展示页面，包含价格、折扣等信息
 */
const ProductsView: React.FC<CustomTypeViewProps> = ({ posts, type, routePrefixes, className = '' }) => {
  if (!posts || posts.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">暂无产品内容</p>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      {/* 产品筛选器 */}
      <div className="mb-8 bg-white rounded-lg shadow p-4">
        <div className="mb-4 pb-4 border-b border-gray-100">
          <h3 className="font-medium text-gray-800 mb-2">价格范围</h3>
          <div className="flex items-center space-x-4">
            <div className="flex rounded-md overflow-hidden border border-gray-300 w-24">
              <input type="number" placeholder="最低" className="w-full px-2 py-1 text-sm" />
            </div>
            <span className="text-gray-400">-</span>
            <div className="flex rounded-md overflow-hidden border border-gray-300 w-24">
              <input type="number" placeholder="最高" className="w-full px-2 py-1 text-sm" />
            </div>
            <button className="bg-blue-600 text-white text-sm px-3 py-1 rounded">
              确定
            </button>
          </div>
        </div>
        
        <div className="flex flex-wrap gap-4">
          <div>
            <h3 className="text-sm font-medium text-gray-800 mb-2">排序方式</h3>
            <select className="text-sm border border-gray-300 rounded py-1 px-2">
              <option value="default">默认排序</option>
              <option value="price_asc">价格从低到高</option>
              <option value="price_desc">价格从高到低</option>
              <option value="newest">最新发布</option>
              <option value="popular">人气优先</option>
            </select>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-800 mb-2">仅看有货</h3>
            <div className="flex items-center">
              <input type="checkbox" className="mr-2" id="in-stock" />
              <label htmlFor="in-stock" className="text-sm text-gray-700">显示有货产品</label>
            </div>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-800 mb-2">仅看特惠</h3>
            <div className="flex items-center">
              <input type="checkbox" className="mr-2" id="on-sale" />
              <label htmlFor="on-sale" className="text-sm text-gray-700">显示特惠产品</label>
            </div>
          </div>
        </div>
      </div>

      {/* 产品网格 */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {posts.map((post: CustomTypePost) => {
          const uuid = post.shortUuid || post.databaseId?.toString() || '';
          const postUrl = buildCustomPostUrl(type, uuid, post.slug, routePrefixes);
          
          // 计算折扣比例
          const discountPercent = post.originalPrice && post.price 
            ? Math.round(100 - (post.price / post.originalPrice * 100)) 
            : 0;
          
          return (
            <div 
              key={post.id} 
              className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-all group"
            >
              <Link href={postUrl} className="block">
                {/* 产品图片 */}
                <div className="relative h-48 overflow-hidden bg-gray-100">
                  {post.featuredImage?.node?.sourceUrl ? (
                    <img 
                      src={post.featuredImage.node.sourceUrl}
                      alt={post.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                      暂无图片
                    </div>
                  )}
                  
                  {/* 折扣标签 */}
                  {discountPercent > 0 && (
                    <div className="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                      -{discountPercent}%
                    </div>
                  )}
                  
                  {/* 库存状态标签 */}
                  {post.inStock === false && (
                    <div className="absolute top-0 left-0 right-0 bottom-0 bg-black/50 flex items-center justify-center">
                      <span className="bg-gray-900 text-white px-3 py-1 rounded-sm text-sm font-medium">
                        已售罄
                      </span>
                    </div>
                  )}
                </div>
                
                {/* 产品信息 */}
                <div className="p-4">
                  <h3 className="text-sm font-medium text-gray-900 mb-1 line-clamp-2 h-10">
                    {post.title}
                  </h3>
                  
                  {/* 价格区域 */}
                  <div className="mt-2 flex items-end">
                    {post.price !== undefined && (
                      <div className="text-red-600 font-bold">
                        ¥{post.price?.toFixed(2)}
                      </div>
                    )}
                    
                    {post.originalPrice !== undefined && post.originalPrice > (post.price || 0) && (
                      <div className="ml-2 text-xs text-gray-500 line-through">
                        ¥{post.originalPrice?.toFixed(2)}
                      </div>
                    )}
                  </div>
                  
                  {/* 商品评价 */}
                  {post.reviews && (
                    <div className="mt-2 flex items-center text-xs text-gray-500">
                      <div className="flex items-center">
                        {/* 星星评分 */}
                        <span className="text-yellow-400">★</span>
                        <span className="ml-1">{post.reviews.rating?.toFixed(1)}</span>
                      </div>
                      <span className="mx-1">|</span>
                      <span>{post.reviews.count} 条评价</span>
                    </div>
                  )}
                </div>
              </Link>
              
              {/* 购买按钮 */}
              <div className="px-4 pb-4">
                <button 
                  className={`w-full py-2 text-sm rounded font-medium ${
                    post.inStock === false 
                      ? 'bg-gray-200 text-gray-500 cursor-not-allowed' 
                      : 'bg-red-50 text-red-600 hover:bg-red-600 hover:text-white transition-colors'
                  }`}
                  disabled={post.inStock === false}
                >
                  {post.inStock === false ? '已售罄' : '加入购物车'}
                </button>
              </div>
            </div>
          );
        })}
      </div>
      
      {/* 分页器 */}
      <div className="mt-10 flex justify-center">
        <div className="flex">
          <button className="px-4 py-2 mx-1 border border-gray-300 rounded-md bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
            上一页
          </button>
          
          <button className="px-4 py-2 mx-1 border border-gray-300 rounded-md bg-blue-600 text-sm font-medium text-white">
            1
          </button>
          
          <button className="px-4 py-2 mx-1 border border-gray-300 rounded-md bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
            2
          </button>
          
          <button className="px-4 py-2 mx-1 border border-gray-300 rounded-md bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
            3
          </button>
          
          <button className="px-4 py-2 mx-1 border border-gray-300 rounded-md bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
            下一页
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProductsView; 