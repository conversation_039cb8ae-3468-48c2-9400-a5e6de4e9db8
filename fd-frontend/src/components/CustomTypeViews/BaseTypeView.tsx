import React from 'react';
import { RoutePrefixes } from '@/types/routes';

/**
 * 自定义类型文章接口
 */
export interface CustomTypePost {
  id: string;
  title: string;
  slug: string;
  date: string;
  shortUuid?: string;
  databaseId?: number;
  excerpt?: string;
  featuredImage?: {
    node: {
      sourceUrl: string;
      altText?: string;
    }
  };
  // 作者信息
  author?: {
    node: {
      id: string;
      name: string;
      slug: string;
      nickname?: string;
      avatar?: {
        url: string;
      };
    };
  };
  // 反应数据
  likeCount?: number;
  userHasLiked?: boolean;
  bookmarkCount?: number;
  userHasBookmarked?: boolean;
  recommendCount?: number;
  userHasRecommended?: boolean;
  commentCount?: number;
  // 可能的特定类型字段，这些会在具体的视图组件中使用
  // 笔记特有字段
  noteCategory?: string;
  tags?: string[];
  lastModified?: string;
  
  // 书籍特有字段
  bookAuthor?: string;
  publisher?: string;
  isbn?: string;
  pageCount?: number;
  rating?: number;
  
  // 软件特有字段
  version?: string;
  platforms?: string[];
  downloadUrl?: string;
  screenshots?: {url: string; alt?: string}[];
  
  // 产品特有字段
  price?: number;
  originalPrice?: number;
  inStock?: boolean;
  specifications?: {[key: string]: string};
  reviews?: {rating: number; count: number};
}

/**
 * 自定义类型视图组件基础接口
 */
export interface CustomTypeViewProps {
  posts: CustomTypePost[];
  type: string;
  routePrefixes: RoutePrefixes;
  className?: string;
  query?: string;
}

/**
 * 视图组件注册表接口
 */
export interface TypeViewRegistry {
  [key: string]: React.ComponentType<CustomTypeViewProps>;
} 