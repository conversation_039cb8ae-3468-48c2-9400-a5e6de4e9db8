'use client';

import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import { PHONE_REGISTER } from '../../lib/graphql/mutations';

/**
 * 简化的手机注册API测试组件
 * 直接测试mutation格式和后端通信
 */
const PhoneVerificationTest: React.FC = () => {
  // 直接使用mutation钩子
  const [phoneRegisterMutation] = useMutation(PHONE_REGISTER);

  // 表单状态
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('123456'); // 默认设置一个验证码，因为后端已临时修改为跳过验证
  const [nationCode, setNationCode] = useState('86');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [displayName, setDisplayName] = useState('');

  // 操作结果状态
  const [registerResult, setRegisterResult] = useState<any>(null);
  const [isRegistering, setIsRegistering] = useState(false);
  
  // 直接测试手机注册API
  const handleTestRegister = async () => {
    if (!phone || !code) {
      alert('请输入手机号和验证码');
      return;
    }
    
    setRegisterResult(null);
    setIsRegistering(true);

    try {
      // 直接调用mutation，测试格式是否正确
      const { data } = await phoneRegisterMutation({
        variables: {
          phone,
          code,
          nationCode,
          username: username || undefined,
          displayName: displayName || username || undefined,
          password: password || undefined
        }
      });

      setRegisterResult(data);
      console.log('注册结果:', data);
    } catch (error) {
      console.error('注册失败:', error);
      setRegisterResult({
        error: true,
        message: error instanceof Error ? error.message : '未知错误'
      });
    } finally {
      setIsRegistering(false);
    }
  };
  
  return (
    <div className="p-4 max-w-md mx-auto bg-white rounded-lg border border-gray-200 shadow-md">
      <h2 className="text-xl font-bold mb-4">手机注册API直接测试</h2>
      <p className="text-sm text-gray-500 mb-4">
        此测试直接调用PHONE_REGISTER mutation，无需真实验证码校验
      </p>
      
      <form className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">手机号 <span className="text-red-500">*</span></label>
          <div className="mt-1 flex rounded-md shadow-sm">
            <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500">
              +{nationCode}
            </span>
            <input
              type="text"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300"
              placeholder="请输入手机号"
              pattern="^1[3-9]\d{9}$"
              required
            />
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">国际区号</label>
          <input
            type="text"
            value={nationCode}
            onChange={(e) => setNationCode(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
            placeholder="国际区号（默认86）"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">验证码 <span className="text-red-500">*</span></label>
          <input
            type="text"
            value={code}
            onChange={(e) => setCode(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
            placeholder="验证码（后端已临时修改为跳过验证）"
            required
          />
          <p className="mt-1 text-xs text-gray-500">后端已临时修改为验证码全部通过</p>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">用户名 (可选)</label>
          <input
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
            placeholder="用户名"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">显示名称 (可选)</label>
          <input
            type="text"
            value={displayName}
            onChange={(e) => setDisplayName(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
            placeholder="显示名称，不填则使用用户名"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">密码 (可选)</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
            placeholder="密码"
          />
        </div>
        
        <button
          type="button"
          onClick={handleTestRegister}
          disabled={!phone || !code || isRegistering}
          className={`w-full px-4 py-2 text-sm font-medium text-white rounded-md ${
            !phone || !code || isRegistering
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-green-600 hover:bg-green-700'
          }`}
        >
          {isRegistering ? '测试中...' : '测试手机注册 API'}
        </button>
        
        {registerResult && (
          <div className={`mt-4 p-3 rounded ${registerResult.error ? 'bg-red-100' : registerResult.phoneRegister?.success ? 'bg-green-100' : 'bg-yellow-100'}`}>
            <h4 className="font-bold">{registerResult.error ? '测试错误' : '测试结果'}</h4>
            <pre className="whitespace-pre-wrap text-xs mt-2 bg-white p-2 rounded border overflow-auto">
              {JSON.stringify(registerResult, null, 2)}
            </pre>
          </div>
        )}
      </form>
    </div>
  );
};

export default PhoneVerificationTest; 