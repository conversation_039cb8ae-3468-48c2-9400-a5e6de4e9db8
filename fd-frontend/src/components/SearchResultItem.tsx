import Link from 'next/link';
import { formatDate } from '@/utils/date-utils';
import { getContentTypeLabel } from '@/utils/content-utils';

interface SearchResultItemProps {
  result: any;
  getLinkByContentType: (result: any) => string;
}

/**
 * 搜索结果项组件
 * 用于显示搜索结果中的单个条目
 */
export function SearchResultItem({ result, getLinkByContentType }: SearchResultItemProps) {
  // 获取标题
  const getTitle = (title: any): string => {
    if (!title) return '无标题';
    if (typeof title === 'string') return title;
    if (typeof title === 'object' && title.rendered) return title.rendered;
    return '无标题';
  };

  return (
    <div className="border p-4 rounded-lg hover:bg-gray-50 transition-colors">
      <Link href={getLinkByContentType(result)} className="block">
        <div className="flex items-center mb-1">
          <span className="inline-block px-2 py-0.5 mr-2 text-xs bg-gray-100 rounded text-gray-600">
            {getContentTypeLabel(result.__typename || result.contentType)}
          </span>
          <h2 className="text-xl font-semibold text-blue-600 hover:underline">
            {getTitle(result.title)}
          </h2>
        </div>
        <p className="text-sm text-gray-500 mb-2">
          {formatDate(result.date)}
        </p>
        {result.excerpt && (
          <p className="text-gray-700">{result.excerpt}</p>
        )}
      </Link>
    </div>
  );
}

export default SearchResultItem; 