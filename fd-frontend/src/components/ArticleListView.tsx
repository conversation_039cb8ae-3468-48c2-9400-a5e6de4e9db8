import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { buildPostUrl } from '@/utils/url-builder';
import { getContentTypeLabel } from '@/utils/content-utils';

// 定义文章类型接口
export interface Article {
  id: string;
  title: string;
  date: string;
  slug: string;
  shortUuid?: string;
  excerpt?: string;
  featuredImage?: {
    node: {
      sourceUrl: string;
      altText?: string;
    }
  };
  author?: {
    node: {
      name: string;
      slug?: string;
      avatar?: {
        url?: string;
      }
    }
  };
  categories?: {
    nodes: {
      id: string;
      name: string;
      slug: string;
    }[]
  };
  contentType?: string;
  __typename?: string;
  uri?: string;
}

// 组件属性
export interface ArticleListViewProps {
  articles: Article[];
  mode?: 'list' | 'grid' | 'compact' | 'magazine';
  columns?: 1 | 2 | 3 | 4;
  showFeaturedImage?: boolean;
  showExcerpt?: boolean;
  showDate?: boolean;
  showAuthor?: boolean;
  showCategory?: boolean;
  showReadMore?: boolean;
  className?: string;
  routePrefixes?: any;
}

/**
 * 文章列表视图组件
 * 支持多种显示模式：列表、网格、紧凑和杂志
 */
export default function ArticleListView({
  articles,
  mode = 'list',
  columns = 3,
  showFeaturedImage = true,
  showExcerpt = true,
  showDate = true,
  showAuthor = true,
  showCategory = true,
  showReadMore = true,
  className = '',
  routePrefixes
}: ArticleListViewProps) {
  
  // 如果没有文章，显示空状态
  if (!articles || articles.length === 0) {
    return <div className="text-gray-500 text-center py-8">暂无内容</div>;
  }
  
  // 获取文章链接
  const getArticleLink = (article: Article) => {
    const type = article.__typename || article.contentType;
    
    // 处理标准文章
    if (!type || type === 'Post') {
      return buildPostUrl(article.shortUuid || '', article.slug, routePrefixes);
    }
    
    // 处理自定义文章类型
    if (type && type !== 'Post' && type !== 'Page') {
      const customType = type.toLowerCase();
      if (article.shortUuid) {
        return `/${routePrefixes?.customTypePrefix || 'post-type'}/${customType}/${article.shortUuid}/${article.slug}`;
      }
      return `/${routePrefixes?.customTypePrefix || 'post-type'}/${customType}/${article.slug}`;
    }
    
    // 默认返回文章URI或#
    return article.uri || '#';
  };
  
  // 列表视图
  if (mode === 'list') {
    return (
      <div className={`space-y-6 ${className}`}>
        {articles.map((article) => (
          <article key={article.id} className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow">
            <div className="flex flex-col md:flex-row">
              {showFeaturedImage && article.featuredImage?.node?.sourceUrl && (
                <div className="md:w-1/3 h-48 md:h-auto relative">
                  <img
                    src={article.featuredImage.node.sourceUrl}
                    alt={article.featuredImage.node.altText || article.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              <div className={`p-6 flex flex-col ${showFeaturedImage && article.featuredImage?.node?.sourceUrl ? 'md:w-2/3' : 'w-full'}`}>
                {showCategory && article.categories?.nodes && article.categories.nodes.length > 0 && (
                  <div className="mb-2">
                    <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                      {article.categories.nodes[0].name}
                    </span>
                  </div>
                )}
                <Link href={getArticleLink(article)}>
                  <h2 className="text-xl font-bold mb-2 hover:text-blue-600 transition-colors">
                    {article.title}
                  </h2>
                </Link>
                <div className="flex items-center text-sm text-gray-500 space-x-4 mb-3">
                  {showDate && (
                    <span>{new Date(article.date).toLocaleDateString('zh-CN')}</span>
                  )}
                  {showAuthor && article.author?.node && (
                    <span className="flex items-center">
                      {article.author.node.avatar?.url && (
                        <img 
                          src={article.author.node.avatar.url} 
                          alt={article.author.node.name}
                          className="w-5 h-5 rounded-full mr-1" 
                        />
                      )}
                      <span>{article.author.node.name}</span>
                    </span>
                  )}
                </div>
                {showExcerpt && article.excerpt && (
                  <div 
                    className="text-gray-600 mb-4 line-clamp-3"
                    dangerouslySetInnerHTML={{ __html: article.excerpt || '' }}
                  />
                )}
                {showReadMore && (
                  <div className="mt-auto">
                    <Link href={getArticleLink(article)} className="text-blue-600 hover:text-blue-800 font-medium text-sm">
                      阅读全文 →
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </article>
        ))}
      </div>
    );
  }
  
  // 网格视图
  if (mode === 'grid') {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-${columns} gap-6 ${className}`}>
        {articles.map((article) => (
          <article key={article.id} className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow">
            {showFeaturedImage && article.featuredImage?.node?.sourceUrl ? (
              <div className="h-48 relative">
                <img
                  src={article.featuredImage.node.sourceUrl}
                  alt={article.featuredImage.node.altText || article.title}
                  className="w-full h-full object-cover"
                />
              </div>
            ) : (
              <div className="h-48 bg-gray-100 flex items-center justify-center">
                <span className="text-gray-400">{getContentTypeLabel(article.__typename || article.contentType || 'Post')}</span>
              </div>
            )}
            <div className="p-4">
              {showCategory && article.categories?.nodes && article.categories.nodes.length > 0 && (
                <div className="mb-2">
                  <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                    {article.categories.nodes[0].name}
                  </span>
                </div>
              )}
              <Link href={getArticleLink(article)}>
                <h2 className="text-lg font-bold mb-2 line-clamp-2 hover:text-blue-600 transition-colors">
                  {article.title}
                </h2>
              </Link>
              <div className="flex items-center text-xs text-gray-500 space-x-2 mb-2">
                {showDate && (
                  <span>{new Date(article.date).toLocaleDateString('zh-CN')}</span>
                )}
                {showAuthor && article.author?.node && (
                  <span className="flex items-center">
                    <span className="mx-1">•</span>
                    {article.author.node.name}
                  </span>
                )}
              </div>
              {showExcerpt && article.excerpt && (
                <div 
                  className="text-sm text-gray-600 mb-3 line-clamp-2"
                  dangerouslySetInnerHTML={{ __html: article.excerpt || '' }}
                />
              )}
              {showReadMore && (
                <Link href={getArticleLink(article)} className="text-blue-600 hover:text-blue-800 text-xs font-medium">
                  阅读全文 →
                </Link>
              )}
            </div>
          </article>
        ))}
      </div>
    );
  }
  
  // 紧凑视图
  if (mode === 'compact') {
    return (
      <div className={`${className}`}>
        <ul className="divide-y divide-gray-100">
          {articles.map((article) => (
            <li key={article.id} className="py-3">
              <Link href={getArticleLink(article)} className="flex items-center">
                {showFeaturedImage && article.featuredImage?.node?.sourceUrl && (
                  <div className="flex-shrink-0 w-16 h-16 mr-4">
                    <img
                      src={article.featuredImage.node.sourceUrl}
                      alt={article.featuredImage.node.altText || article.title}
                      className="w-full h-full object-cover rounded"
                    />
                  </div>
                )}
                <div className="min-w-0 flex-1">
                  <h3 className="text-base font-medium text-gray-900 truncate">
                    {article.title}
                  </h3>
                  <div className="flex text-xs text-gray-500 mt-1">
                    {showDate && (
                      <span>{new Date(article.date).toLocaleDateString('zh-CN')}</span>
                    )}
                    {showAuthor && article.author?.node && (
                      <span className="ml-2">
                        <span className="mx-1">•</span>
                        {article.author.node.name}
                      </span>
                    )}
                  </div>
                </div>
              </Link>
            </li>
          ))}
        </ul>
      </div>
    );
  }
  
  // 杂志视图
  if (mode === 'magazine') {
    const [featuredArticle, ...restArticles] = articles;
    
    return (
      <div className={`space-y-8 ${className}`}>
        {/* 特色文章 */}
        <article className="bg-white rounded-lg shadow-md overflow-hidden">
          {showFeaturedImage && featuredArticle.featuredImage?.node?.sourceUrl && (
            <div className="h-72 sm:h-96 relative">
              <img
                src={featuredArticle.featuredImage.node.sourceUrl}
                alt={featuredArticle.featuredImage.node.altText || featuredArticle.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
              <div className="absolute bottom-0 left-0 p-6 text-white">
                {showCategory && featuredArticle.categories?.nodes && featuredArticle.categories.nodes.length > 0 && (
                  <div className="mb-2">
                    <span className="text-xs font-medium bg-blue-600 px-2 py-1 rounded">
                      {featuredArticle.categories.nodes[0].name}
                    </span>
                  </div>
                )}
                <Link href={getArticleLink(featuredArticle)}>
                  <h2 className="text-2xl sm:text-3xl font-bold mb-2 hover:text-blue-300 transition-colors">
                    {featuredArticle.title}
                  </h2>
                </Link>
                <div className="flex items-center text-sm text-gray-200 space-x-4">
                  {showDate && (
                    <span>{new Date(featuredArticle.date).toLocaleDateString('zh-CN')}</span>
                  )}
                  {showAuthor && featuredArticle.author?.node && (
                    <span className="flex items-center">
                      {featuredArticle.author.node.avatar?.url && (
                        <img 
                          src={featuredArticle.author.node.avatar.url} 
                          alt={featuredArticle.author.node.name}
                          className="w-5 h-5 rounded-full mr-1" 
                        />
                      )}
                      <span>{featuredArticle.author.node.name}</span>
                    </span>
                  )}
                </div>
              </div>
            </div>
          )}
          {!featuredArticle.featuredImage?.node?.sourceUrl && (
            <div className="p-6">
              {showCategory && featuredArticle.categories?.nodes && featuredArticle.categories.nodes.length > 0 && (
                <div className="mb-2">
                  <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                    {featuredArticle.categories.nodes[0].name}
                  </span>
                </div>
              )}
              <Link href={getArticleLink(featuredArticle)}>
                <h2 className="text-2xl sm:text-3xl font-bold mb-2 hover:text-blue-600 transition-colors">
                  {featuredArticle.title}
                </h2>
              </Link>
              <div className="flex items-center text-sm text-gray-500 space-x-4 mb-3">
                {showDate && (
                  <span>{new Date(featuredArticle.date).toLocaleDateString('zh-CN')}</span>
                )}
                {showAuthor && featuredArticle.author?.node && (
                  <span className="flex items-center">
                    {featuredArticle.author.node.avatar?.url && (
                      <img 
                        src={featuredArticle.author.node.avatar.url} 
                        alt={featuredArticle.author.node.name}
                        className="w-5 h-5 rounded-full mr-1" 
                      />
                    )}
                    <span>{featuredArticle.author.node.name}</span>
                  </span>
                )}
              </div>
              {showExcerpt && featuredArticle.excerpt && (
                <div 
                  className="text-gray-600 mb-4"
                  dangerouslySetInnerHTML={{ __html: featuredArticle.excerpt || '' }}
                />
              )}
            </div>
          )}
        </article>
        
        {/* 其余文章网格 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {restArticles.map((article) => (
            <article key={article.id} className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow">
              {showFeaturedImage && article.featuredImage?.node?.sourceUrl && (
                <div className="h-40 relative">
                  <img
                    src={article.featuredImage.node.sourceUrl}
                    alt={article.featuredImage.node.altText || article.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              <div className="p-4">
                <Link href={getArticleLink(article)}>
                  <h3 className="text-lg font-semibold mb-2 hover:text-blue-600 transition-colors line-clamp-2">
                    {article.title}
                  </h3>
                </Link>
                <div className="flex items-center text-xs text-gray-500">
                  {showDate && (
                    <span>{new Date(article.date).toLocaleDateString('zh-CN')}</span>
                  )}
                  {showAuthor && article.author?.node && (
                    <span className="ml-2">
                      <span className="mx-1">•</span>
                      {article.author.node.name}
                    </span>
                  )}
                </div>
              </div>
            </article>
          ))}
        </div>
      </div>
    );
  }
  
  // 默认返回列表视图
  return (
    <div className="space-y-6">
      {articles.map((article) => (
        <div key={article.id} className="border-b pb-4">
          <Link href={getArticleLink(article)}>
            <h2 className="text-xl font-bold mb-2">{article.title}</h2>
          </Link>
          <div className="text-sm text-gray-500">
            {new Date(article.date).toLocaleDateString('zh-CN')}
          </div>
        </div>
      ))}
    </div>
  );
} 