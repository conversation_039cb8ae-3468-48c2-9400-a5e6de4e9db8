export interface Post {
  __typename: string;
  id: string;
  databaseId: number;
  title: string;
  content: string;
  date: string;
  slug: string;
  author?: {
    node: {
      name: string;
      slug: string;
      nickname?: string;
      avatar?: {
        url: string;
      };
    };
  };
  categories?: {
    nodes: Category[];
  };
  tags?: {
    nodes: Tag[];
  };
  featuredImage?: {
    node: {
      sourceUrl: string;
      altText?: string;
    };
  };
  likeCount?: number;
  userHasLiked?: boolean;
  bookmarkCount?: number;
  userHasBookmarked?: boolean;
  recommendCount?: number;
  userHasRecommended?: boolean;
  commentCount?: number;
  postTemplate?: PostTemplateType;
  // Add other fields that might be missing for templates
  shortUuid?: string;
  unlockPrice?: number;
  requiredMemberLevel?: number;
  isUnlockedByCurrentUser?: boolean;
  isMembersOnly?: boolean;
  paywallInfo?: any | null;
  excerpt?: string;
  commentStatus?: string;
  aiSeoTitle?: string;
  aiSeoDescription?: string;
  aiSeoJsonLd?: string;
  shareImage?: string;
  comments?: {
    nodes: Comment[];
    pageInfo: {
      hasNextPage: boolean;
      endCursor: string | null;
    };
  } | null;
}

export interface Category {
  id: string;
  databaseId: number;
  name: string;
  slug: string;
  count: number;
  description?: string;
  // AI SEO 可选字段
  aiSeoTitle?: string;
  aiSeoDescription?: string;
  aiSeoJsonLd?: string;
  // Banner 图片字段
  bannerImageUrl?: string;
  bannerImage?: {
    sourceUrl: string;
    altText?: string;
    mediaDetails?: {
      width: number;
      height: number;
    };
  };
}

export interface Tag {
  id: string;
  databaseId: number;
  name: string;
  slug: string;
  count: number;
  description?: string;
  // AI SEO 可选字段
  aiSeoTitle?: string;
  aiSeoDescription?: string;
  aiSeoJsonLd?: string;
  // Banner 图片字段
  bannerImageUrl?: string;
  bannerImage?: {
    sourceUrl: string;
    altText?: string;
    mediaDetails?: {
      width: number;
      height: number;
    };
  };
}

export interface User {
  id: string;
  name: string;
  slug: string;
  description?: string;
  avatar?: {
    url: string;
  };
}

export interface CustomPost {
  id: string;
  title: string;
  date: string;
  slug: string;
  uri: string;
  featuredImage?: {
    node: {
      sourceUrl: string;
      altText?: string;
    }
  };
  author?: {
    node: {
      id: string;
      name: string;
      slug: string;
      nickname?: string;
      avatar?: {
        url: string;
      };
    }
  };
}

export interface Comment {
  __typename?: string;
  id: string;
  databaseId: number;
  content: string;
  date: string;
  parentId?: string;
  status: string;
  author?: {
    __typename?: string;
    node: {
      __typename?: string;
      name: string;
      url?: string;
      avatar?: {
        __typename?: string;
        url: string;
      }
    }
  };
  commentedOn?: {
    node: {
      id: string;
      title?: string;
      slug?: string;
    }
  };
}

export interface PostTemplateType {
  templateType?: string[];
  subtitle?: string;
  articleAuthor?: string;
  articleSource?: string;
  copyrightType?: string[];
  videoUrl?: string;
  additionalImages?: {
    nodes: {
      sourceUrl: string;
      altText?: string;
    }[];
  };
}