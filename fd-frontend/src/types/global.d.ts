// 全局类型声明

// 扩展Window接口以包含我们的全局错误变量
interface Window {
  // GraphQL错误相关
  __LAST_GRAPHQL_ERROR__: any | null;
  __ENHANCED_GRAPHQL_ERROR__: Error | null;
  __ENHANCED_NETWORK_ERROR__: Error | null;
}

// 声明全局变量，使其在类型检查中可用
declare global {
  interface Window {
    __LAST_GRAPHQL_ERROR__: any | null;
    __ENHANCED_GRAPHQL_ERROR__: Error | null;
    __ENHANCED_NETWORK_ERROR__: Error | null;
  }
}

export {}; 