/**
 * 用户相关类型定义
 */

/**
 * 会员等级信息
 */
export interface MemberLevel {
  id: number;
  name: string;
  description?: string;
  priority?: number;
  price?: number;
  duration?: number;
  durationUnit?: 'days' | 'months' | 'years';
  tier?: string;
}

/**
 * 用户基本信息
 */
export interface User {
  id: string;
  databaseId: number;
  name: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  description?: string;
  nickname?: string;
  slug: string;
  roles?: {
    nodes: {
      name: string;
    }[];
  };
  avatar?: {
    url: string;
  };
  phone?: string;
  /**
   * 用户主角色，例如 subscriber、author、editor、administrator
   */
  role?: string;
  memberLevel?: MemberLevel;
  memberExpiration?: string;
}

/**
 * 认证响应数据
 */
export interface AuthResponse {
  authToken: string;
  user: User;
}

/**
 * 用户注册输入数据
 */
export interface RegisterUserInput {
  username: string;
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  code?: string;
}

/**
 * 用户登录输入数据
 */
export interface LoginUserInput {
  username: string;
  password: string;
}

/**
 * 重置密码输入数据
 */
export interface ResetPasswordInput {
  key: string;
  login: string;
  password: string;
}

/**
 * 更新用户资料输入数据
 */
export interface UpdateUserInput {
  id?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  description?: string;
  nickname?: string;
  password?: string;
}

/**
 * 手机验证码发送输入数据
 */
export interface SendPhoneCodeInput {
  phone: string;
  nationCode?: string;
  verify?: boolean;
  code?: string;
}

/**
 * 手机号登录输入数据
 */
export interface PhoneLoginInput {
  phone: string;
  code: string;
  nationCode?: string;
}

/**
 * 手机号注册输入数据
 */
export interface PhoneRegisterInput {
  phone: string;
  code: string;
  nationCode?: string;
  username?: string;
  displayName?: string;
  password?: string;
}

/**
 * 绑定手机号输入数据
 */
export interface BindPhoneInput {
  phone: string;
  token: string;
  nationCode?: string;
}

/**
 * 手机验证响应数据
 */
export interface PhoneAuthResponse {
  success: boolean;
  message: string;
  user?: User;
  authToken?: string;
}

/**
 * 使用验证令牌注册输入数据
 */
export interface RegisterWithTokenInput {
  username: string;
  email: string;
  password: string;
  token: string;
}

/**
 * 使用验证令牌进行手机注册输入数据
 */
export interface PhoneRegisterWithTokenInput {
  phone: string;
  username?: string;
  password?: string;
  displayName?: string;
  token: string;
  nationCode?: string;
}

/**
 * 绑定邮箱输入数据
 */
export interface BindEmailInput {
  email: string;
  token: string;
} 