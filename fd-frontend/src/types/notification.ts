/**
 * 通知相关类型定义
 */

/**
 * 通知类型枚举
 */
export enum NotificationType {
  SYSTEM = 'system',
  ACCOUNT = 'account',
  PAYMENT = 'payment',
  MEMBER = 'member'
}

/**
 * 通知状态枚举
 */
export enum NotificationStatus {
  UNREAD = 'unread',
  READ = 'read'
}

/**
 * 通知接口
 */
export interface Notification {
  id: string;
  userId: number;
  title: string;
  content: string;
  type: NotificationType;
  typeName: string;
  status: NotificationStatus;
  createdAt: string;
  readAt: string | null;
}

/**
 * 通知响应接口
 */
export interface NotificationResponse {
  success: boolean;
  notification: Notification;
}

/**
 * 删除通知响应接口
 */
export interface DeleteNotificationResponse {
  success: boolean;
  deletedId: string;
}

/**
 * 标记所有通知已读响应接口
 */
export interface MarkAllNotificationsReadResponse {
  success: boolean;
  count: number;
}

/**
 * 通知查询参数接口
 */
export interface NotificationQueryParams {
  status?: NotificationStatus;
  type?: NotificationType;
  perPage?: number;
  page?: number;
} 