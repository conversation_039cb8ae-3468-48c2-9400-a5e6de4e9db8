// A common interface for paginated results
export interface PageInfo {
  hasNextPage: boolean
  endCursor?: string | null
}

// Base entity types from the API documentation
export interface UserAvatar {
  url: string
}

// NEW (v2.1.0): A simplified user type for conversation participants.
// This matches the `ConversationParticipant` type in the backend GraphQL schema.
export interface ConversationParticipant {
  id: string
  databaseId: number
  name: string
  avatar?: UserAvatar | null
}

export interface PrivateMessage {
  id: string
  databaseId: number
  content: string
  sentAt: string
  isRead: boolean
  sender: ConversationParticipant
}

export interface PrivateMessageConversation {
  id: string
  databaseId: number
  updatedAt: string
  unreadCount: number
  otherUser: ConversationParticipant
  lastMessage: {
    content: string
    sentAt: string
  }
  messages?: PrivateMessageConnection
}

// Connection types for pagination
export interface PrivateMessageEdge {
  cursor: string
  node: PrivateMessage
}

export interface PrivateMessageConnection {
  edges: PrivateMessageEdge[]
  pageInfo: PageInfo
}

export interface PrivateMessageConversationEdge {
  cursor: string
  node: PrivateMessageConversation
}

export interface PrivateMessageConversationConnection {
  edges: PrivateMessageConversationEdge[]
  pageInfo: PageInfo
}

// Types for Viewer queries
export interface ViewerPrivateConversations {
  viewer: {
    id: string
    privateConversations: PrivateMessageConversationConnection
  }
}

export interface ViewerUnreadMessageCount {
  viewer: {
    unreadMessageCount: number
  }
}

// Types for root queries
export interface PrivateConversationData {
  privateConversation: PrivateMessageConversation
}

// Types for Mutations
export interface SendPrivateMessagePayload {
  sendPrivateMessage: {
    success: boolean
    sentMessage: {
      id: string
      content: string
    }
    conversation: {
      id: string
    }
  }
}

export interface MarkConversationAsReadPayload {
  markConversationAsRead: {
    success: boolean
    conversation: {
      id: string
      unreadCount: number
    }
  }
}

export interface DeleteConversationPayload {
  deleteConversation: {
    success: boolean
    deletedConversationId: string
  }
} 