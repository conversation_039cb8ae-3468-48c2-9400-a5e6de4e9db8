import { Block } from './block';

/**
 * 页面类型定义
 */
export interface Page {
  id: string;
  title: string;
  content: string;
  date: string;
  modified?: string;
  slug: string;
  uri: string;
  status?: string;
  author?: {
    node: {
      id: string;
      name: string;
      slug: string;
      avatar?: {
        url: string;
      };
    };
  };
  featuredImage?: {
    node: {
      sourceUrl: string;
      altText?: string;
    };
  };
  blocks?: Block[];
}
