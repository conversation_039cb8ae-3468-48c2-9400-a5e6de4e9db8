/**
 * 区块基本属性接口
 */
export interface Block {
  __typename: string;
  name?: string;
  isDynamic: boolean;
  dynamicContent?: string;
  saveContent?: string;
  attributesJSON?: string;
  innerBlocks?: Block[];
}

/**
 * 从Block的attributesJSON字符串解析属性
 * @param block 区块对象
 * @returns 解析后的属性对象
 */
export function getBlockAttributes(block: Block): any {
  if (!block) return {};
  
  try {
    // 尝试从JSON字符串解析属性
    if (block.attributesJSON) {
      return JSON.parse(block.attributesJSON);
    }
    return {};
  } catch (error) {
    console.error('解析区块属性失败:', error);
    return {};
  }
}

/**
 * 段落区块属性
 */
export interface ParagraphBlockAttributes {
  content: string;
  align?: string;
  dropCap?: boolean;
  backgroundColor?: string;
  textColor?: string;
}

/**
 * 标题区块属性
 */
export interface HeadingBlockAttributes {
  content: string;
  level: number;
  align?: string;
  textColor?: string;
}

/**
 * 图像区块属性
 */
export interface ImageBlockAttributes {
  url: string;
  alt: string;
  caption?: string;
  width?: number;
  height?: number;
  sizeSlug?: string;
  align?: string;
}

/**
 * 列表区块属性
 */
export interface ListBlockAttributes {
  values: string;
  ordered?: boolean;
  start?: number;
}

/**
 * 引用区块属性
 */
export interface QuoteBlockAttributes {
  value: string;
  citation?: string;
  align?: string;
}

/**
 * 段落区块类型
 */
export interface ParagraphBlock extends Block {
  __typename: 'CoreParagraphBlock';
}

/**
 * 标题区块类型
 */
export interface HeadingBlock extends Block {
  __typename: 'CoreHeadingBlock';
}

/**
 * 图像区块类型
 */
export interface ImageBlock extends Block {
  __typename: 'CoreImageBlock';
}

/**
 * 列表区块类型
 */
export interface ListBlock extends Block {
  __typename: 'CoreListBlock';
}

/**
 * 引用区块类型
 */
export interface QuoteBlock extends Block {
  __typename: 'CoreQuoteBlock';
} 