/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: 'var(--primary-color)',
          50: '#eef2ff',
          100: '#e0e7ff',
          200: '#c7d2fe',
          300: '#a5b4fc',
          400: '#818cf8',
          500: 'var(--primary-color, #6366f1)', // 使用CSS变量，默认值为Tailwind蓝色
          600: '#4f46e5',
          700: '#4338ca',
          800: '#3730a3',
          900: '#312e81',
          950: '#1e1b4b',
        },
        secondary: {
          DEFAULT: 'var(--secondary-color)',
          500: 'var(--secondary-color, #4285f4)',
        },
        success: {
          DEFAULT: 'var(--success-color)',
          light: '#ecfdf5',
          main: 'var(--success-color, #10b981)',
          dark: '#065f46',
        },
        warning: {
          DEFAULT: 'var(--amber-color)',
          light: '#fffbeb',
          main: 'var(--amber-color, #f59e0b)',
          dark: '#92400e',
        },
        error: {
          DEFAULT: 'var(--error-color)',
          light: '#fef2f2',
          main: 'var(--error-color, #ef4444)',
          dark: '#991b1b',
        },
        rose: {
          DEFAULT: 'var(--rose-color)',
          500: 'var(--rose-color, #EC407A)',
        },
        background: {
          DEFAULT: 'var(--background-color)',
          light: 'var(--background-color, #ffffff)',
          dark: 'var(--dark-background-color, #121212)',
        },
      },
      fontFamily: {
        sans: ['var(--body-font)', 'Inter', 'var(--font-inter)', 'sans-serif'],
        serif: ['"Noto Serif SC"', 'serif'],
        heading: ['var(--heading-font)', 'Inter', 'var(--font-inter)', 'sans-serif'],
      },
      borderRadius: {
        'sm': 'var(--radius-small, 0.125rem)',
        'md': 'var(--radius-medium, 0.375rem)',
        'lg': 'var(--radius-large, 0.5rem)',
      },
      boxShadow: {
        'sm': 'var(--shadow-small, 0 1px 2px 0 rgba(0, 0, 0, 0.05))',
        'md': 'var(--shadow-medium, 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1))',
        'lg': 'var(--shadow-large, 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1))',
      },
      typography: (theme) => ({
        DEFAULT: {
          css: {
            '--tw-prose-body': theme('colors.gray.900'),
            '--tw-prose-headings': theme('colors.gray.900'),
            '--tw-prose-links': 'var(--primary-color)',
            a: {
              color: 'var(--primary-color)',
              '&:hover': {
                color: theme('colors.primary.600'),
              },
            },
            h1: {
              fontFamily: 'var(--heading-font)',
              color: theme('colors.gray.900'),
            },
            h2: {
              fontFamily: 'var(--heading-font)',
              color: theme('colors.gray.900'),
            },
            h3: {
              fontFamily: 'var(--heading-font)',
              color: theme('colors.gray.900'),
            },
            h4: {
              fontFamily: 'var(--heading-font)',
              color: theme('colors.gray.900'),
            },
          },
        },
        dark: {
          css: {
            '--tw-prose-body': theme('colors.gray.200'),
            '--tw-prose-headings': theme('colors.gray.100'),
            '--tw-prose-links': 'var(--primary-color)',
            color: theme('colors.gray.200'),
            a: {
              color: 'var(--primary-color)',
              '&:hover': {
                color: theme('colors.primary.300'),
              },
            },
            h1: {
              color: theme('colors.gray.100'),
            },
            h2: {
              color: theme('colors.gray.100'),
            },
            h3: {
              color: theme('colors.gray.100'),
            },
            h4: {
              color: theme('colors.gray.100'),
            },
          },
        },
      }),
      keyframes: {
        shake: {
          '0%, 100%': { transform: 'translateX(0)' },
          '10%, 30%, 50%, 70%, 90%': { transform: 'translateX(-5px)' },
          '20%, 40%, 60%, 80%': { transform: 'translateX(5px)' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'fade-in-up': {
          '0%': {
            opacity: '0',
            transform: 'translateY(20px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)'
          },
        }
      },
      animation: {
        shake: 'shake 0.8s cubic-bezier(.36,.07,.19,.97) both',
        fadeIn: 'fadeIn 0.5s ease-in-out forwards',
        'fade-in-up': 'fade-in-up 0.6s ease-out forwards',
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
} 