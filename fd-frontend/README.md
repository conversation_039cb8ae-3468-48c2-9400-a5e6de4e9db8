# Frontend Next.js应用

这是一个基于Next.js的最小化应用，使用App Router架构。

## 功能特点

- 使用Next.js 14最新App Router架构
- 集成TailwindCSS
- 针对Docker部署优化
- 支持生产环境优化

## 开发环境

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 生产环境部署

### 使用Docker

```bash
# 构建并启动容器
docker-compose up -d --build

# 查看容器日志
docker logs -f frontend
```

### 手动部署

```bash
# 安装依赖
npm install

# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

## 环境变量

复制`.env.local.example`为`.env.local`并按需修改环境变量。

## 技术栈

- Next.js 14
- React 18
- TailwindCSS
- TypeScript 